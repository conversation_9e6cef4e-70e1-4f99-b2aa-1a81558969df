# 订单详情页面退款信息展示功能说明

## 概述
在 `order-detail.vue` 页面中添加了退款信息的详细展示功能，参考了 `refund-detail.vue` 的设计，为用户提供完整的退款信息查看体验。

## 主要更新内容

### 1. API接口扩展
在 `src/api/refund.api.ts` 中新增了根据订单ID获取退款信息的接口：

```typescript
/**
 * @Description: 根据订单ID获取退款信息
 */
export const getRefundByOrderId = (orderId: string, customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/by-order/${orderId}`, { customerId }, HeaderType.AUTH.code);
};
```

### 2. 页面结构更新

#### 2.1 导入依赖
- 新增退款API导入：`getRefundByOrderId`
- 新增退款类型导入：`RefundOrder, REFUND_STATUS_MAP`
- 新增时间轴步骤接口：`TimelineStep`

#### 2.2 数据属性
- 新增 `refundInfo: RefundOrder | null = null` 用于存储退款信息

#### 2.3 模板结构
在商品信息和评价信息之间添加了退款信息展示区域，包括：

1. **退款状态卡片**
   - 状态图标和颜色指示
   - 退款状态文本和描述
   - 退款金额显示

2. **退款基本信息**
   - 退款单号
   - 申请时间
   - 退款原因

3. **退款进度时间轴**
   - 提交申请
   - 商家审核
   - 退款处理
   - 退款成功

4. **操作按钮**
   - 查看退款详情按钮

### 3. 业务逻辑实现

#### 3.1 数据加载
在 `loadOrderDetail()` 方法中添加了退款信息加载：

```typescript
// 加载退款信息（如果存在）
await this.loadRefundInfo();
```

#### 3.2 核心方法

1. **loadRefundInfo()** - 加载退款信息
   - 调用 `getRefundByOrderId` API
   - 处理成功和失败情况
   - 无退款信息时不显示错误

2. **退款状态相关方法**
   - `getRefundStatusText()` - 获取状态文本
   - `getRefundStatusDescription()` - 获取状态描述
   - `getRefundStatusIcon()` - 获取状态图标
   - `getRefundStatusIconClass()` - 获取图标样式类

3. **工具方法**
   - `formatRefundTime()` - 格式化退款时间
   - `goToRefundDetail()` - 跳转到退款详情页面

4. **计算属性**
   - `refundTimelineSteps` - 生成退款进度时间轴步骤

### 4. 样式设计

#### 4.1 退款状态卡片样式
- 渐变背景色
- 状态图标根据不同状态显示不同颜色
- 响应式布局适配

#### 4.2 时间轴样式
- 圆点指示器
- 完成状态的视觉反馈
- 紧凑的布局设计

#### 4.3 信息展示样式
- 清晰的标签-值对布局
- 统一的间距和字体大小
- 与现有页面风格保持一致

## 功能特点

### 1. 智能显示
- 只有存在退款信息时才显示退款区域
- 根据退款状态动态显示不同的进度步骤
- 状态图标和颜色与退款状态对应

### 2. 信息完整
- 展示退款的关键信息（单号、金额、原因、时间）
- 提供退款进度的可视化时间轴
- 支持跳转到完整的退款详情页面

### 3. 用户体验
- 与订单详情页面的整体设计风格保持一致
- 清晰的信息层次和视觉引导
- 便捷的操作入口

## 技术实现要点

### 1. 错误处理
- API调用失败时不影响页面其他功能
- 无退款信息时正常处理，不显示错误提示

### 2. 数据安全
- 验证用户权限，只能查看自己的退款信息
- 参数校验和异常处理

### 3. 性能优化
- 退款信息与订单信息并行加载
- 计算属性缓存时间轴步骤

## 后续扩展建议

1. **状态更新**
   - 可以考虑添加实时状态更新功能
   - 支持退款状态变更的推送通知

2. **操作功能**
   - 在特定状态下支持取消退款申请
   - 添加退款进度查询功能

3. **视觉优化**
   - 可以添加更多的动画效果
   - 支持深色模式适配

## 注意事项

1. **API依赖**
   - 需要后端提供 `/api/v1/mall/refund/by-order/{orderId}` 接口
   - 接口需要支持根据订单ID查询退款信息

2. **权限控制**
   - 确保用户只能查看自己的退款信息
   - 需要传递正确的 customerId 参数

3. **兼容性**
   - 保持与现有订单详情页面功能的兼容性
   - 不影响原有的订单状态显示和操作
