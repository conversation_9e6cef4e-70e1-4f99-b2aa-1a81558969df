// 通用声明
declare type ClassName = { [className: string]: any } | ClassName[] | string;

declare interface ImportMeta {
  env: {
    MODE: 'mock' | 'development' | 'test' | 'release';
    VITE_APP_AUTH_API: string;
    VITE_APP_API_PREFIX: string;
    VITE_APP_LOGIN_URL: string;
    VITE_APP_STATISTICS_API: string;
    VITE_APP_ADMIN_API: string;
    VITE_APP_ADMIN_API: string;
    VITE_APP_BUSINESS_API: string;
    VITE_APP_RECODING_API: string;
  };
}

declare module '*.svg' {
  const content: string;
  export default content;
}
