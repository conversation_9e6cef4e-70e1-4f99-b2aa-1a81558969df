# 订单接口退款状态查询功能添加说明

## 修改概述

在 `hxsy-store/api/v1/mall/order/page` 接口中添加了 `refundStatus` 字段查询逻辑，使订单列表查询支持按退款状态筛选，并在订单响应中包含完整的退款信息。

## 修改内容

### 1. 请求参数扩展

**文件**: `hxsy-store/src/main/java/cn/hxsy/request/MallOrderRequest.java`

在 `OrderQueryRequest` 内部类中新增字段：

```java
@ApiModelProperty(value = "退款状态：PENDING-待审核，APPROVED-已同意，REJECTED-已拒绝，PROCESSING-退款处理中，SUCCESS-退款成功，FAILED-退款失败")
private String refundStatus;
```

**支持的退款状态值**：
- `PENDING` - 待审核
- `APPROVED` - 已同意
- `REJECTED` - 已拒绝
- `PROCESSING` - 退款处理中
- `SUCCESS` - 退款成功
- `FAILED` - 退款失败

### 2. 响应对象扩展

**文件**: `hxsy-store/src/main/java/cn/hxsy/response/MallOrderResponse.java`

在 `MallOrderResponse` 类中新增退款相关字段：

```java
// ==================== 退款相关字段 ====================

@ApiModelProperty(value = "退款ID")
private Long refundId;

@ApiModelProperty(value = "退款单号")
private String refundNo;

@ApiModelProperty(value = "退款状态")
private String refundStatus;

@ApiModelProperty(value = "退款状态文本")
private String refundStatusText;

@ApiModelProperty(value = "退款金额")
private BigDecimal refundAmount;

@ApiModelProperty(value = "退款原因")
private String refundReason;

@ApiModelProperty(value = "退款申请时间")
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private LocalDateTime refundApplyTime;

@ApiModelProperty(value = "审核结果")
private String auditResult;

@ApiModelProperty(value = "审核时间")
@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
private LocalDateTime auditTime;
```

### 3. 数据库查询优化

**文件**: `hxsy-store/src/main/resources/mapper/MallOrderMapper.xml`

#### 3.1 ResultMap 映射扩展

在 `MallOrderResponseMap` 中添加退款字段映射：

```xml
<!-- 退款相关字段 -->
<result column="refund_id" property="refundId"/>
<result column="refund_no" property="refundNo"/>
<result column="refund_status" property="refundStatus"/>
<result column="refund_status_text" property="refundStatusText"/>
<result column="refund_amount" property="refundAmount"/>
<result column="refund_reason" property="refundReason"/>
<result column="refund_apply_time" property="refundApplyTime"/>
<result column="audit_result" property="auditResult"/>
<result column="audit_time" property="auditTime"/>
```

#### 3.2 分页查询 SQL 优化

在 `queryOrderPage` 查询中：

**新增表关联**：
```sql
LEFT JOIN mall_order_refunds r ON o.id = r.order_id AND r.status = 1
```

**新增查询字段**：
```sql
-- 退款相关字段
r.id as refund_id,
r.refund_no,
r.refund_status,
r.status_text as refund_status_text,
r.refund_amount,
r.refund_reason,
r.apply_time as refund_apply_time,
r.audit_result,
r.audit_time,
```

**新增筛选条件**：
```xml
<if test="request.refundStatus != null and request.refundStatus != ''">
    AND r.refund_status = #{request.refundStatus}
</if>
```

#### 3.3 订单详情查询 SQL 优化

在 `getOrderDetailById` 查询中同样添加了退款表关联和字段查询，确保订单详情也包含完整的退款信息。

## 功能特性

### 1. 退款状态筛选

**接口**: `POST /api/v1/mall/order/page`

**请求示例**：
```json
{
  "customerId": 123,
  "refundStatus": "PENDING",
  "pageNum": 1,
  "pageSize": 10
}
```

**功能说明**：
- 支持按退款状态筛选订单
- 可与其他筛选条件组合使用
- 只返回有退款申请且符合状态条件的订单

### 2. 退款信息展示

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "records": [
      {
        "id": 1001,
        "orderNumber": "ORD20250109001",
        "orderStatus": "paid",
        "totalAmount": 299.00,
        // 订单基本信息...
        
        // 退款信息
        "refundId": 2001,
        "refundNo": "REF20250109001",
        "refundStatus": "PENDING",
        "refundStatusText": "待审核",
        "refundAmount": 299.00,
        "refundReason": "商品质量问题",
        "refundApplyTime": "2025-01-09 14:30:00",
        "auditResult": null,
        "auditTime": null
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 3. 兼容性保证

- **向后兼容**：不传 `refundStatus` 参数时，查询所有订单（包含有退款和无退款的）
- **数据完整性**：无退款申请的订单，退款相关字段返回 `null`
- **性能优化**：使用 `LEFT JOIN` 确保查询性能，避免遗漏订单数据

## 使用场景

### 1. 用户端场景

- **查看退款中的订单**：`refundStatus: "PENDING"`
- **查看退款成功的订单**：`refundStatus: "SUCCESS"`
- **查看被拒绝的退款**：`refundStatus: "REJECTED"`

### 2. 管理端场景

- **待处理退款列表**：`refundStatus: "PENDING"`
- **退款处理中列表**：`refundStatus: "PROCESSING"`
- **退款完成统计**：`refundStatus: "SUCCESS"`

### 3. 组合查询

```json
{
  "customerId": 123,
  "orderStatus": "paid",
  "refundStatus": "PENDING",
  "pageNum": 1,
  "pageSize": 10
}
```

查询指定用户已支付但退款待审核的订单。

## 数据库影响

### 1. 查询性能

- **索引建议**：在 `mall_order_refunds` 表上建立复合索引
  ```sql
  CREATE INDEX idx_order_refund_status ON mall_order_refunds(order_id, refund_status, status);
  ```

- **查询优化**：使用 `LEFT JOIN` 避免笛卡尔积，保证查询效率

### 2. 数据一致性

- **关联条件**：`r.status = 1` 确保只关联有效的退款记录
- **唯一性保证**：一个订单最多只有一条有效退款记录

## 注意事项

### 1. 业务逻辑

- 订单和退款是一对一关系（一个订单最多一次退款）
- 退款状态筛选只对有退款申请的订单生效
- 无退款申请的订单在使用退款状态筛选时不会被返回

### 2. 前端适配

- 前端需要处理退款字段可能为 `null` 的情况
- 退款状态显示需要根据 `refundStatus` 和 `refundStatusText` 字段
- 时间字段格式为 `yyyy-MM-dd HH:mm:ss`

### 3. 接口兼容

- 现有不传 `refundStatus` 的调用方式完全兼容
- 新增字段不影响现有业务逻辑
- 响应结构向后兼容，只是新增了退款相关字段

## 测试建议

### 1. 功能测试

- 测试不同退款状态的筛选功能
- 测试退款状态与其他筛选条件的组合
- 测试无退款申请订单的正常显示

### 2. 性能测试

- 测试大数据量下的查询性能
- 验证索引的有效性
- 测试并发查询的稳定性

### 3. 兼容性测试

- 验证现有调用方式的兼容性
- 测试新字段的正确返回
- 验证 `null` 值的正确处理

这次修改为订单查询接口增加了强大的退款状态筛选功能，同时保持了良好的向后兼容性和查询性能。
