{
  "compilerOptions": {
    "target": "es2015",
    "module": "esnext",
    "strict": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "noEmit": false,
    "inlineSourceMap": false,
    "baseUrl": ".",
    "types": [
      "webpack-env",
    ],
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@private/*": [
        "private/*"
      ],
      "tslib" : ["node_modules/tslib/tslib.d.ts"],
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx"
  ],
  "exclude": [
    "node_modules"
  ]
}
