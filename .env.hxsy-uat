MODE = development
# 打包路径
VITE_BASE_URL = /hxsy-ui/

# APP名称
VITE_APP_NAME = 社群管理系统

# 小程序APPID
VITE_APP_ID = wxc3246f7c75bdd2ee
# 图片存放地址
VITE_IMAGE_ADDRESS= staticImage
# 商城图片存放地址
VITE_SHOP_IMAGE_ADDRESS= storeStaticImage
# 测试环境图片存放域名
VITE_TEST_IMAGE_DOMAIN= https://hxsy-dev-1351054319.cos.ap-guangzhou.myqcloud.com
# 服务器回调地址
VITE_SERVER_CALLBACK_URL = https://dev.huaxiacomp.cn/gateway/hxsy-admin/api/v1/wecom/callback/

# AUTH服务前缀
VITE_APP_AUTH_API = /hxsy-auth

# ADMIN服务前缀
VITE_APP_ADMIN_API = /hxsy-admin

# BUSINESS服务前缀
VITE_APP_BUSINESS_API = /hxsy-business

# STORE服务前缀
VITE_APP_STORE_API = /hxsy-store

# API服务前缀
VITE_APP_API_PREFIX = /api/v1

# 登录地址
VITE_APP_LOGIN_URL= /auth/web-login

# 图标地址
# VITE_APP_ICON_URL = ""

# COS前缀
VITE_APP_COS_URL = ""

# 业务PCCOS前缀
VITE_BUSINESS_COS_URL = ""

// RECODING
VITE_APP_RECODING_API = ""

// 用户服务
VITE_APP_ADMIN_API = http://127.0.0.1:10100
// 系统管理
VITE_APP_BUSINESS_API = http://127.0.0.1:10200
