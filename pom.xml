<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.6</version>
        <relativePath/>
    </parent>

    <groupId>cn.hxsy</groupId>
    <artifactId>hxsy-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.7.6</spring-boot.version>
        <spring-cloud.version>2021.0.6</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <dubbo.version>3.2.10</dubbo.version>
    </properties>

    <modules>
        <module>hxsy-common</module>
        <module>hxsy-business</module>
        <module>hxsy-admin</module>
        <module>hxsy-auth</module>
        <module>hxsy-store</module>
        <module>hxsy-gateway</module>
    </modules>

    <!--父工程引入框架强制指定依赖版本，方便后续子工程直接应用对应版本-->
    <dependencyManagement>
        <dependencies>
            <!--微服务框架-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>5.3.24</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>5.3.24</version>
            </dependency>
            <!-- rpc调用 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <!-- Spring Cloud OpenFeign -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>3.1.2</version>
            </dependency>
            <!-- FastJSON -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <!-- Spring Web -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <!--公共依赖-->
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-rpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-datasource</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-cache</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-sa-token</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hxsy</groupId>
                <artifactId>hxsy-web</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>*/**</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>