-- 添加推荐商品测试数据
-- 确保推荐商品表有数据可以显示

-- 首先检查是否有商品数据
SELECT COUNT(*) as product_count FROM mall_products WHERE status = 1 AND is_active = 1;

-- 检查是否有推荐商品数据
SELECT COUNT(*) as recommend_count FROM mall_recommend_products WHERE status = 1;

-- 如果没有推荐商品数据，添加一些测试数据
-- 注意：需要确保 product_id 对应的商品在 mall_products 表中存在

-- 方案1：如果有商品数据，将前几个商品添加为推荐商品
INSERT INTO mall_recommend_products (product_id, sort_order, status, created_by, created_at, updated_at)
SELECT 
    id as product_id,
    ROW_NUMBER() OVER (ORDER BY sales_count DESC, created_at DESC) as sort_order,
    1 as status,
    'system' as created_by,
    NOW() as created_at,
    NOW() as updated_at
FROM mall_products 
WHERE status = 1 AND is_active = 1 
LIMIT 10
ON DUPLICATE KEY UPDATE 
    sort_order = VALUES(sort_order),
    updated_at = NOW();

-- 方案2：如果没有商品数据，先添加一些测试商品
-- 检查分类是否存在，如果不存在先添加
INSERT IGNORE INTO mall_categories (id, name, parent_id, sort_order, status, created_by, created_at, updated_at)
VALUES 
(1, '电子产品', 0, 1, 1, 'system', NOW(), NOW()),
(2, '服装鞋帽', 0, 2, 1, 'system', NOW(), NOW()),
(3, '食品饮料', 0, 3, 1, 'system', NOW(), NOW());

-- 添加测试商品（如果不存在）
INSERT IGNORE INTO mall_products (
    id, name, subtitle, description, price, original_price, stock, 
    image, category_id, specs, tags, is_active, sales_count, 
    status, created_by, created_at, updated_at
) VALUES 
(1, 'iPhone 15 Pro Max', '全新iPhone 15 Pro Max', '苹果最新旗舰手机，性能强劲', 9999.00, 10999.00, 100, 
 '/static/images/test.jpeg', 1, '{"颜色": ["深空黑", "银色", "金色"], "容量": ["128GB", "256GB", "512GB"]}', 
 '["新品", "热销", "5G"]', 1, 1500, 1, 'system', NOW(), NOW()),

(2, 'MacBook Pro 14英寸', '专业级笔记本电脑', 'M3芯片，专业性能，创作利器', 14999.00, 16999.00, 50, 
 '/static/images/test.jpeg', 1, '{"颜色": ["深空灰", "银色"], "配置": ["M3", "M3 Pro", "M3 Max"]}', 
 '["专业", "创作", "高性能"]', 1, 800, 1, 'system', NOW(), NOW()),

(3, 'Nike Air Max 270', '经典运动鞋', '舒适透气，时尚百搭的运动鞋', 899.00, 1199.00, 200, 
 '/static/images/test.jpeg', 2, '{"尺码": ["36", "37", "38", "39", "40", "41", "42"], "颜色": ["黑色", "白色", "蓝色"]}', 
 '["运动", "时尚", "舒适"]', 1, 2000, 1, 'system', NOW(), NOW()),

(4, '优衣库基础T恤', '纯棉舒适T恤', '100%纯棉材质，舒适透气，多色可选', 99.00, 129.00, 500, 
 '/static/images/test.jpeg', 2, '{"尺码": ["S", "M", "L", "XL", "XXL"], "颜色": ["白色", "黑色", "灰色", "蓝色"]}', 
 '["基础款", "纯棉", "舒适"]', 1, 3000, 1, 'system', NOW(), NOW()),

(5, '三只松鼠坚果礼盒', '精选坚果组合', '多种坚果组合，营养健康，送礼佳品', 128.00, 168.00, 300, 
 '/static/images/test.jpeg', 3, '{"规格": ["500g", "1000g"], "口味": ["原味", "盐焗", "蜂蜜"]}', 
 '["健康", "坚果", "礼盒"]', 1, 1200, 1, 'system', NOW(), NOW()),

(6, '农夫山泉天然水', '天然矿泉水', '取自优质水源，天然纯净，健康饮用', 45.00, 55.00, 1000, 
 '/static/images/test.jpeg', 3, '{"规格": ["550ml*24瓶", "1.5L*12瓶"], "类型": ["天然水", "矿物质水"]}', 
 '["天然", "健康", "矿泉水"]', 1, 5000, 1, 'system', NOW(), NOW()),

(7, 'AirPods Pro 2', '主动降噪耳机', '苹果无线耳机，主动降噪，音质出色', 1899.00, 2199.00, 150, 
 '/static/images/test.jpeg', 1, '{"颜色": ["白色"], "功能": ["主动降噪", "透明模式", "空间音频"]}', 
 '["无线", "降噪", "音质"]', 1, 900, 1, 'system', NOW(), NOW()),

(8, '小米13 Ultra', '徕卡影像旗舰', '徕卡专业影像，骁龙8 Gen2，性能旗舰', 5999.00, 6999.00, 80, 
 '/static/images/test.jpeg', 1, '{"颜色": ["黑色", "白色", "绿色"], "容量": ["256GB", "512GB", "1TB"]}', 
 '["徕卡", "影像", "旗舰"]', 1, 600, 1, 'system', NOW(), NOW());

-- 将这些商品添加为推荐商品
INSERT INTO mall_recommend_products (product_id, sort_order, status, created_by, created_at, updated_at)
VALUES 
(1, 1, 1, 'system', NOW(), NOW()),
(2, 2, 1, 'system', NOW(), NOW()),
(3, 3, 1, 'system', NOW(), NOW()),
(4, 4, 1, 'system', NOW(), NOW()),
(5, 5, 1, 'system', NOW(), NOW()),
(6, 6, 1, 'system', NOW(), NOW()),
(7, 7, 1, 'system', NOW(), NOW()),
(8, 8, 1, 'system', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    sort_order = VALUES(sort_order),
    status = VALUES(status),
    updated_at = NOW();

-- 验证数据是否添加成功
SELECT 
    r.id,
    r.product_id,
    p.name,
    p.price,
    r.sort_order,
    r.status
FROM mall_recommend_products r
INNER JOIN mall_products p ON r.product_id = p.id
WHERE r.status = 1
ORDER BY r.sort_order ASC;

-- 测试推荐商品查询（模拟前端API调用）
SELECT 
    p.id,
    p.name,
    p.subtitle,
    p.description,
    p.price,
    p.original_price,
    p.stock,
    p.image,
    p.images,
    p.detail_images,
    p.category_id,
    c.name as category_name,
    p.specs,
    p.tags,
    p.promotions,
    p.is_active,
    p.sales_count,
    p.created_at,
    p.updated_at
FROM mall_recommend_products r
INNER JOIN mall_products p ON r.product_id = p.id
LEFT JOIN mall_categories c ON p.category_id = c.id
WHERE r.status = 1 
AND p.status = 1 
AND p.is_active = 1
ORDER BY r.sort_order ASC, r.created_at DESC
LIMIT 20;
