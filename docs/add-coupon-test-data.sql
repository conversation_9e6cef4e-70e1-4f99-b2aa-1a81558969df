-- 添加优惠券测试数据
-- 确保优惠券系统有数据可以显示和测试

-- 检查优惠券表是否存在数据
SELECT COUNT(*) as coupon_count FROM mall_coupons WHERE status = 1;
SELECT COUNT(*) as customer_coupon_count FROM mall_customer_coupons WHERE status = 1;

-- 添加优惠券模板数据
INSERT INTO mall_coupons (
    id, coupon_name, coupon_type, discount_value, min_amount, 
    total_quantity, used_quantity, start_time, end_time, 
    status, created_by, created_at, updated_at
) VALUES 
-- 现金券
(1, '新用户专享券', 'cash', 20.00, 100.00, 1000, 0, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 
 1, 'system', NOW(), NOW()),

(2, '满减优惠券', 'cash', 50.00, 300.00, 500, 0, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 
 1, 'system', NOW(), NOW()),

(3, '大额优惠券', 'cash', 100.00, 500.00, 200, 0, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 
 1, 'system', NOW(), NOW()),

-- 折扣券
(4, '会员专享折扣券', 'discount', 8.5, 200.00, 300, 0, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 
 1, 'system', NOW(), NOW()),

(5, '限时折扣券', 'discount', 9.0, 0.00, 1000, 0, 
 '2024-01-01 00:00:00', '2024-06-30 23:59:59', 
 1, 'system', NOW(), NOW()),

(6, '生日特惠券', 'cash', 30.00, 200.00, 100, 0, 
 '2024-01-01 00:00:00', '2024-12-31 23:59:59', 
 1, 'system', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    coupon_name = VALUES(coupon_name),
    discount_value = VALUES(discount_value),
    updated_at = NOW();

-- 为测试用户添加优惠券
-- 注意：这里假设用户ID为1，如果用户表中没有对应用户，需要先添加用户数据

-- 添加可使用的优惠券
INSERT INTO mall_customer_coupons (
    id, customer_id, coupon_id, coupon_name, coupon_type, 
    discount_value, min_amount, receive_time, expire_time, 
    coupon_status, status, created_by, created_at, updated_at
) VALUES 
-- 用户1的优惠券 - 可使用状态
(1, 1, 1, '新用户专享券', 'cash', 20.00, 100.00, 
 NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 
 'unused', 1, 'system', NOW(), NOW()),

(2, 1, 2, '满减优惠券', 'cash', 50.00, 300.00, 
 NOW(), DATE_ADD(NOW(), INTERVAL 60 DAY), 
 'unused', 1, 'system', NOW(), NOW()),

(3, 1, 4, '会员专享折扣券', 'discount', 8.5, 200.00, 
 NOW(), DATE_ADD(NOW(), INTERVAL 45 DAY), 
 'unused', 1, 'system', NOW(), NOW()),

-- 用户1的优惠券 - 已使用状态
(4, 1, 5, '限时折扣券', 'discount', 9.0, 0.00, 
 DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_ADD(NOW(), INTERVAL 20 DAY), 
 'used', 1, 'system', DATE_SUB(NOW(), INTERVAL 10 DAY), NOW()),

-- 用户1的优惠券 - 已过期状态
(5, 1, 6, '生日特惠券', 'cash', 30.00, 200.00, 
 DATE_SUB(NOW(), INTERVAL 40 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY), 
 'expired', 1, 'system', DATE_SUB(NOW(), INTERVAL 40 DAY), NOW()),

-- 添加更多可使用的优惠券
(6, 1, 3, '大额优惠券', 'cash', 100.00, 500.00, 
 NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY), 
 'unused', 1, 'system', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    coupon_name = VALUES(coupon_name),
    coupon_status = VALUES(coupon_status),
    updated_at = NOW();

-- 为其他测试用户添加优惠券（用户ID 2-5）
INSERT INTO mall_customer_coupons (
    customer_id, coupon_id, coupon_name, coupon_type, 
    discount_value, min_amount, receive_time, expire_time, 
    coupon_status, status, created_by, created_at, updated_at
) 
SELECT 
    user_id,
    coupon_id,
    coupon_name,
    coupon_type,
    discount_value,
    min_amount,
    NOW() as receive_time,
    DATE_ADD(NOW(), INTERVAL 30 DAY) as expire_time,
    'unused' as coupon_status,
    1 as status,
    'system' as created_by,
    NOW() as created_at,
    NOW() as updated_at
FROM (
    SELECT 2 as user_id, 1 as coupon_id, '新用户专享券' as coupon_name, 'cash' as coupon_type, 20.00 as discount_value, 100.00 as min_amount
    UNION ALL
    SELECT 2, 2, '满减优惠券', 'cash', 50.00, 300.00
    UNION ALL
    SELECT 3, 1, '新用户专享券', 'cash', 20.00, 100.00
    UNION ALL
    SELECT 3, 4, '会员专享折扣券', 'discount', 8.5, 200.00
    UNION ALL
    SELECT 4, 2, '满减优惠券', 'cash', 50.00, 300.00
    UNION ALL
    SELECT 5, 1, '新用户专享券', 'cash', 20.00, 100.00
) as test_data
WHERE NOT EXISTS (
    SELECT 1 FROM mall_customer_coupons 
    WHERE customer_id = test_data.user_id AND coupon_id = test_data.coupon_id
);

-- 验证数据是否添加成功
SELECT 
    '优惠券模板' as data_type,
    COUNT(*) as count
FROM mall_coupons 
WHERE status = 1

UNION ALL

SELECT 
    '用户优惠券' as data_type,
    COUNT(*) as count
FROM mall_customer_coupons 
WHERE status = 1;

-- 查看用户1的优惠券分布
SELECT 
    coupon_status,
    COUNT(*) as count
FROM mall_customer_coupons 
WHERE customer_id = 1 AND status = 1
GROUP BY coupon_status;

-- 测试查询用户优惠券（模拟前端API调用）
SELECT 
    id,
    customer_id,
    coupon_id,
    coupon_name,
    coupon_type,
    discount_value,
    min_amount,
    receive_time,
    expire_time,
    coupon_status,
    CASE 
        WHEN expire_time < NOW() THEN 'expired'
        ELSE coupon_status
    END as actual_status
FROM mall_customer_coupons 
WHERE customer_id = 1 AND status = 1
ORDER BY 
    CASE coupon_status 
        WHEN 'unused' THEN 1 
        WHEN 'used' THEN 2 
        WHEN 'expired' THEN 3 
        ELSE 4 
    END,
    receive_time DESC;

-- 查看可用于订单的优惠券（订单金额300元）
SELECT 
    id,
    coupon_name,
    coupon_type,
    discount_value,
    min_amount,
    expire_time
FROM mall_customer_coupons 
WHERE customer_id = 1 
    AND status = 1 
    AND coupon_status = 'unused'
    AND expire_time > NOW()
    AND min_amount <= 300.00
ORDER BY 
    CASE coupon_type 
        WHEN 'cash' THEN discount_value 
        WHEN 'discount' THEN (300.00 * (10 - discount_value) / 10)
        ELSE 0 
    END DESC;
