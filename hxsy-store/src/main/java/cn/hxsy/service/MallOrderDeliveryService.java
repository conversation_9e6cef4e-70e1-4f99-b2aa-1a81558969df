package cn.hxsy.service;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.MallOrderDelivery;
import cn.hxsy.request.MallOrderDeliveryDetailRequest;
import cn.hxsy.request.MallOrderDeliveryRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单配送主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 21:36:19
 */
public interface MallOrderDeliveryService extends IService<MallOrderDelivery> {

	/**
	* @description: 保存订单配送基本信息
	 * 运单号、物流公司、收件人、寄件人
	* @author: xiaQL
	* @date: 2025/9/7 17:10
	*/
	Result<Object> saveOrderDelivery(List<MallOrderDeliveryRequest> request);

	/**
	* @description: 查询订单配送基本信息
	* @author: xiaQL
	* @date: 2025/9/8 23:53
	*/
	Result<Object> queryOrderDelivery(MallOrderDeliveryRequest request);
}
