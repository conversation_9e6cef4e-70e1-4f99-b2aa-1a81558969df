package cn.hxsy.service;

import cn.hxsy.base.response.Result;
import cn.hxsy.entity.MallOrderRefund;
import cn.hxsy.request.ApplyRefundRequest;
import cn.hxsy.request.AuditRefundRequest;
import cn.hxsy.request.RefundQueryRequest;
import cn.hxsy.response.RefundDetailResponse;
import cn.hxsy.response.RefundResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;

/**
 * 退款服务接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface MallRefundService extends IService<MallOrderRefund> {

    /**
     * 申请退款
     *
     * @param request 退款申请请求
     * @return 退款单号
     */
    String applyRefund(ApplyRefundRequest request);

    /**
     * 查询退款列表
     *
     * @param request 查询条件
     * @return 退款列表
     */
    IPage<RefundResponse> getRefundList(RefundQueryRequest request);

    /**
     * 获取退款详情
     *
     * @param refundId 退款ID
     * @param customerId 用户ID
     * @return 退款详情
     */
    RefundDetailResponse getRefundDetail(Long refundId, Long customerId);

    /**
     * 商家审核退款
     *
     * @param request 审核请求
     * @return 审核结果
     */
    Boolean auditRefund(AuditRefundRequest request);

    /**
     * 查询商家待处理退款列表
     *
     * @param companyId 公司ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 待处理退款列表
     */
    IPage<RefundResponse> getPendingRefundList(Long companyId, Integer pageNum, Integer pageSize);

    /**
     * 取消退款申请
     *
     * @param refundId 退款ID
     * @param customerId 用户ID
     * @return 操作结果
     */
    Boolean cancelRefund(Long refundId, Long customerId);

    /**
     * 处理微信退款回调
     *
     * @param request 回调数据
     * @return 处理结果
     */
    Result<Object> handleWxRefundCallback(HttpServletRequest request);

    /**
     * 查询退款状态（用于检查退款进度）
     *
     * @param refundId 退款ID
     * @return 退款状态
     */
    String queryRefundStatus(Long refundId);

    /**
     * 检查订单退款资格
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 退款资格检查结果
     */
    cn.hxsy.response.RefundEligibilityResponse checkRefundEligibility(Long orderId, Long customerId);

    /**
     * 根据订单ID获取退款信息
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 退款详情
     */
    RefundDetailResponse getRefundByOrderId(Long orderId, Long customerId);
}