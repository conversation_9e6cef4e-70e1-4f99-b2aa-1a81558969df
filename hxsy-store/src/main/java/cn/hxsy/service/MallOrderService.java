package cn.hxsy.service;

import cn.hxsy.api.user.model.response.AppletInfoResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.response.*;
import cn.hxsy.entity.MallOrder;
import cn.hxsy.request.MallOrderRequest;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;

/**
 * 订单服务接口
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
public interface MallOrderService extends IService<MallOrder> {


    /**
     * 分页查询订单列表
     *
     * @param request 查询条件
     * @return 订单列表
     */
    IPage<MallOrderResponse> queryOrderPage(MallOrderRequest.OrderQueryRequest request);

    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 订单详情
     */
    MallOrderResponse getOrderDetail(Long orderId, Long customerId);

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 操作结果
     */
    Boolean cancelOrder(Long orderId, Long customerId);

    /**
     * 发货
     *
     * @param orderId 订单ID
     * @return 操作结果
     */
    Boolean shipOrder(String orderId);

    /**
     * 确认收货
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 操作结果
     */
    Boolean confirmOrder(Long orderId, Long customerId);

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    String generateOrderNumber();

    /**
    * @description: 处理微信支付回调
     * 1、回调验签处理
     * 2、更新系统内对应单号支付状态
    * @author: xiaQL
    * @date: 2025/8/20 15:04
    */
    Result<Object> handlePayCallback(HttpServletRequest request);

    /**
     * 创建订单并生成支付参数
     *
     * @param request 订单请求
     * @return 订单创建结果（包含支付参数）
     */
    CreateOrderResponse createOrderWithPayment(MallOrderRequest request);

    /**
     * 获取订单状态统计
     *
     * @param customerId 用户ID（可选，如果为null则统计所有订单）
     * @return 订单状态统计
     */
    OrderStatusStatsResponse getOrderStatusStats(Long customerId);

    /**
     * 获取订单支付状态信息（包含剩余支付时间和支付参数）
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @param appId 微信小程序AppId（可选，用于生成支付参数）
     * @return 订单支付状态信息
     */
    OrderPaymentStatusResponse getOrderPaymentStatus(Long orderId, Long customerId, String appId);

    /**
     * 根据订单号更新订单状态
     * @param orderNumber
     * @param orderStatus
     * @return
     */
    boolean checkOrderStatus(String orderNumber, String orderStatus);
}
