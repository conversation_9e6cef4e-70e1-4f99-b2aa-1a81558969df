package cn.hxsy.service.impl;

import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.datasource.model.entity.MallOrderDelivery;
import cn.hxsy.datasource.model.entity.MallOrderDeliveryDetail;
import cn.hxsy.feign.kuaidi100.request.QueryDoRequest;
import cn.hxsy.mapper.MallOrderDeliveryDetailMapper;
import cn.hxsy.request.MallOrderDeliveryDetailRequest;
import cn.hxsy.response.MallOrderDeliveryDetailResponse;
import cn.hxsy.service.MallOrderDeliveryDetailService;
import cn.hxsy.service.MallOrderDeliveryService;
import cn.hxsy.utils.KDUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaidi100.sdk.response.QueryTrackData;
import com.kuaidi100.sdk.response.QueryTrackResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.hxsy.constant.order.OrderRedisKeyPrefix.ORDER_DELIVERY_REPEAT_REQUEST;

/**
 * <p>
 * 订单配送物流轨迹明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 22:11:38
 */
@Service
@Slf4j
public class MallOrderDeliveryDetailServiceImpl extends ServiceImpl<MallOrderDeliveryDetailMapper, MallOrderDeliveryDetail> implements MallOrderDeliveryDetailService {

	@Resource
	private MallOrderDeliveryService mallOrderDeliveryService;

	@Resource
	private RedisJsonUtils redisJsonUtils;

	@Resource
	private SnowflakeIdWorker idWorker;

	@Resource
	private KDUtil kdUtil;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Result<Object> queryByOrderId(MallOrderDeliveryDetailRequest request) {
		String deliveryId = request.getDeliveryId();
		if(StringUtils.isEmpty(deliveryId)){
			return Result.error("请确认查询订单，稍后重试");
		}
		// 0、校验当前单号查询频率是否满足要求（如果距离上一次未超过30分支，禁止第三方调用，只能返回上一次查询数据）
		String lockKey = ORDER_DELIVERY_REPEAT_REQUEST + deliveryId;
		Boolean lock = redisJsonUtils.getLock(lockKey, 35 * 60);
		LambdaQueryWrapper<MallOrderDeliveryDetail> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(MallOrderDeliveryDetail::getDeliveryId, deliveryId)
				.eq(MallOrderDeliveryDetail::getStatus, UseStatusEnum.EFFECTIVE.getCode());
		List<MallOrderDeliveryDetail> mallOrderDeliveryDetails = baseMapper.selectList(wrapper);
		if(!lock){
			if(CollectionUtils.isEmpty(mallOrderDeliveryDetails)){
				return Result.ok("暂未获取到物流信息，请稍后重试");
			}
			// 0.1、距离上一次查询在半小时以内，不需要查询最新结果，直接去数据库获取上一次查询以后同步的数据即可；
			List<MallOrderDeliveryDetailResponse> responses = mallOrderDeliveryDetails.stream()
					.sorted(Comparator.comparing(MallOrderDeliveryDetail::getTrackingTime).reversed())
					.map(item -> {
				MallOrderDeliveryDetailResponse response = new MallOrderDeliveryDetailResponse();
				BeanUtils.copyProperties(item, response);
				return response;
			}).collect(Collectors.toList());
			return Result.ok(responses);
		}
		// 0.2、距离上一次查询超过半小时，就需要调用快递100查询最新结果，再保存到数据库中
		try {
			QueryTrackResp queryTrackResp = this.getTrackRespByDeliveryId(deliveryId);
			// 1、将结果封装成数据库保存所需list，批量保存到数据库中；响应前端
			List<MallOrderDeliveryDetailResponse> mallOrderDeliveryDetailResponses = this.saveTrackRespByDeliveryId(queryTrackResp, deliveryId, mallOrderDeliveryDetails);
			return Result.ok(mallOrderDeliveryDetailResponses);
		} catch (Exception e) {
			// 2、异常需要释放锁
			redisJsonUtils.delete(lockKey);
			log.info("系统运单：{}，查询物流信息失败：{}", deliveryId, e.getMessage());
			throw new RuntimeException(e);
		}
	}

	/**
	* @description: 根据运单号查询最新物料信息
	* @author: xiaQL
	* @date: 2025/9/7 0:14
	*/
	public QueryTrackResp getTrackRespByDeliveryId(String deliveryId){
		// 0、通过配送单号获取到对应物料信息
		LambdaQueryWrapper<MallOrderDelivery> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(MallOrderDelivery::getDeliveryId, deliveryId)
				.eq(MallOrderDelivery::getStatus, UseStatusEnum.EFFECTIVE.getCode());
		List<MallOrderDelivery> mallOrderDeliveries = mallOrderDeliveryService.getBaseMapper().selectList(queryWrapper);
		if(CollectionUtils.isEmpty(mallOrderDeliveries)){
			throw new RuntimeException("请确认查询订单，稍后重试");
		}
		// 1、构造物流查询所需参数，发起物流查询
		MallOrderDelivery mallOrderDelivery = mallOrderDeliveries.get(0);
		QueryDoRequest queryDoRequest = new QueryDoRequest();
		queryDoRequest.setNum(mallOrderDelivery.getTrackingNo());
		queryDoRequest.setCom(mallOrderDelivery.getLogisticsCompany());
		String receiverInfo = mallOrderDelivery.getReceiverInfo();
		if(("SF".equals(mallOrderDelivery.getLogisticsCompany()) || "sf".equals(mallOrderDelivery.getLogisticsCompany())) && StringUtils.isNotEmpty(receiverInfo)){
			JSONObject jsonObject = JSON.parseObject(receiverInfo);
			queryDoRequest.setPhone(jsonObject.get("phone").toString());
		}
		return kdUtil.queryDo(queryDoRequest);
	}

	/**
	 * @description: 将三方快递查询结果批量保存到数据库，并构造对应响应数据
	 * @param existMallOrderDeliveryDetails 上一轮查询已保存的物流信息
	 * @author: xiaQL
	 * @date: 2025/9/7 0:14
	 */
	@Transactional(rollbackFor = Exception.class)
	public List<MallOrderDeliveryDetailResponse> saveTrackRespByDeliveryId(QueryTrackResp queryTrackResp, String deliveryId,
	                                                                       List<MallOrderDeliveryDetail> existMallOrderDeliveryDetails){
		// 1、获取到快递100接口返回的最新物流数据，转化为系统数据库所需存储格式
		if(!"200".equals(queryTrackResp.getStatus())){
			log.info("系统运单：{}，查询物流信息失败：{}", deliveryId, queryTrackResp.getMessage());
		}
//		log.info("系统运单：{}，查询到物流信息：{}", deliveryId, queryTrackResp.getMessage());
		// 2、以配送时间为维度，同上轮查询的快递结果比对，看是否有新增物流信息，有则更新
		List<QueryTrackData> data = queryTrackResp.getData();
		if(CollectionUtils.isEmpty(data)){
			// 2.1、本次没有查询到快递100返回的配送信息，那就直接返回之前数据库的已经同步的配送信息
			if(CollectionUtils.isEmpty(existMallOrderDeliveryDetails)){
				return new ArrayList<>();
			}
			return existMallOrderDeliveryDetails.stream()
					.sorted(Comparator.comparing(MallOrderDeliveryDetail::getTrackingTime).reversed())
					.map(item -> {
						MallOrderDeliveryDetailResponse response = new MallOrderDeliveryDetailResponse();
						BeanUtils.copyProperties(item, response);
						return response;
					}).collect(Collectors.toList());
		}
		List<MallOrderDeliveryDetail> mallOrderDeliveryDetails = data.stream().map(item -> {
			MallOrderDeliveryDetail mallOrderDeliveryDetail = new MallOrderDeliveryDetail();
			mallOrderDeliveryDetail.setId(idWorker.nextId());
			mallOrderDeliveryDetail.setDeliveryId(deliveryId);
			String fTime = item.getFtime();
			// 运单时间转化
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			LocalDateTime trackingTime = LocalDateTime.parse(fTime, formatter);
			mallOrderDeliveryDetail.setTrackingTime(trackingTime);
			mallOrderDeliveryDetail.setTrackingDescription(item.getContext());
			mallOrderDeliveryDetail.setTrackingLocation(item.getAreaName());
			mallOrderDeliveryDetail.setTrackingStatus(Integer.valueOf(item.getStatusCode()));
			mallOrderDeliveryDetail.setCreatedAt(LocalDateTime.now());
			return mallOrderDeliveryDetail;
		}).collect(Collectors.toList());
		// 2.2、快递100接口已返回有物料信息，需要看上一轮查询结果是否有保存，有则比对，没有则直接批量新增
		List<MallOrderDeliveryDetail> allDetails = new ArrayList<>();
		if(CollectionUtils.isEmpty(existMallOrderDeliveryDetails)){
			this.saveBatch(mallOrderDeliveryDetails);
			allDetails.addAll(mallOrderDeliveryDetails);
		}else {
			// 获取已存在的物流轨迹时间集合
			Set<String> existingTimes = existMallOrderDeliveryDetails.stream()
					.map(detail -> detail.getTrackingTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
					.collect(Collectors.toSet());
			// 找出新增的物流信息
			List<MallOrderDeliveryDetail> newDetails = mallOrderDeliveryDetails.stream()
					.filter(detail -> !existingTimes.contains(detail.getTrackingTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))))
					.collect(Collectors.toList());
			// 如果有新增物流信息，则保存
			if (CollectionUtils.isNotEmpty(newDetails)) {
				this.saveBatch(newDetails);
			}
			// 合并已有和新增的物流信息返回
			allDetails.addAll(existMallOrderDeliveryDetails);
			allDetails.addAll(newDetails);
		}
		// 按时间倒序排列，响应前端
		return allDetails.stream()
				.sorted(Comparator.comparing(MallOrderDeliveryDetail::getTrackingTime).reversed())
				.map(item -> {
					MallOrderDeliveryDetailResponse response = new MallOrderDeliveryDetailResponse();
					BeanUtils.copyProperties(item, response);
					return response;
				}).collect(Collectors.toList());
	}
}
