package cn.hxsy.service.impl;

import cn.hxsy.constant.order.OrderStateType;
import cn.hxsy.entity.MallOrder;
import cn.hxsy.entity.MallOrderRefund;
import cn.hxsy.mapper.MallOrderMapper;
import cn.hxsy.mapper.MallOrderRefundMapper;
import cn.hxsy.request.ApplyRefundRequest;
import cn.hxsy.request.AuditRefundRequest;
import cn.hxsy.request.RefundQueryRequest;
import cn.hxsy.response.RefundDetailResponse;
import cn.hxsy.response.RefundResponse;
import cn.hxsy.response.RefundEligibilityResponse;
import cn.hxsy.service.MallRefundService;
import cn.hxsy.service.MallRefundProgressService;
import cn.hxsy.utils.WxCustomerPayUtils;
import cn.hxsy.constant.refund.RefundStatusConstants;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 退款服务实现类
 * 参考 MallProductReviewServiceImpl 的图片处理模式
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MallRefundServiceImpl extends ServiceImpl<MallOrderRefundMapper, MallOrderRefund> 
        implements MallRefundService {

    private final MallOrderRefundMapper refundMapper;
    private final MallOrderMapper orderMapper;
    private final MallRefundProgressService progressService;
    private final WxCustomerPayUtils wxCustomerPayUtils;
    // 使用与评论功能相同的ObjectMapper
    private final ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String applyRefund(ApplyRefundRequest request) {
        try {
            // 1. 验证订单是否存在且属于当前用户
            MallOrder order = orderMapper.selectById(request.getOrderId());
            if (order == null || !order.getCustomerId().equals(request.getCustomerId())) {
                log.warn("订单不存在或不属于当前用户: orderId={}, customerId={}", 
                        request.getOrderId(), request.getCustomerId());
                throw new RuntimeException("订单不存在或不属于当前用户");
            }

            // 2. 验证订单状态是否可以申请退款
            if (!canApplyRefund(order.getOrderStatus())) {
                log.warn("订单状态不允许申请退款: orderId={}, status={}",
                        request.getOrderId(), order.getOrderStatus());
                throw new RuntimeException("当前订单状态不支持退款申请");
            }

            // 3. 验证支付方式（仅支持微信支付）
            if (!"WECHAT".equals(order.getPaymentMethod())) {
                log.warn("非微信支付订单不支持退款: orderId={}, paymentMethod={}",
                        request.getOrderId(), order.getPaymentMethod());
                throw new RuntimeException("仅支持微信支付订单的退款申请");
            }

            // 4. 验证是否已存在退款申请
            LambdaQueryWrapper<MallOrderRefund> existQuery = new LambdaQueryWrapper<>();
            existQuery.eq(MallOrderRefund::getOrderId, request.getOrderId())
                     .eq(MallOrderRefund::getCustomerId, request.getCustomerId())
                     .notIn(MallOrderRefund::getRefundStatus, 
                            RefundStatusConstants.FAILED, 
                            RefundStatusConstants.REJECTED,
                            RefundStatusConstants.CANCELLED);
            long existCount = count(existQuery);
            if (existCount > 0) {
                log.warn("订单已存在退款申请: orderId={}, customerId={}",
                        request.getOrderId(), request.getCustomerId());
                throw new RuntimeException("该订单已存在退款申请，请勿重复申请");
            }

            // 5. 创建退款记录
            MallOrderRefund refund = new MallOrderRefund();
            refund.setRefundNo(generateRefundNo());
            refund.setOrderId(request.getOrderId());
            refund.setOrderNumber(order.getOrderNumber());
            refund.setCustomerId(request.getCustomerId());
            // 预留多商户字段，暂时设置为null或使用默认值
            refund.setCompanyId(order.getCompanyId());
            refund.setRefundType(RefundStatusConstants.REFUND_TYPE_FULL);
            refund.setRefundReason(request.getRefundReason());
            refund.setRefundDescription(request.getRefundDescription());
            // 强制全额退款
            refund.setRefundAmount(order.getTotalAmount());
            refund.setWxRefundChannel(RefundStatusConstants.CHANNEL_ORIGINAL);
            refund.setApplyTime(LocalDateTime.now());

            // 6. 处理退款凭证图片列表（参考评论功能的图片处理模式）
            if (!CollectionUtils.isEmpty(request.getRefundImages())) {
                try {
                    // 验证图片数量（最多5张，比评论功能少1张）
                    if (request.getRefundImages().size() > 5) {
                        throw new RuntimeException("退款凭证图片最多只能上传5张");
                    }
                    
                    // 使用ObjectMapper将List<String>序列化为JSON存储
                    // 这与MallProductReviewServiceImpl中的处理方式完全一致
                    refund.setRefundImages(objectMapper.writeValueAsString(request.getRefundImages()));
                    log.info("退款凭证图片处理成功，图片数量: {}", request.getRefundImages().size());
                } catch (JsonProcessingException e) {
                    log.error("序列化退款凭证图片列表失败", e);
                    refund.setRefundImages("[]");
                }
            } else {
                refund.setRefundImages("[]");
            }

            // 7. 根据订单状态确定退款状态和审核结果
            if ("paid".equals(order.getOrderStatus())) {
                // 未发货订单，自动审核通过
                refund.setRefundStatus(RefundStatusConstants.APPROVED);
                refund.setStatusText(RefundStatusConstants.getStatusText(RefundStatusConstants.APPROVED));
                refund.setAuditResult(RefundStatusConstants.AUDIT_APPROVED);
                refund.setAuditRemark("未发货订单，自动审核通过");
                refund.setAuditUserName("系统自动审核");
                refund.setAuditTime(LocalDateTime.now());
            } else {
                // 已发货或已完成订单，需要人工审核
                refund.setRefundStatus(RefundStatusConstants.PENDING);
                refund.setStatusText(RefundStatusConstants.getStatusText(RefundStatusConstants.PENDING));
            }

            // 8. 保存退款记录
            refundMapper.insert(refund);

            // 9. 添加退款进度记录
            progressService.addProgress(
                refund.getId(),
                refund.getRefundStatus(),
                refund.getStatusText(),
                "用户提交退款申请",
                "用户"
            );
            // 10.更新订单状态
            LambdaUpdateWrapper<MallOrder> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(MallOrder::getId, request.getOrderId())
                .set(MallOrder::getOrderStatus, OrderStateType.refund_processing.getCode())
                .set(MallOrder::getStatusText, OrderStateType.refund_processing.getInfo());
            orderMapper.update(null, updateWrapper);


            log.info("退款申请提交成功: refundNo={}, orderId={}, customerId={}", 
                    refund.getRefundNo(), request.getOrderId(), request.getCustomerId());
            
            return refund.getRefundNo();

        } catch (Exception e) {
            log.error("申请退款失败: orderId={}, customerId={}", 
                    request.getOrderId(), request.getCustomerId(), e);
            throw new RuntimeException("申请退款失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<RefundResponse> getRefundList(RefundQueryRequest request) {
        Page<RefundResponse> page = new Page<>(request.getPageNum(), request.getPageSize());
        return refundMapper.selectRefundPage(page, request);
    }

    @Override
    public RefundDetailResponse getRefundDetail(Long refundId, Long customerId) {
        // 验证退款记录是否属于当前用户
        LambdaQueryWrapper<MallOrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MallOrderRefund::getId, refundId)
                   .eq(MallOrderRefund::getCustomerId, customerId);
        
        MallOrderRefund refund = getOne(queryWrapper);
        if (refund == null) {
            throw new RuntimeException("退款记录不存在或不属于当前用户");
        }

        RefundDetailResponse response = new RefundDetailResponse();
        // 复制基本信息（省略具体实现，实际开发时需要完善）
        // BeanUtils.copyProperties(refund, response);
        
        // 处理退款凭证图片列表（参考评论功能的图片反序列化）
        if (refund.getRefundImages() != null && !"[]".equals(refund.getRefundImages())) {
            try {
                // 使用ObjectMapper将JSON字符串反序列化为List<String>
                List<String> imageList = objectMapper.readValue(
                    refund.getRefundImages(), 
                    new TypeReference<List<String>>() {}
                );
                response.setRefundImages(imageList);
            } catch (JsonProcessingException e) {
                log.error("反序列化退款凭证图片列表失败: refundId={}", refundId, e);
                response.setRefundImages(new ArrayList<>());
            }
        } else {
            response.setRefundImages(new ArrayList<>());
        }

        // 获取退款进度列表
        response.setProgressList(progressService.getProgressList(refundId));

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditRefund(AuditRefundRequest request) {
        log.info("开始审核退款: refundId={}, auditResult={}, auditUserId={}",
                request.getRefundId(), request.getAuditResult(), request.getAuditUserId());

        try {
            // 1. 验证退款申请是否存在且状态正确
            MallOrderRefund refund = validateRefundForAudit(request.getRefundId());

            // 2. 验证审核结果参数
            validateAuditRequest(request);

            // 3. 更新退款记录
            updateRefundAuditInfo(refund, request);

            // 4. 添加进度记录
            addAuditProgressRecord(refund, request);

            // 5. 处理审核结果
            if (RefundStatusConstants.AUDIT_APPROVED.equals(request.getAuditResult())) {
                // 同意退款 - 发起微信退款
                processApprovedRefund(refund);
            } else if (RefundStatusConstants.AUDIT_REJECTED.equals(request.getAuditResult())) {
                // 拒绝退款 - 更新状态为已拒绝
                processRejectedRefund(refund);
            }

            log.info("退款审核完成: refundId={}, auditResult={}",
                    request.getRefundId(), request.getAuditResult());
            return true;

        } catch (Exception e) {
            log.error("审核退款失败: refundId={}, error={}", request.getRefundId(), e.getMessage(), e);
            throw new RuntimeException("审核退款失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<RefundResponse> getPendingRefundList(Long companyId, Integer pageNum, Integer pageSize) {
        Page<RefundResponse> page = new Page<>(pageNum, pageSize);
        return refundMapper.selectPendingRefundPage(page, companyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelRefund(Long refundId, Long customerId) {
        log.info("开始取消退款申请: refundId={}, customerId={}", refundId, customerId);

        try {
            // 1. 验证退款申请是否存在且属于该用户
            MallOrderRefund refund = validateRefundForCancel(refundId, customerId);

            // 2. 更新退款状态为已取消
            updateRefundStatus(refundId, RefundStatusConstants.CANCELLED,
                             RefundStatusConstants.getStatusText(RefundStatusConstants.CANCELLED));

            // 3. 添加进度记录
            progressService.addProgress(refundId, RefundStatusConstants.CANCELLED,
                                      RefundStatusConstants.getStatusText(RefundStatusConstants.CANCELLED),
                                      "用户主动取消退款申请", "用户");

            log.info("退款申请取消成功: refundId={}", refundId);
            return true;

        } catch (Exception e) {
            log.error("取消退款申请失败: refundId={}, error={}", refundId, e.getMessage(), e);
            throw new RuntimeException("取消退款申请失败: " + e.getMessage());
        }
    }

    /**
     * 验证退款申请是否可以取消
     */
    private MallOrderRefund validateRefundForCancel(Long refundId, Long customerId) {
        MallOrderRefund refund = this.getById(refundId);
        if (refund == null) {
            throw new RuntimeException("退款申请不存在");
        }

        if (!refund.getCustomerId().equals(customerId)) {
            throw new RuntimeException("无权限操作该退款申请");
        }

        // 只有待审核状态的退款申请可以取消
        if (!RefundStatusConstants.PENDING.equals(refund.getRefundStatus())) {
            throw new RuntimeException("当前状态不允许取消，退款状态: " + refund.getRefundStatus());
        }

        return refund;
    }

    @Override
    public Boolean handleWxRefundCallback(String notificationData) {
        // TODO: 实现微信退款回调处理
        log.info("处理微信退款回调: {}", notificationData);
        return true;
    }

    @Override
    public String queryRefundStatus(Long refundId) {
        MallOrderRefund refund = getById(refundId);
        return refund != null ? refund.getRefundStatus() : null;
    }

    @Override
    public RefundEligibilityResponse checkRefundEligibility(Long orderId, Long customerId) {
        try {
            // 1. 验证订单是否存在且属于当前用户
            MallOrder order = orderMapper.selectById(orderId);
            if (order == null || !order.getCustomerId().equals(customerId)) {
                log.warn("订单不存在或不属于当前用户: orderId={}, customerId={}", orderId, customerId);
                return RefundEligibilityResponse.ineligible("订单不存在或不属于当前用户");
            }

            // 2. 验证订单状态是否可以申请退款
            if (!canApplyRefund(order.getOrderStatus())) {
                log.warn("订单状态不允许申请退款: orderId={}, status={}", orderId, order.getOrderStatus());
                return RefundEligibilityResponse.ineligible("当前订单状态不支持退款申请");
            }

            // 3. 验证支付方式（仅支持微信支付）
            if (!"WECHAT".equals(order.getPaymentMethod())) {
                log.warn("非微信支付订单不支持退款: orderId={}, paymentMethod={}", orderId, order.getPaymentMethod());
                return RefundEligibilityResponse.ineligible("仅支持微信支付订单的退款申请");
            }

            // 4. 验证是否已存在退款申请
            LambdaQueryWrapper<MallOrderRefund> existQuery = new LambdaQueryWrapper<>();
            existQuery.eq(MallOrderRefund::getOrderId, orderId)
                     .eq(MallOrderRefund::getCustomerId, customerId)
                     .notIn(MallOrderRefund::getRefundStatus, 
                            RefundStatusConstants.FAILED, 
                            RefundStatusConstants.REJECTED,
                            RefundStatusConstants.CANCELLED);
            long existCount = count(existQuery);
            if (existCount > 0) {
                log.warn("订单已存在退款申请: orderId={}, customerId={}", orderId, customerId);
                return RefundEligibilityResponse.ineligible("该订单已存在退款申请，请勿重复申请");
            }

            // 5. 根据订单状态判断是否需要人工审核
            boolean requiresManualReview = !"paid".equals(order.getOrderStatus());
            
            log.info("订单退款资格检查通过: orderId={}, customerId={}, requiresManualReview={}", 
                    orderId, customerId, requiresManualReview);
            
            return RefundEligibilityResponse.eligible(requiresManualReview);
            
        } catch (Exception e) {
            log.error("检查退款资格失败: orderId={}, customerId={}", orderId, customerId, e);
            return RefundEligibilityResponse.ineligible("检查退款资格失败: " + e.getMessage());
        }
    }

    /**
     * 验证订单状态是否可以申请退款
     */
    private boolean canApplyRefund(String orderStatus) {
        return "paid".equals(orderStatus) || 
               "shipped".equals(orderStatus) || 
               "completed".equals(orderStatus);
    }

    /**
     * 生成退款单号
     */
    private String generateRefundNo() {
        return "RF" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) +
               String.valueOf(System.currentTimeMillis() % 10000);
    }

    // ==================== 审核相关方法 ====================

    /**
     * 验证退款申请是否可以审核
     */
    private MallOrderRefund validateRefundForAudit(Long refundId) {
        MallOrderRefund refund = this.getById(refundId);
        if (refund == null) {
            throw new RuntimeException("退款申请不存在");
        }

        if (!RefundStatusConstants.PENDING.equals(refund.getRefundStatus())) {
            throw new RuntimeException("退款申请状态不正确，当前状态: " + refund.getRefundStatus());
        }

        return refund;
    }

    /**
     * 验证审核请求参数
     */
    private void validateAuditRequest(AuditRefundRequest request) {
        if (!RefundStatusConstants.AUDIT_APPROVED.equals(request.getAuditResult())
            && !RefundStatusConstants.AUDIT_REJECTED.equals(request.getAuditResult())) {
            throw new RuntimeException("审核结果参数错误: " + request.getAuditResult());
        }

        // 拒绝退款时必须填写备注
        if (RefundStatusConstants.AUDIT_REJECTED.equals(request.getAuditResult())
            && (request.getAuditRemark() == null || request.getAuditRemark().trim().isEmpty())) {
            throw new RuntimeException("拒绝退款时必须填写拒绝原因");
        }
    }

    /**
     * 更新退款审核信息
     */
    private void updateRefundAuditInfo(MallOrderRefund refund, AuditRefundRequest request) {
        LambdaUpdateWrapper<MallOrderRefund> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MallOrderRefund::getId, refund.getId())
                    .set(MallOrderRefund::getAuditResult, request.getAuditResult())
                    .set(MallOrderRefund::getAuditRemark, request.getAuditRemark())
                    .set(MallOrderRefund::getAuditUserId, request.getAuditUserId())
                    .set(MallOrderRefund::getAuditUserName, request.getAuditUserName())
                    .set(MallOrderRefund::getAuditTime, LocalDateTime.now());

        // 根据审核结果更新退款状态
        if (RefundStatusConstants.AUDIT_APPROVED.equals(request.getAuditResult())) {
            updateWrapper.set(MallOrderRefund::getRefundStatus, RefundStatusConstants.APPROVED)
                        .set(MallOrderRefund::getStatusText, RefundStatusConstants.getStatusText(RefundStatusConstants.APPROVED));
        } else {
            updateWrapper.set(MallOrderRefund::getRefundStatus, RefundStatusConstants.REJECTED)
                        .set(MallOrderRefund::getStatusText, RefundStatusConstants.getStatusText(RefundStatusConstants.REJECTED));
        }

        boolean updated = this.update(updateWrapper);
        if (!updated) {
            throw new RuntimeException("更新退款审核信息失败");
        }
    }

    /**
     * 添加审核进度记录
     */
    private void addAuditProgressRecord(MallOrderRefund refund, AuditRefundRequest request) {
        String progressStatus = RefundStatusConstants.AUDIT_APPROVED.equals(request.getAuditResult())
                              ? RefundStatusConstants.APPROVED
                              : RefundStatusConstants.REJECTED;
        String statusText = RefundStatusConstants.getStatusText(progressStatus);
        String description = String.format("商家审核%s，备注：%s",
                           RefundStatusConstants.AUDIT_APPROVED.equals(request.getAuditResult()) ? "同意" : "拒绝",
                           request.getAuditRemark() != null ? request.getAuditRemark() : "无");
        String operator = request.getAuditUserName() != null ? request.getAuditUserName() : "商家";

        progressService.addProgress(refund.getId(), progressStatus, statusText, description, operator);
    }

    /**
     * 处理同意退款的情况
     */
    private void processApprovedRefund(MallOrderRefund refund) {
        try {
            log.info("开始处理同意退款: refundId={}, refundAmount={}", refund.getId(), refund.getRefundAmount());

            // 获取订单信息
            MallOrder order = orderMapper.selectById(refund.getOrderId());
            if (order == null) {
                throw new RuntimeException("订单不存在");
            }

            // 发起微信退款
            initiateWxRefund(refund, order);

        } catch (Exception e) {
            log.error("处理同意退款失败: refundId={}, error={}", refund.getId(), e.getMessage(), e);
            // 更新退款状态为失败
            updateRefundStatus(refund.getId(), RefundStatusConstants.FAILED, "退款处理失败: " + e.getMessage());
            throw new RuntimeException("退款处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理拒绝退款的情况
     */
    private void processRejectedRefund(MallOrderRefund refund) {
        log.info("退款已被拒绝: refundId={}", refund.getId());
        // 拒绝退款时，状态已在 updateRefundAuditInfo 中更新为 REJECTED
        // 这里可以添加其他业务逻辑，如发送通知等
    }

    /**
     * 发起微信退款
     */
    private void initiateWxRefund(MallOrderRefund refund, MallOrder order) {
        try {
            log.info("发起微信退款: refundId={}, orderId={}, refundAmount={}",
                    refund.getId(), order.getId(), refund.getRefundAmount());

            // 更新退款状态为处理中
            updateRefundStatus(refund.getId(), RefundStatusConstants.PROCESSING, "正在处理退款");

            // 添加进度记录
            progressService.addProgress(refund.getId(), RefundStatusConstants.PROCESSING,
                                      RefundStatusConstants.getStatusText(RefundStatusConstants.PROCESSING),
                                      "开始处理微信退款", "系统");

            // 调用微信退款接口
            // 注意：这里需要根据实际的微信支付工具类方法进行调用
            // wxCustomerPayUtils.refund(...);

            // 模拟微信退款成功（实际应该根据微信返回结果处理）
            // 在实际项目中，这部分逻辑应该在微信回调中处理
            log.info("微信退款请求已发起，等待微信处理结果");

        } catch (Exception e) {
            log.error("发起微信退款失败: refundId={}, error={}", refund.getId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新退款状态
     */
    private void updateRefundStatus(Long refundId, String status, String statusText) {
        LambdaUpdateWrapper<MallOrderRefund> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MallOrderRefund::getId, refundId)
                    .set(MallOrderRefund::getRefundStatus, status)
                    .set(MallOrderRefund::getStatusText, statusText);

        // 根据状态设置相应的时间字段
        if (RefundStatusConstants.PROCESSING.equals(status)) {
            updateWrapper.set(MallOrderRefund::getProcessTime, LocalDateTime.now());
        } else if (RefundStatusConstants.SUCCESS.equals(status)) {
            updateWrapper.set(MallOrderRefund::getCompleteTime, LocalDateTime.now());
        }

        boolean updated = this.update(updateWrapper);
        if (!updated) {
            throw new RuntimeException("更新退款状态失败");
        }
    }

    @Override
    public RefundDetailResponse getRefundByOrderId(Long orderId, Long customerId) {
        try {
            log.info("根据订单ID获取退款信息: orderId={}, customerId={}", orderId, customerId);

            // 1. 验证订单是否存在且属于当前用户
            MallOrder order = orderMapper.selectById(orderId);
            if (order == null || !order.getCustomerId().equals(customerId)) {
                log.warn("订单不存在或不属于当前用户: orderId={}, customerId={}", orderId, customerId);
                throw new RuntimeException("订单不存在或不属于当前用户");
            }

            // 2. 查询该订单的退款记录
            LambdaQueryWrapper<MallOrderRefund> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MallOrderRefund::getOrderId, orderId)
                       .eq(MallOrderRefund::getCustomerId, customerId)
                       .eq(MallOrderRefund::getStatus, 1)
                       .orderByDesc(MallOrderRefund::getApplyTime)
                       .last("LIMIT 1"); // 只取最新的一条退款记录

            MallOrderRefund refund = getOne(queryWrapper);
            if (refund == null) {
                log.info("该订单没有退款记录: orderId={}, customerId={}", orderId, customerId);
                return null; // 没有退款记录，返回null
            }

            // 3. 转换为响应对象
            RefundDetailResponse response = new RefundDetailResponse();
            response.setId(refund.getId());
            response.setRefundNo(refund.getRefundNo());
            response.setOrderId(refund.getOrderId());
            response.setOrderNumber(refund.getOrderNumber());
            response.setCustomerId(refund.getCustomerId());
            response.setRefundType(refund.getRefundType());
            response.setRefundReason(refund.getRefundReason());
            response.setRefundDescription(refund.getRefundDescription());
            response.setRefundAmount(refund.getRefundAmount());
            response.setRefundStatus(refund.getRefundStatus());
            response.setStatusText(refund.getStatusText());
            response.setAuditResult(refund.getAuditResult());
            response.setAuditRemark(refund.getAuditRemark());
            response.setAuditUserName(refund.getAuditUserName());
            response.setAuditTime(refund.getAuditTime());
            response.setApplyTime(refund.getApplyTime());
            response.setProcessTime(refund.getProcessTime());
            response.setCompleteTime(refund.getCompleteTime());
            response.setWxSuccessTime(refund.getWxSuccessTime());

            // 4. 处理退款凭证图片
            if (refund.getRefundImages() != null && !refund.getRefundImages().trim().isEmpty()) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<String> images = objectMapper.readValue(refund.getRefundImages(),
                            new TypeReference<List<String>>() {});
                    response.setRefundImages(images);
                } catch (JsonProcessingException e) {
                    log.warn("解析退款凭证图片失败: refundId={}, images={}", refund.getId(), refund.getRefundImages(), e);
                    response.setRefundImages(new ArrayList<>());
                }
            } else {
                response.setRefundImages(new ArrayList<>());
            }

            log.info("根据订单ID获取退款信息成功: orderId={}, refundId={}, refundStatus={}",
                    orderId, refund.getId(), refund.getRefundStatus());

            return response;

        } catch (Exception e) {
            log.error("根据订单ID获取退款信息失败: orderId={}, customerId={}", orderId, customerId, e);
            throw new RuntimeException("获取退款信息失败: " + e.getMessage());
        }
    }
}