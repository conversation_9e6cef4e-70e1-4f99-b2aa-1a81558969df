package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.datasource.model.entity.MallOrderDelivery;
import cn.hxsy.datasource.model.entity.MallOrderDeliveryDetail;
import cn.hxsy.mapper.MallOrderDeliveryMapper;
import cn.hxsy.request.MallOrderDeliveryDetailRequest;
import cn.hxsy.request.MallOrderDeliveryRequest;
import cn.hxsy.response.MallOrderDeliveryResponse;
import cn.hxsy.service.MallOrderDeliveryService;
import cn.hxsy.utils.UserCacheUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单配送主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 21:36:19
 */
@Service
public class MallOrderDeliveryServiceImpl extends ServiceImpl<MallOrderDeliveryMapper, MallOrderDelivery> implements MallOrderDeliveryService {

	@Resource
	private UserCacheUtil userCacheUtil;

	@Resource
	private SnowflakeIdWorker idWorker;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Result<Object> saveOrderDelivery(List<MallOrderDeliveryRequest> request) {
		if(CollectionUtils.isEmpty(request)){
			return Result.error("订单数据不能为空");
		}
		// 0、权限校验
		SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
		userCacheUtil.checkUserAdmin(systemUserSelfInfo);
		// 1、保存数据处理
		List<MallOrderDelivery> collect = request.stream().map(item -> {
			MallOrderDelivery mallOrderDelivery = new MallOrderDelivery();
			BeanUtils.copyProperties(item, mallOrderDelivery);
			// 1.1、生成系统配送单号，用于后续查询
			mallOrderDelivery.setDeliveryId(idWorker.nextIdString());
			mallOrderDelivery.setCreatedAt(LocalDateTime.now());
			mallOrderDelivery.setCreatedBy(systemUserSelfInfo.getId().toString());
			// 1.2、单独处理收发件人信息，转为json格式存储至数据库方便前端传输，后端存储后提取
			mallOrderDelivery.setSenderInfo(JSON.toJSONString(item.getSenderInfo()));
			mallOrderDelivery.setReceiverInfo(JSON.toJSONString(item.getReceiverInfo()));
			return mallOrderDelivery;
		}).collect(Collectors.toList());
		this.saveBatch(collect);
		return Result.ok();
	}

	@Override
	public Result<Object> queryOrderDelivery(MallOrderDeliveryRequest request) {
		if(request == null || request.getOrderId() == null){
			return Result.error("订单数据不能为空");
		}
		// 1、查询对应订单号的配送信息
		LambdaQueryWrapper<MallOrderDelivery> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(MallOrderDelivery::getOrderId, request.getOrderId())
				.eq(MallOrderDelivery::getStatus, UseStatusEnum.EFFECTIVE.getCode());
		List<MallOrderDelivery> mallOrderDeliveryList = baseMapper.selectList(wrapper);
		// 2、构造响应信息
		if(CollectionUtils.isEmpty(mallOrderDeliveryList)){
			return Result.ok();
		}
		List<MallOrderDeliveryResponse> collect = mallOrderDeliveryList.stream().map(item -> {
			MallOrderDeliveryResponse response = new MallOrderDeliveryResponse();
			BeanUtils.copyProperties(item, response);
			response.setId(String.valueOf(item.getId()));
			response.setOrderId(String.valueOf(item.getOrderId()));
			// 2.1、单独处理收发件人信息，数据库存储为json格式，需要手动转化
			response.setSenderInfo(JSON.parseObject(item.getSenderInfo(), MallOrderDeliveryResponse.SenderInfo.class));
			response.setReceiverInfo(JSON.parseObject(item.getReceiverInfo(), MallOrderDeliveryResponse.ReceiverInfo.class));
			return response;
		}).collect(Collectors.toList());
		return Result.ok(collect);
	}
}
