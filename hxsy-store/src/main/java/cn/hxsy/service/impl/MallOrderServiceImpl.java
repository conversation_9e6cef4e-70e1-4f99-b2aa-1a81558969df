package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.response.CustomerResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.constant.order.OrderStateType;
import cn.hxsy.datasource.model.entity.WxMerchantKeyPO;
import cn.hxsy.entity.MallProduct;
import cn.hxsy.response.CreateOrderResponse;
import cn.hxsy.response.OrderPaymentStatusResponse;
import cn.hxsy.response.WechatPayResponse;
import cn.hxsy.entity.MallCustomerAddress;
import cn.hxsy.entity.MallOrder;
import cn.hxsy.entity.MallOrderItem;
import cn.hxsy.mapper.MallOrderMapper;
import cn.hxsy.model.request.WxPayParam;
import cn.hxsy.model.response.DirectAPIv3QueryResponse;
import cn.hxsy.model.response.WxPayOrderResponse;
import cn.hxsy.request.MallOrderRequest;
import cn.hxsy.response.MallOrderResponse;
import cn.hxsy.response.OrderStatusStatsResponse;
import cn.hxsy.service.*;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.WxCustomerPayUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static cn.hxsy.constant.order.OrderRedisKeyPrefix.ORDER_REPEAT_REQUEST;

/**
 * 订单服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MallOrderServiceImpl extends ServiceImpl<MallOrderMapper, MallOrder> implements MallOrderService {

    @Resource
    private MallOrderMapper mallOrderMapper;

    @Resource
    private MallOrderItemService mallOrderItemService;

    @Resource
    private MallAddressService mallAddressService;

    @Resource
    private MallProductService mallProductService;

    @Resource
    private WxMerchantKeyService wxMerchantKeyService;

    @Resource
    private RedisJsonUtils redisJsonUtils;

    @Resource
    private UserCacheUtil userCacheUtil;

    @Resource
    private SnowflakeIdWorker idWorker;

    @Resource
    private WxCustomerPayUtils wxCustomerPayUtils;

    private static final AtomicLong orderSequence = new AtomicLong(1);

    /*
     * 订单生成本地测试挡板
     */
    @Value("${wx.pay.order.mock:false}")
    private Boolean orderMock;

    @Transactional(rollbackFor = Exception.class)
    public String createOrder(MallOrderRequest request) {
        // 1、获取收货地址
        MallCustomerAddress address = null;
        if(orderMock){
            // 挡板开启，本地测试订单生成逻辑
            if(request.getCompanyId() == null){
                request.setCompanyId(10000L);
            }
            address = mallAddressService.getAddressById(request.getAddressId(), request.getCustomerId());
        }else {
            // 挡板关闭，正式环境订单生成逻辑
            request.setCompanyId(10000L);
            CustomerResponse customerResponse = userCacheUtil.getCustomerInfo();
            // 0、防重校验
            String requestId = request.getRequestId();
            if (StringUtils.isBlank(requestId)) {
                throw new RuntimeException("缺少请求编号");
            }
            String redisKey = ORDER_REPEAT_REQUEST + customerResponse.getId() + requestId;
            if (!redisJsonUtils.getLock(redisKey, 1L)) {
                throw new RuntimeException("下单成功，请稍后刷新查看订单信息");
            }
            address = mallAddressService.getAddressById(request.getAddressId(), customerResponse.getId());
        }
        // 2、计算订单总金额和总数量（商品单价肯定得从数据库获取）
        String orderNumber = generateOrderNumber();
        this.buildTotalAmountAndQuantity(request, orderNumber);
        // 3、构建订单信息
        MallOrder order = this.buildOrderInfo(request, address, orderNumber, request.getTotalAmount(), request.getTotalQuantity());
        // 4、调用微信支付接口，获取完整支付参数
        WxPayOrderResponse wxPayResponse = this.callWechatPayAPI(request, orderNumber, request.getTotalAmount());
        order.setPrepayId(wxPayResponse.getPrepayId());
        this.save(order);
        return wxPayResponse.getPrepayId();
    }

    /**
     * 创建订单并生成支付参数
     *
     * @param request 订单请求
     * @return 订单创建结果（包含支付参数）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderResponse createOrderWithPayment(MallOrderRequest request) {
        // 1、获取收货地址
        MallCustomerAddress address = mallAddressService.getAddressById(request.getAddressId(), request.getCustomerId());
        // 2、计算订单总金额和总数量
        BigDecimal totalAmount = BigDecimal.ZERO;
        Integer totalQuantity = 0;
        for (MallOrderRequest.OrderItemRequest item : request.getItems()) {
            BigDecimal itemTotal = item.getPrice().multiply(new BigDecimal(item.getQuantity()));
            totalAmount = totalAmount.add(itemTotal);
            totalQuantity += item.getQuantity();
        }
        // 2.1、加上配送费
        totalAmount = totalAmount.add(request.getDeliveryFee());
        String orderNumber = generateOrderNumber();
        // 3、创建订单记录，需要等待微信支付拉起后，记录对应订单预支付id再保存
        MallOrder order = this.buildOrderInfo(request, address, orderNumber, totalAmount, totalQuantity);
        // 4、调用微信支付接口，获取完整支付参数
        WxPayOrderResponse wxPayResponse = this.callWechatPayAPI(request, orderNumber, totalAmount);
        order.setPrepayId(wxPayResponse.getPrepayId());
        
        // 4.1、设置支付过期时间（创建时间 + 30分钟）
        LocalDateTime paymentExpireTime = LocalDateTime.now().plusMinutes(30);
        order.setPaymentExpireTime(paymentExpireTime);
        order.setIsExpired(0);
        
        this.save(order);
        
        // 5、转换为前端需要的格式
        WechatPayResponse paymentParams = convertToWechatPayResponse(wxPayResponse);
        
        // 5.1、计算剩余支付时间（秒）
        long remainingSeconds = java.time.Duration.between(LocalDateTime.now(), paymentExpireTime).getSeconds();
        if (remainingSeconds < 0) {
            remainingSeconds = 0;
        }
        
        // 6、返回订单信息和支付参数（包含过期时间信息）
        return new CreateOrderResponse(
            String.valueOf(order.getId()),
            order.getOrderStatus(),
            paymentParams,
            paymentExpireTime,
            remainingSeconds
        );
    }

    /**
     * 分页查询订单列表
     *
     * @param request 查询条件
     * @return 订单列表
     */
    @Override
    public IPage<MallOrderResponse> queryOrderPage(MallOrderRequest.OrderQueryRequest request) {
        Page<MallOrderResponse> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<MallOrderResponse> result = mallOrderMapper.queryOrderPage(page, request);
        // 对查询结果进行实时状态校正
        if (ObjectUtils.isNotEmpty(result.getRecords())) {
            LocalDateTime now = LocalDateTime.now();
            for (MallOrderResponse order : result.getRecords()) {
                // 检查是否为待支付且已过期的订单
                if ("pending".equals(order.getOrderStatus())
                        && ObjectUtils.isNotEmpty(order.getPaymentExpireTime())
                        && now.isAfter(order.getPaymentExpireTime())) {
                    // 仅在返回结果中修改状态，不更新数据库
                    order.setOrderStatus("cancelled");
                    order.setStatusText("支付超时自动取消");
                }
            }
        }
        return result;
    }

    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 订单详情
     */
    @Override
    public MallOrderResponse getOrderDetail(Long orderId, Long customerId) {
        // 验证订单归属
        LambdaQueryWrapper<MallOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MallOrder::getId, orderId)
                .eq(MallOrder::getCustomerId, customerId)
                .eq(MallOrder::getStatus, 1);

        MallOrder order = this.getOne(queryWrapper);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        return mallOrderMapper.getOrderDetailById(orderId);
    }

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId, Long customerId) {
        MallOrder order = this.getOrderForUpdate(orderId, customerId);
        
        if (!"pending".equals(order.getOrderStatus())) {
            throw new RuntimeException("订单状态不允许取消");
        }

        order.setOrderStatus("cancelled");
        order.setStatusText("已取消");
        order.setUpdatedBy(String.valueOf(customerId));
        order.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(order);
    }

    /**
     * 发货
     *
     * @param orderId 订单ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean shipOrder(String orderId) {
        MallOrder order = this.getById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        
        if (!"paid".equals(order.getOrderStatus())) {
            throw new RuntimeException("订单状态不允许发货");
        }

        order.setOrderStatus("shipped");
        order.setStatusText("待收货");
        order.setShippedAt(LocalDateTime.now());
        order.setUpdatedBy("system"); // 发货操作由系统执行
        order.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(order);
    }

    /**
     * 确认收货
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOrder(Long orderId, Long customerId) {
        MallOrder order = this.getOrderForUpdate(orderId, customerId);
        
        if (!"shipped".equals(order.getOrderStatus())) {
            throw new RuntimeException("订单状态不允许确认收货");
        }

        order.setOrderStatus("completed");
        order.setStatusText("已完成");
        order.setCompletedAt(LocalDateTime.now());
        order.setUpdatedBy(String.valueOf(customerId)); // 使用用户ID作为更新人
        order.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(order);
    }

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    @Override
    public String generateOrderNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        long sequence = orderSequence.getAndIncrement() % 10000;
        return timestamp + String.format("%04d", sequence);
    }

    /**
     * 获取订单并验证归属（用于更新操作）
     */
    private MallOrder getOrderForUpdate(Long orderId, Long customerId) {
        LambdaQueryWrapper<MallOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MallOrder::getId, orderId)
                .eq(MallOrder::getCustomerId, customerId)
                .eq(MallOrder::getStatus, 1);

        MallOrder order = this.getOne(queryWrapper);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        return order;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> handlePayCallback(HttpServletRequest request) {
        // 1、先进行回调验签处理，如果验签失败会直接抛出异常，这里就不处理了
        Result<Object> handlePayCallbackResult = wxCustomerPayUtils.handlePayCallback(request);
        JSONObject callbackResultData = (JSONObject)handlePayCallbackResult.getData();
        if(callbackResultData == null) {
            // 1.1、解签数据怎么几把会为空呢
            throw new RuntimeException("微信支付回调解签失败");
        }
        // 2、解签获得对应订单号，并根据官方返回支付状态更新该订单状态
        String outTradeNo = callbackResultData.getString("out_trade_no");
        String tradeState = callbackResultData.getString("trade_state");
        LambdaUpdateWrapper<MallOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MallOrder::getOrderNumber, outTradeNo);
        switch (tradeState){
            case "SUCCESS":
                wrapper.set(MallOrder::getOrderStatus, OrderStateType.paid.getCode());
                break;
            case "NOTPAY":
                wrapper.set(MallOrder::getOrderStatus, OrderStateType.pending.getCode());
                break;
            case "REFUND":
                wrapper.set(MallOrder::getOrderStatus, OrderStateType.refund.getCode());
                break;
            case "CLOSED":
                wrapper.set(MallOrder::getOrderStatus, OrderStateType.cancelled.getCode());
                break;
            default:
                log.info("微信支付回调成功，但支付状态获取失败，订单号：{}，支付状态为：{}", outTradeNo, tradeState);
                throw new RuntimeException("微信支付状态获取失败");
        }
        JSONObject amount = callbackResultData.getJSONObject("amount");
        // 更新订单状态的同时，存储微信支付信息
        wrapper.set(MallOrder::getOrderStatus, OrderStateType.paid.getCode())
                .set(MallOrder::getWxTransactionId, callbackResultData.get("transaction_id"))
                .set(MallOrder::getActualPaidAmount, amount.getBigDecimal("total").divide(new BigDecimal(100)).intValue())
                .set(MallOrder::getWxPaidTime, callbackResultData.getDate("success_time"))
                .set(MallOrder::getPaidAt, LocalDateTime.now());
        boolean update = this.update(wrapper);
        // 3、根据订单号更新状态，如果更新失败需要响应微信方错误码500，保证再次回调重新尝试更新
        if(update){
            return Result.ok();
        }
        log.info("微信支付回调成功，但系统订单数据更新失败，订单号：{}", outTradeNo);
        throw new RuntimeException("微信支付回调成功，但系统订单数据更新失败");
    }

    /**
    * @description: 构造订单相关信息
    * @author: xiaQL
     * @param request 前端商品订单请求
     * @param address 客户地址信息
     * @param orderNumber 订单号
     * @param totalAmount 订单总金额
     * @param totalQuantity 订单商品总数量
     * @return 订单ID
    * @date: 2025/8/20 13:58
    */
    public MallOrder buildOrderInfo(MallOrderRequest request, MallCustomerAddress address, String orderNumber,
                                BigDecimal totalAmount, Integer totalQuantity) {
        // 1、创建此订单信息
        MallOrder order = new MallOrder();
        order.setId(idWorker.nextId());
        order.setOrderNumber(orderNumber);
        order.setCustomerId(request.getCustomerId());
        order.setOrderStatus(OrderStateType.pending.getCode());
        order.setStatusText("待付款");
        order.setTotalAmount(totalAmount);
        order.setTotalQuantity(totalQuantity);
        order.setPaymentMethod(request.getPaymentMethod());
        order.setDeliveryFee(request.getDeliveryFee());
        order.setReceiverName(address.getName());
        order.setReceiverPhone(address.getPhone());
        order.setReceiverAddress(address.getFullAddress());
        order.setCreateTime(LocalDateTime.now());
        order.setCreatedBy(String.valueOf(request.getCustomerId())); // 使用用户ID作为创建人
        order.setCreatedAt(LocalDateTime.now());
        order.setStatus(1);
        // 1.1、新增了微信预支付编号后，需要等待拉起微信支付完成后同步预支付id，所以此处不再直接保存订单信息
//        this.save(order);
        // 2、创建本订单所关联全部商品
        List<MallOrderItem> orderItems = new ArrayList<>();
        for (MallOrderRequest.OrderItemRequest itemRequest : request.getItems()) {
            MallOrderItem orderItem = new MallOrderItem();
            BeanUtils.copyProperties(itemRequest, orderItem);
            orderItem.setOrderId(order.getId());
            orderItem.setCustomerId(request.getCustomerId());
            orderItem.setCreatedBy(String.valueOf(request.getCustomerId())); // 使用用户ID作为创建人
            orderItem.setCreatedAt(LocalDateTime.now());
            orderItem.setStatus(1);
            orderItems.add(orderItem);
        }
        mallOrderItemService.saveBatch(orderItems);
        // 3、返回订单对象
        return order;
    }

    /**
    * @description: 根据选择商品数量，获取系统存储中对应商品价格总额
     * 1、目前倒只是单纯查库筛选价格
     * 2、比对库存
     * 优化：
     * 1、后续可考虑缓存商品价格
     * 2、以及实际支付时的比价处理
    * @author: xiaQL
    * @date: 2025/8/26 0:52
    */
    public void buildTotalAmountAndQuantity(MallOrderRequest request, String orderNumber) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        // 1、根据前端选择商品，获取数据库中对应商品信息
        List<MallOrderRequest.OrderItemRequest> items = request.getItems();
        if(CollectionUtils.isEmpty(items)){
            throw new RuntimeException("请重新选择待购买商品");
        }
        int totalQuantity = 0;
        LambdaQueryWrapper<MallProduct> wrapper = new LambdaQueryWrapper<>();
        List<Long> projectIds = items.stream().map(MallOrderRequest.OrderItemRequest::getProductId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(projectIds)){
            throw new RuntimeException("请重新选择待购买商品");
        }
        wrapper.in(MallProduct::getId, projectIds)
                .eq(MallProduct::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        List<MallProduct> products = mallProductService.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(products)){
            log.info("订单绑定商品查询为空，订单号：{}，商品id:{}", orderNumber, JSON.toJSONString(projectIds));
            throw new RuntimeException("您选择的商品已失效，请重新确认");
        }
        // 1.1、根据商品id收集为map，方便后续快速获取
        Map<Long, MallProduct> productMap = products.stream()
                .collect(Collectors.toMap(MallProduct::getId, v -> v));
        for (MallOrderRequest.OrderItemRequest item : items) {
            // 1.2、获取单个商品库存进行比对
            MallProduct mallProduct = productMap.get(item.getProductId());
            if(mallProduct.getStock() < item.getQuantity()){
                log.info("订单绑定商品库存不足，订单号：{}，商品id:{}", orderNumber, item.getProductId());
                throw new RuntimeException("您选择的商品库存不足，请重新确认");
            }
            // 1.3、计算单个商品总价格，计入此订单总金额
            BigDecimal itemTotal = mallProduct.getPrice().multiply(new BigDecimal(item.getQuantity()));
            totalAmount = totalAmount.add(itemTotal);
            totalQuantity += item.getQuantity();
        }
        // 2、加上配送费
        totalAmount = totalAmount.add(request.getDeliveryFee());
        request.setTotalQuantity(totalQuantity);
        request.setTotalAmount(totalAmount);
    }

    /**
    * @description: 调用微信支付API
    * @author: JayLWU
    * @date: 2025/8/26 21:26
    */
    public WxPayOrderResponse callWechatPayAPI(MallOrderRequest request, String orderNumber, BigDecimal totalAmount) {
        // 1、根据当前商品归属公司，查询对应微信支付参数
        WxMerchantKeyPO wxMerchantKeyPO = wxMerchantKeyService.getByCompanyId(10000L);
        WxPayParam wxPayParam = new WxPayParam();
        // 2、商户配置
        wxPayParam.setMode(wxMerchantKeyPO.getMode());
        wxPayParam.setMerId(wxMerchantKeyPO.getMerchantId());
        wxPayParam.setPrivateKeyPath(wxMerchantKeyPO.getPrivateKeyPath());
        wxPayParam.setPublicKeyPath(wxMerchantKeyPO.getPublicKeyPath());
        wxPayParam.setApiV3Key(wxMerchantKeyPO.getPrivateKey());
        wxPayParam.setMerchantSerialNumber(wxMerchantKeyPO.getMerchantSerialNumber());
        // 3、微信支付所需参数
        wxPayParam.setAppId(request.getAppId());
        wxPayParam.setOutTradeNo(orderNumber);
        wxPayParam.setDescription("购买商品");
        wxPayParam.setOpenid(request.getOpenId());
        wxPayParam.setTimeExpire(OffsetDateTime.now(ZoneOffset.of("+8")).plusMinutes(10)
                .format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        // 3.1、价格别慌，是后端手动set过的，不是前端传入
        wxPayParam.setTotal(totalAmount.multiply(new BigDecimal(100)).intValue());
        // 4、调用微信支付接口
        return wxCustomerPayUtils.createOrder(wxPayParam);
    }

    /**
     * 生成新的支付订单号（用于重新支付）
     * 格式：原订单号_retry_时间戳
     *
     * @param originalOrderNumber 原订单号
     * @return 新的支付订单号
     */
    private String generateRetryPaymentOrderNumber(String originalOrderNumber) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String retryOrderNumber = originalOrderNumber + "_retry_" + timestamp;

        // 确保订单号长度不超过微信支付限制（32位）
        if (retryOrderNumber.length() > 32) {
            // 如果太长，使用订单ID + 时间戳的方式
            retryOrderNumber = "retry_" + timestamp;
            if (retryOrderNumber.length() > 32) {
                retryOrderNumber = retryOrderNumber.substring(0, 32);
            }
        }

        log.info("生成重试支付订单号，原订单号：{}，新订单号：{}", originalOrderNumber, retryOrderNumber);
        return retryOrderNumber;
    }

    /**
     * 检查订单是否需要重新生成支付订单号
     *
     * @param order 订单信息
     * @param appId 微信小程序AppId
     * @return 是否需要重新生成订单号
     */
    private boolean shouldGenerateNewPaymentOrderNumber(MallOrder order, String appId) {
        try {
            // 1、获取订单相关的公司信息
            Long companyId = 10000L;
            WxMerchantKeyPO wxMerchantKeyPO = wxMerchantKeyService.getByCompanyId(companyId);

            // 2、构造查询参数
            WxPayParam queryParam = new WxPayParam();
            queryParam.setMode(wxMerchantKeyPO.getMode());
            queryParam.setMerId(wxMerchantKeyPO.getMerchantId());
            queryParam.setPrivateKeyPath(wxMerchantKeyPO.getPrivateKeyPath());
            queryParam.setPublicKeyPath(wxMerchantKeyPO.getPublicKeyPath());
            queryParam.setApiV3Key(wxMerchantKeyPO.getPrivateKey());
            queryParam.setMerchantSerialNumber(wxMerchantKeyPO.getMerchantSerialNumber());
            queryParam.setAppId(appId);
            queryParam.setOutTradeNo(order.getOrderNumber());
            queryParam.setQueryScene("out_trade_no"); // 使用商户订单号查询

            // 3、查询微信支付订单状态
            DirectAPIv3QueryResponse queryResponse = wxCustomerPayUtils.queryOrderByTradeNo(queryParam);

            if (queryResponse != null) {
                String tradeState = queryResponse.getTradeState();
                log.info("查询微信支付订单状态，订单号：{}，状态：{}", order.getOrderNumber(), tradeState);

                // 如果订单状态为SUCCESS（支付成功），不需要重新生成订单号
                if ("SUCCESS".equals(tradeState)) {
                    log.info("订单已支付成功，无需重新生成订单号");
                    return false;
                }

                // 如果订单状态为NOTPAY（未支付）或CLOSED（已关闭），需要重新生成订单号
                if ("NOTPAY".equals(tradeState) || "CLOSED".equals(tradeState)) {
                    log.info("订单状态为{}，需要重新生成订单号", tradeState);
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("查询微信支付订单状态失败，将重新生成订单号，订单号：{}，异常：{}", order.getOrderNumber(), e.getMessage());
            // 查询失败时，为了避免订单号重复，选择重新生成
            return true;
        }

        // 默认重新生成订单号
        return true;
    }

    /**
     * 获取订单状态统计
     *
     * @param customerId 用户ID（可选，如果为null则统计所有订单）
     * @return 订单状态统计
     */
    @Override
    public OrderStatusStatsResponse getOrderStatusStats(Long customerId) {
        try {
            return mallOrderMapper.getOrderStatusStats(customerId);
        } catch (Exception e) {
            log.error("获取订单状态统计失败", e);
            // 返回默认值
            return new OrderStatusStatsResponse();
        }
    }

    /**
     * 获取订单支付状态信息（包含剩余支付时间和支付参数）
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @param appId 微信小程序AppId（可选，用于生成支付参数）
     * @return 订单支付状态信息
     */
    @Override
    public OrderPaymentStatusResponse getOrderPaymentStatus(Long orderId, Long customerId, String appId) {
        // 验证订单存在和归属
        LambdaQueryWrapper<MallOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MallOrder::getId, orderId)
                .eq(MallOrder::getCustomerId, customerId)
                .eq(MallOrder::getStatus, UseStatusEnum.EFFECTIVE.getCode());

        MallOrder order = this.getOne(queryWrapper);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        // 计算剩余支付时间
        long remainingSeconds = 0;
        Integer isExpired = order.getIsExpired() != null ? order.getIsExpired() : 0;
        WechatPayResponse paymentParams = null;
        if (order.getPaymentExpireTime() != null && "pending".equals(order.getOrderStatus())) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(order.getPaymentExpireTime())) {
                remainingSeconds = java.time.Duration.between(now, order.getPaymentExpireTime()).getSeconds();
                
                // 如果订单未过期且提供了appId和code，则生成支付参数
                if (StringUtils.isNotBlank(appId) && remainingSeconds > 0) {
                    try {
                        WxMerchantKeyPO wxMerchantKeyPO = wxMerchantKeyService.getByCompanyId(10000L);
                        WxPayParam wxPayParam = new WxPayParam();
                        wxPayParam.setPrivateKeyPath(wxMerchantKeyPO.getPrivateKeyPath());
                        wxPayParam.setPublicKeyPath(wxMerchantKeyPO.getPublicKeyPath());
                        wxPayParam.setAppId(appId);
                        WxPayOrderResponse wxPayOrderResponse = wxCustomerPayUtils.generatePaymentParams(order.getPrepayId(), wxPayParam);
                        paymentParams = convertToWechatPayResponse(wxPayOrderResponse);
                    } catch (Exception e) {
                        log.warn("生成支付参数失败，订单ID：{}，原因：{}", orderId, e.getMessage());
                    }
                }
            } else {
                // 已过期但数据库还未更新的情况
                isExpired = 1;
                remainingSeconds = 0;
            }
        }
        return new OrderPaymentStatusResponse(
                String.valueOf(order.getId()),
                order.getOrderStatus(),
                order.getStatusText(),
                order.getPaymentExpireTime(),
                remainingSeconds,
                isExpired,
                order.getPrepayId(),
                paymentParams
        );
    }

    /**
     * 转换微信支付响应为前端格式
     */
    private WechatPayResponse convertToWechatPayResponse(WxPayOrderResponse wxPayResponse) {
        return new WechatPayResponse(
            wxPayResponse.getPrepayId(),
            wxPayResponse.getAppId(),
            wxPayResponse.getTimeStamp(),
            wxPayResponse.getNonceStr(),
            wxPayResponse.getPackageValue(),
            wxPayResponse.getSignType(),
            wxPayResponse.getPaySign()
        );
    }
}
