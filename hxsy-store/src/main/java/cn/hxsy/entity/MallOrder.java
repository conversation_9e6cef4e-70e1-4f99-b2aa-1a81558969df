package cn.hxsy.entity;

import cn.hxsy.datasource.model.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MallOrder", description = "订单信息")
@TableName("mall_orders")
public class MallOrder extends BaseEntity {

    @ApiModelProperty(value = "订单号")
    @TableField("order_number")
    private String orderNumber;

    @ApiModelProperty(value = "用户ID")
    @TableField("customer_id")
    private Long customerId;

    @ApiModelProperty(value = "归属公司id")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty(value = "订单状态：pending-待付款，paid-已付款，shipped-待收货，completed-已完成，cancelled-已取消")
    @TableField("order_status")
    private String orderStatus;

    @ApiModelProperty(value = "状态文本")
    @TableField("status_text")
    private String statusText;

    @ApiModelProperty(value = "订单总金额")
    @TableField("total_amount")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "商品总数量")
    @TableField("total_quantity")
    private Integer totalQuantity;

    @ApiModelProperty(value = "支付方式：微信支付、钱包支付")
    @TableField("payment_method")
    private String paymentMethod;

    @ApiModelProperty(value = "配送费")
    @TableField("delivery_fee")
    private BigDecimal deliveryFee;

    @ApiModelProperty(value = "收货人姓名")
    @TableField("receiver_name")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机")
    @TableField("receiver_phone")
    private String receiverPhone;

    @ApiModelProperty(value = "收货地址")
    @TableField("receiver_address")
    private String receiverAddress;

    @ApiModelProperty(value = "拉起微信支付官方返回-预支付id")
    @TableField("prepay_id")
    private String prepayId;

    @ApiModelProperty(value = "支付链接过期时间")
    @TableField("payment_expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paymentExpireTime;

    @ApiModelProperty(value = "是否已过期：0-未过期，1-已过期")
    @TableField("is_expired")
    private Integer isExpired;

    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "支付时间")
    @TableField("paid_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paidAt;

    @ApiModelProperty(value = "发货时间")
    @TableField("shipped_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime shippedAt;

    @ApiModelProperty(value = "完成时间")
    @TableField("completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completedAt;

    @ApiModelProperty(value = "微信支付订单号")
    @TableField("wx_transaction_id")
    private String wxTransactionId;

    @ApiModelProperty(value = "实际支付金额")
    @TableField("actual_paid_amount")
    private BigDecimal actualPaidAmount;

    @ApiModelProperty(value = "微信支付时间")
    @TableField("wx_paid_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime wxPaidTime;
}
