package cn.hxsy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

/**
 * 订单请求类
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@ApiModel(value = "MallOrderRequest", description = "订单请求")
public class MallOrderRequest {

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long customerId;

    @ApiModelProperty(value = "公司id-当前商品所属公司-用于查询绑定商户信息")
    private Long companyId;

    @ApiModelProperty(value = "商户在微信开放平台（移动应用）或公众平台（公众号/小程序）上申请的一个唯一标识。需确保该appid与mchid有绑定关系")
    @NotNull(message = "AppId不能为空")
    private String appId;

    @ApiModelProperty(value = "当前支付用户小程序内openId")
    @NotNull(message = "OpenId不能为空")
    private String openId;

    @ApiModelProperty(value = "收货地址ID")
    @NotNull(message = "收货地址ID不能为空")
    private Long addressId;

    @ApiModelProperty(value = "支付方式：微信支付、钱包支付")
    @NotNull(message = "支付方式不能为空")
    private String paymentMethod;

    @ApiModelProperty(value = "配送费（已免费）")
    private BigDecimal deliveryFee = BigDecimal.ZERO;

    @ApiModelProperty(value = "订单商品列表")
    @NotNull(message = "订单商品列表不能为空")
    private List<OrderItemRequest> items;

    @ApiModelProperty(value = "前端生成的唯一请求ID，单个商品组件内对应一次请求id")
    private String requestId;

    @ApiModelProperty(value = "订单商品总数-前端传了也没用，我会重新设置ouo")
    private int totalQuantity;

    @ApiModelProperty(value = "订单总金额-前端传了也没用，我会重新设置ouo")
    private BigDecimal totalAmount;

    /**
     * 订单商品请求内部类
     */
    @Data
    @ApiModel(value = "OrderItemRequest", description = "订单商品请求")
    public static class OrderItemRequest {
        @ApiModelProperty(value = "商品ID")
        @NotNull(message = "商品ID不能为空")
        private Long productId;

        @ApiModelProperty(value = "商品名称")
        private String name;

        @ApiModelProperty(value = "商品规格")
        private String spec;

        @ApiModelProperty(value = "商品单价")
        @NotNull(message = "商品单价不能为空")
        private BigDecimal price;

        @ApiModelProperty(value = "购买数量")
        @NotNull(message = "购买数量不能为空")
        private Integer quantity;

        @ApiModelProperty(value = "商品图片")
        private String image;
    }

    /**
     * 订单查询请求内部类
     */
    @Data
    @ApiModel(value = "OrderQueryRequest", description = "订单查询请求")
    public static class OrderQueryRequest {
        @ApiModelProperty(value = "用户ID")
        private Long customerId;

        @ApiModelProperty(value = "订单状态")
        private String orderStatus;

        @ApiModelProperty(value = "订单号")
        private String orderNumber;

        @ApiModelProperty(value = "退款状态：PENDING-待审核，APPROVED-已同意，REJECTED-已拒绝，PROCESSING-退款处理中，SUCCESS-退款成功，FAILED-退款失败")
        private String refundStatus;

        @ApiModelProperty(value = "页码")
        private Integer pageNum = 1;

        @ApiModelProperty(value = "每页大小")
        private Integer pageSize = 10;
    }
}
