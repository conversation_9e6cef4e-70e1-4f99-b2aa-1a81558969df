package cn.hxsy.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单响应类
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Data
@ApiModel(value = "MallOrderResponse", description = "订单响应")
public class MallOrderResponse {

    @ApiModelProperty(value = "订单ID")
    private Long id;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "归属公司id")
    private Long companyId;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "状态文本")
    private String statusText;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "商品总数量")
    private Integer totalQuantity;

    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;

    @ApiModelProperty(value = "配送费（已免费）")
    private BigDecimal deliveryFee;

    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收货人手机")
    private String receiverPhone;

    @ApiModelProperty(value = "收货地址")
    private String receiverAddress;

    @ApiModelProperty(value = "订单商品列表")
    private List<OrderItemResponse> items;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paidAt;

    @ApiModelProperty(value = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime shippedAt;

    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completedAt;

    @ApiModelProperty(value = "拉起微信支付官方返回-预支付id")
    private String prepayId;

    @ApiModelProperty(value = "支付链接过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paymentExpireTime;

    @ApiModelProperty(value = "是否已过期：0-未过期，1-已过期")
    private Integer isExpired;

    // ==================== 退款相关字段 ====================

    @ApiModelProperty(value = "退款ID")
    private Long refundId;

    @ApiModelProperty(value = "退款单号")
    private String refundNo;

    @ApiModelProperty(value = "退款状态")
    private String refundStatus;

    @ApiModelProperty(value = "退款状态文本")
    private String refundStatusText;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款原因")
    private String refundReason;

    @ApiModelProperty(value = "退款申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime refundApplyTime;

    @ApiModelProperty(value = "审核结果")
    private String auditResult;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    /**
     * 订单商品响应内部类
     */
    @Data
    @ApiModel(value = "OrderItemResponse", description = "订单商品响应")
    public static class OrderItemResponse {
        @ApiModelProperty(value = "订单商品ID")
        private Long id;

        @ApiModelProperty(value = "商品ID")
        private Long productId;

        @ApiModelProperty(value = "商品名称")
        private String name;

        @ApiModelProperty(value = "商品规格")
        private String spec;

        @ApiModelProperty(value = "商品单价")
        private BigDecimal price;

        @ApiModelProperty(value = "购买数量")
        private Integer quantity;

        @ApiModelProperty(value = "商品图片")
        private String image;

        @ApiModelProperty(value = "小计金额")
        private BigDecimal subtotal;
    }
}
