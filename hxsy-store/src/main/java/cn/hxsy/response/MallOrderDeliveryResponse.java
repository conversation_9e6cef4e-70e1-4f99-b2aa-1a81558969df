package cn.hxsy.response;

import cn.hxsy.base.response.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> XiaQL
 * @description : 物料详情响应结果集
 * @ClassName : MallOrderDeliveryDetailResponse
 * @date: 2025-09-06 22:51
 */
@Data
public class MallOrderDeliveryResponse extends BaseResponse {

	@ApiModelProperty("关联配送单号")
	private String deliveryId;

	@ApiModelProperty("关联的业务订单ID")
	private String orderId;

	@ApiModelProperty("物流公司编码（如SF, YTO, ZTO）或名称")
	private String logisticsCompany;

	@ApiModelProperty("物流运单号")
	private String trackingNo;

	@ApiModelProperty("配送状态：0-待发货 1-已发货 2-运送中 3-派送中 4-已签收 5-异常")
	private Integer deliveryStatus;

	@ApiModelProperty("当前最新的物流节点描述（如：已从广州发出）")
	private String currentNode;

	@ApiModelProperty("发件人信息（JSON存储，避免连表）")
	private SenderInfo senderInfo;

	@ApiModelProperty("收件人信息（JSON存储，包含姓名、电话、详细地址）")
	private ReceiverInfo receiverInfo;

	@ApiModelProperty("预计到达时间")
	private LocalDateTime estimatedArrivalTime;

	@ApiModelProperty("实际签收时间")
	private LocalDateTime signedTime;

	/*
	 * 发件人信息
	 */
	@Data
	public static class SenderInfo implements Serializable {
		private static final long serialVersionUID = 1L;
		private String name;
		private String phone;
		private String address;
	}

	/*
	 * 收件人信息
	 */
	@Data
	public static class ReceiverInfo implements Serializable {
		private static final long serialVersionUID = 1L;
		private String name;
		private String phone;
		private String address;
	}
}
