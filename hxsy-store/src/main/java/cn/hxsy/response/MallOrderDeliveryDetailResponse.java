package cn.hxsy.response;

import cn.hxsy.base.response.BaseResponse;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR> XiaQL
 * @description : 物料详情响应结果集
 * @ClassName : MallOrderDeliveryDetailResponse
 * @date: 2025-09-06 22:51
 */
@Data
public class MallOrderDeliveryDetailResponse extends BaseResponse {

	@ApiModelProperty("关联配送单号")
	private String deliveryId;

	@ApiModelProperty("物流信息发生的时间")
	private LocalDateTime trackingTime;

	@ApiModelProperty("物流节点描述（如：【广州市】快件已由XX驿站代收）")
	private String trackingDescription;

	@ApiModelProperty("节点发生的地点")
	private String trackingLocation;

	@ApiModelProperty("节点状态（可与主表状态枚举一致，用于更新主表）")
	private Integer trackingStatus;

	@ApiModelProperty("操作员或快递员")
	private String operator;
}
