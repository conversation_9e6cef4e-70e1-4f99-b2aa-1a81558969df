package cn.hxsy.constant.order;

/**
* @description: 订单状态
* @author: xiaQL
* @date: 2025/8/20 10:44
*/
public enum OrderStateType {

    pending("pending", "待付款"),

    paid("paid", "已付款"),

    shipped("shipped", "待收货"),

    completed("completed", "已完成"),

    cancelled("cancelled", "已取消"),

    refund("refund", "退款中"),

    // 扩展退款状态
    refund_processing("refund_processing", "退款处理中"),

    refund_success("refund_success", "退款成功"),

    refund_failed("refund_failed", "退款失败"),

    ;

    private final String code;
    private final String info;

    OrderStateType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}
