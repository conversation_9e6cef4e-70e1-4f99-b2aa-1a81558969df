package cn.hxsy.constant.refund;

/**
 * 退款状态常量
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public class RefundStatusConstants {

    /**
     * 退款状态
     */
    public static final String PENDING = "PENDING";           // 待审核
    public static final String APPROVED = "APPROVED";         // 已同意
    public static final String REJECTED = "REJECTED";         // 已拒绝
    public static final String PROCESSING = "PROCESSING";     // 退款处理中
    public static final String SUCCESS = "SUCCESS";           // 退款成功
    public static final String FAILED = "FAILED";             // 退款失败
    public static final String CANCELLED = "CANCELLED";       // 已取消

    /**
     * 退款类型
     */
    public static final String REFUND_TYPE_FULL = "FULL";     // 全额退款

    /**
     * 审核结果
     */
    public static final String AUDIT_APPROVED = "APPROVED";   // 同意退款
    public static final String AUDIT_REJECTED = "REJECTED";   // 拒绝退款

    /**
     * 退款渠道
     */
    public static final String CHANNEL_ORIGINAL = "ORIGINAL"; // 原路退回

    /**
     * 状态文本映射
     */
    public static String getStatusText(String status) {
        switch (status) {
            case PENDING:
                return "待审核";
            case APPROVED:
                return "已同意退款";
            case REJECTED:
                return "已拒绝退款";
            case PROCESSING:
                return "退款处理中";
            case SUCCESS:
                return "退款成功";
            case FAILED:
                return "退款失败";
            case CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }
}