package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.request.MallOrderDeliveryRequest;
import cn.hxsy.service.MallOrderDeliveryService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 订单配送主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 21:36:19
 */
@RestController
@RequestMapping("/api/v1/mallOrderDelivery")
public class MallOrderDeliveryController {

	@Resource
	private MallOrderDeliveryService mallOrderDeliveryService;

	@ApiOperation("保存订单配送关联运单基本信息")
	@PostMapping("/save-order-delivery")
	public Result<Object> queryByOrderId(@RequestBody List<MallOrderDeliveryRequest> request) {
		return mallOrderDeliveryService.saveOrderDelivery(request);
	}

	@ApiOperation("查询订单配送基本信息")
	@PostMapping("/query-order-delivery")
	public Result<Object> queryByOrderId(@RequestBody MallOrderDeliveryRequest request) {
		return mallOrderDeliveryService.queryOrderDelivery(request);
	}

}
