package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.request.MallOrderDeliveryDetailRequest;
import cn.hxsy.service.MallOrderDeliveryDetailService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 * <p>
 * 订单配送物流轨迹明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-06 22:11:38
 */
@RestController
@RequestMapping("/api/v1/mallOrderDeliveryDetail")
public class MallOrderDeliveryDetailController {

	@Resource
	private MallOrderDeliveryDetailService mallOrderDeliveryDetailService;

	@ApiOperation("查询订单配送到达详情")
	@PostMapping("/query-by-order-id")
	public Result<Object> queryByOrderId(@RequestBody MallOrderDeliveryDetailRequest request) {
		return Result.ok(mallOrderDeliveryDetailService.queryByOrderId(request));
	}

}
