package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.request.ApplyRefundRequest;
import cn.hxsy.request.AuditRefundRequest;
import cn.hxsy.request.RefundQueryRequest;
import cn.hxsy.response.RefundDetailResponse;
import cn.hxsy.response.RefundResponse;
import cn.hxsy.response.RefundEligibilityResponse;
import cn.hxsy.service.MallRefundService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

/**
 * 退款管理控制器（与MallOrderController风格保持一致）
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Api(tags = "退款管理")
@RestController
@RequestMapping("/api/v1/mall/refund")
@RequiredArgsConstructor
@Slf4j
public class MallRefundController {

    private final MallRefundService mallRefundService;

    /**
     * 申请退款
     *
     * @param request 退款申请请求
     * @return 退款单号
     */
    @ApiOperation("申请退款")
    @PostMapping("/apply")
    public Result<String> applyRefund(
            @ApiParam("退款申请请求") @Valid @RequestBody ApplyRefundRequest request) {
        try {
            String refundNo = mallRefundService.applyRefund(request);
            return Result.ok(refundNo);
        } catch (Exception e) {
            log.error("申请退款失败，请求参数：{}，异常信息：", request, e);
            return Result.error("申请退款失败：" + e.getMessage());
        }
    }

    /**
     * 查询我的退款列表
     *
     * @param request 查询条件
     * @return 退款列表
     */
    @ApiOperation("查询我的退款列表")
    @PostMapping("/my-list")
    public Result<IPage<RefundResponse>> getMyRefundList(
            @ApiParam("查询条件") @RequestBody RefundQueryRequest request) {
        try {
            IPage<RefundResponse> result = mallRefundService.getRefundList(request);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询退款列表失败，请求参数：{}，异常信息：", request, e);
            return Result.error("查询退款列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取退款详情
     *
     * @param refundId 退款ID
     * @param customerId 用户ID
     * @return 退款详情
     */
    @ApiOperation("获取退款详情")
    @GetMapping("/detail/{refundId}")
    public Result<RefundDetailResponse> getRefundDetail(
            @ApiParam("退款ID") @PathVariable String refundId,
            @ApiParam("用户ID") @RequestParam String customerId) {
        try {
            Long refundIdLong = Long.valueOf(refundId);
            Long customerIdLong = Long.valueOf(customerId);
            RefundDetailResponse result = mallRefundService.getRefundDetail(refundIdLong, customerIdLong);
            return Result.ok(result);
        } catch (NumberFormatException e) {
            log.error("获取退款详情参数格式错误，refundId：{}，customerId：{}，异常信息：", refundId, customerId, e);
            return Result.error("参数格式错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取退款详情失败，refundId：{}，customerId：{}，异常信息：", refundId, customerId, e);
            return Result.error("获取退款详情失败：" + e.getMessage());
        }
    }

    /**
     * 商家审核退款
     *
     * @param request 审核请求
     * @return 审核结果
     */
    @ApiOperation("商家审核退款")
    @PostMapping("/audit")
    public Result<Boolean> auditRefund(
            @ApiParam("审核请求") @Valid @RequestBody AuditRefundRequest request) {
        try {
            Boolean result = mallRefundService.auditRefund(request);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("审核退款失败，请求参数：{}，异常信息：", request, e);
            return Result.error("审核退款失败：" + e.getMessage());
        }
    }

    /**
     * 查询商家待处理退款列表
     *
     * @param companyId 公司ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 待处理退款列表
     */
    @ApiOperation("查询商家待处理退款列表")
    @GetMapping("/pending-list")
    public Result<IPage<RefundResponse>> getPendingRefundList(
            @ApiParam("公司ID") @RequestParam(required = false) Long companyId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            IPage<RefundResponse> result = mallRefundService.getPendingRefundList(companyId, pageNum, pageSize);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询待处理退款列表失败，companyId：{}，异常信息：", companyId, e);
            return Result.error("查询待处理退款列表失败：" + e.getMessage());
        }
    }

    /**
     * 检查订单退款资格
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 退款资格检查结果
     */
    @ApiOperation("检查订单退款资格")
    @GetMapping("/check-eligibility")
    public Result<RefundEligibilityResponse> checkRefundEligibility(
            @ApiParam("订单ID") @RequestParam String orderId,
            @ApiParam("用户ID") @RequestParam String customerId) {
        try {
            Long orderIdLong = Long.valueOf(orderId);
            Long customerIdLong = Long.valueOf(customerId);
            RefundEligibilityResponse result = mallRefundService.checkRefundEligibility(orderIdLong, customerIdLong);
            return Result.ok(result);
        } catch (NumberFormatException e) {
            log.error("检查退款资格参数格式错误，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("参数格式错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("检查退款资格失败，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("检查退款资格失败：" + e.getMessage());
        }
    }

    /**
     * 取消退款申请
     *
     * @param refundId 退款ID
     * @param customerId 用户ID
     * @return 操作结果
     */
    @ApiOperation("取消退款申请")
    @PostMapping("/cancel")
    public Result<Boolean> cancelRefund(
            @ApiParam("取消退款请求") @RequestBody Map<String, String> request) {
        try {
            String refundId = request.get("refundId");
            String customerId = request.get("customerId");
            
            if (refundId == null || customerId == null) {
                return Result.error("参数不能为空");
            }
            
            Long refundIdLong = Long.valueOf(refundId);
            Long customerIdLong = Long.valueOf(customerId);
            
            Boolean result = mallRefundService.cancelRefund(refundIdLong, customerIdLong);
            return Result.ok(result);
        } catch (NumberFormatException e) {
            log.error("取消退款申请参数格式错误，异常信息：", e);
            return Result.error("参数格式错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("取消退款申请失败，异常信息：", e);
            return Result.error("取消退款申请失败：" + e.getMessage());
        }
    }

    /**
     * 微信退款结果通知回调
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    @ApiOperation("微信退款结果通知回调")
    @PostMapping("/wx-callback")
    public Result<Object> handleWxRefundCallback(HttpServletRequest request) {
        try {
            // 读取回调数据
            String notificationData = request.getReader().lines()
                    .reduce("", (accumulator, actual) -> accumulator + actual);
            
            Boolean result = mallRefundService.handleWxRefundCallback(notificationData);
            if (result) {
                return Result.ok("SUCCESS");
            } else {
                return Result.error("FAIL");
            }
        } catch (Exception e) {
            log.error("处理微信退款回调失败，异常信息：", e);
            return Result.error("FAIL");
        }
    }

    /**
     * 查询退款状态
     *
     * @param refundId 退款ID
     * @return 退款状态
     */
    @ApiOperation("查询退款状态")
    @GetMapping("/status/{refundId}")
    public Result<String> queryRefundStatus(
            @ApiParam("退款ID") @PathVariable Long refundId) {
        try {
            String status = mallRefundService.queryRefundStatus(refundId);
            return Result.ok(status);
        } catch (Exception e) {
            log.error("查询退款状态失败，refundId：{}，异常信息：", refundId, e);
            return Result.error("查询退款状态失败：" + e.getMessage());
        }
    }

    /**
     * 根据订单ID获取退款信息
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 退款详情
     */
    @ApiOperation("根据订单ID获取退款信息")
    @GetMapping("/by-order/{orderId}")
    public Result<RefundDetailResponse> getRefundByOrderId(
            @ApiParam("订单ID") @PathVariable String orderId,
            @ApiParam("用户ID") @RequestParam String customerId) {
        try {
            Long orderIdLong = Long.valueOf(orderId);
            Long customerIdLong = Long.valueOf(customerId);
            RefundDetailResponse result = mallRefundService.getRefundByOrderId(orderIdLong, customerIdLong);
            return Result.ok(result);
        } catch (NumberFormatException e) {
            log.error("根据订单ID获取退款信息参数格式错误，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("参数格式错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("根据订单ID获取退款信息失败，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("根据订单ID获取退款信息失败：" + e.getMessage());
        }
    }
}