package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.service.MallOrderService;
import cn.hxsy.service.MallRefundService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 小程序管理接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RestController
@RequestMapping("/api/v1/mini-program")
@Api(tags = "小程序管理接口")
public class MiniProgramController {

    @Autowired
    private MallOrderService mallOrderService;

    @Autowired
    private MallRefundService mallRefundService;

    @ApiOperation(value = "微信支付回调接口", notes = "处理微信支付结果通知", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/wx-pay-callback", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<Object> wxPayCallback(HttpServletRequest request) {
        return mallOrderService.handlePayCallback(request);
    }

    @ApiOperation(value = "微信退款回调接口", notes = "处理微信退款结果通知", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/wx-refund-callback", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<Object> wxRefundCallback(HttpServletRequest request) {
        return mallRefundService.handleWxRefundCallback(request);
    }
} 