package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.request.ConfirmOrderRequest;
import cn.hxsy.request.PayOrderRequest;
import cn.hxsy.response.WechatPayResponse;
import cn.hxsy.request.MallOrderRequest;
import cn.hxsy.response.MallOrderResponse;
import cn.hxsy.response.OrderPaymentStatusResponse;
import cn.hxsy.response.OrderStatusStatsResponse;
import cn.hxsy.service.MallOrderService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 订单管理控制器
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@Api(tags = "订单管理")
@RestController
@RequestMapping("/api/v1/mall/order")
@RequiredArgsConstructor
@Slf4j
public class MallOrderController {

    private final MallOrderService mallOrderService;

    ///**
    // * 创建订单
    // *
    // * @param request 订单请求
    // * @return 订单ID
    // */
    //@ApiOperation("创建订单")
    //@PostMapping("/create")
    //public Result<String> createOrder(
    //        @ApiParam("订单请求") @Valid @RequestBody MallOrderRequest request) {
    //    String orderId = mallOrderService.createOrder(request);
    //    return Result.ok(orderId);
    //}

    /**
     * 分页查询订单列表
     *
     * @param request 查询条件
     * @return 订单列表
     */
    @ApiOperation("分页查询订单列表")
    @PostMapping("/page")
    public Result<IPage<MallOrderResponse>> queryOrderPage(
            @ApiParam("查询条件") @RequestBody MallOrderRequest.OrderQueryRequest request) {
        try {
            IPage<MallOrderResponse> result = mallOrderService.queryOrderPage(request);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询订单列表失败，请求参数：{}，异常信息：", request, e);
            return Result.error("查询订单列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 订单详情
     */
    @ApiOperation("获取订单详情")
    @GetMapping("/{orderId}")
    public Result<MallOrderResponse> getOrderDetail(
            @ApiParam("订单ID") @PathVariable String orderId,
            @ApiParam("用户ID") @RequestParam String customerId) {
        try {
            Long orderIdLong = Long.valueOf(orderId);
            Long customerIdLong = Long.valueOf(customerId);
            MallOrderResponse result = mallOrderService.getOrderDetail(orderIdLong, customerIdLong);
            return Result.ok(result);
        } catch (NumberFormatException e) {
            log.error("获取订单详情参数格式错误，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("参数格式错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取订单详情失败，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("获取订单详情失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单
     *
     * @param request 取消订单请求
     * @return 操作结果
     */
    @ApiOperation("取消订单")
    @PostMapping("/cancel")
    public Result<Boolean> cancelOrder(@RequestBody CancelOrderRequest request) {
        try {
            Boolean result = mallOrderService.cancelOrder(request.getOrderId(), request.getCustomerId());
            return Result.ok(result);
        } catch (Exception e) {
            log.error("取消订单失败，请求参数：{}，异常信息：", request, e);
            return Result.error("取消订单失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单请求类
     */
    public static class CancelOrderRequest {
        private String orderId;
        private String customerId;

        public Long getOrderId() {
            return orderId != null ? Long.valueOf(orderId) : null;
        }

        public void setOrderId(String orderId) {
            this.orderId = orderId;
        }

        public Long getCustomerId() {
            return customerId != null ? Long.valueOf(customerId) : null;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }
    }

    /**
     * 发货
     *
     * @param orderId 订单ID
     * @return 操作结果
     */
    @ApiOperation("发货")
    @PostMapping("/ship")
    public Result<Boolean> shipOrder(
            @ApiParam("订单ID") @RequestParam String orderId) {
        try {
            Boolean result = mallOrderService.shipOrder(orderId);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("发货失败，订单ID：{}，异常信息：", orderId, e);
            return Result.error("发货失败：" + e.getMessage());
        }
    }

    /**
     * 确认收货
     *
     * @param request 确认收货请求
     * @return 操作结果
     */
    @ApiOperation("确认收货")
    @PostMapping("/confirm")
    public Result<Boolean> confirmOrder(@RequestBody ConfirmOrderRequest request) {
        try {
            Boolean result = mallOrderService.confirmOrder(request.getOrderId(), request.getCustomerId());
            return Result.ok(result);
        } catch (Exception e) {
            log.error("确认收货失败，请求参数：{}，异常信息：", request, e);
            return Result.error("确认收货失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单状态统计
     *
     * @param customerId 用户ID（可选，如果不传则统计所有订单）
     * @return 订单状态统计
     */
    @ApiOperation("获取订单状态统计")
    @GetMapping("/statistics")
    public Result<OrderStatusStatsResponse> getOrderStatusStats(
            @ApiParam("用户ID（可选）") @RequestParam(required = false) Long customerId) {
        try {
            OrderStatusStatsResponse result = mallOrderService.getOrderStatusStats(customerId);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取订单状态统计失败，用户ID：{}，异常信息：", customerId, e);
            return Result.error("获取订单状态统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单支付状态信息（包含剩余支付时间）
     *
     * @param orderId 订单ID
     * @param customerId 用户ID
     * @return 订单支付状态信息
     */
    @ApiOperation("获取订单支付状态")
    @GetMapping("/payment-status/{orderId}")
    public Result<OrderPaymentStatusResponse> getOrderPaymentStatus(
            @ApiParam("订单ID") @PathVariable String orderId,
            @ApiParam("用户ID") @RequestParam String customerId,
            @ApiParam("微信小程序AppId（可选）") @RequestParam(required = false) String appId) {
        try {
            Long orderIdLong = Long.valueOf(orderId);
            Long customerIdLong = Long.valueOf(customerId);
            OrderPaymentStatusResponse result = mallOrderService.getOrderPaymentStatus(orderIdLong, customerIdLong, appId);
            return Result.ok(result);
        } catch (NumberFormatException e) {
            log.error("获取订单支付状态参数格式错误，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("参数格式错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取订单支付状态失败，orderId：{}，customerId：{}，异常信息：", orderId, customerId, e);
            return Result.error("获取订单支付状态失败：" + e.getMessage());
        }
    }
}
