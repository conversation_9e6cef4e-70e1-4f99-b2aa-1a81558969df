package cn.hxsy.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SignUtil;
import cn.hutool.json.JSONUtil;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.base.response.Result;
import cn.hxsy.base.response.TransferNotification;
import cn.hxsy.constant.wxPay.TradeNoSceneType;
import cn.hxsy.datasource.model.entity.WxMerchantKeyPO;
import cn.hxsy.model.request.WxPayClientRequest;
import cn.hxsy.model.request.WxPayParam;
import cn.hxsy.model.request.WxPayRefundRequest;
import cn.hxsy.model.response.DirectAPIv3QueryResponse;
import cn.hxsy.model.response.WxAppTransferResponse;
import cn.hxsy.model.response.WxPayOrderResponse;
import cn.hxsy.model.response.WxPayRefundResponse;
import cn.hxsy.service.MallOrderService;
import cn.hxsy.service.WxMerchantKeyService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.PublicKeyVerifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.cert.CertificatesManager;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationHandler;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.http.*;
import com.wechat.pay.java.service.refund.model.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Signature;
import java.util.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.PublicKey;

/**
* @description: 客户小程序拉起微信支付工具类
* 微信支付V3工具类（小程序支付场景）
* 封装接口：统一下单、支付通知解析、订单查询、关闭订单
* @author: xiaQL
* @date: 2025/8/17 16:16
*/
@Component
@Slf4j
public class WxCustomerPayUtils {

	@Autowired
	private WxMerchantKeyService merchantKeyService;

    @Autowired
    private MallOrderService mallOrderService;

	/**
	 * 系统回调域名
	 */
	@Value("${wx.pay.customer.domain:https://dev.huaxiacomp.cn}")
	private String domain;

	/**
	 * 系统回调接口地址
	 */
	@Value("${wx.pay.customer.notify:/gateway/hxsy-store/api/v1/mini-program/wx-pay-callback}")
	private String notifyUrl;

	/*
	 * 微信支付域名前缀
	 */
	private static final String wxPayPrefix = "https://api.mch.weixin.qq.com";

	/**
	* @description: 初始化签名HttpClient
	* @author: xiaQL
	* @date: 2025/8/18 17:42
	*/
	public WxPayClientRequest buildWxPayClientParam(WxPayParam param) {
		WxPayClientRequest wxPayClientRequest = new WxPayClientRequest();
		// 1、初始化签名的HttpClient
		OkHttpClient okHttpClient = new OkHttpClient();
		HttpClient httpClient = new DefaultHttpClientBuilder()
				.config(param.getMode() == 0 ? rsaAutoCertificateConfig(param) : rsaPublicKeyConfig(param))
				.okHttpClient(okHttpClient)
				.build();
		wxPayClientRequest.setHttpClient(httpClient);
		// 2、构造HttpHeaders
		HttpHeaders headers = new HttpHeaders();
		headers.addHeader("Accept", MediaType.APPLICATION_JSON.getValue());
		headers.addHeader("Content-Type", MediaType.APPLICATION_JSON.getValue());
		headers.addHeader("Wechatpay-Serial", param.getMerchantSerialNumber());
		wxPayClientRequest.setHeaders(headers);
		return wxPayClientRequest;
	}


	/**
	* @description: 统一下单（生成prepay_id和完整支付参数）
	* @author: xiaQL
	* @date: 2025/8/18 21:09
	*/
	public WxPayOrderResponse createOrder(WxPayParam wxPayParam){
		// 1、获取微信支付client
		WxPayClientRequest wxPayClientRequest = this.buildWxPayClientParam(wxPayParam);
		HttpClient httpClient = wxPayClientRequest.getHttpClient();
		// 2、构造请求体（JSON格式）
		Map<String, Object> bodyMap = new HashMap<>();
		bodyMap.put("appid", wxPayParam.getAppId());
		bodyMap.put("mchid", wxPayParam.getMerId());
		bodyMap.put("description", wxPayParam.getDescription());
		bodyMap.put("out_trade_no", wxPayParam.getOutTradeNo());
		bodyMap.put("time_expire", wxPayParam.getTimeExpire());
		bodyMap.put("notify_url", domain + notifyUrl);
		Map<String, Integer> amount = new HashMap<>();
		amount.put("total", wxPayParam.getTotal());
		bodyMap.put("amount", amount);
		Map<String, String> payer = new HashMap<>();
		payer.put("openid", wxPayParam.getOpenid());
		bodyMap.put("payer", payer);
		JsonRequestBody build = new JsonRequestBody.Builder()
				.body(JSONUtil.toJsonStr(bodyMap))
				.build();
		String url = wxPayPrefix + "/v3/pay/transactions/jsapi";
		HttpRequest executeSendGetHttpRequest = new HttpRequest.Builder()
				.httpMethod(HttpMethod.POST)
				.url(url)
				.headers(wxPayClientRequest.getHeaders())
				.body(build)
				.build();
		log.info("统一下单请求参数：{}", JSONUtil.toJsonStr(executeSendGetHttpRequest));
		try {
			HttpResponse<WxAppTransferResponse> execute = httpClient.execute(executeSendGetHttpRequest, WxAppTransferResponse.class);
			String prepayId = execute.getServiceResponse().getPrepayId();

			// 生成前端支付所需的完整参数
			WxPayOrderResponse payOrderResponse = generatePaymentParams(prepayId, wxPayParam);

			log.info("统一下单成功，返回完整支付参数：{}", JSONUtil.toJsonStr(payOrderResponse));
			return payOrderResponse;
		} catch (ServiceException e) {
			log.error("小程序拉起微信支付异常：{}", e.getMessage(), e);
			throw new RuntimeException("小程序拉起微信支付异常：" + e.getMessage());
		} catch (Exception e) {
			log.error("小程序拉起微信支付失败：", e);
			throw new RuntimeException("小程序拉起微信支付失败：" + e.getMessage());
		}
	}

	/**
	 * @description: 根据查询场景获取官方订单
	 * 1、微信支付订单号查询订单：queryOrderByTradeNo
	 * 2、商户订单号查询订单：queryOrderByBusNo
	 * @author: xiaQL
	 * @date: 2025/8/19 17:10
	 */
	public DirectAPIv3QueryResponse queryOrderByTradeNo(WxPayParam wxPayParam) throws Exception {
		// 1、获取微信支付client
		WxPayClientRequest wxPayClientRequest = this.buildWxPayClientParam(wxPayParam);
		HttpClient httpClient = wxPayClientRequest.getHttpClient();
		// 2、构造请求头参数（JSON格式）
		String url = "";
		if(TradeNoSceneType.transaction_id.getCode().equals(wxPayParam.getQueryScene())){
			url = String.format(wxPayPrefix + "/v3/pay/transactions/id/%s?mchid=%s", wxPayParam.getOutTradeNo(), wxPayParam.getMerId());
		}else if(TradeNoSceneType.out_trade_no.getCode().equals(wxPayParam.getQueryScene())){
			url = String.format("https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/%s?mchid=%s",
					wxPayParam.getOutTradeNo(), wxPayParam.getMerId());
		}
		if (StringUtils.isEmpty(url)){
			log.error("微信支付订单号查询失败：请检查查询场景是否正确");
			throw new RuntimeException("微信支付订单号查询失败：请检查查询场景是否正确");
		}
		HttpRequest executeSendGetHttpRequest = new HttpRequest.Builder()
				.httpMethod(HttpMethod.GET)
				.url(url)
				.headers(wxPayClientRequest.getHeaders())
				.build();
		try {
			HttpResponse<DirectAPIv3QueryResponse> execute = httpClient.execute(executeSendGetHttpRequest, DirectAPIv3QueryResponse.class);
			return execute.getServiceResponse();
		}catch (ServiceException e) {
			log.error("微信支付订单号查询异常：{}", e.getMessage(), e);
			throw new RuntimeException("微信支付订单号查询异常：" + e.getMessage());
		} catch (Exception e) {
			log.error("微信支付订单号查询失败：", e);
			throw new RuntimeException("微信支付订单号查询失败：" + e.getMessage());
		}
	}

	/**
	* @description: 微信支付退款申请
	 * 校验项：我觉得在调用发起处做会比较好，此处就只作为微信支付调用的工具类，只做基本参数的校验
	 * 但是必要的校验步骤还是先写明，方便以后在调用退款处一起考虑
	 * 1、幂等：防止重复退款
	 * 2、订单校验：
	 * 1）确认发起退款请求的用户是否为订单的实际购买
	 * 2）确保订单处于可退款状态（仅 已支付（Paid）、已发货（Shipped）（部分情况下）、已完成（Done）（在一定时间内）等状态允许申请退款。
	 * 3、退款金额校验：
	 * 1）退款金额不能大于订单实际支付金额
	 * 2）若部分退款，需校验累计退款金额（包括本次）不能大于实际支付金额。
	* @author: xiaQL
	* @date: 2025/9/4 22:43
	*/
	public WxPayRefundResponse wxPayRefund(WxPayParam wxPayParam) {
		// 1、获取微信支付client
		WxPayClientRequest wxPayClientRequest = this.buildWxPayClientParam(wxPayParam);
		HttpClient httpClient = wxPayClientRequest.getHttpClient();
		// 2、构造请求体（JSON格式）
		WxPayRefundRequest wxPayRefundRequest = wxPayParam.getWxPayRefundRequest();
		// 2.1、使用微信支付SDK提供的CreateRequest对象
		CreateRequest request = new CreateRequest();
		request.setTransactionId(wxPayRefundRequest.getTransactionId());
		request.setOutTradeNo(wxPayRefundRequest.getOutTradeNo());
		request.setOutRefundNo(wxPayRefundRequest.getOutRefundNo());
		request.setReason(wxPayRefundRequest.getReason());
		request.setNotifyUrl(domain + notifyUrl);
		request.setFundsAccount(ReqFundsAccount.AVAILABLE);
		// 2.1 构造金额参数
		AmountReq amountReq = wxPayRefundRequest.getAmount();
		amountReq.setCurrency("CNY");
		List<FundsFromItem> from = amountReq.getFrom();
		if(CollectionUtils.isEmpty(from)){
			throw new RuntimeException("退款金额参数错误：请检查退款金额参数是否正确");
		}
		for (FundsFromItem fundsFromItem : from) {
			fundsFromItem.setAccount(Account.AVAILABLE);
		}
		request.setAmount(wxPayRefundRequest.getAmount());
		// 2.2 构造商品参数
		request.setGoodsDetail(wxPayRefundRequest.getGoodsDetail());
		JsonRequestBody build = new JsonRequestBody.Builder()
				.body(JSONUtil.toJsonStr(request))
				.build();
		String url = wxPayPrefix + "/v3/refund/domestic/refunds";
		HttpRequest executeSendGetHttpRequest = new HttpRequest.Builder()
				.httpMethod(HttpMethod.POST)
				.url(url)
				.headers(wxPayClientRequest.getHeaders())
				.body(build)
				.build();
		log.info("退款请求参数：{}", JSONUtil.toJsonStr(executeSendGetHttpRequest));
		try {
			HttpResponse<WxPayRefundResponse> execute = httpClient.execute(executeSendGetHttpRequest, WxPayRefundResponse.class);
			WxPayRefundResponse serviceResponse = execute.getServiceResponse();
			log.info("退款请求成功，返回参数：{}", JSONUtil.toJsonStr(serviceResponse));
			return serviceResponse;
		} catch (ServiceException e) {
			log.error("小程序拉起退款请求异常：{}", e.getMessage(), e);
			throw new RuntimeException("小程序拉起退款请求异常：" + e.getMessage());
		} catch (Exception e) {
			log.error("小程序拉起退款请求失败：", e);
			throw new RuntimeException("小程序拉起退款请求失败：" + e.getMessage());
		}
	}

	/**
	* @description: 处理微信支付回调
	 * @return
	 * 验签通过：商户需告知微信支付接收回调成功，HTTP应答状态码需返回200或204，无需返回应答报文。
	 * 验签不通过：商户需告知微信支付接收回调失败，HTTP应答状态码需返回5XX或4XX，同时需返回以下应答报文：
	* @author: xiaQL
	* @date: 2025/8/20 15:17
	*/
	public Result<Object> handlePayCallback(HttpServletRequest request) {
		String requestData = null;
		try {
			// 读取回调数据
			requestData = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
			log.info("收到微信支付回调请求");
			// 1、验签处理
			String timestamp = request.getHeader("Wechatpay-Timestamp");
			String nonce = request.getHeader("Wechatpay-Nonce");
			String signature = request.getHeader("Wechatpay-Signature");
			String serialNumber = request.getHeader("Wechatpay-Serial");
			log.info("--------------回调请求头数据START-----------");
			log.info("回调请求头: {}", requestData);
			log.info("Wechatpay-Serial: {}", serialNumber);
			log.info("Wechatpay-signature: {}", signature);
			log.info("Wechatpay-nonce: {}", nonce);
			log.info("Wechatpay-timestamp: {}", timestamp);
			log.info("--------------回调请求头数据END-----------");
			if (StringUtils.isAnyBlank(timestamp, nonce, signature, serialNumber)) {
				log.error("回调请求缺少必要的请求头");
				return Result.error("缺少必要的请求头");
			}
			// 1.1、验证时间戳，防止重放攻击（5分钟内的请求有效）
			long currentTime = System.currentTimeMillis() / 1000;
			long timestampLong = Long.parseLong(timestamp);
			if (Math.abs(currentTime - timestampLong) > 300) {
				log.error("回调请求已过期，当前时间: {}, 请求时间: {}", currentTime, timestamp);
				return Result.error("请求已过期");
			}
			// 1.2、根据证书序列号获取商户配置信息，验签并解密通知数据
			LambdaQueryWrapper<WxMerchantKeyPO> queryWrapper = new LambdaQueryWrapper<WxMerchantKeyPO>()
					.eq(WxMerchantKeyPO::getPlatformSerialNumber, serialNumber);
			List<WxMerchantKeyPO> wxMerchantKeyPOS = merchantKeyService.getBaseMapper().selectList(queryWrapper);
			if (CollectionUtils.isEmpty(wxMerchantKeyPOS)) {
				log.error("商户密钥信息不存在");
				return Result.error("商户配置不存在");
			}
			WxMerchantKeyPO wxMerchantKeyPO = wxMerchantKeyPOS.get(0);
			JSONObject notifyData = this.handlePayNotification(
					nonce,
					timestamp,
					signature,
					serialNumber,
					requestData,
					wxMerchantKeyPO
			);
			if (notifyData == null) {
				log.error("回调通知验签失败或解密失败");
				return Result.error("回调通知验证失败");
			}
			log.info("微信支付回调数据解签完成: {}", notifyData.toJSONString());
			// 处理正常，获取解密后对应系统侧订单号与支付状态，用于更新订单状态
			return Result.ok(notifyData);
		}catch (Exception e){
			// 处理失败，抛出异常以响应微信支付方错误状态码
			log.error("处理微信支付-小程序回调异常: ", e);
			throw new RuntimeException("处理微信支付-小程序回调异常");
		}
	}

	/**
	 * API安全加密配置。PUB_KEY_ID_0116918028532025050500321832000200
	 */
	private static RSAAutoCertificateConfig rsaAutoCertificateConfig(WxPayParam param) {
		// 证书模式
		String privateKeyContent = downloadPrivateKeyFromUrl(param.getPrivateKeyPath());
		return new RSAAutoCertificateConfig.Builder()
				// 商户号
				.merchantId(param.getMerId())
				// 直接从字符串加载私钥，而不是从文件路径
				.privateKey(privateKeyContent)
				// 商户API证书序列号
				.merchantSerialNumber(param.getMerchantSerialNumber())
				// APIv3密钥
				.apiV3Key(param.getApiV3Key())
				.build();
	}

	private static RSAPublicKeyConfig rsaPublicKeyConfig(WxPayParam param) {
		// 公钥模式
		String privateKeyContent = downloadPrivateKeyFromUrl(param.getPrivateKeyPath());
		String publicKeyContent = downloadPrivateKeyFromUrl(param.getPublicKeyPath());
		// 可以根据实际情况使用privateKey或publicKey加载密钥
		return new RSAPublicKeyConfig.Builder()
				.merchantId(param.getMerId()) //微信支付的商户号
				.privateKey(privateKeyContent) // 商户API证书私钥内容
				.publicKey(publicKeyContent) //微信支付公钥内容
				.publicKeyId(param.getPublicKeyId()) //微信支付公钥ID
				.merchantSerialNumber(param.getMerchantSerialNumber()) //商户API证书序列号
				.apiV3Key(param.getApiV3Key()) //APIv3密钥
				.build();
	}

	/**
	* @description: 从远端链接获取私钥证书
	 * 因为需要对接多个商户，所以要将每个证书先上传到cos桶再获取
	* @author: xiaQL
	* @date: 2025/8/19 16:31
	*/
	private static String downloadPrivateKeyFromUrl(String privateKeyUrl){
		URL url = null;
		try {
			url = new URL(privateKeyUrl);
		} catch (MalformedURLException e) {
			throw new RuntimeException(e);
		}
		try (InputStream in = url.openStream();
		     ByteArrayOutputStream out = new ByteArrayOutputStream()) {

			byte[] buffer = new byte[4096];
			int bytesRead;
			while ((bytesRead = in.read(buffer)) != -1) {
				out.write(buffer, 0, bytesRead);
			}
			return new String(out.toByteArray(), StandardCharsets.UTF_8);
		} catch (IOException e) {
			log.error("下载私钥失败", e);
		}
		return "";
	}

	/**
	* @description: 处理微信支付回调通知
	 * @param nonce 随机串
	 * @param timestamp 时间戳
	 * @param signature 签名
	 * @param body 请求体
	 * @param merchantKey 商户配置信息
	 * @return 解密后的通知数据，验签失败时返回null
	* @author: xiaQL
	* @date: 2025/8/20 15:39
	*/
	public JSONObject handlePayNotification(
			String nonce,
			String timestamp,
			String signature,
			String serialNumber,
			String body,
			WxMerchantKeyPO merchantKey
	) {
		try {
			// 构建request，传入必要参数
			NotificationRequest request = new NotificationRequest.Builder().withSerialNumber(serialNumber)
					.withNonce(nonce)
					.withTimestamp(timestamp)
					.withSignature(signature)
					.withBody(body)
					.build();
			NotificationHandler handler;
			if (merchantKey.getMode() == 0) {
				// 证书模式
				String privateKeyContent = downloadPrivateKeyFromUrl(merchantKey.getPrivateKeyPath());
				// 平台证书管理器
				CertificatesManager certificatesManager = CertificatesManager.getInstance();
				certificatesManager.putMerchant(
						merchantKey.getMerchantId(),
						new WechatPay2Credentials(
								merchantKey.getMerchantId(),
								new PrivateKeySigner(merchantKey.getMerchantSerialNumber(), PemUtil.loadPrivateKey(privateKeyContent))
						),
						merchantKey.getPrivateKey().getBytes(StandardCharsets.UTF_8)
				);
				handler = new NotificationHandler(
						certificatesManager.getVerifier(merchantKey.getMerchantId()),
						merchantKey.getPrivateKey().getBytes(StandardCharsets.UTF_8)
				);
			} else {
				String publicKeyContent = downloadPrivateKeyFromUrl(merchantKey.getPublicKeyPath());
				PublicKey wechatPayPublicKey = PemUtil.loadPublicKey(publicKeyContent);
				// 公钥模式
				handler = new NotificationHandler(
						new PublicKeyVerifier(merchantKey.getPublicKeyId(), wechatPayPublicKey),
						merchantKey.getPrivateKey().getBytes(StandardCharsets.UTF_8)
				);
			}
			// 验签和解析请求体
			Notification notification = handler.parse(request);
			// 从notification中获取解密报文

			log.info("处理回调通知成功:{}", notification.getDecryptData());
            JSONObject jsonObject = JSONObject.parseObject(notification.getDecryptData());
            String tradeState = jsonObject.getString("trade_state");
            if("SUCCESS".equals(tradeState)){
                String outTradeNo = jsonObject.getString("out_trade_no");

            }
            return jsonObject;
		} catch (Exception e) {
			log.error("处理回调通知异常: {}", e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 生成前端支付所需的完整参数
	 * 参考文档：https://pay.weixin.qq.com/doc/v3/merchant/4012365341
	 *
	 * @param prepayId 预支付交易会话标识
	 * @param wxPayParam 支付参数
	 * @return 完整的支付参数
	 */
	public WxPayOrderResponse generatePaymentParams(String prepayId, WxPayParam wxPayParam) {
		try {
			// 1. 生成时间戳
			String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);

			// 2. 生成随机字符串
			String nonceStr = RandomUtil.randomString(32);

			// 3. 构造package参数
			String packageValue = "prepay_id=" + prepayId;

			// 4. 签名方式
			String signType = "RSA";

			// 5. 生成签名
			String paySign = generatePaySign(wxPayParam.getAppId(), timeStamp, nonceStr, packageValue, wxPayParam);

			// 6. 构造响应对象
			return new WxPayOrderResponse(
				prepayId,
				wxPayParam.getAppId(),
				timeStamp,
				nonceStr,
				packageValue,
				signType,
				paySign
			);
		} catch (Exception e) {
			log.error("生成支付参数失败：{}", e.getMessage(), e);
			throw new RuntimeException("生成支付参数失败：" + e.getMessage());
		}
	}

	/**
	 * 生成支付签名
	 * 参考文档：https://pay.weixin.qq.com/doc/v3/merchant/4012365341
	 *
	 * @param appId 小程序AppId
	 * @param timeStamp 时间戳
	 * @param nonceStr 随机字符串
	 * @param packageValue package参数
	 * @param wxPayParam 支付参数
	 * @return 签名
	 */
	private String generatePaySign(String appId, String timeStamp, String nonceStr, String packageValue, WxPayParam wxPayParam) {
		try {
			// 1. 构造签名串
			String signString = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + packageValue + "\n";

			log.info("支付签名串：{}", signString);

			// 2. 读取商户私钥
			String privateKeyContent = downloadPrivateKeyFromUrl(wxPayParam.getPrivateKeyPath());
			PrivateKey privateKey = PemUtil.loadPrivateKey(privateKeyContent);

			// 3. 使用SHA256withRSA算法进行签名
			Signature signature = Signature.getInstance("SHA256withRSA");
			signature.initSign(privateKey);
			signature.update(signString.getBytes(StandardCharsets.UTF_8));

			// 4. 对签名结果进行Base64编码
			String paySign = Base64.getEncoder().encodeToString(signature.sign());

			log.info("生成支付签名成功");
			return paySign;
		} catch (Exception e) {
			log.error("生成支付签名失败：{}", e.getMessage(), e);
			throw new RuntimeException("生成支付签名失败：" + e.getMessage());
		}
	}
}