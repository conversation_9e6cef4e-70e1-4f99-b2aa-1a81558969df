<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.mapper.MallOrderMapper">

    <!-- 订单响应结果映射 -->
    <resultMap id="MallOrderResponseMap" type="cn.hxsy.response.MallOrderResponse">
        <id column="id" property="id"/>
        <result column="order_number" property="orderNumber"/>
        <result column="customer_id" property="customerId"/>
        <result column="company_id" property="companyId"/>
        <result column="order_status" property="orderStatus"/>
        <result column="status_text" property="statusText"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="delivery_fee" property="deliveryFee"/>
        <result column="receiver_name" property="receiverName"/>
        <result column="receiver_phone" property="receiverPhone"/>
        <result column="receiver_address" property="receiverAddress"/>
        <result column="create_time" property="createTime"/>
        <result column="paid_at" property="paidAt"/>
        <result column="shipped_at" property="shippedAt"/>
        <result column="completed_at" property="completedAt"/>
        <result column="payment_expire_time" property="paymentExpireTime"/>
        <result column="is_expired" property="isExpired"/>
        <result column="prepay_id" property="prepayId" />
        <!-- 退款相关字段 -->
        <result column="refund_id" property="refundId"/>
        <result column="refund_no" property="refundNo"/>
        <result column="refund_status" property="refundStatus"/>
        <result column="refund_status_text" property="refundStatusText"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_reason" property="refundReason"/>
        <result column="refund_apply_time" property="refundApplyTime"/>
        <result column="audit_result" property="auditResult"/>
        <result column="audit_time" property="auditTime"/>
        <collection property="items" ofType="cn.hxsy.response.MallOrderResponse$OrderItemResponse">
            <id column="item_id" property="id"/>
            <result column="product_id" property="productId"/>
            <result column="item_name" property="name"/>
            <result column="spec" property="spec"/>
            <result column="price" property="price"/>
            <result column="quantity" property="quantity"/>
            <result column="image" property="image"/>
            <result column="subtotal" property="subtotal"/>
        </collection>
    </resultMap>

    <!-- 分页查询订单列表 -->
    <select id="queryOrderPage" resultMap="MallOrderResponseMap">
        SELECT
            o.id,
            o.order_number,
            o.customer_id,
            o.order_status,
            o.status_text,
            o.total_amount,
            o.total_quantity,
            o.payment_method,
            o.delivery_fee,
            o.receiver_name,
            o.receiver_phone,
            o.receiver_address,
            o.create_time,
            o.paid_at,
            o.shipped_at,
            o.completed_at,
            o.payment_expire_time,
            o.is_expired,
            o.prepay_id,
            -- 退款相关字段
            r.id as refund_id,
            r.refund_no,
            r.refund_status,
            r.status_text as refund_status_text,
            r.refund_amount,
            r.refund_reason,
            r.apply_time as refund_apply_time,
            r.audit_result,
            r.audit_time,
            -- 订单商品字段
            oi.id as item_id,
            oi.product_id,
            oi.name as item_name,
            oi.spec,
            oi.price,
            oi.quantity,
            oi.image,
            (oi.price * oi.quantity) as subtotal
        FROM mall_orders o
        LEFT JOIN mall_order_items oi ON o.id = oi.order_id AND oi.status = 1
        LEFT JOIN mall_order_refunds r ON o.id = r.order_id AND r.status = 1
        <where>
            o.status = 1
            <if test="request.customerId != null">
                AND o.customer_id = #{request.customerId}
            </if>
            <if test="request.orderStatus != null and request.orderStatus != ''">
                AND o.order_status = #{request.orderStatus}
            </if>
            <if test="request.orderNumber != null and request.orderNumber != ''">
                AND o.order_number LIKE CONCAT('%', #{request.orderNumber}, '%')
            </if>
            <if test="request.refundStatus != null and request.refundStatus != ''">
                AND r.refund_status = #{request.refundStatus}
            </if>
        </where>
        ORDER BY o.create_time DESC
    </select>

    <!-- 根据订单ID获取订单详情 -->
    <select id="getOrderDetailById" resultMap="MallOrderResponseMap">
        SELECT
            o.id,
            o.order_number,
            o.customer_id,
            o.order_status,
            o.status_text,
            o.total_amount,
            o.total_quantity,
            o.payment_method,
            o.delivery_fee,
            o.receiver_name,
            o.receiver_phone,
            o.receiver_address,
            o.create_time,
            o.paid_at,
            o.shipped_at,
            o.completed_at,
            o.payment_expire_time,
            o.is_expired,
            o.prepay_id,
            -- 退款相关字段
            r.id as refund_id,
            r.refund_no,
            r.refund_status,
            r.status_text as refund_status_text,
            r.refund_amount,
            r.refund_reason,
            r.apply_time as refund_apply_time,
            r.audit_result,
            r.audit_time,
            -- 订单商品字段
            oi.id as item_id,
            oi.product_id,
            oi.name as item_name,
            oi.spec,
            oi.price,
            oi.quantity,
            oi.image,
            (oi.price * oi.quantity) as subtotal
        FROM mall_orders o
        LEFT JOIN mall_order_items oi ON o.id = oi.order_id AND oi.status = 1
        LEFT JOIN mall_order_refunds r ON o.id = r.order_id AND r.status = 1
        WHERE o.id = #{orderId} AND o.status = 1
    </select>

    <!-- 获取订单状态统计 -->
    <select id="getOrderStatusStats" resultType="cn.hxsy.response.OrderStatusStatsResponse">
        SELECT
            SUM(CASE WHEN order_status = 'pending' THEN 1 ELSE 0 END) as pendingCount,
            SUM(CASE WHEN order_status = 'paid' THEN 1 ELSE 0 END) as paidCount,
            SUM(CASE WHEN order_status = 'shipped' THEN 1 ELSE 0 END) as shippedCount,
            SUM(CASE WHEN order_status = 'completed' THEN 1 ELSE 0 END) as completedCount
        FROM mall_orders
        WHERE status = 1
        <if test="customerId != null">
            AND customer_id = #{customerId}
        </if>
    </select>

</mapper>
