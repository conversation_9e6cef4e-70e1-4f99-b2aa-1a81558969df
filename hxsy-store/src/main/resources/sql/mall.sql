-- 商品表
CREATE TABLE mall_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    subtitle VARCHAR(255) COMMENT '商品副标题',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    stock INT DEFAULT 0 COMMENT '库存数量',
    image VARCHAR(500) COMMENT '商品主图',
    images JSON COMMENT '商品图片集合',
    detail_images JSON COMMENT '详情图片',
    category_id BIGINT COMMENT '分类ID',
    specs JSON COMMENT '规格信息 [{"name":"500g装","price":"12.80","stock":100}]',
    tags JSON COMMENT '商品标签',
    promotions JSON COMMENT '促销信息 [{"type":"满减","text":"满50减5元"}]',
    is_active TINYINT DEFAULT 1 COMMENT '是否上架：1-上架，0-下架',
    sales_count INT DEFAULT 0 COMMENT '销量',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  );
-- 商品分类
CREATE TABLE mall_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  );
-- 购物车商品表
CREATE TABLE mall_cart_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称（冗余）',
    spec VARCHAR(100) COMMENT '选择的规格',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
    image VARCHAR(500) COMMENT '商品图片（冗余）',
    selected TINYINT DEFAULT 1 COMMENT '是否选中：1-选中，0-未选中',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_customer_product (customer_id, product_id),
    INDEX idx_customer_id (customer_id)
);
-- 用户地址表
CREATE TABLE mall_customer_addresses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    detail VARCHAR(255) NOT NULL COMMENT '详细地址',
    full_address VARCHAR(500) NOT NULL COMMENT '完整地址',
    tag VARCHAR(20) DEFAULT '家' COMMENT '地址标签：家、公司、学校等',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认地址：1-是，0-否',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     INDEX idx_customer_id (customer_id)
);
-- 订单表
CREATE TABLE mall_orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    order_status VARCHAR(20) NOT NULL COMMENT '订单状态：pending-待付款，paid-已付款，shipped-待收货，completed-已完成，cancelled-已取消',
    status_text VARCHAR(20) NOT NULL COMMENT '状态文本',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    total_quantity INT NOT NULL COMMENT '商品总数量',
    payment_method VARCHAR(20) COMMENT '支付方式：微信支付、钱包支付',
    delivery_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '配送费',
    remark VARCHAR(500) COMMENT '订单备注',

    -- 收货地址信息（冗余存储）
    receiver_name VARCHAR(50) NOT NULL COMMENT '收货人姓名',
    receiver_phone VARCHAR(20) NOT NULL COMMENT '收货人手机',
    receiver_address VARCHAR(500) NOT NULL COMMENT '收货地址',

    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    paid_at DATETIME NULL COMMENT '支付时间',
    shipped_at DATETIME NULL COMMENT '发货时间',
    completed_at DATETIME NULL COMMENT '完成时间',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_customer_id (customer_id),
    INDEX idx_order_number (order_number),
    INDEX idx_order_status (order_status),
    INDEX idx_status (status)
);
alter table mall_orders add column `company_id` bigint null comment '归属公司id';
alter table mall_orders modify column `company_id` bigint null comment '归属公司id' after `customer_id`;
ALTER TABLE hxsy_prod.mall_orders ADD prepay_id varchar(100) NULL COMMENT '拉起微信支付返回的预支付id';
ALTER TABLE hxsy_prod.mall_orders CHANGE prepay_id prepay_id varchar(100) NULL COMMENT '拉起微信支付返回的预支付id' AFTER receiver_address;

-- 订单配送主表
CREATE TABLE `mall_order_delivery` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `delivery_id` varchar(32) NOT NULL COMMENT '配送单号（唯一，可与订单号不同，用于对接物流公司）',
                                  `order_id` bigint NOT NULL COMMENT '关联的业务订单ID',
                                  `logistics_company` varchar(50) NOT NULL COMMENT '物流公司编码（如SF, YTO, ZTO）或名称',
                                  `tracking_no` varchar(50) NOT NULL COMMENT '物流运单号',
                                  `delivery_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '配送状态：0-待发货 1-已发货 2-运送中 3-派送中 4-已签收 5-异常',
                                  `current_node` varchar(255) DEFAULT NULL COMMENT '当前最新的物流节点描述（如：已从广州发出）',
                                  `sender_info` json DEFAULT NULL COMMENT '发件人信息（JSON存储，避免连表）',
                                  `receiver_info` json NOT NULL COMMENT '收件人信息（JSON存储，包含姓名、电话、详细地址）',
                                  `estimated_arrival_time` datetime DEFAULT NULL COMMENT '预计到达时间',
                                  `signed_time` datetime DEFAULT NULL COMMENT '实际签收时间',
                                  remark varchar(255) DEFAULT NULL COMMENT '备注',
                                  status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
                                  created_by varchar(64) DEFAULT NULL COMMENT '创建人',
                                  created_at datetime DEFAULT NULL COMMENT '创建时间（行为时间）',
                                  updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
                                  updated_at datetime DEFAULT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_order_id` (`order_id`),
                                  KEY `idx_status` (`delivery_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单配送主表';

-- 订单配送物流轨迹明细表
CREATE TABLE `mall_order_delivery_detail` (
                                            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `delivery_id` varchar(32) NOT NULL COMMENT '关联配送单号',
                                            `tracking_time` datetime NOT NULL COMMENT '物流信息发生的时间',
                                            `tracking_description` varchar(500) NOT NULL COMMENT '物流节点描述（如：【广州市】快件已由XX驿站代收）',
                                            `tracking_location` varchar(100) DEFAULT NULL COMMENT '节点发生的地点',
                                            `tracking_status` tinyint(4) NOT NULL COMMENT '节点状态（可与主表状态枚举一致，用于更新主表）',
                                            `operator` varchar(50) DEFAULT NULL COMMENT '操作员或快递员',
                                            remark varchar(255) DEFAULT NULL COMMENT '备注',
                                            status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
                                            created_by varchar(64) DEFAULT NULL COMMENT '创建人',
                                            created_at datetime DEFAULT NULL COMMENT '创建时间（行为时间）',
                                            updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
                                            updated_at datetime DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_delivery_id` (`delivery_id`),
                                            KEY `idx_tracking_time` (`tracking_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单配送物流轨迹明细表';

-- 订单商品表
CREATE TABLE mall_order_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    spec VARCHAR(100) COMMENT '商品规格',
    price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    quantity INT NOT NULL COMMENT '购买数量',
    image VARCHAR(500) COMMENT '商品图片',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',    INDEX idx_order_id (order_id),
    INDEX idx_customer_id (customer_id)
);
-- 用户钱包表
CREATE TABLE mall_customer_wallets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT UNIQUE NOT NULL COMMENT '用户ID',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额',
    total_recharge DECIMAL(10,2) DEFAULT 0.00 COMMENT '累计充值',
    total_expense DECIMAL(10,2) DEFAULT 0.00 COMMENT '累计支出',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_customer_id (customer_id)
);

-- 钱包交易记录表
CREATE TABLE mall_wallet_transactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    title VARCHAR(100) NOT NULL COMMENT '交易标题',
    amount DECIMAL(10,2) NOT NULL COMMENT '交易金额',
    type VARCHAR(20) NOT NULL COMMENT '交易类型：income-收入，expense-支出',
    transaction_status VARCHAR(20) NOT NULL COMMENT '交易状态：success-成功，pending-处理中，failed-失败',
    status_text VARCHAR(20) NOT NULL COMMENT '状态文本',
    icon VARCHAR(50) COMMENT '图标名称',
    icon_color VARCHAR(20) COMMENT '图标颜色',
    related_order_id BIGINT COMMENT '关联订单ID',
    payment_method VARCHAR(20) COMMENT '支付方式',
    time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_customer_id (customer_id),
    INDEX idx_type (type),
    INDEX idx_transaction_status (transaction_status),
    INDEX idx_status (status),
    INDEX idx_status (status)
);

-- 用户收藏表
CREATE TABLE mall_customer_favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',    UNIQUE KEY uk_customer_product (customer_id, product_id),
    INDEX idx_customer_id (customer_id)
);
-- 用户统计表
CREATE TABLE mall_customer_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT UNIQUE NOT NULL COMMENT '用户ID',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    favorites_count INT DEFAULT 0 COMMENT '收藏数量',
    footprints_count INT DEFAULT 0 COMMENT '足迹数量',
    points INT DEFAULT 0 COMMENT '积分',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',    INDEX idx_customer_id (customer_id)
);
-- 推荐商品表
CREATE TABLE mall_recommend_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    image VARCHAR(500) COMMENT '商品图片',
    product_id BIGINT COMMENT '关联商品ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  );

-- 用户浏览足迹表
CREATE TABLE mall_customer_footprints (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称（冗余）',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格（冗余）',
    image VARCHAR(500) COMMENT '商品图片（冗余）',
    visit_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_customer_id (customer_id),
    INDEX idx_visit_time (visit_time)
);

-- 搜索历史表
CREATE TABLE mall_search_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    keyword VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    search_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_customer_id (customer_id),
    INDEX idx_search_time (search_time)
);
-- 优惠券表
CREATE TABLE mall_coupons (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
    type VARCHAR(20) NOT NULL COMMENT '优惠券类型：discount-折扣券，cash-现金券',
    discount_value DECIMAL(10,2) NOT NULL COMMENT '优惠值（折扣券为折扣比例，现金券为金额）',
    min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最低消费金额',
    max_discount DECIMAL(10,2) COMMENT '最大优惠金额（折扣券专用）',
    total_quantity INT NOT NULL COMMENT '发行总量',
    used_quantity INT DEFAULT 0 COMMENT '已使用数量',
    start_time datetime NOT NULL COMMENT '有效期开始时间',
    end_time datetime NOT NULL COMMENT '有效期结束时间',
    description TEXT COMMENT '使用说明',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);

-- 用户优惠券表
CREATE TABLE mall_customer_coupons (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    coupon_id BIGINT NOT NULL COMMENT '优惠券ID',
    coupon_name VARCHAR(100) NOT NULL COMMENT '优惠券名称（冗余）',
    coupon_type VARCHAR(20) NOT NULL COMMENT '优惠券类型（冗余）',
    discount_value DECIMAL(10,2) NOT NULL COMMENT '优惠值（冗余）',
    min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最低消费金额（冗余）',
    coupon_status VARCHAR(20) DEFAULT 'unused' COMMENT '优惠券使用状态：unused-未使用，used-已使用，expired-已过期',
    receive_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    use_time datetime NULL COMMENT '使用时间',
    expire_time datetime NOT NULL COMMENT '过期时间',
    order_id BIGINT COMMENT '使用的订单ID',
    remark varchar(255) DEFAULT NULL COMMENT '备注',
    status tinyint DEFAULT '1' COMMENT '使用状态（1-有效，0-无效）',
    created_by varchar(64) DEFAULT NULL COMMENT '创建人',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by varchar(64) DEFAULT NULL COMMENT '更新人',
    updated_at datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_customer_id (customer_id),
    INDEX idx_coupon_status (coupon_status),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time)
);

-- 店铺分类表
CREATE TABLE `mall_store_categories` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺分类表';

-- 店铺基本信息表
CREATE TABLE `mall_stores` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_name` varchar(200) NOT NULL COMMENT '店铺名称',
  `store_code` varchar(50) NOT NULL COMMENT '店铺编码',
  `category_id` bigint(20) DEFAULT NULL COMMENT '店铺分类ID',
  `owner_id` bigint(20) NOT NULL COMMENT '店主用户ID',
  `owner_name` varchar(100) NOT NULL COMMENT '店主姓名',
  `owner_phone` varchar(20) NOT NULL COMMENT '店主手机号',
  `owner_email` varchar(100) DEFAULT NULL COMMENT '店主邮箱',
  `business_license` varchar(100) DEFAULT NULL COMMENT '营业执照号',
  `license_image` varchar(500) DEFAULT NULL COMMENT '营业执照图片',
  `store_logo` varchar(500) DEFAULT NULL COMMENT '店铺Logo',
  `store_banner` varchar(500) DEFAULT NULL COMMENT '店铺横幅图',
  `store_images` text DEFAULT NULL COMMENT '店铺图片集合(JSON格式)',
  `description` text DEFAULT NULL COMMENT '店铺描述',
  `address` varchar(500) DEFAULT NULL COMMENT '店铺地址',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `business_hours` varchar(200) DEFAULT NULL COMMENT '营业时间',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `service_phone` varchar(20) DEFAULT NULL COMMENT '客服电话',
  `qq` varchar(20) DEFAULT NULL COMMENT 'QQ号',
  `wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `website` varchar(200) DEFAULT NULL COMMENT '官网地址',
  `store_type` tinyint(2) DEFAULT '1' COMMENT '店铺类型：1-实体店，2-网店，3-混合店',
  `business_scope` varchar(500) DEFAULT NULL COMMENT '经营范围',
  `tags` varchar(500) DEFAULT NULL COMMENT '店铺标签(JSON格式)',
  `rating` decimal(3,2) DEFAULT '5.00' COMMENT '店铺评分',
  `review_count` int(11) DEFAULT '0' COMMENT '评价数量',
  `follower_count` int(11) DEFAULT '0' COMMENT '关注数量',
  `product_count` int(11) DEFAULT '0' COMMENT '商品数量',
  `order_count` int(11) DEFAULT '0' COMMENT '订单数量',
  `sales_amount` decimal(15,2) DEFAULT '0.00' COMMENT '销售总额',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例(%)',
  `deposit_amount` decimal(10,2) DEFAULT '0.00' COMMENT '保证金金额',
  `audit_status` tinyint(2) DEFAULT '0' COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_by` varchar(64) DEFAULT NULL COMMENT '审核人',
  `open_time` datetime DEFAULT NULL COMMENT '开店时间',
  `close_time` datetime DEFAULT NULL COMMENT '关店时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-正常营业，2-暂停营业，3-永久关闭，0-禁用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_code` (`store_code`),
  KEY `idx_store_name` (`store_name`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_owner_phone` (`owner_phone`),
  KEY `idx_status` (`status`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_city` (`city`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺基本信息表';

-- 店铺员工表
CREATE TABLE `mall_store_staff` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `store_id` bigint(20) NOT NULL COMMENT '店铺ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `staff_name` varchar(100) NOT NULL COMMENT '员工姓名',
  `staff_phone` varchar(20) NOT NULL COMMENT '员工手机号',
  `staff_email` varchar(100) DEFAULT NULL COMMENT '员工邮箱',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `role_type` tinyint(2) DEFAULT '1' COMMENT '角色类型：1-店员，2-店长，3-管理员',
  `permissions` text DEFAULT NULL COMMENT '权限配置(JSON格式)',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `salary` decimal(10,2) DEFAULT NULL COMMENT '薪资',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '提成比例(%)',
  `work_status` tinyint(1) DEFAULT '1' COMMENT '工作状态：1-在职，2-离职，0-禁用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_store_user` (`store_id`, `user_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_staff_phone` (`staff_phone`),
  KEY `idx_role_type` (`role_type`),
  KEY `idx_work_status` (`work_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺员工表';

-- 添加外键约束
ALTER TABLE `mall_stores` ADD CONSTRAINT `fk_store_category` FOREIGN KEY (`category_id`) REFERENCES `mall_store_categories` (`id`) ON DELETE SET NULL;
ALTER TABLE `mall_store_staff` ADD CONSTRAINT `fk_staff_store` FOREIGN KEY (`store_id`) REFERENCES `mall_stores` (`id`) ON DELETE CASCADE;

-- 插入默认店铺分类数据
INSERT INTO `mall_store_categories` (`name`, `description`, `sort_order`, `status`) VALUES
('服装鞋帽', '服装、鞋子、帽子等时尚用品', 1, 1),
('数码电器', '手机、电脑、家电等数码产品', 2, 1),
('食品饮料', '食品、饮料、零食等', 3, 1),
('美妆护肤', '化妆品、护肤品、个人护理', 4, 1),
('家居用品', '家具、装饰、日用品等', 5, 1),
('运动户外', '运动器材、户外用品等', 6, 1),
('母婴用品', '婴儿用品、玩具、孕妇用品', 7, 1),
('图书文具', '图书、文具、办公用品', 8, 1),
('汽车用品', '汽车配件、用品等', 9, 1),
('其他', '其他类型商品', 99, 1);



CREATE TABLE `mall_banners` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '横幅ID',
  `title` varchar(100) NOT NULL COMMENT '横幅标题',
  `banner_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '横幅类型：1-首页轮播，2-消息通知，3-个人中心',

  -- 首页轮播横幅字段
  `image_url` varchar(500) DEFAULT NULL COMMENT '横幅图片URL（首页轮播、个人中心使用）',
  `product_id` bigint(20) DEFAULT NULL COMMENT '关联商品ID（首页轮播可选）',

  -- 消息通知横幅字段
  `message_text` varchar(200) DEFAULT NULL COMMENT '消息文字内容（消息通知使用）',
  `message_icon` varchar(100) DEFAULT NULL COMMENT '消息图标类型（消息通知使用）',
  `icon_color` varchar(20) DEFAULT NULL COMMENT '图标颜色（消息通知使用）',

  -- 个人中心横幅字段
  `content_text` varchar(200) DEFAULT NULL COMMENT '内容文字（个人中心使用）',
  `sub_text` varchar(200) DEFAULT NULL COMMENT '副标题文字（个人中心使用）',
  `background_color` varchar(20) DEFAULT NULL COMMENT '背景颜色（个人中心使用）',
  `small_image_url` varchar(500) DEFAULT NULL COMMENT '小图片URL（个人中心右侧小图）',

  -- 通用字段
  `link_url` varchar(500) DEFAULT NULL COMMENT '跳转链接（可选）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序，数字越小越靠前',
  `click_count` int(11) DEFAULT '0' COMMENT '点击次数',
  `is_active` tinyint(4) DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-有效，0-删除',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  KEY `idx_banner_type` (`banner_type`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='横幅管理表';

-- 插入示例数据
INSERT INTO `mall_banners` (`title`, `banner_type`, `image_url`, `product_id`, `sort_order`, `is_active`) VALUES
('Taylor Swift 1989专辑', 1, 'https://example.com/taylor-swift-1989.jpg', 123, 1, 1),
('春季新品上市', 1, 'https://example.com/spring-collection.jpg', NULL, 2, 1);

INSERT INTO `mall_banners` (`title`, `banner_type`, `message_text`, `message_icon`, `icon_color`, `sort_order`, `is_active`) VALUES
('全场包邮通知', 2, '🚚 全场包邮，新鲜直达您的餐桌！', 'truck', '#ff6b6b', 1, 1),
('限时优惠', 2, '⏰ 限时秒杀，错过再等一年！', 'clock', '#4ecdc4', 2, 1);

INSERT INTO `mall_banners` (`title`, `banner_type`, `content_text`, `sub_text`, `background_color`, `small_image_url`, `sort_order`, `is_active`) VALUES
('限时秒杀', 3, '限时秒杀', '精选商品1折起', '#4ecdc4', 'https://example.com/small-image.jpg', 1, 1),
('会员专享', 3, '会员专享', '更多特权等你来', '#ff9f43', 'https://example.com/vip-image.jpg', 2, 1);
