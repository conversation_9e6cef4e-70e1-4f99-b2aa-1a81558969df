{"_from": "@tencent/exeditor3-preset-editor", "_id": "@tencent/exeditor3-preset-editor@3.5.1", "_inBundle": false, "_integrity": "sha512-nUU1P7K5/9gHPKqteDO+WAlLH/Y+jniJdomDgJByFIgsw9SqkZ8pnV4I2MQ6bjaLlCa2PDvN/hb0Dg1N9H7DqA==", "_location": "/@tencent/exeditor3-preset-editor", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@tencent/exeditor3-preset-editor", "name": "@tencent/exeditor3-preset-editor", "escapedName": "@tencent%2fexeditor3-preset-editor", "scope": "@tencent", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://mirrors.tencent.com/npm/@tencent/exeditor3-preset-editor/-/@tencent/exeditor3-preset-editor-3.5.1.tgz", "_shasum": "d718614ffa9fa542ff40f54f9d18517428ab4554", "_spec": "@tencent/exeditor3-preset-editor", "_where": "/Users/<USER>/Documents/code/test/ex", "author": {"name": "elixiao"}, "bundleDependencies": false, "dependencies": {"@tencent/exeditor3-core": "^3.7.1", "@tencent/exeditor3-plugin-audio": "^2.5.3", "@tencent/exeditor3-plugin-background-color": "^3.3.3", "@tencent/exeditor3-plugin-bold": "^3.2.6", "@tencent/exeditor3-plugin-bullet-list": "^3.3.4", "@tencent/exeditor3-plugin-clear-format": "^3.2.6", "@tencent/exeditor3-plugin-code-block": "^3.4.3", "@tencent/exeditor3-plugin-color": "^3.3.3", "@tencent/exeditor3-plugin-font-family": "^1.5.3", "@tencent/exeditor3-plugin-font-size": "^2.3.3", "@tencent/exeditor3-plugin-fullscreen": "^3.4.3", "@tencent/exeditor3-plugin-heading": "^3.6.2", "@tencent/exeditor3-plugin-hr": "^3.3.3", "@tencent/exeditor3-plugin-italic": "^3.2.6", "@tencent/exeditor3-plugin-line-space": "^3.3.3", "@tencent/exeditor3-plugin-link": "^3.4.5", "@tencent/exeditor3-plugin-list-item": "^3.3.5", "@tencent/exeditor3-plugin-margin-block": "^3.3.3", "@tencent/exeditor3-plugin-margin-side": "^3.4.3", "@tencent/exeditor3-plugin-order-list": "^3.3.4", "@tencent/exeditor3-plugin-paint-format": "^3.3.5", "@tencent/exeditor3-plugin-paragraph-check": "^3.3.3", "@tencent/exeditor3-plugin-picture": "^3.6.1", "@tencent/exeditor3-plugin-placeholder": "^3.2.5", "@tencent/exeditor3-plugin-quote": "^3.3.3", "@tencent/exeditor3-plugin-strikethrough": "^3.4.2", "@tencent/exeditor3-plugin-sub-script": "^2.3.3", "@tencent/exeditor3-plugin-super-script": "^2.3.3", "@tencent/exeditor3-plugin-tables": "^3.6.2", "@tencent/exeditor3-plugin-text-align": "^3.4.3", "@tencent/exeditor3-plugin-text-indent": "^3.4.3", "@tencent/exeditor3-plugin-toolbar-common-items": "^3.3.4", "@tencent/exeditor3-plugin-underline": "^3.2.6", "@tencent/exeditor3-plugin-unsafe-html": "^3.1.3", "@tencent/exeditor3-plugin-video": "^3.5.3", "@tencent/exeditor3-plugin-word-space": "^3.3.3", "@tencent/exeditor3-theme-basic": "^3.2.1", "@tencent/exeditor3-ui-kit": "^3.4.2"}, "deprecated": false, "description": "exeditor preset editor", "devDependencies": {"typescript": "^4.4.2"}, "files": ["dist"], "gitHead": "4269883d27dbe1639fe1aa25915ce12acad9de27", "keywords": [], "main": "dist/index.js", "name": "@tencent/exeditor3-preset-editor", "scripts": {"clean": "rimraf dist *.tsbuildinfo", "start": "tsc --watch --preserveWatchOutput"}, "type": "module", "types": "dist/index.d.ts", "version": "3.5.1"}