import type { Plugin, EditorConfig, Toolbar } from '@tencent/exeditor3-core';
declare type fileTypes = 'picture' | 'video' | 'audio';
export interface PresetEditorConfig extends Omit<EditorConfig, 'plugins'> {
    /**
     * 单个字符串表示一行；字符串数组表示多行。字符串内以空格分隔各项
     * 如果某预设插件定义了工具栏项但这里不包含，对应预设插件便不会启用
     */
    toolbar?: Toolbar;
    /**
     * 占位文字，类似于 input、textarea 的 placeholder
     */
    placeholder?: string;
    /**
     * 预设插件之外的自定义插件，优先级默认高于所有预设插件。
     * 也可通过此字段覆盖预设插件的配置。
     */
    plugins?: (Plugin | null | undefined | false)[];
    /**
     * 对齐、行高等属性插件应用于哪些块。默认为段落和标题
     */
    textblockAttrsTarget?: string[];
    /**
     * 图片、视频、音频等使用行内模式
     */
    inlineMedia?: boolean;
    /**
     * 图片、视频、音频等是否不支持 Title 属性
     */
    noMediaTitle?: boolean;
    /**
     * 图片、视频、音频等的上传实现。返回在线地址
     */
    upload?: (file: Blob) => Promise<string>;
    /**
     * 对图片、视频、音频等的校验
     */
    acceptFile?: (from: fileTypes, file: File) => boolean | Promise<boolean>;
    /**
     * 图片、视频、音频等的 URL 特殊处理。返回在线地址
     */
    uploadBySrc?: (src: string) => Promise<string>;
    /**
     * 图片、视频、音频等的 URL 是否需要特殊处理
     */
    isNeedToUploadBySrc?: (src: string) => boolean;
    /**
     * 链接等插件复制成功回调
     */
    onCopy?: (isSuccess: boolean) => void;
}
export {};
