import type { Toolbar } from '@tencent/exeditor3-core';
import { BasicTheme } from '@tencent/exeditor3-theme-basic';
export declare const defaultConfig: {
    readonly initialContent: "";
    readonly placeholder: "请输入内容";
    readonly plugins: readonly [];
    readonly textblockAttrsTarget: readonly ["paragraph", "heading"];
    readonly theme: BasicTheme;
    readonly toolbar: {
        readonly static: "clearFormat paintFormat | heading headingNumbering fontFamily fontSize bold italic underline strikethrough superScript subScript color backgroundColor link quote bulletList orderList hr codeBlock picture video audio table textAlign textIndent fold(marginTop marginBottom marginSide lineSpace wordSpace paragraphCheck) | undo redo";
        readonly popover: "";
    };
    readonly upload: (file: Blob) => Promise<string>;
    readonly acceptFile: () => boolean;
};
export declare const mergeToolbarConfig: (toolbar: Toolbar) => string;
