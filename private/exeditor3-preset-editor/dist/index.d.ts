import type { Plugin, ChangeEvent } from '@tencent/exeditor3-core';
import { Editor } from '@tencent/exeditor3-core';
import { BasicTheme } from '@tencent/exeditor3-theme-basic';
import { ItalicPlugin } from '@tencent/exeditor3-plugin-italic';
import { BoldPlugin } from '@tencent/exeditor3-plugin-bold';
import { UnderlinePlugin } from '@tencent/exeditor3-plugin-underline';
import { StrikethroughPlugin } from '@tencent/exeditor3-plugin-strikethrough';
import { ColorPlugin } from '@tencent/exeditor3-plugin-color';
import { FontSizePlugin } from '@tencent/exeditor3-plugin-font-size';
import { FontFamilyPlugin } from '@tencent/exeditor3-plugin-font-family';
import { BackgroundColorPlugin } from '@tencent/exeditor3-plugin-background-color';
import { HeadingPlugin } from '@tencent/exeditor3-plugin-heading';
import { QuotePlugin } from '@tencent/exeditor3-plugin-quote';
import { ListItemPlugin } from '@tencent/exeditor3-plugin-list-item';
import { BulletListPlugin } from '@tencent/exeditor3-plugin-bullet-list';
import { OrderListPlugin } from '@tencent/exeditor3-plugin-order-list';
import { TextAlignPlugin } from '@tencent/exeditor3-plugin-text-align';
import { TextIndentPlugin } from '@tencent/exeditor3-plugin-text-indent';
import { MarginBlockPlugin } from '@tencent/exeditor3-plugin-margin-block';
import { MarginSidePlugin } from '@tencent/exeditor3-plugin-margin-side';
import { LineSpacePlugin } from '@tencent/exeditor3-plugin-line-space';
import { WordSpacePlugin } from '@tencent/exeditor3-plugin-word-space';
import { HrPlugin } from '@tencent/exeditor3-plugin-hr';
import { CodeBlockPlugin } from '@tencent/exeditor3-plugin-code-block';
import { VideoPlugin } from '@tencent/exeditor3-plugin-video';
import { AudioPlugin } from '@tencent/exeditor3-plugin-audio';
import { PicturePlugin } from '@tencent/exeditor3-plugin-picture';
import { ClearFormatPlugin } from '@tencent/exeditor3-plugin-clear-format';
import { PaintFormatPlugin } from '@tencent/exeditor3-plugin-paint-format';
import { ParagraphCheckPlugin } from '@tencent/exeditor3-plugin-paragraph-check';
import { PlaceholderPlugin } from '@tencent/exeditor3-plugin-placeholder';
import { TablesPlugin } from '@tencent/exeditor3-plugin-tables';
import { LinkPlugin } from '@tencent/exeditor3-plugin-link';
import { ToolbarCommonItemsPlugin } from '@tencent/exeditor3-plugin-toolbar-common-items';
import { SubScriptPlugin } from '@tencent/exeditor3-plugin-sub-script';
import { SuperScriptPlugin } from '@tencent/exeditor3-plugin-super-script';
import { FullscreenPlugin } from '@tencent/exeditor3-plugin-fullscreen';
import { UnsafeHtmlPlugin } from '@tencent/exeditor3-plugin-unsafe-html';
import { defaultConfig } from './config.js';
import type { PresetEditorConfig } from './types';
/**
 * 快速创建一个包含大部分官方插件的编辑器实例
 */
export declare function createEditor(conf: PresetEditorConfig): Editor;
export { defaultConfig, BasicTheme, ItalicPlugin, BoldPlugin, UnderlinePlugin, StrikethroughPlugin, ColorPlugin, FontSizePlugin, FontFamilyPlugin, BackgroundColorPlugin, HeadingPlugin, QuotePlugin, ListItemPlugin, BulletListPlugin, OrderListPlugin, TextAlignPlugin, TextIndentPlugin, MarginBlockPlugin, MarginSidePlugin, LineSpacePlugin, WordSpacePlugin, HrPlugin, CodeBlockPlugin, VideoPlugin, AudioPlugin, PicturePlugin, ClearFormatPlugin, PaintFormatPlugin, ParagraphCheckPlugin, PlaceholderPlugin, TablesPlugin, LinkPlugin, ToolbarCommonItemsPlugin, SubScriptPlugin, SuperScriptPlugin, FullscreenPlugin, UnsafeHtmlPlugin, };
export type { Editor, Plugin, PresetEditorConfig, ChangeEvent };
