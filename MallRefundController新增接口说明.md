# MallRefundController 新增接口说明

## 概述
为了支持订单详情页面展示退款信息的需求，在 `MallRefundController` 中新增了根据订单ID获取退款信息的接口。

## 新增接口详情

### 接口信息
- **接口路径**: `GET /api/v1/mall/refund/by-order/{orderId}`
- **接口描述**: 根据订单ID获取退款信息
- **接口用途**: 在订单详情页面中展示该订单的退款信息

### 请求参数
| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| orderId | String | Path | 是 | 订单ID |
| customerId | String | Query | 是 | 用户ID |

### 请求示例
```http
GET /api/v1/mall/refund/by-order/123456?customerId=789
```

### 响应数据
成功时返回 `RefundDetailResponse` 对象，包含完整的退款信息：

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "refundNo": "RF20250908123456789",
    "orderId": 123456,
    "orderNumber": "ORD20250908123456",
    "customerId": 789,
    "refundType": "FULL",
    "refundReason": "商品质量问题",
    "refundDescription": "商品有瑕疵，申请退款",
    "refundAmount": 99.00,
    "refundImages": ["http://example.com/image1.jpg"],
    "refundStatus": "PENDING",
    "statusText": "待审核",
    "auditResult": null,
    "auditRemark": null,
    "auditUserName": null,
    "auditTime": null,
    "wxRefundId": null,
    "wxTransactionId": null,
    "wxRefundAccount": null,
    "wxRefundChannel": null,
    "applyTime": "2025-09-08 12:34:56",
    "processTime": null,
    "completeTime": null,
    "wxSuccessTime": null,
    "progressList": []
  }
}
```

如果该订单没有退款记录，则返回：
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null
}
```

### 错误响应
```json
{
  "code": -1,
  "msg": "订单不存在或不属于当前用户",
  "data": null
}
```

## 实现逻辑

### 1. 控制器层 (MallRefundController)
- 接收请求参数并进行基本验证
- 调用服务层方法获取退款信息
- 处理异常并返回统一格式的响应

### 2. 服务层 (MallRefundServiceImpl)
- 验证订单是否存在且属于当前用户
- 查询该订单的最新退款记录
- 转换数据格式并处理退款凭证图片
- 返回完整的退款详情信息

### 3. 数据查询逻辑
```sql
SELECT * FROM mall_order_refunds 
WHERE order_id = ? 
  AND customer_id = ? 
  AND status = 1 
ORDER BY apply_time DESC 
LIMIT 1
```

## 安全性考虑

### 1. 权限验证
- 验证订单是否属于当前用户
- 确保用户只能查看自己的退款信息

### 2. 参数校验
- 订单ID和用户ID的格式验证
- 防止SQL注入和参数篡改

### 3. 异常处理
- 完善的异常捕获和日志记录
- 友好的错误信息返回

## 使用场景

### 1. 订单详情页面
- 在订单详情页面中展示退款状态
- 显示退款进度和相关信息
- 提供跳转到退款详情的入口

### 2. 前端调用示例
```typescript
// 在订单详情页面加载退款信息
async loadRefundInfo() {
  try {
    const response = await getRefundByOrderId(this.orderId, this.customerId);
    if (response.code === 0 && response.data) {
      this.refundInfo = response.data;
    } else {
      this.refundInfo = null; // 没有退款信息
    }
  } catch (error) {
    console.error("加载退款信息失败:", error);
    this.refundInfo = null;
  }
}
```

## 与现有接口的区别

### 1. 与 `/my-list` 接口的区别
- `/my-list`: 返回用户的所有退款记录列表，支持分页和条件查询
- `/by-order/{orderId}`: 返回指定订单的退款信息，直接返回单个记录

### 2. 与 `/detail/{refundId}` 接口的区别
- `/detail/{refundId}`: 根据退款ID获取退款详情
- `/by-order/{orderId}`: 根据订单ID获取退款详情

### 3. 使用场景对比
| 接口 | 使用场景 | 返回数据 |
|------|----------|----------|
| `/my-list` | 退款列表页面 | 分页的退款记录列表 |
| `/detail/{refundId}` | 退款详情页面 | 单个退款的完整信息 |
| `/by-order/{orderId}` | 订单详情页面 | 该订单的退款信息 |

## 注意事项

### 1. 数据一致性
- 如果一个订单有多条退款记录，返回最新的一条
- 确保返回的数据与订单状态保持一致

### 2. 性能考虑
- 查询使用了索引优化（order_id, customer_id）
- 限制返回结果为1条记录，避免不必要的数据传输

### 3. 扩展性
- 接口设计支持未来可能的功能扩展
- 响应格式与现有接口保持一致

## 测试建议

### 1. 正常场景测试
- 有退款记录的订单
- 没有退款记录的订单
- 不同退款状态的测试

### 2. 异常场景测试
- 订单不存在
- 订单不属于当前用户
- 参数格式错误

### 3. 性能测试
- 大量数据下的查询性能
- 并发访问测试
