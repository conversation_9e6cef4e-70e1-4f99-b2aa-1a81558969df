{"name": "hxsy-ui", "version": "0.2.0", "scripts": {"dev:mock": "vite --open --mode mock", "uat": "vite --open --mode hxsy-uat", "prod": "vite --open --mode hxsy-prod", "build-hxsy-prod": "vite build --mode hxsy-prod", "build-hxsy-uat": "vite build --mode hxsy-uat", "site:preview": "npm run build && cp -r dist _site", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --cache --fix src/**/*.{html,vue,vss,sass,less}", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install", "clean": "rm -rf dist/*.js dist/*.map"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-vue-shape": "^2.0.0", "axios": "^0.27.2", "china-area-data": "^5.0.1", "dayjs": "^1.10.8", "echarts": "~5.1.2", "gm-crypto": "^0.1.12", "json2typescript": "^1.5.1", "lodash-es": "^4.17.21", "moment": "^2.30.1", "monent": "0.0.2-security", "nprogress": "^0.2.0", "promise.prototype.finally": "^3.1.3", "qrcode.vue": "^1.7.0", "quill": "^2.0.2", "sass": "^1.83.0", "sm-crypto": "^0.3.13", "store": "^2.0.12", "tdesign-icons-vue": "^0.2.1", "tdesign-vue": "^1.4.5", "tvision-color": "~1.4.0", "typescript": "^4.7.4", "vod-js-sdk-v6": "^1.7.1-beta.1", "vue": "^2.6.14", "vue-axios": "^3.4.0", "vue-clipboard2": "^0.3.1", "vue-color": "^2.8.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.1", "vue-template-compiler": "~2.6.14", "vuex": "^3.6.2", "vuex-router-sync": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^16.2.4", "@commitlint/config-conventional": "^16.2.1", "@types/vue-color": "^2.4.3", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "@vue/composition-api": "^1.7.1", "commitizen": "^4.2.3", "crypto-js": "^4.2.0", "eslint": "^7.22.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-vue": "^7.8.0", "husky": "^7.0.4", "konva": "^8.4.2", "less": "^4.1.0", "lint-staged": "^12.1.2", "mockjs": "^1.1.0", "prettier": "^2.7.1", "vite": "^2.9.13", "vite-plugin-mock": "^2.3.0", "vite-plugin-theme": "^0.8.1", "vite-plugin-vue2": "^1.2.2", "vite-plugin-vue2-svg": "^0.1.8", "vue-class-component": "^7.2.6", "vue-konva": "^2.1.7", "vue-property-decorator": "^9.1.2"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "lint-staged", "prepare-commit-msg": "exec < /dev/tty && git cz --hook || true", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix", "git add ."], "*.{html,vue,vss,sass,less}": ["npm run stylelint:fix", "git add ."]}, "description": "Base on tdesign-starter-cli"}