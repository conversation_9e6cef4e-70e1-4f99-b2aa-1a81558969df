package cn.hxsy.base.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量设置客户企微备注请求参数
 *
 * <AUTHOR>
 * @date 2025/7/26
 */
@Data
@ApiModel(value = "BatchRemarkCustomerRequest", description = "批量设置客户企微备注请求参数")
public class BatchRemarkCustomerRequest {

    @ApiModelProperty(value = "客户ID集合", required = true)
    @NotEmpty(message = "客户ID集合不能为空")
    private List<Long> customerIds;

    @ApiModelProperty(value = "备注信息", required = true)
    @NotNull(message = "备注信息不能为空")
    private String remark;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "备注企业名称")
    private String remarkCompany;

    @ApiModelProperty(value = "备注手机号集合")
    private List<String> remarkMobiles;

    @ApiModelProperty(value = "企微企业ID", required = true)
    @NotNull(message = "企微企业ID不能为空")
    private String corpId;

    @ApiModelProperty(value = "企业userId", required = true)
    @NotNull(message = "企业userId不能为空")
    private String qyUserId;

    @ApiModelProperty(value = "销售用户ID", required = true)
    @NotNull(message = "销售用户ID不能为空")
    private Long salesId;

    @ApiModelProperty(value = "备注图片的mediaid")
    private String remarkPicMediaid;

    @ApiModelProperty(value = "客户备注信息列表（用于单独设置每个客户的备注）")
    @Valid
    private List<CustomerRemark> customerRemarks;

    /**
     * 客户备注信息
     */
    @Data
    @ApiModel(value = "CustomerRemark", description = "客户备注信息")
    public static class CustomerRemark {

        @ApiModelProperty(value = "客户ID", required = true)
        @NotNull(message = "客户ID不能为空")
        private Long customerId;

        @ApiModelProperty(value = "备注信息", required = true)
        @NotNull(message = "备注信息不能为空")
        private String remark;

        @ApiModelProperty(value = "备注手机号集合")
        private List<String> remarkMobiles;
    }
}
