package cn.hxsy.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户操作类型枚举
 * 用于标识需要限制频率的用户操作
 *
 * <AUTHOR>
 * @date 2025/8/10
 */
@Getter
@AllArgsConstructor
public enum UserOperationType {

    /**
     * 更新客户备注
     */
    UPDATE_CUSTOMER_REMARK("UPDATE_CUSTOMER_REMARK", "更新客户备注", "每天只能执行一次"),

    /**
     * 批量导出客户数据
     */
    EXPORT_CUSTOMER_DATA("EXPORT_CUSTOMER_DATA", "批量导出客户数据", "每天只能执行一次"),

    /**
     * 批量发送消息
     */
    BATCH_SEND_MESSAGE("BATCH_SEND_MESSAGE", "批量发送消息", "每天只能执行一次");

    /**
     * 操作代码
     */
    private final String code;

    /**
     * 操作名称
     */
    private final String name;

    /**
     * 限制描述
     */
    private final String limitDescription;

    /**
     * 根据代码获取枚举
     *
     * @param code 操作代码
     * @return 用户操作类型枚举
     */
    public static UserOperationType getByCode(String code) {
        for (UserOperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
