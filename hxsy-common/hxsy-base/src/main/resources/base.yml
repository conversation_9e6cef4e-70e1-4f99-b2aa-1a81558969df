# 这是项目的需要改动的所有配置的地址的汇总，需要改哪个中间件的地址，在本文件中修改即可！
cn:
  hxsy:
    nacos:
      server:
        username: nacos  # nacos.server.username - Nacos 的用户名 #首次启动前务必修改成你自己的
        password: nacos  # nacos.server.psaasword - Nacos 的密码 #首次启动前务必修改成你自己的
        url: 43.137.62.211:8848 # nacos.server.url - Nacos 的地址+端口号
        namespace: 09d6456c-e5a0-4d68-8120-bf646f2e3929  # nacos.namespace - Nacos 的命名空间 #首次启动前务必修改成你自己的
    mysql:
      url: *******************************************************************************************************************************
      username: hxsy_dev  #数据库用户名，root为管理员
      password: 1649280623Hqb #该数据库用户的密码
    redis:
      url: 43.137.62.211  # redis.url - Redis 的地址 #首次启动前务必修改成你自己的
      port: 6379  # redis.port - Redis 的端口号 #首次启动前务必修改成你自己的
      password: 'IeFbMfb0DbsMqByF' # redis.password - Redis 的密码 #首次启动前务必修改成你自己的
      timeout: 3000         # 命令执行超时时间（毫秒）
      connectionPoolSize: 64  # 连接池大小
      idleConnectionTimeout: 10000 # 空闲连接超时
    elasticsearch:
      enable: false # elasticsearch.enable #如果你要使用es(没有启动一键mock)，这里需要改成true
      url: 127.0.0.1:9200 # elasticsearch.url - ElasticSearch 的地址+端口号 ##如果你要使用es(没有启动一键mock)，这里需要改成true
      username: elastic # elasticsearch.username - ElasticSearch 的用户名 ##如果你要使用es(没有启动一键mock)，这里需要改成true
      password: 123456  # elasticsearch.password - ElasticSearch 的密码 ##如果你要使用es(没有启动一键mock)，这里需要改成true
    xxl-job:
      url: 127.0.0.1:23333  # xxl-job.url - XXL-JOB 的地址 # 如果你要用他，这里需要改成你自己的
      appName: xxl-job-executor  # xxl-job.appName - 需要和你在xxl-job上创建的执行器名字保持一致，这里需要改成你自己的
      accessToken: default_token # xxl-job.accessToken - 需要和你在xxl-job上创建的token保持一致，这里需要改成你自己的
    sentinel:
      url: 127.0.0.1:8099 # sentinel.url - Sentinel 的地址+端口号 # 如果用了sentinel 这里需要改成你自己的sentinel的控制台地址
      port: 8098 # sentinel.port - Sentinel 端口号 # 如果用了sentinel 这里需要改成你自己的sentinel的端口号
      nacos:
        data-id: nfturbo-gateway-sentinel # sentinel.nacos.data-id - Sentinel配置保存的 nacos 的 data-id # 如果用了sentinel 这里需要改成你自己的 在nacos上定义的data-id
    rocketmq:
      url: 127.0.0.1:9876 # rocketmq.url - RocketMQ 的地址+端口号 # 如果你要用RocketMQ，这里需要改成你自己的
    dubbo:
      nacos:
        namespace: bc83bf40-3304-4071-a0dc-3dfea4516216  # dubbo.nacos.namespace - 自己到nacos上创建一个给dubbo用的namespce，然后和这里保持一致，首次启动前务必修改成你自己的 #首次启动前务必修改成你自己的
        group: dubbo  # dubbo.nacos.group - 自己到nacos上创建一个给dubbo用的 group，然后和这里保持一致，首次启动前务必修改成你自己的
        username: nacos
        password: nacos
    seata:
      nacos:
        data-id: seataServer.properties # seata.nacos.data-id - Seata 对应的Nacos 保存配置的 data-id #首次启动前务必修改成你自己的
        group: seata # seata.nacos.group - Seata 对应的Nacos 保存配置的 data-id #首次启动前务必修改成你自己的
        namespace: 09d6456c-e5a0-4d68-8120-bf646f2e3929 # seata.nacos.namespace - Seata 对应的Nacos 保存配置的 namespace #首次启动前务必修改成你自己的
spring:
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  servlet:
    multipart:
      max-file-size: 15MB
      max-request-size: 15MB
swagger:
  enabled: false
logging:
  level:
    root: info
cos:
  secretId: AKID35vZ3LcldO3XptjPv47zf8QaZMzRhkaF
  secretKey: a0Ej9bx65uWUNGzUoz1s1jIE0dEdv39F
  bucketName: hxsy-dev-1351054319
  region: ap-guangzhou
vx:
  jump:
    env_version: develop #要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
    coursePath: pages/course/course #课程详情页
    regPath: pages/index/register #注册页
system:
  domain: https://dev.huaxiacomp.cn
