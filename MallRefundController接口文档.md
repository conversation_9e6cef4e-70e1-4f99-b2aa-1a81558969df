# MallRefundController 接口文档

## 基础信息

- **控制器路径**: `/api/v1/mall/refund`
- **认证方式**: Bearer <PERSON>ken (Header: Authorization)
- **响应格式**: 统一 Result 包装
- **时间格式**: `yyyy-MM-dd HH:mm:ss` (GMT+8)

## 接口列表

### 1. 申请退款

**接口地址**: `POST /api/v1/mall/refund/apply`

**接口描述**: 用户申请退款

**请求参数**: `ApplyRefundRequest`

| 字段名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| orderId | Long | 是 | 订单ID | 不能为空 |
| customerId | Long | 是 | 用户ID | 不能为空 |
| refundType | String | 是 | 退款类型 | 默认值: "FULL" (全额退款) |
| refundReason | String | 是 | 退款原因 | 不能为空，最大200字符 |
| refundDescription | String | 否 | 退款详细说明 | 最大500字符 |
| refundImages | List\<String\> | 否 | 退款凭证图片URL列表 | 最多5张图片 |

**响应数据**: `Result<String>`
- 成功时返回退款单号

---

### 2. 查询我的退款列表

**接口地址**: `POST /api/v1/mall/refund/my-list`

**接口描述**: 查询用户的退款申请列表

**请求参数**: `RefundQueryRequest`

| 字段名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|--------|
| customerId | Long | 是 | 用户ID | - |
| refundNo | String | 否 | 退款单号 | - |
| orderNumber | String | 否 | 订单号 | - |
| refundStatus | String | 否 | 退款状态 | - |
| startTime | LocalDateTime | 否 | 开始时间 | - |
| endTime | LocalDateTime | 否 | 结束时间 | - |
| pageNum | Integer | 否 | 页码 | 1 |
| pageSize | Integer | 否 | 每页大小 | 10 |

**响应数据**: `Result<IPage<RefundResponse>>`

**RefundResponse 字段说明**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 退款ID |
| refundNo | String | 退款单号 |
| orderId | Long | 订单ID |
| orderNumber | String | 订单号 |
| customerId | Long | 用户ID |
| refundType | String | 退款类型 |
| refundReason | String | 退款原因 |
| refundDescription | String | 退款说明 |
| refundAmount | BigDecimal | 退款金额 |
| refundImages | List\<String\> | 退款凭证图片列表 |
| refundStatus | String | 退款状态 |
| statusText | String | 状态文本 |
| auditResult | String | 审核结果 |
| auditRemark | String | 审核备注 |
| auditUserName | String | 审核人姓名 |
| auditTime | LocalDateTime | 审核时间 |
| applyTime | LocalDateTime | 申请时间 |
| processTime | LocalDateTime | 处理时间 |
| completeTime | LocalDateTime | 完成时间 |
| wxSuccessTime | LocalDateTime | 微信退款成功时间 |

---

### 3. 获取退款详情

**接口地址**: `GET /api/v1/mall/refund/detail/{refundId}`

**接口描述**: 获取退款申请的详细信息

**路径参数**:
- `refundId`: 退款ID (String)

**查询参数**:
- `customerId`: 用户ID (String, 必填)

**响应数据**: `Result<RefundDetailResponse>`

**RefundDetailResponse 字段说明**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 退款ID |
| refundNo | String | 退款单号 |
| orderId | Long | 订单ID |
| orderNumber | String | 订单号 |
| customerId | Long | 用户ID |
| refundType | String | 退款类型 |
| refundReason | String | 退款原因 |
| refundDescription | String | 退款详细说明 |
| refundAmount | BigDecimal | 退款金额 |
| refundImages | List\<String\> | 退款凭证图片列表 |
| refundStatus | String | 退款状态 |
| statusText | String | 状态文本 |
| auditResult | String | 审核结果 |
| auditRemark | String | 审核备注 |
| auditUserName | String | 审核人姓名 |
| auditTime | LocalDateTime | 审核时间 |
| wxRefundId | String | 微信退款单号 |
| wxTransactionId | String | 微信支付订单号 |
| wxRefundAccount | String | 退款入账账户 |
| wxRefundChannel | String | 退款渠道 |
| applyTime | LocalDateTime | 申请时间 |
| processTime | LocalDateTime | 处理时间 |
| completeTime | LocalDateTime | 完成时间 |
| wxSuccessTime | LocalDateTime | 微信退款成功时间 |
| progressList | List\<RefundProgressResponse\> | 退款进度记录列表 |

**RefundProgressResponse 字段说明**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 进度ID |
| refundId | Long | 退款ID |
| progressStatus | String | 进度状态 |
| statusText | String | 状态文本 |
| description | String | 进度描述 |
| operateTime | LocalDateTime | 操作时间 |
| operator | String | 操作人 |

---

### 4. 商家审核退款

**接口地址**: `POST /api/v1/mall/refund/audit`

**接口描述**: 商家审核退款申请

**请求参数**: `AuditRefundRequest`

| 字段名 | 类型 | 必填 | 描述 | 验证规则 |
|--------|------|------|------|----------|
| refundId | Long | 是 | 退款ID | 不能为空 |
| auditResult | String | 是 | 审核结果 | APPROVED-同意退款，REJECTED-拒绝退款 |
| auditRemark | String | 否 | 审核备注 | 最大500字符 |
| auditUserId | Long | 否 | 审核人ID | - |
| auditUserName | String | 否 | 审核人姓名 | - |

**响应数据**: `Result<Boolean>`
- 成功时返回 true

---

### 5. 查询商家待处理退款列表

**接口地址**: `GET /api/v1/mall/refund/pending-list`

**接口描述**: 查询商家待处理的退款申请列表

**查询参数**:

| 字段名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|--------|
| companyId | Long | 否 | 公司ID | - |
| pageNum | Integer | 否 | 页码 | 1 |
| pageSize | Integer | 否 | 页大小 | 10 |

**响应数据**: `Result<IPage<RefundResponse>>`
- 返回待处理的退款列表，字段同第2个接口

---

### 6. 检查订单退款资格

**接口地址**: `GET /api/v1/mall/refund/check-eligibility`

**接口描述**: 检查订单是否可以申请退款

**查询参数**:
- `orderId`: 订单ID (String, 必填)
- `customerId`: 用户ID (String, 必填)

**响应数据**: `Result<RefundEligibilityResponse>`

**RefundEligibilityResponse 字段说明**:

| 字段名 | 类型 | 描述 |
|--------|------|------|
| eligible | Boolean | 是否可以申请退款 |
| reason | String | 拒绝原因（当eligible为false时返回） |
| requiresManualReview | Boolean | 是否需要人工审核（当eligible为true时返回） |
| refundType | String | 退款类型（当eligible为true时返回） |

---

### 7. 取消退款申请

**接口地址**: `POST /api/v1/mall/refund/cancel`

**接口描述**: 取消退款申请（仅待审核状态可取消）

**请求参数**: JSON对象

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| refundId | String | 是 | 退款ID |
| customerId | String | 是 | 用户ID |

**响应数据**: `Result<Boolean>`
- 成功时返回 true

---

### 8. 微信退款结果通知回调

**接口地址**: `POST /api/v1/mall/refund/wx-callback`

**接口描述**: 微信退款结果通知回调接口

**请求参数**: 微信回调数据（原始请求体）

**响应数据**: `Result<Object>`
- 成功时返回 "SUCCESS"
- 失败时返回 "FAIL"

---

### 9. 查询退款状态

**接口地址**: `GET /api/v1/mall/refund/status/{refundId}`

**接口描述**: 查询退款申请的当前状态

**路径参数**:
- `refundId`: 退款ID (Long)

**响应数据**: `Result<String>`
- 返回退款状态字符串

---

### 10. 根据订单ID获取退款信息

**接口地址**: `GET /api/v1/mall/refund/by-order/{orderId}`

**接口描述**: 根据订单ID获取对应的退款信息

**路径参数**:
- `orderId`: 订单ID (String)

**查询参数**:
- `customerId`: 用户ID (String, 必填)

**响应数据**: `Result<RefundDetailResponse>`
- 返回退款详情，字段同第3个接口

## 状态枚举说明

### 退款状态 (refundStatus)

| 状态值 | 描述 | 说明 |
|--------|------|------|
| PENDING | 待审核 | 用户提交退款申请，等待商家审核 |
| APPROVED | 已同意 | 商家同意退款，等待处理 |
| REJECTED | 已拒绝 | 商家拒绝退款申请 |
| PROCESSING | 退款处理中 | 正在进行退款操作 |
| SUCCESS | 退款成功 | 退款已成功完成 |
| FAILED | 退款失败 | 退款处理失败 |

### 审核结果 (auditResult)

| 状态值 | 描述 |
|--------|------|
| APPROVED | 同意退款 |
| REJECTED | 拒绝退款 |

### 退款类型 (refundType)

| 类型值 | 描述 |
|--------|------|
| FULL | 全额退款 |

## 错误码说明

所有接口遵循统一的错误响应格式：

```json
{
  "code": 1,
  "msg": "错误信息",
  "data": null
}
```

常见错误码：
- `0`: 成功
- `1`: 业务异常
- `500`: 系统异常

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的 Authorization Token
2. **参数验证**: 请求参数会进行严格的验证，不符合规则的请求会返回错误
3. **权限控制**: 用户只能操作自己的退款申请，商家只能处理自己店铺的退款
4. **状态流转**: 退款状态有严格的流转规则，不能随意跳转
5. **时间格式**: 所有时间字段统一使用 `yyyy-MM-dd HH:mm:ss` 格式，时区为 GMT+8
6. **金额精度**: 金额字段使用 BigDecimal 类型，保证精度
7. **图片处理**: 退款凭证图片需要先上传到文件服务器，然后传递图片URL
8. **分页查询**: 列表接口支持分页，默认每页10条记录
