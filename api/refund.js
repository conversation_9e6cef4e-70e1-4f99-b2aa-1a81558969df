/**
 * 退款相关API接口
 * 对接后端退款服务接口
 * <AUTHOR>
 * @date 2025-09-07
 */

import request from '@/utils/request'

const refundApi = {
  /**
   * 申请退款
   * @param {Object} data 退款申请数据
   * @param {number} data.orderId 订单ID
   * @param {number} data.customerId 用户ID
   * @param {string} data.refundType 退款类型：FULL-全额退款
   * @param {string} data.refundReason 退款原因
   * @param {string} data.refundDescription 退款详细说明
   * @param {Array<string>} data.refundImages 退款凭证图片URL列表（最多5张）
   */
  applyRefund(data) {
    return request({
      url: '/api/v1/mall/refund/apply',
      method: 'POST',
      data
    })
  },

  /**
   * 查询我的退款列表
   * @param {Object} params 查询参数
   * @param {number} params.customerId 用户ID
   * @param {string} params.refundStatus 退款状态（可选）
   * @param {string} params.refundNo 退款单号（可选）
   * @param {string} params.orderNumber 订单号（可选）
   * @param {number} params.pageNum 页码，默认1
   * @param {number} params.pageSize 每页大小，默认10
   */
  getMyRefundList(data) {
    return request({
      url: '/api/v1/mall/refund/my-list',
      method: 'POST',
      data
    })
  },

  /**
   * 获取退款详情
   * @param {string} refundId 退款ID
   * @param {string} customerId 用户ID
   */
  getRefundDetail(refundId, customerId) {
    return request({
      url: `/api/v1/mall/refund/detail/${refundId}`,
      method: 'GET',
      params: { customerId }
    })
  }
}

export default refundApi