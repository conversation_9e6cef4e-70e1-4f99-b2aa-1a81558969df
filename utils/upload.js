/**
 * 文件上传工具
 * 复用现有的文件上传接口
 * <AUTHOR>
 * @date 2025-09-07
 */

/**
 * 上传单个文件
 * @param {string} filePath 本地文件路径
 * @param {string} fileType 文件类型，默认为'image'
 * @returns {Promise<string>} 返回上传后的文件URL
 */
export function uploadFile(filePath, fileType = 'image') {
  return new Promise((resolve, reject) => {
    // 获取用户token（假设从缓存获取）
    const token = uni.getStorageSync('token') || ''
    
    uni.uploadFile({
      url: '/api/v1/common/upload', // 假设这是现有的通用上传接口
      filePath: filePath,
      name: 'file',
      formData: {
        type: fileType
      },
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            resolve(data.data.url || data.data)
          } else {
            reject(new Error(data.message || '上传失败'))
          }
        } catch (error) {
          reject(new Error('上传响应解析失败'))
        }
      },
      fail: (error) => {
        console.error('上传文件失败:', error)
        reject(new Error('上传失败'))
      }
    })
  })
}

/**
 * 批量上传文件
 * @param {Array<string>} filePaths 本地文件路径数组
 * @param {string} fileType 文件类型，默认为'image'
 * @returns {Promise<Array<string>>} 返回上传后的文件URL数组
 */
export function uploadFiles(filePaths, fileType = 'image') {
  const uploadPromises = filePaths.map(filePath => uploadFile(filePath, fileType))
  return Promise.all(uploadPromises)
}

/**
 * 选择并上传图片
 * @param {Object} options 选择选项
 * @param {number} options.count 最多选择图片数量，默认1
 * @param {Array<string>} options.sizeType 图片质量，默认['compressed']
 * @param {Array<string>} options.sourceType 图片来源，默认['camera', 'album']
 * @returns {Promise<Array<string>>} 返回上传后的图片URL数组
 */
export function chooseAndUploadImages(options = {}) {
  const {
    count = 1,
    sizeType = ['compressed'],
    sourceType = ['camera', 'album']
  } = options
  
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count,
      sizeType,
      sourceType,
      success: async (res) => {
        try {
          uni.showLoading({ title: '上传中...' })
          const imageUrls = await uploadFiles(res.tempFilePaths, 'image')
          uni.hideLoading()
          resolve(imageUrls)
        } catch (error) {
          uni.hideLoading()
          reject(error)
        }
      },
      fail: (error) => {
        reject(new Error('选择图片失败'))
      }
    })
  })
}