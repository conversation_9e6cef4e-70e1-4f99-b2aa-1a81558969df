# 商城订单退款功能完整设计方案（基于现有架构优化）

## 1. 现有架构分析

### 1.1 现有订单状态枚举
基于现有的 `OrderStateType` 枚举，需要扩展退款相关状态：

```java
public enum OrderStateType {
    pending("pending", "待付款"),
    paid("paid", "已付款"),
    shipped("shipped", "待收货"),
    completed("completed", "已完成"),
    cancelled("cancelled", "已取消"),
    refund("refund", "退款中"),
    
    // 扩展退款状态
    refund_processing("refund_processing", "退款处理中"),
    refund_success("refund_success", "退款成功"),
    refund_failed("refund_failed", "退款失败");
}
```

### 1.2 现有微信支付集成
项目已集成微信支付V3 API，具备以下能力：
- `WxCustomerPayUtils.createOrder()` - 统一下单
- `WxCustomerPayUtils.queryOrderByTradeNo()` - 订单查询
- `WxCustomerPayUtils.wxPayRefund()` - 退款申请
- `WxCustomerPayUtils.handlePayCallback()` - 支付回调处理

### 1.3 现有数据库字段
`MallOrder` 实体已包含必要字段：
- `orderNumber` - 订单号
- `customerId` - 用户ID
- `paymentMethod` - 支付方式
- `totalAmount` - 订单总金额
- `prepayId` - 微信预支付ID
- `paymentExpireTime` - 支付过期时间
- `isExpired` - 是否过期

## 2. 功能需求

### 1.1 功能描述
- 支持用户对已支付订单申请退款
- **仅支持微信支付订单的退款，原路退回到用户支付账户**
- 支持全额退款（暂不开放部分退款）
- 退款申请需要上传图片凭证（最多5张）
- 商家审核机制
- 自动审核：未发货订单自动审核通过
- 人工审核：已发货或已完成订单需要人工审核
- **不支持退款到钱包余额或其他账户**

### 1.2 适用订单状态
- `paid`：已付款（自动审核通过）
- `shipped`：已发货（需人工审核）
- `completed`：已完成（需人工审核）

### 1.3 退款流程
```
用户申请退款 → 系统验证 → 自动/人工审核 → 微信原路退款 → 退款完成
```

### 1.4 退款方式限制
- **仅支持微信支付原路退款**：退款金额将原路退回到用户的微信支付账户
- **不支持钱包余额退款**：系统不提供退款到平台钱包余额的功能
- **不支持其他支付方式**：非微信支付的订单不支持退款申请

## 2. 数据库设计

### 2.1 订单退款主表 `mall_order_refunds`

```sql
CREATE TABLE mall_order_refunds (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    refund_no VARCHAR(32) UNIQUE NOT NULL COMMENT '退款单号',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_number VARCHAR(32) NOT NULL COMMENT '订单号（冗余）',
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 退款基本信息
    refund_type VARCHAR(20) NOT NULL DEFAULT 'FULL' COMMENT '退款类型：FULL-全额退款，PARTIAL-部分退款',
    refund_reason VARCHAR(200) NOT NULL COMMENT '退款原因',
    refund_description VARCHAR(500) COMMENT '退款详细说明',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '申请退款金额',
    refund_images JSON COMMENT '退款凭证图片（JSON数组，最多5张）',
    
    -- 退款状态管理
    refund_status VARCHAR(20) NOT NULL COMMENT '退款状态：PENDING-待审核，APPROVED-已同意，REJECTED-已拒绝，PROCESSING-退款处理中，SUCCESS-退款成功，FAILED-退款失败',
    status_text VARCHAR(20) NOT NULL COMMENT '状态文本',
    
    -- 审核信息
    audit_result VARCHAR(20) COMMENT '审核结果：APPROVED-同意退款，REJECTED-拒绝退款',
    audit_remark VARCHAR(500) COMMENT '审核备注',
    audit_user_id BIGINT COMMENT '审核人ID',
    audit_user_name VARCHAR(50) COMMENT '审核人姓名',
    audit_time DATETIME COMMENT '审核时间',
    
    -- 微信退款信息
    wx_refund_id VARCHAR(64) COMMENT '微信退款单号',
    wx_transaction_id VARCHAR(32) COMMENT '微信支付订单号',
    wx_out_refund_no VARCHAR(64) COMMENT '商户退款单号',
    wx_refund_account VARCHAR(50) COMMENT '退款入账账户',
    wx_success_time DATETIME COMMENT '退款成功时间',
    wx_refund_channel VARCHAR(20) DEFAULT 'ORIGINAL' COMMENT '退款渠道：ORIGINAL-原路退回（微信支付）',
    
    -- 时间字段
    apply_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    process_time DATETIME COMMENT '处理时间',
    complete_time DATETIME COMMENT '完成时间',
    
    -- 系统字段
    remark VARCHAR(500) COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_order_id (order_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_refund_status (refund_status),
    INDEX idx_refund_no (refund_no),
    INDEX idx_apply_time (apply_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单退款申请表';
```

### 2.2 退款进度记录表 `mall_refund_progress`

```sql
CREATE TABLE mall_refund_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    refund_id BIGINT NOT NULL COMMENT '退款ID',
    progress_status VARCHAR(20) NOT NULL COMMENT '进度状态',
    status_text VARCHAR(50) NOT NULL COMMENT '状态文本',
    description VARCHAR(500) NOT NULL COMMENT '进度描述',
    operate_time DATETIME NOT NULL COMMENT '操作时间',
    operator VARCHAR(50) COMMENT '操作人',
    
    -- 系统字段
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    
    INDEX idx_refund_id (refund_id),
    INDEX idx_operate_time (operate_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款进度记录表';
```

## 3. 核心业务逻辑

### 3.1 退款状态流转

```
PENDING(待审核) → APPROVED(已同意) → PROCESSING(退款处理中) → SUCCESS(退款成功)
                                    ↘
                REJECTED(已拒绝)        ↘ FAILED(退款失败)
```

### 3.2 订单状态更新规则

- 申请退款时：`paid/shipped/completed` → `refund_processing`
- 审核通过时：保持 `refund_processing`
- 审核拒绝时：恢复原状态
- 退款成功时：`refund_processing` → `refund_success`  
- 退款失败时：`refund_processing` → `refund_failed`

### 3.3 自动审核规则

```java
// 未发货订单自动审核通过
if ("paid".equals(orderStatus)) {
    // 自动审核通过，直接进入微信退款流程
    refund.setRefundStatus("APPROVED");
    refund.setAuditResult("APPROVED");
    refund.setAuditTime(LocalDateTime.now());
    refund.setAuditUserName("系统自动审核");
    
    // 调用微信退款
    processWxRefund(refund);
}
// 已发货/已完成订单需要人工审核
else if ("shipped".equals(orderStatus) || "completed".equals(orderStatus)) {
    refund.setRefundStatus("PENDING");
    refund.setStatusText("待商家审核");
}
```

## 4. API接口设计

### 4.1 用户端接口

```java
@RestController
@RequestMapping("/api/v1/mall/refund")
public class MallRefundController {

    // 1. 申请退款
    @PostMapping("/apply")
    public Result<String> applyRefund(@RequestBody ApplyRefundRequest request)

    // 2. 查询我的退款列表
    @PostMapping("/my-list")
    public Result<IPage<RefundResponse>> getMyRefundList(@RequestBody RefundQueryRequest request)

    // 3. 获取退款详情
    @GetMapping("/detail/{refundId}")
    public Result<RefundDetailResponse> getRefundDetail(@PathVariable Long refundId, @RequestParam Long customerId)

    // 4. 取消退款申请（仅待审核状态）
    @PostMapping("/cancel/{refundId}")
    public Result<Boolean> cancelRefund(@PathVariable Long refundId, @RequestParam Long customerId)

    // 5. 上传退款凭证图片
    @PostMapping("/upload-images")
    public Result<List<String>> uploadRefundImages(@RequestParam("files") MultipartFile[] files)
}
```

### 4.2 商家端接口

```java
@RestController
@RequestMapping("/api/v1/admin/refund")
public class AdminRefundController {

    // 1. 查询退款申请列表
    @PostMapping("/list")
    public Result<IPage<RefundResponse>> getRefundList(@RequestBody RefundQueryRequest request)

    // 2. 审核退款申请
    @PostMapping("/audit")
    public Result<Boolean> auditRefund(@RequestBody AuditRefundRequest request)

    // 3. 获取待处理退款数量
    @GetMapping("/pending-count")
    public Result<Long> getPendingRefundCount()
}
```

### 4.3 系统回调接口

```java
@RestController
@RequestMapping("/api/v1/system/refund")
public class RefundCallbackController {

    // 微信退款结果通知
    @PostMapping("/wx-callback")
    public Result<String> handleWxRefundCallback(HttpServletRequest request)
}
```

## 5. 关键功能实现

### 5.1 图片上传功能

```java
@Service
public class RefundImageService {
    
    @Autowired
    private FileUploadUtils fileUploadUtils;
    
    public List<String> uploadRefundImages(MultipartFile[] files) {
        // 验证文件数量
        if (files.length > 5) {
            throw new RuntimeException("退款凭证图片最多5张");
        }
        
        List<String> imageUrls = new ArrayList<>();
        for (MultipartFile file : files) {
            String imageUrl = fileUploadUtils.uploadRefundImage(file);
            imageUrls.add(imageUrl);
        }
        
        return imageUrls;
    }
}
```

### 5.2 退款申请核心逻辑

```java
@Service
@Transactional
public class RefundService {
    
    public String applyRefund(ApplyRefundRequest request) {
        // 1. 验证订单状态和支付方式
        MallOrder order = validateOrderForRefund(request.getOrderId(), request.getCustomerId());
        
        // 2. 验证订单支付方式（必须是微信支付）
        if (!"wechat".equals(order.getPaymentMethod()) && !"微信支付".equals(order.getPaymentMethod())) {
            throw new RuntimeException("仅支持微信支付订单的退款申请");
        }
        
        // 3. 检查是否已有退款申请
        checkExistingRefund(request.getOrderId());
        
        // 4. 验证退款金额（全额退款）
        if (request.getRefundAmount() != null && 
            !request.getRefundAmount().equals(order.getTotalAmount())) {
            throw new RuntimeException("当前仅支持全额退款");
        }
        
        // 5. 创建退款记录
        MallOrderRefund refund = createRefundRecord(request, order);
        refund.setRefundAmount(order.getTotalAmount()); // 强制设置为订单总金额
        refund.setWxRefundChannel("ORIGINAL"); // 固定为原路退回
        
        // 6. 判断是否自动审核
        if ("paid".equals(order.getOrderStatus())) {
            // 自动审核通过
            autoApproveRefund(refund, order);
        } else {
            // 等待人工审核
            refund.setRefundStatus("PENDING");
            refund.setStatusText("待商家审核");
        }
        
        // 7. 更新订单状态为退款处理中
        updateOrderStatus(order.getId(), "refund_processing", "退款处理中");
        
        // 8. 记录退款进度
        addRefundProgress(refund.getId(), "APPLY", "用户申请退款", 
            String.format("用户提交退款申请，退款金额：%.2f元，退款方式：微信原路退回", 
                refund.getRefundAmount()));
        
        return refund.getRefundNo();
    }
    
    /**
     * 验证订单是否符合退款条件
     */
    private MallOrder validateOrderForRefund(Long orderId, Long customerId) {
        MallOrder order = mallOrderService.getById(orderId);
        
        if (order == null || !order.getCustomerId().equals(customerId)) {
            throw new RuntimeException("订单不存在或无权限");
        }
        
        // 验证订单状态
        if (!Arrays.asList("paid", "shipped", "completed").contains(order.getOrderStatus())) {
            throw new RuntimeException("当前订单状态不支持退款申请");
        }
        
        // 验证支付方式（必须是微信支付）
        if (!"wechat".equals(order.getPaymentMethod()) && !"微信支付".equals(order.getPaymentMethod())) {
            throw new RuntimeException("仅支持微信支付订单的退款申请");
        }
        
        return order;
    }
}
```

### 5.3 微信原路退款处理

```java
@Service
public class WxRefundService {
    
    @Autowired
    private WxCustomerPayUtils wxCustomerPayUtils;
    
    /**
     * 处理微信原路退款
     */
    public void processWxOriginalRefund(MallOrderRefund refund) {
        try {
            // 1. 构建微信退款参数（固定为原路退回）
            WxPayRefundRequest wxRefundRequest = buildWxRefundRequest(refund);
            
            // 2. 设置退款方式为原路退回
            wxRefundRequest.setFundsAccount(ReqFundsAccount.AVAILABLE); // 可用余额
            
            // 3. 调用微信退款API
            WxPayRefundResponse response = wxCustomerPayUtils.wxPayRefund(wxRefundRequest);
            
            // 4. 更新退款记录
            updateRefundWithWxInfo(refund, response);
            refund.setWxRefundChannel("ORIGINAL"); // 标记为原路退回
            
            // 5. 记录进度
            addRefundProgress(refund.getId(), "PROCESSING", "已提交微信退款", 
                String.format("已向微信发起原路退款申请，微信退款单号：%s，退款金额：%.2f元", 
                    response.getRefundId(), refund.getRefundAmount()));
            
        } catch (Exception e) {
            // 退款失败处理
            handleRefundFailure(refund, "微信退款申请失败：" + e.getMessage());
            log.error("微信退款申请失败，退款ID：{}，订单号：{}，错误信息：{}", 
                refund.getId(), refund.getOrderNumber(), e.getMessage(), e);
        }
    }
    
    /**
     * 构建微信退款请求参数
     */
    private WxPayRefundRequest buildWxRefundRequest(MallOrderRefund refund) {
        WxPayRefundRequest request = new WxPayRefundRequest();
        
        // 基本信息
        request.setOutTradeNo(refund.getOrderNumber()); // 商户订单号
        request.setOutRefundNo(refund.getRefundNo()); // 商户退款单号
        request.setReason(refund.getRefundReason()); // 退款原因
        
        // 退款金额信息
        AmountReq amountReq = new AmountReq();
        amountReq.setTotal(refund.getRefundAmount().multiply(BigDecimal.valueOf(100)).longValue()); // 订单总金额（分）
        amountReq.setRefund(refund.getRefundAmount().multiply(BigDecimal.valueOf(100)).longValue()); // 退款金额（分）
        amountReq.setCurrency("CNY");
        
        // 设置退款来源为可用余额（确保原路退回）
        List<FundsFromItem> fromList = new ArrayList<>();
        FundsFromItem fundsFromItem = new FundsFromItem();
        fundsFromItem.setAccount(Account.AVAILABLE);
        fundsFromItem.setAmount(refund.getRefundAmount().multiply(BigDecimal.valueOf(100)).longValue());
        fromList.add(fundsFromItem);
        amountReq.setFrom(fromList);
        
        request.setAmount(amountReq);
        
        return request;
    }
    
    /**
     * 处理退款失败
     */
    private void handleRefundFailure(MallOrderRefund refund, String errorMsg) {
        refund.setRefundStatus("FAILED");
        refund.setStatusText("退款失败");
        refund.setRemark(errorMsg);
        
        // 恢复订单状态为原状态
        restoreOrderStatus(refund.getOrderId());
        
        // 记录失败进度
        addRefundProgress(refund.getId(), "FAILED", "退款失败", errorMsg);
    }
}
```

## 6. 配置说明

### 6.1 文件上传配置

```yaml
# application.yml
file:
  upload:
    path: /data/uploads  # 文件存储路径
    baseUrl: http://localhost:8080  # 访问基础URL
    maxFileSize: 5MB  # 单个文件最大大小
    maxRequestSize: 25MB  # 请求最大大小（5张图片）
```

### 6.2 微信支付退款配置

```yaml
wx:
  pay:
    customer:
      domain: https://dev.huaxiacomp.cn  # 回调域名
      notify: /gateway/hxsy-store/api/v1/system/refund/wx-callback  # 退款回调地址
    refund:
      # 退款方式配置
      channel: ORIGINAL  # 固定为原路退回
      timeout: 30000  # 退款请求超时时间（毫秒）
      retryCount: 3  # 退款失败重试次数
      retryInterval: 5000  # 重试间隔（毫秒）
```

## 7. 注意事项

1. **退款方式限制**：仅支持微信支付原路退款，不支持退款到钱包余额
2. **订单支付方式验证**：申请退款时必须验证订单支付方式为微信支付
3. **图片存储**：退款图片按日期分层存储，格式：`/uploads/refund/yyyy/MM/dd/`
4. **并发控制**：使用分布式锁避免重复退款申请
5. **状态同步**：微信退款结果通过回调或定时查询同步
6. **数据一致性**：退款操作使用数据库事务确保原子性
7. **安全验证**：微信回调需要验签，确保数据安全性
8. **错误处理**：完善的异常处理和用户提示机制
9. **退款金额限制**：退款金额不能超过订单实际支付金额
10. **重复申请防护**：同一订单只能有一个有效的退款申请

## 8. 业务逻辑验证与错误处理

### 8.1 退款申请验证规则

```java
@Component
public class RefundValidator {
    
    /**
     * 验证退款申请的所有业务规则
     */
    public void validateRefundApplication(ApplyRefundRequest request, MallOrder order) {
        // 1. 订单归属验证
        if (!order.getCustomerId().equals(request.getCustomerId())) {
            throw new RefundException("无权限对此订单申请退款");
        }
        
        // 2. 订单状态验证
        if (!Arrays.asList("paid", "shipped", "completed").contains(order.getOrderStatus())) {
            throw new RefundException("当前订单状态不支持退款申请");
        }
        
        // 3. 支付方式验证（仅支持微信支付）
        if (!"wechat".equals(order.getPaymentMethod()) && !"微信支付".equals(order.getPaymentMethod())) {
            throw new RefundException("仅支持微信支付订单的退款申请");
        }
        
        // 4. 退款类型验证（仅支持全额退款）
        if (!"FULL".equals(request.getRefundType())) {
            throw new RefundException("当前仅支持全额退款");
        }
        
        // 5. 退款金额验证
        if (request.getRefundAmount() != null && 
            !request.getRefundAmount().equals(order.getTotalAmount())) {
            throw new RefundException("退款金额必须等于订单总金额");
        }
        
        // 6. 图片数量验证
        if (request.getRefundImages() != null && request.getRefundImages().size() > 5) {
            throw new RefundException("退款凭证图片最多5张");
        }
        
        // 7. 重复申请验证
        if (hasActiveRefundApplication(order.getId())) {
            throw new RefundException("该订单已有进行中的退款申请");
        }
        
        // 8. 订单时间验证（可选：限制退款申请时间窗口）
        if (isRefundTimeExpired(order)) {
            throw new RefundException("超出退款申请时限");
        }
    }
    
    /**
     * 检查是否有活跃的退款申请
     */
    private boolean hasActiveRefundApplication(Long orderId) {
        List<String> activeStatuses = Arrays.asList("PENDING", "APPROVED", "PROCESSING");
        return refundMapper.countByOrderIdAndStatusIn(orderId, activeStatuses) > 0;
    }
    
    /**
     * 检查退款时限（例如：完成订单后30天内可申请退款）
     */
    private boolean isRefundTimeExpired(MallOrder order) {
        if ("completed".equals(order.getOrderStatus()) && order.getCompletedAt() != null) {
            return order.getCompletedAt().isBefore(LocalDateTime.now().minusDays(30));
        }
        return false;
    }
}
```

### 8.2 错误处理机制

```java
/**
 * 退款异常类
 */
public class RefundException extends RuntimeException {
    private String errorCode;
    private String errorMessage;
    
    public RefundException(String message) {
        super(message);
        this.errorMessage = message;
    }
    
    public RefundException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
}

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class RefundExceptionHandler {
    
    @ExceptionHandler(RefundException.class)
    public Result<String> handleRefundException(RefundException e) {
        log.error("退款业务异常：{}", e.getMessage());
        return Result.error(e.getMessage());
    }
    
    @ExceptionHandler(WxPayException.class)
    public Result<String> handleWxPayException(WxPayException e) {
        log.error("微信支付退款异常：{}", e.getMessage(), e);
        return Result.error("退款处理失败，请稍后重试");
    }
}
```

### 8.3 状态流转控制

```java
@Component
public class RefundStatusController {
    
    /**
     * 验证状态流转是否合法
     */
    public boolean isValidStatusTransition(String fromStatus, String toStatus) {
        Map<String, List<String>> validTransitions = Map.of(
            "PENDING", Arrays.asList("APPROVED", "REJECTED"),
            "APPROVED", Arrays.asList("PROCESSING", "FAILED"),
            "PROCESSING", Arrays.asList("SUCCESS", "FAILED"),
            "REJECTED", Collections.emptyList(),
            "SUCCESS", Collections.emptyList(),
            "FAILED", Arrays.asList("PROCESSING") // 允许重新处理失败的退款
        );
        
        return validTransitions.getOrDefault(fromStatus, Collections.emptyList()).contains(toStatus);
    }
    
    /**
     * 更新退款状态（带验证）
     */
    @Transactional
    public void updateRefundStatus(Long refundId, String newStatus, String remark) {
        MallOrderRefund refund = refundMapper.selectById(refundId);
        if (refund == null) {
            throw new RefundException("退款记录不存在");
        }
        
        if (!isValidStatusTransition(refund.getRefundStatus(), newStatus)) {
            throw new RefundException("非法的状态流转");
        }
        
        refund.setRefundStatus(newStatus);
        refund.setStatusText(getStatusText(newStatus));
        if (StringUtils.isNotBlank(remark)) {
            refund.setRemark(remark);
        }
        
        refundMapper.updateById(refund);
        
        // 记录状态变更进度
        addRefundProgress(refundId, newStatus, getStatusText(newStatus), remark);
    }
}
```

## 9. 开发清单

### 9.1 数据层开发
- [ ] 创建退款相关数据库表
- [ ] 实现MallOrderRefund实体类
- [ ] 实现MallRefundProgress实体类  
- [ ] 实现RefundMapper接口
- [ ] 编写XML映射文件

### 9.2 业务层开发
- [ ] 实现RefundService核心业务逻辑
- [ ] 实现WxRefundService微信退款处理
- [ ] 实现RefundValidator业务验证
- [ ] 实现RefundStatusController状态管理
- [ ] 实现FileUploadUtils文件上传工具类

### 9.3 控制层开发
- [ ] 实现MallRefundController用户端接口
- [ ] 实现AdminRefundController商家端接口
- [ ] 实现RefundCallbackController系统回调接口
- [ ] 创建请求响应对象
- [ ] 异常处理器实现

### 9.4 微信支付集成
- [ ] 完善WxCustomerPayUtils退款方法
- [ ] 实现退款回调处理逻辑
- [ ] 实现退款状态查询机制
- [ ] 配置微信退款参数

### 9.5 测试与部署
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 接口文档编写
- [ ] 配置文件更新
- [ ] 部署脚本准备