import request from '@/utils/request';
import mallDashboardApi from '@/constants/api/back/mall-dashboard.api';
import type {
  OverviewResponse,
  OrderTrendResponse,
  OrderStatusDistributionResponse,
  RealtimeResponse,
  TodayComparisonResponse,
  SalesStatsResponse,
  OrderTrendParams,
  SalesStatsParams
} from '@/types/mall-dashboard.types';

/**
 * @description: 商城驾驶舱服务类
 * <AUTHOR>
 * @date 2025/1/22
 */
class MallDashboardService {
  /**
   * 获取首页概览数据
   */
  async getOverview(): Promise<OverviewResponse> {
    try {
      const response = await request({
        url: mallDashboardApi.getOverview.url,
        method: mallDashboardApi.getOverview.method,
      });
      return response;
    } catch (error) {
      console.error('获取概览数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单趋势分析数据
   * @param params 查询参数
   */
  async getOrderTrend(params?: OrderTrendParams): Promise<OrderTrendResponse> {
    try {
      const response = await request({
        url: mallDashboardApi.getOrderTrend.url,
        method: mallDashboardApi.getOrderTrend.method,
        params: params || { period: 30 }, // 默认30天
      });
      return response;
    } catch (error) {
      console.error('获取订单趋势数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单状态分布数据
   */
  async getOrderStatusDistribution(): Promise<OrderStatusDistributionResponse> {
    try {
      const response = await request({
        url: mallDashboardApi.getOrderStatusDistribution.url,
        method: mallDashboardApi.getOrderStatusDistribution.method,
      });
      return response;
    } catch (error) {
      console.error('获取订单状态分布数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取实时数据
   */
  async getRealtime(): Promise<RealtimeResponse> {
    try {
      const response = await request({
        url: mallDashboardApi.getRealtime.url,
        method: mallDashboardApi.getRealtime.method,
      });
      return response;
    } catch (error) {
      console.error('获取实时数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取今日数据对比
   */
  async getTodayComparison(): Promise<TodayComparisonResponse> {
    try {
      const response = await request({
        url: mallDashboardApi.getTodayComparison.url,
        method: mallDashboardApi.getTodayComparison.method,
      });
      return response;
    } catch (error) {
      console.error('获取今日数据对比失败:', error);
      throw error;
    }
  }

  /**
   * 获取销售数据统计
   * @param params 查询参数
   */
  async getSalesStats(params?: SalesStatsParams): Promise<SalesStatsResponse> {
    try {
      const response = await request({
        url: mallDashboardApi.getSalesStats.url,
        method: mallDashboardApi.getSalesStats.method,
        params: params || { period: 'today' }, // 默认今日
      });
      return response;
    } catch (error) {
      console.error('获取销售数据统计失败:', error);
      throw error;
    }
  }

  /**
   * 批量获取所有驾驶舱数据
   */
  async getAllDashboardData() {
    try {
      const [
        overviewData,
        orderTrendData,
        statusDistributionData
      ] = await Promise.all([
        this.getOverview(),
        this.getOrderTrend(),
        this.getOrderStatusDistribution()
      ]);

      return {
        overview: overviewData.data,
        orderTrend: orderTrendData.data,
        statusDistribution: statusDistributionData.data
      };
    } catch (error) {
      console.error('批量获取驾驶舱数据失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export default new MallDashboardService();
