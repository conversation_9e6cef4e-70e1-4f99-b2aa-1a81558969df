<!-- 退款管理主页面 -->
<template>
  <div class="refund-manage-container">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-title">
        <h2>退款管理</h2>
        <span class="header-desc">管理和审核用户退款申请</span>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-download" @click="handleExport">
          导出数据
        </el-button>
        <el-button type="success" icon="el-icon-refresh" @click="handleRefresh">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingCount || 0 }}</div>
              <div class="stat-label">待审核</div>
            </div>
            <i class="el-icon-time stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card processing">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.processingCount || 0 }}</div>
              <div class="stat-label">处理中</div>
            </div>
            <i class="el-icon-loading stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.successCount || 0 }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <i class="el-icon-check stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card amount">
            <div class="stat-content">
              <div class="stat-number">{{ refundManager.formatAmount(statistics.totalAmount) }}</div>
              <div class="stat-label">退款总额</div>
            </div>
            <i class="el-icon-money stat-icon"></i>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card">
      <div class="search-form">
        <el-form :model="searchForm" :inline="true" size="small">
          <el-form-item label="退款单号">
            <el-input
              v-model="searchForm.refundNo"
              placeholder="请输入退款单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input
              v-model="searchForm.orderNumber"
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="退款状态">
            <el-select v-model="searchForm.refundStatus" placeholder="全部状态" clearable style="width: 150px">
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="申请时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 350px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh-left" @click="handleResetSearch">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">退款列表</div>
        <div class="table-actions">
          <el-button
            type="primary"
            size="small"
            :disabled="!multipleSelection.length"
            @click="handleBatchAudit('APPROVED')"
          >
            批量同意
          </el-button>
          <el-button
            type="danger"
            size="small"
            :disabled="!multipleSelection.length"
            @click="handleBatchAudit('REJECTED')"
          >
            批量拒绝
          </el-button>
        </div>
      </div>

      <el-table
        ref="refundTable"
        v-loading="tableLoading"
        :data="tableData"
        border
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="refundNo" label="退款单号" width="180" fixed="left">
          <template slot-scope="scope">
            <el-button type="text" @click="handleViewDetail(scope.row)">
              {{ scope.row.refundNo }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column prop="refundAmount" label="退款金额" width="120" sortable="custom">
          <template slot-scope="scope">
            <span class="amount-text">{{ refundManager.formatAmount(scope.row.refundAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="refundStatus" label="退款状态" width="120">
          <template slot-scope="scope">
            <el-tag
              :type="refundManager.getStatusConfig(scope.row.refundStatus).type"
              size="small"
            >
              {{ refundManager.getStatusConfig(scope.row.refundStatus).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="refundReason" label="退款原因" width="150" show-overflow-tooltip />
        <el-table-column prop="applyTime" label="申请时间" width="180" sortable="custom">
          <template slot-scope="scope">
            {{ refundManager.formatTime(scope.row.applyTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="auditUserName" label="审核人" width="100" />
        <el-table-column prop="auditTime" label="审核时间" width="180">
          <template slot-scope="scope">
            {{ refundManager.formatTime(scope.row.auditTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-view"
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="refundManager.canAudit(scope.row.refundStatus)"
              type="text"
              size="small"
              icon="el-icon-check"
              @click="handleAudit(scope.row)"
            >
              审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 退款详情对话框 -->
    <refund-detail-dialog
      :visible.sync="detailDialogVisible"
      :refund-id="selectedRefundId"
      @audit-success="handleAuditSuccess"
    />
  </div>
</template>

<script>
import { refundManager } from '@/utils/refundManager'
import RefundDetailDialog from './components/RefundDetailDialog.vue'

export default {
  name: 'RefundManage',
  components: {
    RefundDetailDialog
  },
  data() {
    return {
      refundManager,
      // 搜索表单
      searchForm: {
        refundNo: '',
        orderNumber: '',
        refundStatus: '',
        dateRange: []
      },
      // 状态选项
      statusOptions: [
        { value: 'PENDING', label: '待审核' },
        { value: 'APPROVED', label: '已同意' },
        { value: 'REJECTED', label: '已拒绝' },
        { value: 'PROCESSING', label: '退款处理中' },
        { value: 'SUCCESS', label: '退款成功' },
        { value: 'FAILED', label: '退款失败' }
      ],
      // 表格数据
      tableData: [],
      tableLoading: false,
      multipleSelection: [],
      // 分页
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      },
      // 排序
      sortField: '',
      sortOrder: '',
      // 统计数据
      statistics: {},
      // 详情对话框
      detailDialogVisible: false,
      selectedRefundId: null
    }
  },
  created() {
    this.loadData()
    this.loadStatistics()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.tableLoading = true
      
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }

        // 处理时间范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.startTime = this.searchForm.dateRange[0]
          params.endTime = this.searchForm.dateRange[1]
        }
        delete params.dateRange

        // 处理排序
        if (this.sortField) {
          params.sortField = this.sortField
          params.sortOrder = this.sortOrder
        }

        const result = await refundManager.getRefundList(params)
        
        if (result.success) {
          this.tableData = result.data.records || []
          this.pagination.total = result.data.total || 0
        } else {
          this.$message.error(result.message || '加载数据失败')
        }
      } catch (error) {
        console.error('加载退款数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.tableLoading = false
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const result = await refundManager.getStatistics()
        if (result.success) {
          this.statistics = result.data
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 重置搜索
    handleResetSearch() {
      this.searchForm = {
        refundNo: '',
        orderNumber: '',
        refundStatus: '',
        dateRange: []
      }
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 刷新
    handleRefresh() {
      this.loadData()
      this.loadStatistics()
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.loadData()
    },

    // 排序改变
    handleSortChange({ column, prop, order }) {
      this.sortField = prop
      this.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.loadData()
    },

    // 选择改变
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 查看详情
    handleViewDetail(row) {
      this.selectedRefundId = row.id
      this.detailDialogVisible = true
    },

    // 审核
    async handleAudit(row) {
      try {
        await refundManager.showAuditDialog(row, (result) => {
          if (result.success) {
            this.loadData()
            this.loadStatistics()
          }
        })
      } catch (error) {
        // 用户取消审核
      }
    },

    // 批量审核
    async handleBatchAudit(auditResult) {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要审核的退款申请')
        return
      }

      const action = auditResult === 'APPROVED' ? '同意' : '拒绝'
      
      try {
        await this.$confirm(`确定要批量${action}选中的 ${this.multipleSelection.length} 个退款申请吗？`, '批量审核', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const refundIds = this.multipleSelection.map(item => item.id)
        const auditRemark = `批量${action}退款`
        
        // 获取当前用户信息
        const auditor = {
          id: 1, // 从用户信息获取
          name: '管理员' // 从用户信息获取
        }

        const result = await refundManager.batchAuditRefunds(refundIds, auditResult, auditRemark, auditor)
        
        if (result.success) {
          this.loadData()
          this.loadStatistics()
          // 清空选择
          this.$refs.refundTable.clearSelection()
        }
      } catch (error) {
        // 用户取消操作
      }
    },

    // 审核成功回调
    handleAuditSuccess() {
      this.loadData()
      this.loadStatistics()
    },

    // 导出数据
    async handleExport() {
      try {
        const params = { ...this.searchForm }
        
        // 处理时间范围
        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
          params.startTime = this.searchForm.dateRange[0]
          params.endTime = this.searchForm.dateRange[1]
        }
        delete params.dateRange

        await refundManager.exportData(params)
      } catch (error) {
        console.error('导出数据失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-manage-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .header-title {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
      font-size: 24px;
    }
    
    .header-desc {
      color: #909399;
      font-size: 14px;
    }
  }
}

.statistics-cards {
  margin-bottom: 20px;
  
  .stat-card {
    position: relative;
    overflow: hidden;
    
    ::v-deep .el-card__body {
      padding: 20px;
    }
    
    .stat-content {
      .stat-number {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
    
    .stat-icon {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40px;
      opacity: 0.3;
    }
    
    &.pending {
      .stat-number { color: #e6a23c; }
      .stat-icon { color: #e6a23c; }
    }
    
    &.processing {
      .stat-number { color: #409eff; }
      .stat-icon { color: #409eff; }
    }
    
    &.success {
      .stat-number { color: #67c23a; }
      .stat-icon { color: #67c23a; }
    }
    
    &.amount {
      .stat-number { color: #f56c6c; }
      .stat-icon { color: #f56c6c; }
    }
  }
}

.search-card {
  margin-bottom: 20px;
  
  ::v-deep .el-card__body {
    padding-bottom: 0;
  }
}

.table-card {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .table-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .amount-text {
    color: #f56c6c;
    font-weight: bold;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>