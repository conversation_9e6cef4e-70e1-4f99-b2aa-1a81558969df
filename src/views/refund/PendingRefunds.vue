<!-- 待处理退款列表页面 -->
<template>
  <div class="pending-refunds-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-title">
        <h2>待处理退款</h2>
        <span class="header-desc">需要审核的退款申请</span>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="handleRefresh">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="quick-stats">
      <el-alert
        :title="`当前有 ${pagination.total} 个退款申请待处理`"
        type="warning"
        :closable="false"
        show-icon
        class="stats-alert"
      />
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">待处理列表</div>
        <div class="table-actions">
          <el-button
            type="primary"
            size="small"
            :disabled="!multipleSelection.length"
            @click="handleBatchAudit('APPROVED')"
          >
            批量同意
          </el-button>
          <el-button
            type="danger"
            size="small"
            :disabled="!multipleSelection.length"
            @click="handleBatchAudit('REJECTED')"
          >
            批量拒绝
          </el-button>
        </div>
      </div>

      <el-table
        ref="pendingTable"
        v-loading="tableLoading"
        :data="tableData"
        border
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="refundNo" label="退款单号" width="180" fixed="left">
          <template slot-scope="scope">
            <el-button type="text" @click="handleViewDetail(scope.row)">
              {{ scope.row.refundNo }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column prop="refundAmount" label="退款金额" width="120" sortable="custom">
          <template slot-scope="scope">
            <span class="amount-text">{{ refundManager.formatAmount(scope.row.refundAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="refundReason" label="退款原因" width="150" show-overflow-tooltip />
        <el-table-column prop="applyTime" label="申请时间" width="180" sortable="custom">
          <template slot-scope="scope">
            <span>{{ refundManager.formatTime(scope.row.applyTime) }}</span>
            <el-tag
              v-if="getUrgencyLevel(scope.row.applyTime) === 'urgent'"
              type="danger"
              size="mini"
              class="urgency-tag"
            >
              紧急
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="customerInfo" label="客户信息" width="150">
          <template slot-scope="scope">
            <div class="customer-info">
              <div class="customer-name">{{ scope.row.customerName || '用户' + scope.row.customerId }}</div>
              <div class="customer-id">ID: {{ scope.row.customerId }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="等待时长" width="120">
          <template slot-scope="scope">
            <span class="waiting-time" :class="getWaitingTimeClass(scope.row.applyTime)">
              {{ getWaitingTime(scope.row.applyTime) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-view"
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              type="text"
              size="small"
              icon="el-icon-check"
              style="color: #67c23a"
              @click="handleQuickAudit(scope.row, 'APPROVED')"
            >
              同意
            </el-button>
            <el-button
              type="text"
              size="small"
              icon="el-icon-close"
              style="color: #f56c6c"
              @click="handleQuickAudit(scope.row, 'REJECTED')"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50]"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 退款详情对话框 -->
    <refund-detail-dialog
      :visible.sync="detailDialogVisible"
      :refund-id="selectedRefundId"
      @audit-success="handleAuditSuccess"
    />

    <!-- 快速审核对话框 -->
    <el-dialog
      title="快速审核"
      :visible.sync="quickAuditDialogVisible"
      width="500px"
    >
      <div class="quick-audit-content">
        <div class="audit-info">
          <p><strong>退款单号：</strong>{{ currentRefund.refundNo }}</p>
          <p><strong>退款金额：</strong>{{ refundManager.formatAmount(currentRefund.refundAmount) }}</p>
          <p><strong>退款原因：</strong>{{ currentRefund.refundReason }}</p>
        </div>
        <el-form :model="quickAuditForm" :rules="quickAuditRules" ref="quickAuditForm">
          <el-form-item label="审核结果">
            <el-radio-group v-model="quickAuditForm.auditResult">
              <el-radio label="APPROVED">
                <span style="color: #67c23a">同意退款</span>
              </el-radio>
              <el-radio label="REJECTED">
                <span style="color: #f56c6c">拒绝退款</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="auditRemark">
            <el-input
              v-model="quickAuditForm.auditRemark"
              type="textarea"
              :rows="3"
              placeholder="请输入审核意见..."
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="quickAuditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitQuickAudit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { refundManager } from '@/utils/refundManager'
import RefundDetailDialog from './components/RefundDetailDialog.vue'

export default {
  name: 'PendingRefunds',
  components: {
    RefundDetailDialog
  },
  data() {
    return {
      refundManager,
      // 表格数据
      tableData: [],
      tableLoading: false,
      multipleSelection: [],
      // 分页
      pagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0
      },
      // 排序
      sortField: '',
      sortOrder: '',
      // 详情对话框
      detailDialogVisible: false,
      selectedRefundId: null,
      // 快速审核对话框
      quickAuditDialogVisible: false,
      currentRefund: {},
      quickAuditForm: {
        auditResult: 'APPROVED',
        auditRemark: ''
      },
      quickAuditRules: {
        auditRemark: [
          { required: true, message: '请输入审核意见', trigger: 'blur' },
          { min: 2, max: 500, message: '审核意见长度在 2 到 500 个字符', trigger: 'blur' }
        ]
      },
      // 自动刷新定时器
      refreshTimer: null
    }
  },
  created() {
    this.loadData()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.tableLoading = true
      
      try {
        const params = {
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        }

        // 处理排序
        if (this.sortField) {
          params.sortField = this.sortField
          params.sortOrder = this.sortOrder
        }

        const result = await refundManager.getPendingRefunds(params)
        
        if (result.success) {
          this.tableData = result.data.records || []
          this.pagination.total = result.data.total || 0
        } else {
          this.$message.error(result.message || '加载数据失败')
        }
      } catch (error) {
        console.error('加载待处理退款数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.tableLoading = false
      }
    },

    // 刷新
    handleRefresh() {
      this.loadData()
    },

    // 自动刷新
    startAutoRefresh() {
      // 每30秒自动刷新一次
      this.refreshTimer = setInterval(() => {
        this.loadData()
      }, 30000)
    },

    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNum = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.pageNum = val
      this.loadData()
    },

    // 排序改变
    handleSortChange({ column, prop, order }) {
      this.sortField = prop
      this.sortOrder = order === 'ascending' ? 'asc' : 'desc'
      this.loadData()
    },

    // 选择改变
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 查看详情
    handleViewDetail(row) {
      this.selectedRefundId = row.id
      this.detailDialogVisible = true
    },

    // 快速审核
    handleQuickAudit(row, auditResult) {
      this.currentRefund = row
      this.quickAuditForm.auditResult = auditResult
      this.quickAuditForm.auditRemark = auditResult === 'APPROVED' ? '同意退款' : '拒绝退款'
      this.quickAuditDialogVisible = true
    },

    // 提交快速审核
    async submitQuickAudit() {
      try {
        await this.$refs.quickAuditForm.validate()

        const auditData = {
          refundId: this.currentRefund.id,
          auditResult: this.quickAuditForm.auditResult,
          auditRemark: this.quickAuditForm.auditRemark,
          auditUserId: 1, // 从用户信息获取
          auditUserName: '管理员' // 从用户信息获取
        }

        const result = await refundManager.auditRefund(auditData)
        
        if (result.success) {
          this.quickAuditDialogVisible = false
          this.loadData()
          
          // 重置表单
          if (this.$refs.quickAuditForm) {
            this.$refs.quickAuditForm.resetFields()
          }
        }
      } catch (error) {
        // 表单验证失败或审核失败
      }
    },

    // 批量审核
    async handleBatchAudit(auditResult) {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要审核的退款申请')
        return
      }

      const action = auditResult === 'APPROVED' ? '同意' : '拒绝'
      
      try {
        await this.$confirm(`确定要批量${action}选中的 ${this.multipleSelection.length} 个退款申请吗？`, '批量审核', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const refundIds = this.multipleSelection.map(item => item.id)
        const auditRemark = `批量${action}退款`
        
        // 获取当前用户信息
        const auditor = {
          id: 1, // 从用户信息获取
          name: '管理员' // 从用户信息获取
        }

        const result = await refundManager.batchAuditRefunds(refundIds, auditResult, auditRemark, auditor)
        
        if (result.success || result.successCount > 0) {
          this.loadData()
          // 清空选择
          this.$refs.pendingTable.clearSelection()
        }
      } catch (error) {
        // 用户取消操作
      }
    },

    // 审核成功回调
    handleAuditSuccess() {
      this.loadData()
    },

    // 获取紧急程度
    getUrgencyLevel(applyTime) {
      const now = new Date()
      const apply = new Date(applyTime)
      const hours = (now - apply) / (1000 * 60 * 60)
      
      if (hours > 24) return 'urgent'
      if (hours > 12) return 'warning'
      return 'normal'
    },

    // 获取等待时长
    getWaitingTime(applyTime) {
      const now = new Date()
      const apply = new Date(applyTime)
      const diff = now - apply
      
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      
      if (hours > 0) {
        return `${hours}小时${minutes}分钟`
      } else {
        return `${minutes}分钟`
      }
    },

    // 获取等待时长样式类
    getWaitingTimeClass(applyTime) {
      const level = this.getUrgencyLevel(applyTime)
      return {
        'waiting-urgent': level === 'urgent',
        'waiting-warning': level === 'warning',
        'waiting-normal': level === 'normal'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pending-refunds-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .header-title {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
      font-size: 24px;
    }
    
    .header-desc {
      color: #909399;
      font-size: 14px;
    }
  }
}

.quick-stats {
  margin-bottom: 20px;
  
  .stats-alert {
    ::v-deep .el-alert__content {
      font-size: 16px;
    }
  }
}

.table-card {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .table-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .amount-text {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .customer-info {
    .customer-name {
      font-weight: bold;
      color: #303133;
    }
    
    .customer-id {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .urgency-tag {
    margin-left: 5px;
  }
  
  .waiting-time {
    font-weight: bold;
    
    &.waiting-urgent {
      color: #f56c6c;
    }
    
    &.waiting-warning {
      color: #e6a23c;
    }
    
    &.waiting-normal {
      color: #67c23a;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.quick-audit-content {
  .audit-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border-left: 4px solid #409eff;
    
    p {
      margin: 5px 0;
      color: #606266;
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}
</style>