<!-- 退款统计页面 -->
<template>
  <div class="refund-statistics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-title">
        <h2>退款统计</h2>
        <span class="header-desc">退款数据统计分析</span>
      </div>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleDateChange"
        />
        <el-button type="primary" icon="el-icon-refresh" @click="handleRefresh">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 概览统计卡片 -->
    <div class="overview-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">总退款申请</div>
              <div class="stat-trend">
                <span class="trend-label">较昨日</span>
                <span class="trend-value" :class="getTrendClass(statistics.totalCountTrend)">
                  {{ formatTrend(statistics.totalCountTrend) }}
                </span>
              </div>
            </div>
            <i class="el-icon-document stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.successCount || 0 }}</div>
              <div class="stat-label">成功退款</div>
              <div class="stat-trend">
                <span class="trend-label">成功率</span>
                <span class="trend-value success">
                  {{ formatPercentage(statistics.successRate) }}
                </span>
              </div>
            </div>
            <i class="el-icon-check stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingCount || 0 }}</div>
              <div class="stat-label">待处理</div>
              <div class="stat-trend">
                <span class="trend-label">较昨日</span>
                <span class="trend-value" :class="getTrendClass(statistics.pendingCountTrend)">
                  {{ formatTrend(statistics.pendingCountTrend) }}
                </span>
              </div>
            </div>
            <i class="el-icon-time stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card amount">
            <div class="stat-content">
              <div class="stat-number">{{ refundManager.formatAmount(statistics.totalAmount) }}</div>
              <div class="stat-label">退款总金额</div>
              <div class="stat-trend">
                <span class="trend-label">较昨日</span>
                <span class="trend-value" :class="getTrendClass(statistics.totalAmountTrend)">
                  {{ formatTrend(statistics.totalAmountTrend) }}
                </span>
              </div>
            </div>
            <i class="el-icon-money stat-icon"></i>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计 -->
    <el-row :gutter="20">
      <!-- 状态分布 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>退款状态分布</span>
          </div>
          <div class="status-distribution">
            <div
              v-for="status in statusDistribution"
              :key="status.status"
              class="status-item"
            >
              <div class="status-info">
                <el-tag
                  :type="refundManager.getStatusConfig(status.status).type"
                  size="small"
                >
                  {{ refundManager.getStatusConfig(status.status).label }}
                </el-tag>
                <span class="status-count">{{ status.count }}</span>
              </div>
              <div class="status-bar">
                <div
                  class="status-progress"
                  :style="{ width: getStatusPercentage(status.count) + '%' }"
                  :class="'progress-' + refundManager.getStatusConfig(status.status).type"
                ></div>
              </div>
              <span class="status-percentage">{{ getStatusPercentage(status.count) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 退款原因分析 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>退款原因分析</span>
          </div>
          <div class="reason-analysis">
            <div
              v-for="reason in reasonAnalysis"
              :key="reason.reason"
              class="reason-item"
            >
              <div class="reason-info">
                <span class="reason-text">{{ reason.reason }}</span>
                <span class="reason-count">{{ reason.count }}</span>
              </div>
              <div class="reason-bar">
                <div
                  class="reason-progress"
                  :style="{ width: getReasonPercentage(reason.count) + '%' }"
                ></div>
              </div>
              <span class="reason-percentage">{{ getReasonPercentage(reason.count) }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 时间趋势 -->
    <el-row>
      <el-col :span="24">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span>退款趋势分析</span>
            <div class="chart-actions">
              <el-radio-group v-model="trendType" size="small" @change="loadTrendData">
                <el-radio-button label="day">按天</el-radio-button>
                <el-radio-button label="week">按周</el-radio-button>
                <el-radio-button label="month">按月</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div class="trend-chart">
            <!-- 这里可以集成ECharts图表 -->
            <div class="chart-placeholder">
              <el-empty description="图表组件待集成" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 性能指标 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="metric-card">
          <div slot="header" class="card-header">
            <span>平均处理时长</span>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatDuration(statistics.avgProcessTime) }}</div>
            <div class="metric-desc">
              <el-progress
                :percentage="getProcessTimeScore(statistics.avgProcessTime)"
                :color="getProcessTimeColor(statistics.avgProcessTime)"
                :show-text="false"
              />
              <span class="metric-label">处理效率</span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="metric-card">
          <div slot="header" class="card-header">
            <span>客户满意度</span>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatPercentage(statistics.satisfactionRate) }}</div>
            <div class="metric-desc">
              <el-rate
                v-model="satisfactionStars"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value} 分"
              />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="metric-card">
          <div slot="header" class="card-header">
            <span>审核通过率</span>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatPercentage(statistics.approvalRate) }}</div>
            <div class="metric-desc">
              <el-progress
                :percentage="statistics.approvalRate || 0"
                color="#67c23a"
                :show-text="false"
              />
              <span class="metric-label">审核效率</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { refundManager } from '@/utils/refundManager'

export default {
  name: 'RefundStatistics',
  data() {
    return {
      refundManager,
      // 时间范围
      dateRange: [],
      // 统计数据
      statistics: {},
      statusDistribution: [],
      reasonAnalysis: [],
      trendData: [],
      trendType: 'day',
      // 加载状态
      loading: false
    }
  },
  computed: {
    satisfactionStars() {
      const rate = this.statistics.satisfactionRate || 0
      return Math.round(rate / 20) // 转换为5星制
    }
  },
  created() {
    // 默认显示最近7天的数据
    const now = new Date()
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    this.dateRange = [
      weekAgo.toISOString().slice(0, 19).replace('T', ' '),
      now.toISOString().slice(0, 19).replace('T', ' ')
    ]
    
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      
      try {
        const params = {}
        if (this.dateRange && this.dateRange.length === 2) {
          params.startTime = this.dateRange[0]
          params.endTime = this.dateRange[1]
        }

        const result = await refundManager.getStatistics(params)
        
        if (result.success) {
          this.statistics = result.data
          this.statusDistribution = result.data.statusDistribution || []
          this.reasonAnalysis = result.data.reasonAnalysis || []
        } else {
          this.$message.error(result.message || '加载统计数据失败')
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载统计数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载趋势数据
    async loadTrendData() {
      // 根据趋势类型加载对应的趋势数据
      console.log('加载趋势数据:', this.trendType)
    },

    // 时间范围改变
    handleDateChange() {
      this.loadData()
    },

    // 刷新
    handleRefresh() {
      this.loadData()
    },

    // 获取状态百分比
    getStatusPercentage(count) {
      const total = this.statistics.totalCount || 1
      return Math.round((count / total) * 100)
    },

    // 获取原因百分比
    getReasonPercentage(count) {
      const total = this.reasonAnalysis.reduce((sum, item) => sum + item.count, 0) || 1
      return Math.round((count / total) * 100)
    },

    // 格式化趋势
    formatTrend(trend) {
      if (!trend) return '--'
      const prefix = trend > 0 ? '+' : ''
      return `${prefix}${trend}`
    },

    // 获取趋势样式类
    getTrendClass(trend) {
      if (!trend) return ''
      return trend > 0 ? 'trend-up' : 'trend-down'
    },

    // 格式化百分比
    formatPercentage(value) {
      if (value == null) return '--'
      return `${Math.round(value)}%`
    },

    // 格式化时长
    formatDuration(minutes) {
      if (!minutes) return '--'
      
      const hours = Math.floor(minutes / 60)
      const mins = Math.round(minutes % 60)
      
      if (hours > 0) {
        return `${hours}h${mins}m`
      } else {
        return `${mins}m`
      }
    },

    // 获取处理时长评分
    getProcessTimeScore(minutes) {
      if (!minutes) return 0
      
      // 2小时内满分，超过8小时为0分
      if (minutes <= 120) return 100
      if (minutes >= 480) return 0
      
      return Math.round((480 - minutes) / 360 * 100)
    },

    // 获取处理时长颜色
    getProcessTimeColor(minutes) {
      const score = this.getProcessTimeScore(minutes)
      
      if (score >= 80) return '#67c23a'
      if (score >= 60) return '#e6a23c'
      return '#f56c6c'
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-statistics-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .header-title {
    h2 {
      margin: 0 0 5px 0;
      color: #303133;
      font-size: 24px;
    }
    
    .header-desc {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
  }
}

.overview-stats {
  margin-bottom: 20px;
  
  .stat-card {
    position: relative;
    overflow: hidden;
    
    ::v-deep .el-card__body {
      padding: 25px;
    }
    
    .stat-content {
      position: relative;
      z-index: 2;
      
      .stat-number {
        font-size: 36px;
        font-weight: bold;
        margin-bottom: 8px;
        line-height: 1;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
        display: block;
      }
      
      .stat-trend {
        font-size: 12px;
        
        .trend-label {
          color: #909399;
          margin-right: 5px;
        }
        
        .trend-value {
          font-weight: bold;
          
          &.trend-up {
            color: #f56c6c;
          }
          
          &.trend-down {
            color: #67c23a;
          }
          
          &.success {
            color: #67c23a;
          }
        }
      }
    }
    
    .stat-icon {
      position: absolute;
      right: 25px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 50px;
      opacity: 0.15;
      z-index: 1;
    }
    
    &.total {
      .stat-number, .stat-icon { color: #409eff; }
    }
    
    &.success {
      .stat-number, .stat-icon { color: #67c23a; }
    }
    
    &.pending {
      .stat-number, .stat-icon { color: #e6a23c; }
    }
    
    &.amount {
      .stat-number, .stat-icon { color: #f56c6c; }
    }
  }
}

.chart-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    color: #303133;
  }
  
  .status-distribution, .reason-analysis {
    .status-item, .reason-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .status-info, .reason-info {
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 15px;
        
        .status-count, .reason-count {
          font-weight: bold;
          color: #303133;
        }
        
        .reason-text {
          color: #606266;
          font-size: 14px;
        }
      }
      
      .status-bar, .reason-bar {
        flex: 1;
        height: 10px;
        background: #f0f2f5;
        border-radius: 5px;
        overflow: hidden;
        margin-right: 10px;
        
        .status-progress, .reason-progress {
          height: 100%;
          border-radius: 5px;
          transition: width 0.5s ease;
          
          &.progress-success {
            background: linear-gradient(90deg, #67c23a, #85ce61);
          }
          
          &.progress-warning {
            background: linear-gradient(90deg, #e6a23c, #ebb563);
          }
          
          &.progress-danger {
            background: linear-gradient(90deg, #f56c6c, #f78989);
          }
          
          &.progress-primary {
            background: linear-gradient(90deg, #409eff, #66b1ff);
          }
        }
        
        .reason-progress {
          background: linear-gradient(90deg, #409eff, #66b1ff);
        }
      }
      
      .status-percentage, .reason-percentage {
        width: 40px;
        text-align: right;
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .trend-chart {
    height: 300px;
    
    .chart-placeholder {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.metric-card {
  .metric-content {
    text-align: center;
    
    .metric-value {
      font-size: 32px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
    }
    
    .metric-desc {
      .metric-label {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
        display: block;
      }
    }
  }
}
</style>