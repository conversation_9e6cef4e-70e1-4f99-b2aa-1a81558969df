<template>
  <div class="placeholder-textarea-box">
    <div ref="textarea" class="placeholder-textarea" contenteditable="true" @keyup="updateHtml" @blur="onBlur"></div>
    <div v-if="maxlength" class="text-number" :key="value">
      {{ (encodeData(value) || '').length || 0 }}/{{ maxlength }}
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'PlaceholderTextarea',
  components: {},
  props: {
    maxlength: {
      type: Number,
    },
    value: String,
  },
  data() {
    return {
      inputValue: '',
      range: null,
    };
  },
  watch: {
    value(newData) {
      if (newData === this.inputValue || newData === this.encodeData(this.inputValue || '')) {
        return false;
      }
      this.inputValue = newData;
      (this.$refs.textarea as any).innerHTML = this.decodeData(newData);
    },
  },
  mounted() {
    document.getElementsByClassName('placeholder-textarea')[0].removeEventListener('click', this.eventBind);
    document.getElementsByClassName('placeholder-textarea')[0].addEventListener('click', this.eventBind, true);
  },
  destroyed() {
    document.getElementsByClassName('placeholder-textarea')[0]?.removeEventListener('click', this.eventBind);
  },

  methods: {
    encodeData(str?: string) {
      let res = (str || '').replace(
        /<span class="pla" contenteditable="false">(.*?)<div class="close">✕<\/div><\/span>/g,
        `\${$1}`,
      );
      res = (res || '').replace(/<span class="pla" contenteditable="false">(.*?)<\/span>/g, `\${$1}`);
      return res;
    },
    decodeData(str?: string) {
      const result = (str || '').replace(
        /\${(.*?)}/g,
        '<span class="pla" contenteditable="false">$1<div class="close">✕</div></span>',
      );
      return result;
    },
    eventBind(e: any) {
      if (e.target && (e.target as any).getAttribute('class') === 'close') {
        // eslint-disable-next-line no-unused-expressions
        e.target && (e.target as any).parentElement.remove();
      }
      this.updateHtml();
    },
    updateHtml(e?: any) {
      const textareaDiv: any = this.$refs.textarea;

      let html = textareaDiv.getInnerHTML?textareaDiv.getInnerHTML():textareaDiv.innerHTML;
      const htmlEncode = this.encodeData(html);

      let htmlText = htmlEncode.replace(/<[^>]*>/gi, '');
      htmlText = this.decodeData(htmlText);
      if (htmlText !== html) {
        textareaDiv.innerHTML = htmlText;
        html = htmlText;
      }
      const { length } = this.encodeData(htmlText);
      if (length > this.maxlength) {
        (this.$refs.textarea as any).innerHTML = this.decodeData(this.inputValue);
        return;
      }

      this.inputValue = htmlText;
      this.$emit('change', this.encodeData(htmlText));
    },
    onBlur() {
      const range = this.saveSelection();
      this.$data.range = range;
    },
    saveSelection() {
      const sel: any = window.getSelection();
      if (
        sel.getRangeAt &&
        sel.rangeCount &&
        (sel.anchorNode === this.$refs.textarea || sel.anchorNode.parentElement === this.$refs.textarea)
      ) {
        return sel.getRangeAt(0);
      }
      return null;
    },
    /**
     * 插入数据
     */
    insert(params: any) {
      const { type = '', text = '' } = params;

      this.insertContent(text);
      const html = (this.$refs.textarea as any).innerHTML;
      this.inputValue = this.encodeData(html);
      this.$emit('change', this.encodeData(html));
    },
    // 光标位置插入 content
    insertContent(content: any) {
      if (content) {
        const { range } = this.$data;
        // debugger
        const sel: any = window.getSelection();

        if (range) {
          range.deleteContents(); // 删除选中的内容
          const el = document.createElement('span'); // 创建一个空的div外壳
          el.className = 'pla';
          el.setAttribute('contenteditable', 'false');
          el.innerHTML = `${content}<div class="close">✕</div>`; // 设置div内容为我们想要插入的内容。
          const frag = document.createDocumentFragment(); // 创建一个空白的文档片段，便于之后插入dom树
          const node: any = el;
          frag.appendChild(node);
          range.insertNode(frag); // 插入内容
        } else {
          const { textarea } = this.$refs;
          const el = document.createElement('span'); // 创建一个空的div外壳
          el.className = 'pla';
          el.setAttribute('contenteditable', 'false');
          el.innerHTML = `${content}<div class="close">✕</div>`; // 设置div内容为我们想要插入的内容。
          (textarea as any).appendChild(el);
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import '@/style/variables';
.placeholder-textarea-box {
  position: relative;
  width: 100%;
  .placeholder-textarea {
    width: 100%;
    border: 1px solid #ccc;
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    min-height: 100px;
  }
  .text-number {
    text-align: right;
    display: inline-block;
    position: absolute;
    right: 10px;
    bottom: 5px;
    color: #666;
  }
  ::v-deep {
    .pla {
      display: inline-block;
      color: #0052d9;
      // margin: 0 2px;
      position: relative;
      .close {
        content: '';
        position: absolute;
        cursor: pointer;
        width: 12px;
        height: 12px;
        border-radius: 10px;
        background: rgb(0 0 0 / 62%);
        font-size: 12px;
        top: -4px;
        right: -9px;
        line-height: 12px;
        color: #fff;
        text-align: center;
        display: none;
      }
      &:hover {
        .close {
          display: block;
        }
      }
    }
  }
}
.t-is-error {
  .placeholder-textarea {
    border: 1px solid #e6636d;
    outline: #e6636d;
  }
}
</style>
