<template>
  <div class="result-empty">
    <file-add-icon class="result-empty-icon" />
    <div class="result-empty-describe">{{ emptyName || '暂无数据' }}</div>
    <div v-if="addName">
      <t-button  @click="handelClick">{{ addName }}</t-button>
    </div>
  </div>
</template>
<script lang="ts">
import { FileAddIcon } from 'tdesign-icons-vue';

export default {
  name: 'EmptyStatus',
  components: {
    FileAddIcon,
  },
  props: {
    addName: {
      type: String,
      default: '',
    },
    emptyName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {};
  },
  methods: {
    handelClick() {
      this.$emit('addClick');
    },
  },
};
</script>

<style lang="less" scoped>
@import '@/style/variables';
.result-empty {
  text-align: center;
  .result-empty-icon {
    font-size: 50px;
  }
  .result-empty-describe {
    font-size: 14px;
    margin: 10px 0;
  }
}
</style>
