<template>
  <t-dialog
    width="45%"
    header="分享营期"
    :closeOnOverlayClick="false"
    :visible.sync="modalVisible"
    @close="closeModal"
    class="edit-task-dialog"
    :confirmBtn="null"
    :cancelBtn="null"
  >
    <t-card id="share-card">
      <t-loading attach="#share-card" size="small" :loading="dataLoading"></t-loading>
      <div class="share-link">
        <div>请先选择小程序：</div>
        <t-select class="link-input" v-model="miniProgramId" :options="miniProgramList" @change="getShareLink" clearable placeholder="请选择小程序" :loading="dataLoading"></t-select>
      </div>
      <div class="share-link mt-3">
        <div>小程序分享链接：</div>
        <div class="link-input"><t-input placeholder="" v-model="shareLink" readonly></t-input></div>
        <t-button class="copy-btn" theme="primary" block @click="copyLink">复制链接</t-button>
      </div>
    </t-card>
  </t-dialog>
</template>

<script>
import systemMiniProgramApi from "@/constants/api/back/system-mini-program.api";
import copyToClipboard from "@/utils/copy";
import sysMiniProgram from "@/constants/api/back/sys-miniprogram.api";

export default {
  name: 'CourseSharing',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    shareData: {
      type: Object,
    }
  },
  data() {
    return {
      miniProgramList: [],
      miniProgramId: "",
      shareLink: '',
      modalVisible: false,
      dataLoading: false,
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.modalVisible = true;
        this.getMiniProgramList();
      }
    },
  },
  methods: {
    closeModal() {
      this.modalVisible = false;
      this.$emit('close');
    },
    async getMiniProgramList() {
      if (!this.shareData || !this.shareData.companyId) {
        this.$message.error('缺少公司ID，无法获取小程序列表');
        return;
      }

      try {
        this.dataLoading = true;
        const companyId = this.shareData.companyId;
        const res = await this.$request.get(`${sysMiniProgram.getMiniProgramsByCompanyId.url}/${companyId}`);

        if (res.code === 0 && res.data) {
          // 将小程序数据转换为下拉选择框所需的格式
          this.miniProgramList = res.data.map(item => ({
            label: item.miniProgramName || '未命名小程序',
            value: item.appid
          }));

          if (this.miniProgramList.length) {
            this.miniProgramId = this.miniProgramList[0].value;
            this.getShareLink();
          } else {
            this.$message.warning('当前公司没有关联小程序，请联系管理员分配');
          }
        } else {
          this.$message.error(res.msg || '获取公司关联小程序失败');
        }
        this.dataLoading = false;
      } catch (error) {
        console.error('获取公司关联小程序失败', error);
        this.dataLoading = false;
        this.$message.error('获取小程序列表失败');
      }
    },
    async getShareLink() {
      try {
        this.dataLoading = true;
        const shareData = {
          appId: this.miniProgramId,
          ...this.shareData,
        };
        // 发送请求获取分享链接
        const { data, code } = await this.$request.post(systemMiniProgramApi.getCampPeriodUrl.url, shareData);
        this.dataLoading = false;
        if (code === 0) {
          this.shareLink = data;
        } else {
          this.shareLink = '';
          this.$message.error('获取分享链接失败');
        }
      } catch (error) {
        this.dataLoading = false;
        this.shareLink = '';
      }
    },
    copyLink() {
      const result = copyToClipboard(this.shareLink);
      if (result) {
        this.$message.success('链接已复制到粘贴板');
      } else {
        this.$message.error('复制失败，请手动复制');
      }
      this.closeModal();
    }
  }
}
</script>

<style lang="less" scoped>
.share-link {
  display: flex;
  align-items: center;
  .link-input {
    flex: 1;
    margin-left: 10px;
  }
  .copy-btn {
    margin-left: 10px;
    width: 80px;
  }
}
</style>
