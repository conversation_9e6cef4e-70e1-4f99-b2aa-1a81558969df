<template>
  <div class="quill-editor">
    <quill-editor
      v-model="content"
      :options="editorOptions"
      @blur="$emit('blur')"
    />
  </div>
</template>

<script>
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import {quillEditor} from 'vue-quill-editor'

export default {
  name: 'QuillEditor',
  components: {quillEditor},
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      editorOptions: {
        placeholder: '请输入内容',
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline'],
            [{'list': 'ordered'}, {'list': 'bullet'}]
          ]
        }
      }
    }
  },
  computed: {
    content: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style>
.quill-editor {
  width: 100%;
}

.ql-editor {
  min-height: 200px;
}
</style>
