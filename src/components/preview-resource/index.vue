<template>
  <t-dialog
    :visible="modalVisible"
    custom-class="preview-dialog"
    @close="closeModal"
    :closeOnOverlayClick="true"
    :cancelBtn="null"
    :confirmBtn="null"
    top="5vh"
  >
    <!-- 媒体内容展示 -->
    <div class="media-container" id="share-card">
      <t-loading attach="#share-card" size="samll" :loading="dataLoading"></t-loading>
      <div class="media-header">
        <h3 class="media-title">{{ title }}</h3> <!-- 标题 -->
      </div>
      <div class="share-link" v-if="miniProgramList.length">
        <div>选择小程序：</div>
        <t-select class="link-input" v-model="miniProgramId" :options="miniProgramList" @change="getInviteImage" clearable placeholder="请选择小程序" :loading="dataLoading"></t-select>
        <t-button class="refresh-btn" theme="primary" @click="refreshInviteImage" :loading="dataLoading">刷新</t-button>
      </div>
      <img
        v-if="isImage"
        :src="srcUrl"
        alt="预览图片"
        class="preview-media"
      />
      <video
        v-else-if="isVideo"
        controls
        class="preview-media"
        ref="previewVideo"
      >
        <source :src="srcUrl" :type="videoType">
        您的浏览器不支持视频播放
      </video>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import systemMiniProgramApi from "@/constants/api/back/system-mini-program.api";
import sysMiniProgram from "@/constants/api/back/sys-miniprogram.api";

export default {
  name: 'preview-resource',
  props: {
    src: {
      type: String,
      default: '',
    },
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      modalVisible: false,
      srcUrl: '',
      inviteTitle: '',
      dataLoading: false,
      miniProgramId: '',
      miniProgramList: [],
      firstCompWx: [],
    }
  },
  computed: {
    isImage() {
      return /\.(jpe?g|png|gif)$/i.test(this.src || this.srcUrl)
    },
    isVideo() {
      return /\.(mp4|mov|avi|webm)$/i.test(this.src)
    },
    videoType() {
      if (!this.isVideo) return ''
      const ext = this.src.split('.').pop()
      return `video/${ext === 'mov' ? 'quicktime' : ext}`
    },
    title() {
      return this.inviteTitle || (this.isImage ? '预览图片' : (this.isVideo ? '预览视频' : '预览'))
    }
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    },
    src(val) {
      this.srcUrl = val;
      const videoElement: HTMLVideoElement = this.$refs.previewVideo as HTMLVideoElement;
      if (videoElement) {
        videoElement.src = val;
      }
    },
    "rowData": {
      handler(val) {
        if (Object.keys(val).length) {
          // 如果有公司ID，则获取该公司关联的小程序列表
          if (val && val.id) {
            this.getCompanyMiniPrograms(val.id);
            this.inviteTitle = val.companyName;
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    closeModal() {
      this.pauseVideo();
      this.destroyInfo();
      this.$emit('close');
    },
    pauseVideo() {
      if (this.isVideo) {
        const videoElement: HTMLVideoElement = this.$refs.previewVideo as HTMLVideoElement;
        videoElement.pause();
        videoElement.currentTime = 0; // 可选，重置视频到开始
        videoElement.src = '';
      }
    },
    destroyInfo() {
      if (this.inviteTitle) {
        this.firstCompWx = [];
        this.srcUrl = '';
        this.inviteTitle = '';
      }
    },
    getInviteImage() {
      // 查找当前选中的小程序
      const selectedProgram = this.firstCompWx.find(item => item.appid === this.miniProgramId);

      // 如果找到了选中的小程序并且它已经有邀请码地址，直接使用
      if (selectedProgram && selectedProgram.sellUrl) {
        this.srcUrl = selectedProgram.sellUrl;
        return;
      }

      // 如果没有现成的邀请码地址，则请求API获取
      this.requestInviteImage();
    },

    // 强制刷新邀请码，无论是否已有邀请码地址
    refreshInviteImage() {
      this.requestInviteImage();
    },

    // 请求获取邀请码的API方法
    async requestInviteImage() {
      this.dataLoading = true;
      try {
        const res = await this.$request.post(
          systemMiniProgramApi.getSellUrl.url, {
            appId: this.miniProgramId,
            headquartersId: this.rowData.headquartersId,
            columnId: this.rowData.columnId,
            companyId: this.rowData.id,
          }
        );
        const { code, data, msg } = res;
        if (code === 0) {
          this.srcUrl = data;

          // 更新firstCompWx中对应小程序的sellUrl
          const index = this.firstCompWx.findIndex(item => item.appid === this.miniProgramId);
          if (index !== -1) {
            this.firstCompWx[index].sellUrl = data;
          }
        } else {
          this.srcUrl = '';
          this.$message.error(msg || '获取小程序邀请码失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.srcUrl = '';
        this.$message.error('获取小程序邀请码接口请求失败');
        this.dataLoading = false;
      }
    },
    // 获取公司关联的小程序信息列表
    async getCompanyMiniPrograms(companyId) {
      try {
        this.dataLoading = true;
        const res = await this.$request.get(`${sysMiniProgram.getMiniProgramsByCompanyId.url}/${companyId}`);

        if (res.code === 0 && res.data) {
          this.firstCompWx = res.data;
          // 将小程序数据转换为下拉选择框所需的格式
          this.miniProgramList = this.firstCompWx.map(item => ({
            label: item.miniProgramName || '未命名小程序',
            value: item.appid
          }));

          if (this.firstCompWx.length > 0) {
            this.miniProgramId = this.firstCompWx[0].appid;
            this.srcUrl = this.firstCompWx[0].sellUrl;
          } else {
            this.miniProgramId = '';
            this.srcUrl = '';
            this.$message.warning('当前公司没有关联小程序，请联系管理员分配');
          }
        } else {
          this.$message.error(res.msg || '获取公司关联小程序失败');
        }
        this.dataLoading = false;
      } catch (error) {
        console.error('获取公司关联小程序失败', error);
        this.$message.error('获取公司关联小程序失败');
        this.dataLoading = false;
      }
    },
  }
}
</script>

<style lang="less" scoped>
.preview-dialog {
  z-index: 1000;
}

.media-container {
  max-width: 80vw;
  max-height: 80vh;
  position: relative; /* 为了使标题能够相对居中 */

  .media-header {
    text-align: center;  /* 居中标题 */
    margin-bottom: 15px;
  }

  .media-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
  }

  .preview-media {
    display: block;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    object-fit: cover;
  }

  video {
    width: auto;
    height: auto;
    min-width: 300px;
    min-height: 150px;
  }
}

.close-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  transition: opacity 0.3s;

  &:hover {
    opacity: 0.8;
  }
}
.share-link {
  display: flex;
  align-items: center;
  .link-input {
    flex: 1;
    margin-left: 10px;
  }
  .copy-btn {
    margin-left: 10px;
    width: 80px;
  }
  .refresh-btn {
    margin-left: 10px;
    width: 60px;
  }
}
</style>
