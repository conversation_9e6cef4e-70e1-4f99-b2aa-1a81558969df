<template>
  <div class="upload-list-container" :class="{ 'is-expand': isExpand }">
    <div class="upload-list-header">
      <div class="upload-list-header-title">
        文件上传列表
      </div>
      <div class="upload-list-header-icon">
        <chevron-down-icon v-if="isExpand" size="20px" @click="isExpand =!isExpand" />
        <chevron-up-icon v-else size="20px" @click="isExpand =!isExpand" />
        <close-icon size="20px" @click="closeUploadList" />
      </div>
    </div>
    <div class="upload-list-header-content" v-if="isExpand">
      <t-table :bordered="true" rowKey="id" :data="currentPageData" :columns="columns" :pagination="pagination" @page-change="handlePageChange">
        <template #status="{ row }">
          <t-tag shape="round" :theme='getModelStatus(row.status, row).theme' variant="light-outline">
            <cloud-upload-icon v-if="row.status === 'pending'" />
            <check-circle-filled-icon v-else-if="row.status ==='success'" />
            <close-circle-filled-icon v-else-if="row.status === 'error' && row.retries >= 3" />
            <refresh-icon v-else-if="row.status === 'retrying' && row.retries < 3" />
            {{ getModelStatus(row.status, row).label }}
          </t-tag>
        </template>
      </t-table>
    </div>
  </div>
</template>

<script>
import { ChevronUpIcon, ChevronDownIcon, RefreshIcon, CloseIcon, CloudUploadIcon, CheckCircleFilledIcon, CloseCircleFilledIcon } from 'tdesign-icons-vue';

export default {
  name: 'UploadList',
  components: {
    ChevronUpIcon,
    ChevronDownIcon,
    CloseIcon,
    CloudUploadIcon,
    CheckCircleFilledIcon,
    CloseCircleFilledIcon,
    RefreshIcon,
  },
  data() {
    return {
      columns: [
        {
          colKey: 'name',
          title: '文件名',
          width: 150,
          ellipsis: true,
        },
        {
          colKey: 'size',
          title: '文件大小',
          width: 80,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val ? `${ Math.round(val / 1024 / 1024) }MB` : '-';
          },
        },
        {
          colKey: 'progress',
          title: '上传进度',
          width: 80,
          ellipsis: true,
          align: 'center',
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val ? `${ val }%` : '-';
          },
        },
        {
          colKey: 'status',
          title: '上传状态',
          align: 'center',
          width: 80,
        },
      ],
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },
      isExpand: true,
    }
  },
  computed: {
    fileList() {
      return this.$store.state.upload.activeTasks;
    },
    currentPageData() {
      const start = (this.pagination.current - 1) * this.pagination.pageSize;
      return this.fileList.slice(start, start + this.pagination.pageSize);
    },
  },
  watch: {
    '$store.state.upload.activeTasks': {
      handler(newVal, oldVal) {
        this.pagination.total = newVal.length;
        const maxPage = Math.ceil(newVal.length / this.pagination.pageSize);
        if (this.pagination.current > maxPage && maxPage > 0) {
          this.pagination.current = maxPage;
        }
      },
      deep: true,
    }
  },
  methods: {
    handlePageChange(pageInfo) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
    },
    getModelStatus(status, row ) {
      const statusNameListMap = {
        'success': { label: '上传成功', theme: 'success' },
        'error': { label: '上传失败', theme: 'danger' },
        'pending': { label: '正在上传', theme: 'primary' },
        'retrying': { label: '正在重试', theme: 'warning' },
      };
      return statusNameListMap[status];
    },
    closeUploadList() {
      // 关闭上传列表
      this.$store.commit('SET_SHOW_UPLOAD_LIST', false);
      this.$store.commit('deleteAllTasks');
    },
  }
}
</script>

<style scoped lang="less">
.upload-list-container {
  min-height: 100px;
  width: 40%;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  position: fixed;
  right: 20px;
  bottom: 50px;
  z-index: 9999;
  .upload-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    border-bottom: 1px solid #e8e8e8;
    padding: 0 10px;
    .upload-list-header-title {
       font-size: 16px;
       font-weight: bold;
       color: #333;
     }
    .upload-list-header-icon {
       display: flex;
       align-items: center;
       cursor: pointer;
       svg {
         margin-right: 5px;
       }
     }
  }
  .upload-list-header-content {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #999;
    overflow: scroll;
  }
}
.is-expand {
 height: 40%;
}
</style>
