<template>
  <div class='no-data-tip'>

    <t-loading text="加载中..." size="small" v-if='loading'></t-loading>

    <template v-else>
      <i></i>
      <span>暂无数据</span>
    </template>

  </div>
</template>

<script>
export default {
  name: 'no-data-tip',
  props: ['loading']
};
</script>

<style lang='less' scoped>
.no-data-tip{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  i{
    width: 80px;
    height: 80px;
    display: inline-block;
    margin-bottom: 20px;
  }
  span{
    color: #bababa;
  }
}
</style>
