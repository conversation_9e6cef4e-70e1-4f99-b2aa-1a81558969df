<template>
  <div :class="prefix + '-back'">
    <t-card header-bordered>
      <template #title>
        <chevron-left-icon v-if="showIcon" class="back-icon" @click="handelBack"></chevron-left-icon>
        {{ title }}
      </template>
    </t-card>
  </div>
</template>

<script lang="ts">
import { prefix } from '@/config/global';
import Vue from 'vue';
import { ChevronLeftIcon } from 'tdesign-icons-vue';

export default Vue.extend({
  name: `${prefix}-footer`,
  components: {
    ChevronLeftIcon,
  },
  props: {
    showIcon: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      prefix,
    };
  },
  methods: {
    handelBack() {
      this.$emit('back');
    },
  },
});
</script>
<style lang="less" scoped>
@import '@/style/variables';

.@{prefix}-back {
  color: @text-color-placeholder;
  margin: 0 0 20px 0;
}
.back-icon {
  display: inline-block;
  font-size: 24px;
  cursor: pointer;
}
</style>
