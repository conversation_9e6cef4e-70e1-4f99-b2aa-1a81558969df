<template>
  <t-table
    stripe
    ref="tableRef"
    :rowKey="rowKey"
    :data="listData"
    :columns="columns"
    :pagination="pagination"
    :loading="loading"
    :selected-row-keys="selectedRowKeys"
    @select-change="handleRowSelectChange"
    class="common-table"
  >
    <template #op="{ row }">
      <!-- 默认来点简单的编辑和删除 -->
      <div class="operation-item">
      <div v-for="(item, index) in operations" :key="index">
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'edit' && item.visible"
          @click="item.onClick(row)"
          >编辑
        </a>
        <t-popconfirm :key="index" v-if="item.type === 'delete' && item.visible" content="确认删除吗" @confirm="item.onClick(row)">
          <a class="t-button-link t-button-link--theme-danger">删除 </a>
        </t-popconfirm>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'getActivationCode'"
          @click="item.onClick(row)"
        >手动获取激活码</a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'activate'"
          @click="item.onClick(row)"
        >激活互通账号</a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'generateCode'"
          @click="item.onClick(row)"
        >生成二维码</a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'inherit'"
          @click="item.onClick(row)"
        >账号继承</a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'preview'"
          @click="item.onClick(row)"
        >预览</a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'copy'"
          @click="item.onClick(row)"
        >复制</a>
        <t-popconfirm :key="index" v-if="item.type === 'removed'" content="确认下架吗" @confirm="item.onClick(row)">
        <a
          class="t-button-link t-button-link--theme-danger"
          v-if="row.courseStatus !== '0'"
          :key="index"
        >下架</a>
        </t-popconfirm>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'download'"
          @click="item.onClick(row)"
        >下载</a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'share'"
          @click="item.onClick(row)">分享营期
        </a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'detail'"
          @click="item.onClick(row)">详情
        </a>
        <a
          class="t-button-link"
          :key="index"
          v-if="item.type === 'viewdata'"
          @click="item.onClick(row)">数据
        </a>
        <t-divider layout="vertical" v-if="index < operations.length - 1" style="background: #e4e4e4"/>
      </div>
      </div>

      <!--  如果需要自定义，就用插槽，外层按下面写-->
      <!--
       <template #row="{ row }">
          <t-button theme='primary' shape='square' variant='text' @click='edit(row)'>编辑</t-button>
          <t-popconfirm  content="确认删除吗" @confirm='del(row)'>
            <t-button theme='primary' shape='square' variant='text'>删除</t-button>
          </t-popconfirm>
        </template>
      -->
      <slot name="row" v-bind:row="row"></slot>
    </template>
    <template #questionDetail="{ row }">
      <div v-html="getHtml(row)"></div>
    </template>
  </t-table>
</template>

<script>

export default {
  name: 'common-table',
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    url: {
      type: String,
      default: '',
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    isRequest: {
      type: Boolean,
      default: true,
    },
    transferReq: {
      type: Function,
    },
    transferRes: {
      type: Function,
    },
    operations: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      listData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showJumper: true,
        onChange: (pageInfo) => {
          this.pagination.pageSize = pageInfo.pageSize;
          this.pagination.current = pageInfo.current;
          this.getList();
        },
      },
      loading: false,
      selectedRowKeys: [],
      selectedRows: {},
    };
  },
  watch: {
    params: {
      handler(val) {
        this.pagination.current = 1;
        this.getList();
      },
      deep: true,
    },
  },
  created() {
    this.getList();
  },
  methods: {
    clearSelection() {
      this.selectedRowKeys = []
      this.selectedRows = {}

      // 安全调用表格方法
      if (this.$refs.tableRef && this.$refs.tableRef.clearSelection) {
        this.$refs.tableRef.clearSelection() // 注意方法名可能是clearSelection
      }

      this.$emit('getValue', [])
    },
    getHtml(row){
      return row.questionDetail.replace(/\${(.*?)}/g, `<span class="pla" contenteditable="false">[$1]</span>`)
    },
    async getList() {
      if (!this.isRequest) return;

      this.loading = true;

      //  请求参数转换
      let params = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
        ...this.params,
      };

      if (this.transferReq) {
        params = this.transferReq(params);
      }

      const { data } = await this.$request.get(this.url, { params }).catch((err) => err);


      const { records, total } = this.transferRes ? this.transferRes(data) : data;
      // todo 数据字段处理

      this.listData = records || [];
      this.pagination.total = +total || 0;

      this.loading = false;
    },
    selectedValue() {
      // 值变化时自动推送
    },
    refreshTable() {
      this.getList();
    },
    handleRowSelectChange(selectedRowKeys, { selectedRowData }) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRowData;
      this.$emit('getValue', this.selectedRows);
    },
  },
};
</script>

<style lang="less" scoped>
.common-table {
  /deep/ table {
    tr th {
      background: #fff;
    }
  }
  /deep/ .t-table__pagination {
    background: #f3f3f3;
    padding: 10px 20px;
    border-top: 1px solid #eee;
  }
  /deep/ .pla {
    color: #0052d9;
    padding: 0 3px;
  }
  /deep/ .t-table--striped {
    background: #999999;
    color: red;
  }
}
.operation-item {
  display: flex;
}
</style>
