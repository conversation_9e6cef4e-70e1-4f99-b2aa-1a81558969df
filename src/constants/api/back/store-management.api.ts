const storePrefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/mall/store`;
const categoryPrefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/mall/store-category`;

/**
 * @description: 店铺管理相关API
 * <AUTHOR>
 * @date 2025/1/19
 */

const storeManagementApi = {
  // 店铺管理接口
  queryStorePage: {
    url: `${storePrefix}/page`,
    method: 'post',
    des: '分页查询店铺列表',
  },
  getStoreDetail: {
    url: `${storePrefix}/`,
    method: 'get',
    des: '获取店铺详情',
  },
  saveStore: {
    url: `${storePrefix}/save`,
    method: 'post',
    des: '保存店铺信息',
  },
  deleteStore: {
    url: `${storePrefix}/`,
    method: 'post',
    des: '删除店铺',
  },
  batchDeleteStore: {
    url: `${storePrefix}/batch-delete`,
    method: 'post',
    des: '批量删除店铺',
  },
  updStoreStatus: {
    url: `${storePrefix}/update-status`,
    method: 'post',
    des: '更新店铺状态',
  },
  auditStore: {
    url: `${storePrefix}/audit`,
    method: 'post',
    des: '审核店铺',
  },
  generateStoreCode: {
    url: `${storePrefix}/generate-code`,
    method: 'get',
    des: '生成店铺编码',
  },

  // 店铺分类管理接口
  queryCategoryList: {
    url: `${categoryPrefix}/list`,
    method: 'get',
    des: '查询分类列表',
  },
  getEnabledCategories: {
    url: `${categoryPrefix}/enabled`,
    method: 'get',
    des: '查询启用的分类列表',
  },
  getCategoryDetail: {
    url: `${categoryPrefix}/get-detail`,
    method: 'get',
    des: '获取分类详情',
  },
  saveCategory: {
    url: `${categoryPrefix}/save`,
    method: 'post',
    des: '保存分类信息',
  },
  deleteCategory: {
    url: `${categoryPrefix}/delete`,
    method: 'post',
    des: '删除分类',
  },
  updCategoryStatus: {
    url: `${categoryPrefix}/upd-status`,
    method: 'post',
    des: '更新分类状态',
  },
};

export default storeManagementApi;
