const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/column`;

/**
 * @description: 栏目管理api
 */
const columnApi = {
  queryColumnByPage: {
    url: `${prefix}/page`,
    method: 'post',
    des: '分页查询栏目',
  },
  updateColumn: {
    url: `${prefix}`,
    method: 'put',
    des: '更新系统栏目信息',
  },
  saveColumn: {
    url: `${prefix}`,
    method: 'post',
    des: '新增系统栏目信息',
  },
  deleteColumn: {
    url: `${prefix}/delete`,
    method: 'post',
    des: '删除栏目信息',
  },
  updateColumnStatus: {
    url: `${prefix}/update-status`,
    method: 'post',
    des: '更新栏目状态',
  },

};

export default columnApi;
