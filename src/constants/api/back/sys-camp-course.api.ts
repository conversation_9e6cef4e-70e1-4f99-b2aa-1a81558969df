const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/camp-course`;

/**
 * @description: 营期分组api
 */
const sysCampCourseUploadApi = {
  addCampCourse: {
    url: `${prefix}`,
    method: 'post',

    des: '新增营期课程',
  },
  batchAddCampCourse: {
    url: `${prefix}/batchCreate`,
    method: 'post',

    des: '批量新增营期课程',
  },
  deleteCampCourse: {
    url: `${prefix}/deleteById`,
    method: 'post',

    des: '删除营期课程',
  },
  updateCampCourse: {
    url: `${prefix}/updateById`,
    method: 'post',

    des: '修改营期课程',
  },
  queryCampListById: {
    url: `${prefix}/`,
    method: 'get',

    des: '查询课程列表通过营期id',
  },
  queryCampListByPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询课程列表通过分页',
  },
  queryCampCourseList: {
    url: `${prefix}/list`,
    method: 'get',

    des: '查询营期课程列表不分页',
  },
  updateCourseSort: {
    url: `${prefix}/sort`,
    method: 'post',

    des: '批量修改课程排序',
  },
  updateCourseVideo: {
    url: `${prefix}/batch`,
    method: 'post',

    des: '根据课程分组批量插入营期课程',
  },
  saveCampCourse: {
    url: `${prefix}/save-by-camp-courses`,
    method: 'post',

    des: '保存营期课程',
  },
  getCampCourseListByCampId: {
    url: `${prefix}/get-camp-courses`,
    method: 'get',
    des: '根据营期id查询营期课程列表',
  }
};

export default sysCampCourseUploadApi;
