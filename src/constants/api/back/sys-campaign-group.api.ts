const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/activity-group`;

/**
 * @description: 营销活动api
 */
const sysCampaignGroupApi = {
  addCampaignGroup: {
    url: `${prefix}`,
    method: 'post',
    des: '新增营销活动分组',
  },
  deleteCampaignGroup: {
    url: `${prefix}/deleteById`,
    method: 'post',
    des: '删除营销活动分组',
  },
  updateCampaignGroup: {
    url: `${prefix}/updateById`,
    method: 'post',

    des: '修改营销活动分组',
  },
  queryCampaignGroupById: {
    url: `${prefix}/`,
    method: 'get',

    des: '查询营销活动分组详情',
  },
  queryCampaignGroupListByPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询营销活动分组列表通过分页',
  },
  queryCampaignGroupList: {
    url: `${prefix}/list`,
    method: 'get',

    des: '查询营销活动分组列表',
  },
  queryCampaignGroupListByGroupId: {
    url: `${prefix}/activityList`,
    method: 'get',
    des: '查询营销活动分组下的课程列表',
  }
};

export default sysCampaignGroupApi;
