const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/camp-period-data`;

/**
 * @description: 营期分组api
 */
const sysCampPeriodDataApi = {
  queryCampPeriodData: {
    url: `${prefix}/getCampPeriodData`,
    method: 'post',
    des: '营期数据',
  },
  queryCampPeriodTableData: {
    url: `${prefix}/getCampCourseData`,
    method: 'post',
    des: '营期数据',
  },

  queryCustomerDataTableData: {
    url: `${prefix}/getCustomerData`,
    method: 'post',

    des: '营期数据',
  },

  queryCampCourseListData: {
    url: `${prefix}/getCampCourseList`,
    method: 'post',
    des: '营期课程数据',
  },

  queryCustomerCourseListData: {
    url: `${prefix}/getCustomerCourseList`,
    method: 'post',
    des: '营期数据',
  }

};

export default sysCampPeriodDataApi;
