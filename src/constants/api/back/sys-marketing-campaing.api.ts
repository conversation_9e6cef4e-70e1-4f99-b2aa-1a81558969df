const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/activities`;

/**
 * @description: 营销活动api
 */
const sysMarketingCampaignApi = {
  addCampCourse: {
    url: `${prefix}`,
    method: 'post',

    des: '新建营销活动',
  },
  deleteMarketingCampaign: {
    url: `${prefix}/deleteById`,
    method: 'post',

    des: '删除营销活动',
  },
  updateMarketingCampaign: {
    url: `${prefix}/updateById`,
    method: 'post',

    des: '修改营销活动',
  },
  queryMarketingCampaignById: {
    url: `${prefix}/`,
    method: 'get',

    des: '查询营销活动详情',
  },
  queryMarketingCampaignListByPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询`营销活动列表`分页',
  },
  queryMarketingCampaignListNotPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询`营销活动列表`不分页',
  },
};

export default sysMarketingCampaignApi;
