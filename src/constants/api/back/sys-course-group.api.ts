const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/course-group`;

/**
 * @description: 课程分组api
 */
const sysCourseGroupApi = {
  addCourseGroup: {
    url: `${prefix}`,
    method: 'post',
    des: '新增课程分组',
  },
  deleteCourseGroup: {
    url: `${prefix}/deleteById`,
    method: 'post',
    des: '删除课程分组',
  },
  updateCourseGroup: {
    url: `${prefix}/updateById`,
    method: 'post',

    des: '修改视频分组',
  },
  queryCourseGroupListById: {
    url: `${prefix}/`,
    method: 'get',

    des: '查询课程分组列表通过分组id',
  },
  queryCourseGroupListByPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询课程分组列表通过分页',
  },
  queryCourseGroupList: {
    url: `${prefix}/list`,
    method: 'get',

    des: '查询课程分组列表',
  },
  queryCourseGroupListByGroupId: {
    url: `${prefix}/courseVideoList`,
    method: 'get',
    des: '查询课程分组列表通过课程id',
  }
};

export default sysCourseGroupApi;
