const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/camp-period`;

/**
 * @description: 营期分组api
 */
const sysCampGroupApi = {
  addCampGroup: {
    url: `${prefix}`,
    method: 'post',
    des: '新增营期',
  },
  deleteCampGroup: {
    url: `${prefix}/deleteById`,
    method: 'post',
    des: '删除营期',
  },
  updateCampGroup: {
    url: `${prefix}/updateById`,
    method: 'post',

    des: '修改视频',
  },
  queryCampGroupListById: {
    url: `${prefix}/`,
    method: 'get',

    des: '查询营期分组列表通过id',
  },
  queryCampGroupListByPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询营期分组列表通过分页',
  },
  queryCourseGroupList: {
    url: `${prefix}/list`,
    method: 'get',

    des: '查询课程分组列表',
  },
  queryCourseGroupListByGroupId: {
    url: `${prefix}/courseVideoList`,
    method: 'get',
    des: '查询课程分组列表通过课程id',
  },
  queryCampCourseListByPage: {
    url: `${prefix}/getCourseByCompanyIdANDSalesId`,
    method: 'get',

    des: '根据companyId和salesId查询课程视频',
  },
  getCampPeriodsByCompanyId: {
    url: `${prefix}/getCampPeriodsByCompanyId`,
    method: 'get',

    des: '根据companyId查询营期',
  },
};

export default sysCampGroupApi;
