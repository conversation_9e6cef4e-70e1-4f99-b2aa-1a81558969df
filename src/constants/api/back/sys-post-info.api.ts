const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/sys-post-info`;

/**
 * @description: 用户与部门岗位信息
 */
const sysPostInfoApi = {
  searchPostInfo: {
    url: `${prefix}/query-post-by-like`,
    method: 'post',

    des: '查询-分页条件查询登录用户角色权限下的公司信息',
  },
  queryEffectivePostByTenantId: {
    url: `${prefix}/query-effective-post-by-tenantId`,
    method: 'get',

    des: '租户条件下可用岗位',
  },
  insertSysPostInfo: {
    url: `${prefix}/add`,
    method: 'post',
  },
  updateSysPostInfo: {
    url: `${prefix}/update-post`,
    method: 'put',
  },
  deleteBatchPost: {
    url: `${prefix}/delete_BatchPost`,
    method: 'post',
  },
  deleteSysPostInfoByPost: {
    url: `${prefix}/delete-post-by-id`,
    method: 'delete',
  },
  updataPostsUseState: {
    url: `${prefix}/update-posts-use-state`,
    method: 'put',
  },
};

export default sysPostInfoApi;
