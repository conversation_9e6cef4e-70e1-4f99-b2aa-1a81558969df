const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/sales-group`;

/**
 * @description: 销售组管理api
 */
const salesGroupApi = {
  querySalesGroupByPage: {
    url: `${prefix}/page`,
    method: 'post',
    des: '分页查询销售组',
  },
  saveSalesGroup: {
    url: `${prefix}`,
    method: 'post',
    des: '新增销售组',
  },
  updateSalesGroup: {
    url: `${prefix}`,
    method: 'put',
    des: '更新销售组',
  },
  deleteSalesGroup: {
    url: `${prefix}/delete`,
    method: 'post',
    des: '删除销售组信息',
  },
  updateSalesGroupStatus: {
    url: `${prefix}/update-status`,
    method: 'post',
    des: '更新销售组状态',
  },
  listByCompanyId: {
    url: `${prefix}/list-by-company-id`,
    method: 'get',
    des: '根据公司ID查询销售组列表',
  }

};

export default salesGroupApi;
