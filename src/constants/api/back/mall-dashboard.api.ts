const dashboardPrefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/dashboard`;

/**
 * @description: 商城驾驶舱相关API
 * <AUTHOR>
 * @date 2025/1/22
 */

const mallDashboardApi = {
  // 获取首页概览数据
  getOverview: {
    url: `${dashboardPrefix}/overview`,
    method: 'get',
    des: '获取首页四个核心指标卡片的数据',
  },
  
  // 获取订单趋势分析数据
  getOrderTrend: {
    url: `${dashboardPrefix}/order-trend`,
    method: 'get',
    des: '获取订单数量和金额的趋势图数据',
  },
  
  // 获取订单状态分布数据
  getOrderStatusDistribution: {
    url: `${dashboardPrefix}/order-status-distribution`,
    method: 'get',
    des: '获取订单状态的饼图分布数据',
  },
  
  // 获取实时数据
  getRealtime: {
    url: `${dashboardPrefix}/realtime`,
    method: 'get',
    des: '获取实时更新的数据（用于定时刷新）',
  },
  
  // 获取今日数据对比
  getTodayComparison: {
    url: `${dashboardPrefix}/today-comparison`,
    method: 'get',
    des: '获取今日与昨日的数据对比',
  },
  
  // 获取销售数据统计
  getSalesStats: {
    url: `${dashboardPrefix}/sales-stats`,
    method: 'get',
    des: '获取不同周期的销售统计数据',
  },
};

export default mallDashboardApi;
