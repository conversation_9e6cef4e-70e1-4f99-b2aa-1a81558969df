const bannerPrefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/admin/banners`;

/**
 * @description: 横幅管理相关API
 * <AUTHOR>
 * @date 2025/1/24
 */

const bannerApi = {
  // 分页查询横幅列表
  getBannerList: {
    url: bannerPrefix,
    method: 'get',
    des: '分页查询横幅列表',
  },

  // 查询横幅详情
  getBannerDetail: {
    url: `${bannerPrefix}/{id}`,
    method: 'get',
    des: '查询横幅详情',
  },

  // 新增横幅
  createBanner: {
    url: bannerPrefix,
    method: 'post',
    des: '新增横幅',
  },

  // 更新横幅
  updateBanner: {
    url: `${bannerPrefix}/{id}`,
    method: 'put',
    des: '更新横幅',
  },

  // 删除横幅
  deleteBanner: {
    url: `${bannerPrefix}/{id}`,
    method: 'delete',
    des: '删除横幅',
  },

  // 批量删除横幅
  batchDeleteBanner: {
    url: `${bannerPrefix}/batch-delete`,
    method: 'post',
    des: '批量删除横幅',
  },

  // 更新横幅启用状态
  updateBannerStatus: {
    url: `${bannerPrefix}/{id}/active`,
    method: 'post',
    des: '更新横幅启用状态',
  },

  // 上传横幅图片
  uploadBannerImage: {
    url: `/api/v1/admin/upload/banner`,
    method: 'post',
    des: '上传横幅图片',
  },

  // 获取商品列表（用于关联商品选择）
  getProductList: {
    url: `/api/v1/admin/products/simple`,
    method: 'get',
    des: '获取商品列表用于横幅关联',
  },
};

export default bannerApi;
