const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/company`;
const suffix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/headquarters`;

/**
 * @description: 公司api
 */
const sysCompanyGroupApi = {
  queryCompanyListByCompanyId: {
    url: `${prefix}/get-company-group`,
    method: 'get',
    des: '查询公司下所有部门',
  },
  saveCompany: {
    url: `${prefix}`,
    method: 'get',
    des: '新增公司',
  },
  updateCompany: {
    url: `${prefix}`,
    method: 'get',
    des: '更新公司',
  },
  queryHeadquartersCompanyList: {
    url: `${suffix}/tree`,
    method: 'get',
    des: '查询总部树形结构',
  },
  queryCompanyByPage: {
    url: `${prefix}/page`,
    method: 'post',
    des: '查询总部分页',
  },
  updateCompanyStatus: {
    url: `${prefix}/update-status`,
    method: 'post',
    des: '更新公司状态',
  },
  deleteCompany: {
    url: `${prefix}/delete`,
    method: 'post',
    des: '删除公司信息',
  },

};

export default sysCompanyGroupApi;
