const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/system-user`;

/**
 * @description: 用户管理api
 */
const systemUserApi = {
  queryUserByPage: {
    url: `${prefix}/query-user-by-page`,
    method: 'post',
    des: '分页查询系统用户',
  },
  updateUser: {
    url: `${prefix}/update`,
    method: 'put',
    des: '更新系统用户信息',
  },
  updateAuditStatusBatch: {
    url: `${prefix}/update-audit-status`,
    method: 'post',
    des: '更新系统用户审核状态',
  },
  deleteUser: {
    url: `${prefix}`,
    method: 'delete',
    des: '删除用户',
  },
  updateCustomerRemark: {
    url: `${prefix}/update-customer-remark`,
    method: 'get',
    des: '更新客户备注',
  },
};

export default systemUserApi;
