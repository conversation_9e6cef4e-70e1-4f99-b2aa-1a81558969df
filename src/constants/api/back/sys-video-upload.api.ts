const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/video-group`;
const prefix2 = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/video_upload`;

/**
 * @description: 视频分组api
 */
const sysVideoUploadApi = {
  addVideoGroup: {
    url: `${prefix}`,
    method: 'post',
    des: '新增视频分组',
  },
  deleteVideoGroup: {
    url: `${prefix}/deleteById`,
    method: 'post',
    des: '删除视频分组',
  },
  updateVideoGroup: {
    url: `${prefix}/updateById`,
    method: 'post',
    des: '修改视频分组',
  },
  queryVideoGroupListById: {
    url: `${prefix}/`,
    method: 'get',

    des: '查询视频分组列表通过分组id',
  },
  queryVideoGroupListByPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询视频分组列表',
  },
  queryVideoGroupList: {
    url: `${prefix}/list`,
    method: 'get',

    des: '查询视频分组列表',
  },
  queryVideoListByGroupId: {
    url: `${prefix2}/page`,
    method: 'get',
    des: '查询视频列表通过分组id',
  },
  commonDocumentUpload: {
    url: `${prefix2}/upload`,
    method: 'post',
    des: '通用文件上传',
  },
  coursewareUpload: {
    url: `${prefix2}/uploadCourseImage`,
    method: 'post',
    des: '课程封面上传',
  },
  queryCoverList: {
    url: `${prefix2}/imageList`,
    method: 'get',
    des: '查询课程封面列表',
  },
  deleteVideoById: {
    url: `${prefix2}/deleteById`,
    method: 'post',
    des: '删除视频',
  },
  getUploadToken: {
    url: `${prefix2}/getVodSignature`,
    method: 'get',
    des: '获取上传凭证',
  },
};

export default sysVideoUploadApi;
