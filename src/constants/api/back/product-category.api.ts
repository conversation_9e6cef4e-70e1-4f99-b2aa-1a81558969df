const categoryPrefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/mall/category`;

/**
 * @description: 商品分类管理相关API
 * <AUTHOR>
 * @date 2025/1/19
 */
const productCategoryApi = {
  // 获取分类树形结构
  getCategoryTree: {
    url: `${categoryPrefix}/tree`,
    method: 'get',
    des: '获取完整的分类树形结构',
  },

  // 获取一级分类列表
  getTopCategories: {
    url: `${categoryPrefix}/top`,
    method: 'get',
    des: '获取所有一级分类列表',
  },

  // 根据父分类ID获取子分类列表
  getChildrenCategories: {
    url: `${categoryPrefix}/children`,
    method: 'get',
    des: '根据父分类ID获取子分类列表',
  },

  // 根据ID获取分类详情
  getCategoryDetail: {
    url: `${categoryPrefix}`,
    method: 'get',
    des: '根据分类ID获取分类详情',
  },

  // 保存分类（新增或更新）
  saveCategory: {
    url: `${categoryPrefix}/save`,
    method: 'post',
    des: '新增或更新分类信息',
  },

  // 删除分类
  deleteCategory: {
    url: `${categoryPrefix}`,
    method: 'delete',
    des: '删除指定分类',
  },

  // 获取所有分类的扁平列表
  getAllCategories: {
    url: `${categoryPrefix}/all`,
    method: 'get',
    des: '获取所有分类的扁平列表',
  },
};

export default productCategoryApi;
