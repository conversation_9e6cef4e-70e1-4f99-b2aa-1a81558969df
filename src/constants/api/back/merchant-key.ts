const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/wx/merchant-key`;

/**
 * @description: 微信商户管理api
 */
const merchantKeyApi = {
  queryMerchantKey: {
    url: `${prefix}/company`,
    method: 'get',
    des: '查询商户密钥',
  },
  updateMerchantKey: {
    url: `${prefix}/update`,
    method: 'put',
    des: '更新商户密钥',
  },
  saveMerchantKey: {
    url: `${prefix}/bind`,
    method: 'post',
    des: '绑定商户密钥',
  },
  deleteColumn: {
    url: `${prefix}/unbind`,
    method: 'delete',
    des: '删除栏目信息',
  },
};

export default merchantKeyApi;
