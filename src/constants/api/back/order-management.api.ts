const orderPrefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/mall/order`;

/**
 * @description: 订单管理相关API
 * <AUTHOR>
 * @date 2025/1/19
 */
const orderManagementApi = {
  // 分页查询订单列表
  queryOrderPage: {
    url: `${orderPrefix}/page`,
    method: 'post',
    des: '分页查询订单列表',
  },

  // 获取订单详情
  getOrderDetail: {
    url: `${orderPrefix}/`,
    method: 'get',
    des: '获取订单详情',
  },

  // 更新订单状态
  updateOrderStatus: {
    url: `${orderPrefix}/update-status`,
    method: 'post',
    des: '更新订单状态',
  },

  // 发货
  shipOrder: {
    url: `${orderPrefix}/ship`,
    method: 'post',
    des: '订单发货',
  },

  // 取消订单
  cancelOrder: {
    url: `${orderPrefix}/cancel`,
    method: 'post',
    des: '取消订单',
  },

  // 删除订单
  deleteOrder: {
    url: `${orderPrefix}/delete`,
    method: 'post',
    des: '删除订单',
  },

  // 批量删除订单
  batchDeleteOrder: {
    url: `${orderPrefix}/batch-delete`,
    method: 'post',
    des: '批量删除订单',
  },

  // 导出订单
  exportOrder: {
    url: `${orderPrefix}/export`,
    method: 'post',
    des: '导出订单数据',
  },

  // 获取订单统计
  getOrderStats: {
    url: `${orderPrefix}/statistics`,
    method: 'get',
    des: '获取订单统计数据',
  },

  // 获取物流信息
  getLogistics: {
    url: `${orderPrefix}/logistics`,
    method: 'get',
    des: '获取物流跟踪信息',
  },
};

export default orderManagementApi;
