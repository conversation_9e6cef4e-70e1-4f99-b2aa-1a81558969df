const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/mini-program`;
const adminfix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/company-mini-program`;

/**
 * @description: 小程序分组api
 */
const sysMiniProgramApi = {
  addMiniProgram: {
    url: `${prefix}/add`,
    method: 'post',
    des: '新增小程序',
  },
  deleteMiniProgram: {
    url: `${prefix}/delete`,
    method: 'post',
    des: '删除小程序',
  },
  updateMiniProgram: {
    url: `${prefix}`,
    method: 'put',

    des: '修改小程序',
  },
  queryMiniProgramListById: {
    url: `${prefix}/getById`,
    method: 'get',
    des: '查询小程序分组列表通过id',
  },
  queryMiniProgramListByPage: {
    url: `${prefix}/page`,
    method: 'get',
    des: '查询小程序分组列表通过分页',
  },
  toPayTest: {
    url: `${prefix}/wx-pay-test`,
    method: 'post',
    des: '新增商户微信转账测试',
  },
  getCompaniesByMiniProgramId: {
    url: `${adminfix}/companies`,
    method: 'get',
    des: '获取小程序关联的公司ID列表',
  },
  getMiniProgramsByCompanyId: {
    url: `${adminfix}/mini-programs`,
    method: 'get',
    des: '获取公司关联的小程序信息列表',
  },
  assignCompaniesToMiniProgram: {
    url: `${adminfix}/assign-companies`,
    method: 'post',
    des: '为小程序设置可访问的公司列表',
  },
};

export default sysMiniProgramApi;
