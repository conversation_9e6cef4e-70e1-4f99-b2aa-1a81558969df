const productPrefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/mall/product`;

/**
 * @description: 商品管理相关API
 * <AUTHOR>
 * @date 2025/1/19
 */
const productManagementApi = {
  // 分页查询商品列表
  queryProductPage: {
    url: `${productPrefix}/page`,
    method: 'post',
    des: '分页查询商品列表',
  },

  // 获取商品详情
  getProductDetail: {
    url: `${productPrefix}/`,
    method: 'get',
    des: '获取商品详情',
  },

  // 保存商品（新增或更新）
  saveProduct: {
    url: `${productPrefix}/save`,
    method: 'post',
    des: '保存商品信息',
  },

  // 删除商品
  deleteProduct: {
    url: `${productPrefix}/delete`,
    method: 'post',
    des: '删除商品',
  },

  // 批量删除商品
  batchDeleteProduct: {
    url: `${productPrefix}/batch-delete`,
    method: 'post',
    des: '批量删除商品',
  },

  // 更新商品状态
  updateProductStatus: {
    url: `${productPrefix}/update-status`,
    method: 'post',
    des: '更新商品状态',
  },

  // 批量更新商品状态
  batchUpdateStatus: {
    url: `${productPrefix}/batch-update-active`,
    method: 'post',
    des: '批量更新商品状态',
  },

  // 上架商品
  publishProduct: {
    url: `${productPrefix}/publish`,
    method: 'post',
    des: '上架商品',
  },

  // 下架商品
  unpublishProduct: {
    url: `${productPrefix}/update-status`,
    method: 'post',
    des: '下架商品',
  },

  // 复制商品
  copyProduct: {
    url: `${productPrefix}/copy`,
    method: 'post',
    des: '复制商品',
  },

  // 导出商品
  exportProduct: {
    url: `${productPrefix}/export`,
    method: 'post',
    des: '导出商品数据',
  },

  // 获取商品统计
  getProductStats: {
    url: `${productPrefix}/statistics`,
    method: 'get',
    des: '获取商品统计数据',
  },

  // 上传商品图片
  uploadProductImage: {
    url: `${productPrefix}/upload-image`,
    method: 'post',
    des: '上传商品图片',
  },
};

export default productManagementApi;
