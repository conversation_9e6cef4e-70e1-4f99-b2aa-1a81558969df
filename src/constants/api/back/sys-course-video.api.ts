const prefix = `${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/course-video`;

/**
 * @description: 课程分组api
 */
const sysCourseGroupUploadApi = {
  addCourse: {
    url: `${prefix}`,
    method: 'post',

    des: '新增课程',
  },
  deleteCourse: {
    url: `${prefix}/deleteById`,
    method: 'post',

    des: '删除课程',
  },
  updateCourse: {
    url: `${prefix}/updateById`,
    method: 'post',

    des: '修改课程',
  },
  queryCourseListById: {
    url: `${prefix}/`,
    method: 'get',

    des: '查询课程列表通过课程id',
  },
  queryCourseListByPage: {
    url: `${prefix}/page`,
    method: 'get',

    des: '查询课程列表通过分页',
  },
  removeCourseById: {
    url: `${prefix}/removeShellById`,
    method: 'post',

    des: '下架视频',
  },

  batchCourseByGroupId: {
    url: `${prefix}/batch`,
    method: 'post',

    des: '批量复制',
  },
};

export default sysCourseGroupUploadApi;
