const prefix = `${import.meta.env.VITE_APP_STORE_API}${import.meta.env.VITE_APP_API_PREFIX}/mall/refund`;
/**
 * @description: 退款管理相关API
 * <AUTHOR>
 * @date 2025/09/08
 */
const mallRefundApi = {
  // 申请退款
  applyRefund: {
    url: `${prefix}/apply`,
    method: 'post',
    des: '申请退款',
  },

  // 查询我的退款列表
  getMyRefundList: {
    url: `${prefix}/my-list`,
    method: 'post',
    des: '查询我的退款列表',
  },

  // 获取退款详情
  getRefundDetail: {
    url: `${prefix}/detail`,
    method: 'get',
    des: '获取退款详情',
  },

  // 商家审核退款
  auditRefund: {
    url: `${prefix}/audit`,
    method: 'post',
    des: '商家审核退款',
  },

  // 查询商家待处理退款列表
  getPendingRefundList: {
    url: `${prefix}/pending-list`,
    method: 'get',
    des: '查询商家待处理退款列表',
  },

  // 检查订单退款资格
  checkRefundEligibility: {
    url: `${prefix}/check-eligibility`,
    method: 'get',
    des: '检查订单退款资格',
  },

  // 查询退款统计
  getRefundStats: {
    url: `${prefix}/stats`,
    method: 'get',
    des: '查询退款统计',
  },

  // 微信退款结果通知回调
  wxRefundCallback: {
    url: `${prefix}/wx-callback`,
    method: 'post',
    des: '微信退款结果通知回调',
  },

  // 查询退款状态
  getRefundStatus: {
    url: `${prefix}/status`,
    method: 'get',
    des: '查询退款状态',
  },

  // 根据订单ID获取退款信息
  getRefundByOrderId: {
    url: `${prefix}/by-order`,
    method: 'get',
    des: '根据订单ID获取退款信息',
  },

  // 批量审核退款
  batchAuditRefund: {
    url: `${prefix}/batch-audit`,
    method: 'post',
    des: '批量审核退款',
  },

  // 查询退款列表（管理端）
  getRefundList: {
    url: `${prefix}/list`,
    method: 'post',
    des: '查询退款列表（管理端）',
  },
};

export default mallRefundApi;
