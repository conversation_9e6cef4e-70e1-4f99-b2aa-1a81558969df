const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/sys-role-menu`;

/**
 * @Description: 角色-菜单API
 * <AUTHOR>
 * @date 2025/5/7
 * @time 23:41
 */
const systemRoleMenuApi = {
  getRoleMenu: {
    url: `${prefix}/get-system-role-menu-page`,
    method: 'post',
    des: '获取当前角色可分配的系统菜单列表',
  },
  saveRoleMenu: {
    url: `${prefix}/save-system-role-menu`,
    method: 'post',
    des: '保存角色菜单关联关系',
  },
};

export default systemRoleMenuApi;
