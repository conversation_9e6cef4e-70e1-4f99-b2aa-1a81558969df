const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/customer-wecom-tag`;

/**
 * @Description: 标签管理API
 * @time 20:43
 */
const customerWecomTagsApi = {
  // 获取本地标签数据
  getLocalTags: {
    url: `${prefix}/local`,
    method: 'get',
    des: '获取本地标签数据',
  },
  // 查询标签同步记录
  getSyncRecords: {
    url: `${prefix}/sync-records`,
    method: 'get',
    des: '查询标签同步记录',
  },
  // 同步标签数据
  syncTags: {
    url: `${prefix}/list`,
    method: 'post',
    des: '同步标签数据',
  },
  // 新增标签或标签组
  addTag: {
    url: `${prefix}/add`,
    method: 'post',
    des: '新增标签或标签组',
  },
  // 编辑标签或标签组
  updateTag: {
    url: `${prefix}/edit`,
    method: 'put',
    des: '编辑标签或标签组',
  },
  // 删除标签
  deleteTag: {
    url: `${prefix}/delete`,
    method: 'post',
    des: '删除标签',
  },
  markCustomerTag: {
    url: `${prefix}/mark`,
    method: 'post',
    des: '打企微标签',
  },
  batchMarkCustomerTag: {
    url: `${prefix}/batch-mark`,
    method: 'post',
    des: '批量打企微标签',
  },
  // 删除标签组
  deleteTagGroup: {
    url: `${prefix}/delete`,
    method: 'post',
    des: '删除标签组',
  },
};

export default customerWecomTagsApi;
