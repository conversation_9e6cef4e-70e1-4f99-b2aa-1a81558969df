const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}`;

/**
 * @Description: 微信、订单、用户管理API
 */
const orderAndUserApi = {
  queryExternalAccountOrderPage: {
    url: `${prefix}/external-account-order/query/page`,
    method: 'get',
    des: '分页查询互通账号订单列表',
  },
  queryExternalAccountOrder: {
    url: `${prefix}/external-account-order/`,
    method: 'get',
    des: '根据ID查询订单',
  },
  deleteAccountOrder: {
    url: `${prefix}/external-account-order/delete/`,
    method: 'delete',
    des: '删除订单',
  },
  queryAccountById: {
    url: `${prefix}/external-account/`,
    method: 'get',
    des: '根据ID查询互通账号',
  },
  batchActivateAccount: {
    url: `${prefix}/external-account/batchActivation`,
    method: 'post',
    des: '批量激活账号（不可跨企业批量激活）',
  },
  queryAccountList: {
    url: `${prefix}/external-account/query/page`,
    method: 'get',
    des: '查询互通账号列表',
  },
  deleteActiveAccount: {
    url: `${prefix}/external-account/deleteById`,
    method: 'post',
    des: '删除互通账号',
  },
  getActivationCode: {
    url: `${prefix}/external-account-order/getActivationCode`,
    method: 'get',
    des: '手动获取订单中激活码',
  },
  createOrder: {
    url: `${prefix}/external-account-order/create`,
    method: 'post',
    des: '创建订单',
  },
  createRenewalOrder: {
    url: `${prefix}/external-account-order/createRenewal`,
    method: 'post',
    des: '创建续费订单',
  },
  queryQyAccountList: {
    url: `${prefix}/systemUserQyRelation/query-qy-user-page`,
    method: 'get',
    des: '分页查询企微员工信息列表',
  },
  queryQyList: {
    url: `${prefix}/companyQyRelation/query-qy-list`,
    method: 'get',
    des: '查询企微列表',
  },
  generateQyContactQrCode: {
    url: `${prefix}/systemUserQyRelation/generate-qy-contact-qr-code`,
    method: 'post',
    des: '生成企微联系人二维码',
  },
  queryActivationCode: {
    url: `${prefix}/external-account/queryActivationCode/`,
    method: 'get',
    des: '根据订单号查询激活码',
  },
  saveQyContact: {
    url: `${prefix}/systemUserQyRelation/save-qy-user`,
    method: 'post',
    des: '企微账号下员工数据同步',
  },
  queryCompanyUnionQy: {
    url: `${prefix}/companyQyRelated/query-company-bind-qy`,
    method: 'post',
    des: '查询公司下关联的企微',
  },
  batchCompanyBindQy: {
    url: `${prefix}/companyQyRelated/save-company-qy-bind-batch`,
    method: 'post',
    des: '公司批量绑定企微',
  },
  queryQyListAuth: {
    url: `${prefix}/companyQyRelation/query-qy-list-auth`,
    method: 'post',
    des: '查询企微列表权限',
  },
  queryBindQyUserList: {
    url: `${prefix}/systemUserQyRelation/query-qy-user-to-system-user`,
    method: 'post',
    des: '查询已同步企微员工信息',
  },
  userBindQy: {
    url: `${prefix}/systemUserQyRelation/bind-qy-user-to-system-user`,
    method: 'post',
    des: '员工绑定企微',
  },
  transferExternalAccount: {
    url: `${prefix}/external-account/transferExternalAccount`,
    method: 'post',
    des: '账号继承',
  },
  queryInactiveAndExpiredUsers: {
    url: `${prefix}/systemUserQyRelation/query-inactive-and-expired-users`,
    method: 'get',
    des: '查询未激活和过期用户列表',
  },
};

export default orderAndUserApi;
