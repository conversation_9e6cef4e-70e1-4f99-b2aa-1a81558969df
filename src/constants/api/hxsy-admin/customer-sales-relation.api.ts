const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/customer-sales-relation`;

const sysPrefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/systemUserQyRelation`;

/**
 * @Description: 客户销售关系API
 * <AUTHOR>
 * @date 2025/4/19
 * @time 15:41
 */
const customerSalesRelationApi = {
  assignCustomer: {
    url: `${prefix}/assign`,
    method: 'get',
    des: '分配客户给销售人员',
  },
  listByCustomerId: {
    url: `${prefix}/list-by-customer-id`,
    method: 'get',
    des: '通过客户ID查询客户销售关系列表',
  },
  unbindQyUserToSystemUser: {
    url: `${sysPrefix}/unbind-qy-user-to-system-user`,
    method: 'post',
    des: '取消员工与单个企微账号间的关联',
  }
};

export default customerSalesRelationApi;
