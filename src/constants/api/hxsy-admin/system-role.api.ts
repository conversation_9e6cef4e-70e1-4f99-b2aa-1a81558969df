const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/sysRole`;

/**
 * @Description: 角色管理API
 * <AUTHOR>
 * @date 2025/4/19
 * @time 15:41
 */
const systemRoleApi = {
  queryPage: {
    url: `${prefix}/get-system-role-page`,
    method: 'post',
    des: '分页查询系统已有角色列表',
  },
  saveOrUpdateRole: {
    url: `${prefix}/save-or-update-role`,
    method: 'post',
    des: '新增或更新系统角色',
  },
};

export default systemRoleApi;
