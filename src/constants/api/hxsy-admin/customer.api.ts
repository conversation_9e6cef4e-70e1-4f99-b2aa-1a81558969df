const prefix = `${import.meta.env.VITE_APP_ADMIN_API}${import.meta.env.VITE_APP_API_PREFIX}/customer`;

/**
 * @Description: 客户管理API
 * <AUTHOR>
 * @date 2025/4/19
 * @time 15:41
 */
const customerApi = {
  queryPage: {
    url: `${prefix}/query/page`,
    method: 'post',
    des: '分页查询客户列表',
  },
  updStatus: {
    url: `${prefix}/upd-status`,
    method: 'post',
    des: '更新客户状态',
  },
  delCustomer: {
    url: `${prefix}/del-customer`,
    method: 'post',
    des: '删除客户',
  },
  getCustomerDetail: {
    url: `${prefix}/get-customer-detail`,
    method: 'get',
    des: '获取客户详情',
  },
  batchRemark: {
    url: `${prefix}/batch-remark`,
    method: 'post',
    des: '批量设置客户企微备注',
  },
  updCourseCustomerRelStatus: {
    url: `${prefix}/upd-course-customer-rel-status`,
    method: 'post',
    des: '更新客户课程关系',
  }
};

export default customerApi;
