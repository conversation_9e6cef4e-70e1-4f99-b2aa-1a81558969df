interface IOption {
  value: number | string;
  label: string;
}

export const API_CODE = {
  SUCCESS: 1,
};

// 合同状态枚举
export const CONTRACT_STATUS = {
  FAIL: 0,
  AUDIT_PENDING: 1,
  EXEC_PENDING: 2,
  EXECUTING: 3,
  FINISH: 4,
};

// 使用状态
export const STATUS = {
  INVALID: { code: '0', name: '删除' },
  EFFECTIVE: { code: '1', name: '有效' },
  FROZEN: { code: '2', name: '禁用' },
};
// 使用状态
export const STATUS_OPTIONS = [
  { value: 1, label: '有效', theme: 'success' },
  { value: 0, label: '禁用', theme: 'danger' },
];
export const  USER_STATE_OPTIONS= [
  { value: 0, label: '禁用', theme: 'danger' },
  { value: 1, label: '有效', theme: 'success' },
];
export const  AUDIT_STATE_OPTIONS= [
  { value: 0, label: '待审核', theme: 'danger' },
  { value: 1, label: '不通过', theme: 'danger' },
  { value: 2, label: '已通过', theme: 'success' },
];

// 上架状态
export const COURSE_STATE_OPTIONS = [
  { value: '0', label: '下架' },
  { value: '1', label: '上架' },
];
// 性别
export const SEX = [
  { value: '1', label: '男' },
  { value: '2', label: '女' },
];

// 日期
export const DATE_OPTIONS = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
  { value: 7, label: '周日' },
];


// 活跃行为类型 （1-客户注册 2-训练营营期报名 3-训练营视频课学习 4-课后答题 5-领取红包 6-添加企微 7-删除企微 8-加入群聊）
export const ACTIVE_TYPE_OPTIONS = [
  { value: 1, label: '客户注册' },
  { value: 2, label: '训练营营期报名' },
  { value: 3, label: '训练营视频课学习' },
  { value: 4, label: '课后答题' },
  { value: 5, label: '领取红包' },
  { value: 6, label: '添加企微' },
  { value: 7, label: '删除企微' },
  { value: 8, label: '加入群聊' },
]

