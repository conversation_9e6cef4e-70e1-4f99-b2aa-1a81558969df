/**
 * @description: 商城驾驶舱相关类型定义
 * <AUTHOR>
 * @date 2025/1/22
 */

// 通用响应结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 首页概览数据类型
export interface OverviewData {
  todayOrders: number;
  todayOrdersGrowth: string;
  todayRevenue: number;
  todayRevenueGrowth: string;
  totalUsers: number;
  totalUsersGrowth: string;
  avgOrderAmount: number;
  avgOrderAmountGrowth: string;
  totalProducts: number;
  totalProductsGrowth: string;
  totalStores: number;
  totalStoresGrowth: string;
  pendingOrders: number;
  lowStockProducts: number;
  monthlyOrders: number;
  monthlyRevenue: number;
  yesterdayOrders: number;
  yesterdayRevenue: number;
  lastMonthOrders: number;
  lastMonthRevenue: number;
}

// 订单趋势数据项类型
export interface OrderTrendItem {
  date: string;
  orderCount: number;
  orderAmount: number;
  dateLabel: string;
  dayOfWeek: number;
  isToday: boolean;
  growthRate: string;
}

// 订单趋势查询参数
export interface OrderTrendParams {
  period?: 7 | 30 | 90; // 时间周期：7-近7天，30-近30天，90-近3月
}

// 订单状态分布数据项类型
export interface OrderStatusDistributionItem {
  statusName: string;
  statusCode: string;
  count: number;
  percentage: string;
  color: string;
  sortOrder: number;
}

// 销售数据统计查询参数
export interface SalesStatsParams {
  period?: 'today' | 'week' | 'month'; // 统计周期：today-今日，week-本周，month-本月
}

// 销售数据统计响应类型
export interface SalesStatsData {
  orderCount: number;
  totalAmount: number;
  avgAmount: number;
  customerCount: number;
}

// 接口响应类型定义
export type OverviewResponse = ApiResponse<OverviewData>;
export type OrderTrendResponse = ApiResponse<OrderTrendItem[]>;
export type OrderStatusDistributionResponse = ApiResponse<OrderStatusDistributionItem[]>;
export type RealtimeResponse = ApiResponse<OverviewData>;
export type TodayComparisonResponse = ApiResponse<OverviewData>;
export type SalesStatsResponse = ApiResponse<SalesStatsData>;
