/**
 * @description: 秒数转换为时分秒
 */
function convertSecond(time: number) {
  if (time) {
    const seconds = Math.ceil(time);
    const hour = Math.floor(seconds / 3600); // 小时
    const minute = Math.floor((seconds % 3600) / 60); // 分钟
    const second = (seconds % 3600) % 60; // 秒
    return `${hour > 9 ? hour : `0${hour}`}:${minute > 9 ? minute : `0${minute}`}:${
      second > 9 ? second : `0${second}`
    }`;
  }
  return '--';
}

const secondConvertFilter = (time: number) => convertSecond(time);

export default secondConvertFilter;
