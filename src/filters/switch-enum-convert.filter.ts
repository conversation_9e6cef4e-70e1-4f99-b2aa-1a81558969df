/**
 * @description: 通用字典转换
 */
function encode(code: string, attribute?: [], resultKey?: string, defaultVal?: any) {
  let result = '';
  attribute.forEach((element: any) => {
    if (code === element.value) {
      if (resultKey) {
        result = element[resultKey];
      }else{
        result = element.label;
      }
    }
  });
  return result || defaultVal || '';
}
const switchEnumConvert = (code: string, attribute?: [], resultKey?: string, defaultVal?: any) =>
  code != null ? encode(code, attribute, resultKey, defaultVal) : '';

export default switchEnumConvert;
