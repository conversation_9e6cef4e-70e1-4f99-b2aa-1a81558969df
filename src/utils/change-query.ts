function updateQueryString(
  paramsToUpdate: Record<string, string | number | undefined | null>,
  router: any,
  route: any,
): Promise<void> {
  // 获取当前 URL
  const currentUrl = window.location.href;

  // 解析当前 URL 中的查询参数
  const url = new URL(currentUrl);

  // 更新或添加要更改的查询参数
  for (const param in paramsToUpdate) {
    // eslint-disable-next-line no-prototype-builtins
    if (paramsToUpdate.hasOwnProperty(param)) {
      const value = paramsToUpdate[param];
      if (value === null || value === undefined || value === '') {
        url.searchParams.delete(param);
      } else {
        url.searchParams.set(param, `${value}`);
      }
    }
  }
  const { searchParams } = url;
  // 使用 history.pushState 更新 URL
  // window.history.pushState({}, "", url.toString());
  const paramsObject: any = {};
  searchParams.forEach((value, key) => {
    console.log(key, '===', value);
    paramsObject[key] = value;
  });
  return updateQueryStringWidthRouter(router, route, paramsObject);
}
function updateQueryStringWidthRouter(router: any, route: any, query: any) {
  return new Promise<void>((resolve) => {
    router.push(
      {
        path: route.path,
        query: {
          ...query,
        },
      },
      () => resolve(),
    );
  });
}
export default updateQueryString;
