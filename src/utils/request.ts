import axios from 'axios';
import { MessagePlugin } from 'tdesign-vue';

const env = import.meta.env.MODE || 'development';

const API_HOST = '/gateway'; // 如果是mock模式 就不配置host 会走本地Mock拦截

const CODE = {
  LOGIN_TIMEOUT: 1000,
  REQUEST_SUCCESS: [0, 1, '0'],
  REQUEST_FOBID: 1001,
  UNLOGIN: 500,
};
let token: any = {};
try {
  token = window.localStorage.getItem('token');
  console.log(token);
} catch (e) {
  console.log(e);
}

const instance = axios.create({
  baseURL: API_HOST,
  timeout: 60000,
  withCredentials: true,
  headers: {
    'token': token,
  },
});

// eslint-disable-next-line
// @ts-ignore
// axios的retry ts类型有问题
instance.interceptors.retry = 3;

instance.interceptors.request.use((config) => config);

instance.interceptors.response.use(
  (response) => {
    // 二进制数据直接返回
    if (response.headers['content-type'] === 'multipart/form-data') {
      return response.data;
    }
    if (response.status === 200) {
      const { data } = response;
      if (CODE.REQUEST_SUCCESS.includes(data.code)) {
        return data;
      }
      // header的auth是none，不需要登录
      if (response.config.headers.auth === 'none') {
        return data;
      }
      if (data.code === CODE.UNLOGIN) {
        // 未登录
        MessagePlugin.warning({ content: '登录已失效，请重新登录', duration: 2000 });
        const baseUrl = import.meta.env.VITE_BASE_URL || '/';
        const backUrl = window.location.pathname.replace(baseUrl, '/') || '/';
        const { search } = window.location || '';
        const user = localStorage.getItem('core:user');
        if (backUrl === '' || backUrl === '/login' || backUrl === 'login') {
          window.location.replace(`${baseUrl}login`);
        } else {
          window.location.replace(
            `${baseUrl}login?redirect=${encodeURIComponent(backUrl + search)}`,
          );
        }
      }
      const msg = data.msg || data.message || `请求失败，错误码【${data.code}】`;

      if (data.code !== 9001) {
        MessagePlugin.error({ content: msg });
      }

      return Promise.reject(response);
    }
  },
  (err) => {
    const { config, message } = err;

    if (!config || !config.retry) {
      MessagePlugin.error({ content: message });
      return Promise.reject(err);
    }

    config.retryCount = config.retryCount || 0;

    if (config.retryCount >= config.retry) {
      MessagePlugin.error({ content: message });
      return Promise.reject(err);
    }

    config.retryCount += 1;

    const backoff = new Promise((resolve) => {
      setTimeout(() => {
        resolve({});
      }, config.retryDelay || 1);
    });

    return backoff.then(() => instance(config));
  },
);

export default instance;
