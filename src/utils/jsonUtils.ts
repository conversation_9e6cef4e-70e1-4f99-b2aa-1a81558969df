import { JsonConvert, OperationMode, ValueCheckingMode } from 'json2typescript';

export const jsonToClass = (jsonObject: any, clazz?: any): any => {
  const jsonConvert: JsonConvert = new JsonConvert();
  jsonConvert.operationMode = OperationMode.ENABLE; // print some debug data
  jsonConvert.ignorePrimitiveChecks = true; // don't allow assigning number to string etc.
  jsonConvert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL; // never allow null

  try {
    return jsonConvert.deserialize(jsonObject, clazz);
  } catch (e) {
    console.log(<Error>e);
    throw new Error('Fatal error in JsonConvert');
  }
};
