const downloadFile = async (path: any = '', fileName = '') => {
  let resp;
  let blob;
  let name;

  if (path instanceof Blob) {
    // 直接是二进制数据
    blob = path;
    if ((blob as any).headers) {
      const nameString = (blob as any)?.headers['content-disposition'] || '';
      if (nameString) {
        if (nameString.split("''")[1]) {
          name = decodeURIComponent(nameString.split("''")[1]);
        }

        // name = nameString;
      }
    }
  } else {
    try {
      resp = await fetch(path);
      blob = await resp.blob();
    } catch (e: any) {
      throw Error('下载失败');
    }
  }

  const link = document.createElement('a');
  if (!name) {
    name = fileName;
  }

  if (!name) {
    try {
      name = path.substring(path.lastIndexOf('/') + 1, path.length);
    } catch (e) {
      name = '文件';
    }
  }

  link.setAttribute('href', URL.createObjectURL(blob));
  link.setAttribute('download', name);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export default downloadFile;
