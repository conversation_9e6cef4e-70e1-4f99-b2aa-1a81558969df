import axios from "axios";
import Vuex from "vuex";
import TcVod from "vod-js-sdk-v6";
import {debounce} from "lodash-es";
import { uuidService } from "@/service/uuid.service";


class VodUploadManager {
  private static store: any;

  private static instance: VodUploadManager;

  private uploadSign: any;

  private readonly MAX_CONCURRENT = 3; // 并发控制

  private activeUploads = 0;

  public static getInstance(): VodUploadManager {
    if (!VodUploadManager.instance) {
      VodUploadManager.instance = new VodUploadManager();
    }
    return VodUploadManager.instance;
  }

  private debounceProcessQueue = debounce(async () => {
    await this.processQueue();
  }, 50);

  public async addFiles(filesData: any, store: Vuex.Store<any>) {

    const { files, groupId } = filesData;
    this.store = store;
    this.uploadSign = 'NyUHiN5+gt3o8D2DgWYv/Tssl+hzZWNyZXRJZD1BS0lEMzV2WjNMY2xkTzNYcHRqUHY0N3pmOFFhWk16UmhrYUYmY3VycmVudFRpbWVTdGFtcD0xNzQ5MzAwMjYzJmV4cGlyZVRpbWU9MTc0OTMwMzg2MyZyYW5kb209NjQ1OTYyNzE='; // 云点播上传凭证
    console.error(this.uploadSign);
    const tasks = await Promise.all(files.map(async (file: any, index) => ({
      id: uuidService.getUuid(),
      groupId,
      file,
      name: file.name,
      size: file.size,
      status: 'pending',
      progress: 0,
      retries: 0
    })));
    console.error(tasks);
    store.commit('ADD_UPLOAD_TASKS', tasks);
    store.commit('SET_SHOW_UPLOAD_LIST', true);
    this.processQueue();
  }

  // 获取云点播上传凭证
  private async getUploadCredentials() {
    if (!this.uploadSign) {
      const res = await axios.get(`/gateway${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/video_upload/getVodSignature`, {
        headers: { 'token': window.localStorage.getItem('token') || '' },
      });
      console.error(res);
      if (res.status === 200 && res.data.code === 0) {
        this.uploadSign = res.data.data;
      } else {
        this.uploadSign = "";
      }
    }
    return this.uploadSign;
  }

  // 上传视频
  private async uploadVideoCloud(task: any) {
    const { file, id, groupId } = task;
    const tcVod = new TcVod({
      getSignature: this.getUploadCredentials,
      appId: 1351054319,
    });
    const uploader = tcVod.upload({
      mediaFile: file.raw, // 媒体文件（视频或音频或图片），类型为 File
    })
    // 视频上传完成时
    // uploader.on('media_upload', (info) => {
    //   console.log('media_upload', info);
    // })
    // 视频上传进度
    uploader.on('media_progress', (info) => {
      console.log('media_progress', info);
      this.store.commit('UPDATE_PROGRESS', {id, progress: Math.round(info.percent * 100)});
    })
    uploader.done().then(async (doneResult) => {
      // 将doneResult的video.url请求接口保存到数据库
      const params = {
        videoGroupid: groupId,
        videoName: file.name,
        videoUrl: doneResult.video.url,
        videoDuration: "",
        videoSize: (file.size / 1048576).toFixed(2),
      };
      const res = await this.saveVideo(params);
      if (res.status === 200 && res.data.code === 0) {
        // 请求成功后更新task.status为success
        this.store.commit('SET_TASK_SUCCESS', task);
      } else {
        const error = new Error("save video failed");
        error.params = params; // 将params附加到错误对象上
        throw error;
      }
    }).catch(async (error) => {
      if (error.message && error.message.includes("signature expired")) this.uploadSign = "";
      if (error.message && error.message.includes("save video failed")) {
        // 保存视频失败，重试一次
        const res = await this.saveVideo(error.params);
        if (res.status === 200 && res.data.code === 0) {
          this.store.commit('SET_TASK_SUCCESS', task);
        }
      }
      this.handleUploadError(task, error);
    }).finally(() => {
      this.activeUploads -= 1;
      this.store.commit('UNLOCK_TASK', task.id);
      this.debounceProcessQueue(); // 每个任务完成都触发队列检查
    })
  }

  private async processQueue() {
    // 计算当前可并行任务数
    const parallelCount = this.MAX_CONCURRENT - this.activeUploads;
    if (parallelCount <= 0) return;
    const tasks = this.store.getters.nextPendingTasks(parallelCount);
    console.error(tasks);
    if (!tasks.length) return;
    // 批量锁定任务
    tasks.forEach((task: any) => this.store.commit('LOCK_TASK', task.id));
    this.activeUploads += tasks.length;

    const uploadOperations = tasks.map((task: any) =>
      this.uploadVideoCloud(task)
        .catch((error: any) => {
          console.error(`Task ${task.id} failed:`, error);
          this.handleUploadError(task, error);
        })

    );
    // 启动并行上传
    await Promise.allSettled(uploadOperations);
  }

  handleUploadError(task: any, error: any) {
    console.error(error.code, error.message, error.status);
    // 更新task.retries
    task.retries += 1;
    // 上传失败
    this.store.commit('SET_TASK_ERROR', task);
  }

  // 保存视频
  private async saveVideo(params: any) {
    return axios.post(`/gateway${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/video_upload/save`, params, {
      headers: { 'token': window.localStorage.getItem('token') || '' },
    });
  }
}
const vodUploadManager = new VodUploadManager();
export default vodUploadManager;
