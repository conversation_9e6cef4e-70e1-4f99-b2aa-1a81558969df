// string to object
const string2Query = (search: string): Record<string, string> => {
  if (!search) return {};
  if (search[0] === "?") search = search.substring(1);

  const queries = search.split("&");

  const query: Record<string, string> = {};
  queries.forEach((v) => {
    const [key, val] = v.split("=");
    query[decodeURIComponent(key)] = val && decodeURIComponent(val);
  });
  return query;
};

// 将一个对象转换为 xxx=qqq&fff=eee 的形式
const query2String = (obj: Record<string, string>, needEncode = true): string => {
  if (!obj) return "";

  const temp = Object.keys(obj).map((key) => `${key}=${needEncode ? encodeURIComponent(obj[key]) : obj[key]}`);

  return temp.join("&");
};

export { query2String, string2Query };
