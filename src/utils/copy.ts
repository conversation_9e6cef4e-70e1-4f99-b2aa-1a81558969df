/**
 * 复制字符串到剪贴板
 */
async function copyToClipboard(text: string): Promise<boolean> {
  if (navigator.clipboard) {
    try {
      await navigator.clipboard.writeText(text);
      console.log("已成功复制到剪贴板:", text);
      return true; // 表示复制成功
    } catch (err) {
      console.error("复制到剪贴板失败:", err);
      return false; // 表示复制失败
    }
  } else if (document.queryCommandSupported("copy") && document.execCommand) {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.setAttribute("readonly", "");
    textarea.style.position = "absolute";
    textarea.style.left = "-9999px";
    document.body.appendChild(textarea);
    textarea.select();

    try {
      const result = document.execCommand("copy");
      console.log("已成功复制到剪贴板:", text);
      return result; // 表示复制成功
    } catch (err) {
      console.error("复制到剪贴板失败:", err);
      return false; // 表示复制失败
    } finally {
      document.body.removeChild(textarea);
    }
  } else {
    console.warn("浏览器不支持复制到剪贴板功能");
    return false; // 表示复制失败
  }
}

// // 示例用法
// const originalString = "Hello, World!";
// copyToClipboard(originalString).then((success) => {
//   if (success) {
//     // 复制成功的处理逻辑
//   } else {
//     // 复制失败的处理逻辑
//   }
// });

export default copyToClipboard;
