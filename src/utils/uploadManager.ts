import axios from "axios";
import Vuex from "vuex";
import { debounce } from "lodash-es";
import { uuidService } from "@/service/uuid.service";


class UploadManager {
  private static store: any;

  private static instance: UploadManager;

  private readonly MAX_CONCURRENT = 3; // 并发控制

  private activeUploads = 0;

  public static getInstance(): UploadManager {
    if (!UploadManager.instance) {
      UploadManager.instance = new UploadManager();
    }
    return UploadManager.instance;
  }

  private debounceProcessQueue = debounce(async () => {
    await this.processQueue();
  }, 50);

  public async addFiles(filesData: any, store: Vuex.Store<any>) {

    const { files, groupId } = filesData;
    const filesNameArray = files.map((file: any) => file.name);
    this.store = store;
    // 分片处理
    const tasks = await Promise.all(files.map(async (file: any, index) => ({
      id: uuidService.getUuid(),
      groupId,
      file,
      name: file.name,
      size: file.size,
      fileNames: filesNameArray,
      chunks: this.createChunks(file.raw),
      currentChunk: 0,
      status: 'pending',
      progress: 0,
      retries: 0
    })));
    console.error(tasks);

    store.commit('ADD_UPLOAD_TASKS', tasks);
    store.commit('SET_SHOW_UPLOAD_LIST', true);
    this.processQueue();
  }

  private createChunks(file: File): Blob[] {
    const chunkSize = 10 * 1024 * 1024; // 50MB分片
    const chunks = [];
    let offset = 0;

    while (offset < file.size) {
      chunks.push(file.slice(offset, offset + chunkSize));
      offset += chunkSize;
    }
    return chunks;
  }

  private async processQueue() {
    // 计算当前可并行任务数
    const parallelCount = this.MAX_CONCURRENT - this.activeUploads;
    if (parallelCount <= 0) return;
    const tasks = this.store.getters.nextPendingTasks(parallelCount);
    if (!tasks.length) return;
    // 批量锁定任务
    tasks.forEach((task: any) => this.store.commit('LOCK_TASK', task.id));
    this.activeUploads += tasks.length;

    const uploadOperations = tasks.map((task: any) =>
      this.uploadChunk(task) // 保持原有分片逻辑
        .then(response => {
          if (response &&response?.data.code !== 0) {
            throw new Error(`Upload failed with code ${response.data.code}`);
          }
          this.handleUploadSuccess(task, response);
        })
        .catch((error: any) => {
          console.error(`Task ${task.id} failed:`, error);
          this.handleUploadError(task, error);
        })
        .finally(() => {
          this.activeUploads -= 1;
          this.store.commit('UNLOCK_TASK', task.id);
          this.debounceProcessQueue(); // 每个任务完成都触发队列检查
        })
    );
    // 启动并行上传
    await Promise.allSettled(uploadOperations);
  }

  private async uploadChunk(task: any) {
    if (task.currentChunk >= task.chunks.length) {
      console.warn('Invalid chunk index:', task.currentChunk);
      return;
    }
    const chunk = task.chunks[task.currentChunk];
    const formData = new FormData();
    task.fileNames.forEach((name: string) => formData.append("fileNamesArray", name));
    formData.append('fileNames', task.file.name);
    formData.append('partNumbers', (task.currentChunk + 1).toString());
    formData.append('totalParts', task.chunks.length.toString());
    formData.append('groupId', task.groupId);
    formData.append('files', chunk);

    return axios.post(`/gateway${import.meta.env.VITE_APP_BUSINESS_API}${import.meta.env.VITE_APP_API_PREFIX}/video_upload/batchUploadParts`, formData, {
      headers: { 'Content-Type': 'multipart/form-data', 'token': window.localStorage.getItem('token') || '' },
      onUploadProgress: (progressEvent) => {
        const freshTask = this.store.getters.getTaskById(task.id);
        const currentChunk = freshTask?.currentChunk || 0;
        const chunkProgress = (progressEvent.loaded / progressEvent.total) * 100;
        const totalProgress =
          (currentChunk / freshTask.chunks.length) * 100 +
          (chunkProgress / freshTask.chunks.length);
        this.store.commit('UPDATE_PROGRESS', { id: task.id, progress: Math.round(totalProgress) });
      }
    });
  }

  handleUploadSuccess(task: any, response: any) {
    // 更新task.currentChunk
    const freshTask = this.store.getters.getTaskById(task.id);
    if (!freshTask) return;
    if (response.data.code === 0) {
      const nextChunk = freshTask.currentChunk + 1;
      if (nextChunk < freshTask.chunks.length) {
        task.currentChunk = nextChunk;
        this.store.commit('SET_TASK_CHUNK', { id: freshTask.id, currentChunk: nextChunk });
      } else {
        this.store.commit('SET_TASK_SUCCESS', task);
      }
    } else {
      this.handleUploadError(task, new Error('Unexpected success status'));
    }
  }

  handleUploadError(task: any, error: any) {
    // 更新task.retries
    task.retries += 1;
    // 上传失败
    this.store.commit('SET_TASK_ERROR', task);
  }
}
const uploadManager = new UploadManager();
export default uploadManager;
