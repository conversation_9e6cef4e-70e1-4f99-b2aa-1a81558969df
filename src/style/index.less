@import './variables.less';

@import './font-family.less';

@import './theme/index.less';

@import './components.less';

body {
  color: @text-color-secondary;
  font-family: -apple-system, BlinkMacSystemFont, @font-family;
  font-size: @font-size-base;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  padding: 0;
  margin: 0;
}

pre {
  font-family: @font-family;
}

ul,
dl,
li,
dd,
dt {
  margin: 0;
  padding: 0;
  list-style: none;
}

figure,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}

* {
  box-sizing: border-box;
}


.t-button-link {
  color: @brand-color;
  text-decoration: none;
  margin-right: @spacer-3;
  cursor: pointer;
  transition: color @anim-duration-base @anim-time-fn-easing;

  &:hover {
    color: @brand-color-hover;
  }

  &:active {
    color: @brand-color-active;
  }

  &--active {
    color: @brand-color-active;
  }

  &:focus {
    text-decoration: none;
  }

  &:last-child {
    margin-right: 0;
  }

  &:last-child {
    margin-right: 0;
  }

  &.t-button-link--theme-danger {
    color: @error-color;
  }

  &.t-is-disabled {
    color: @text-color-disabled;
    cursor: not-allowed;
  }

  &.t-button-link--theme-warning {
    color: @warning-color;
  }
}

.t-status {
  &.t-status-unhealth {
    color: @error-color;
  }

  &.t-status-unhealth:before {
    background-color: @error-color;
  }

  &.t-status-warning {
    color: @warning-color;
  }

  &.t-status-warning:before {
    background-color: @warning-color;
  }

  &.t-status-success {
    color: @success-color;
  }

  &.t-status-success:before {
    background-color: @success-color;
  }
}

.t-button + .t-button {
  margin-left: @spacer-c;
}

.container-base-margin-top {
  margin-top: 16px;
}

.card-date-picker-container {
  width: 240px;
}

.t-table__body {
  .t-table__empty-row {
    .t-table__empty {
      height: 200px;
    }
  }
}


.mb-1, .my-1 {
  margin-bottom: 0.25rem !important;
}

.mt-1, .my-1 {
  margin-top: 0.25rem !important;
}


.mb-2, .my-2 {
  margin-bottom: 0.5rem !important;
}

.mt-2, .my-2 {
  margin-top: 0.5rem !important;
}

.mb-3, .my-3 {
  margin-bottom: 1rem !important;
}

.mt-3, .my-3 {
  margin-top: 1rem !important;
}


&::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

&::-webkit-scrollbar-thumb {
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  background-color: var(--td-scrollbar-color);
}

.sys-gc-tabs-container {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 1400px;

  .sys-gc-tabs-box {
    height: 44px;
    line-height: 44px;
    padding: 0 20px;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #e4e4e4;

    .sys-gc-name {
      font-size: 16px;
      font-weight: 600;
      margin-right: 10px;
    }

    .sys-gc-divider {
      .ant-divider {
        color: #333333;
        height: 1.3em;
        width: 1.5px;
      }
    }

    .sys-gc-tabs {
      .ant-tabs {
        .ant-tabs-bar {
          margin-bottom: 0;
          border-bottom: 0;

          .ant-tabs-nav-wrap {
            .ant-tabs-tab {
              font-size: 16px;
              color: #999999;
              padding: 13px 0 11px 0;
              margin-right: 23px;

              .anticon {
                visibility: hidden;
                font-size: 18px;
                margin-right: -3px;

                svg {
                  margin-bottom: -1px;
                }
              }

              &.ant-tabs-tab-active {
                color: #42a1f7;
                font-weight: 600;

                .anticon {
                  visibility: visible;
                }
              }
            }

            .ant-tabs-ink-bar {
              display: none !important;
            }
          }
        }

        .ant-tabs-content {
          display: none;
        }
      }
    }
  }

  .sys-gc-context-box {
    padding-left: 20px;
  }
}

// ==================== 全局输入框样式优化 ====================
// 去除所有输入框组件的突出边框线和阴影效果

.t-input,
.t-select,
.t-textarea,
.t-date-picker,
.t-time-picker,
.t-input-number,
.t-date-range-picker {
  &:focus,
  &.t-is-focused {
    outline: none !important;
    box-shadow: none !important;
    border-color: var(--td-brand-color) !important;
  }

  &:hover:not(:disabled) {
    border-color: var(--td-brand-color-hover, #4dabf7) !important;
  }
}

// 输入组合的特殊处理
.t-input-group {
  .t-input,
  .t-select {
    &:focus,
    &.t-is-focused {
      outline: none !important;
      box-shadow: none !important;
      border-color: var(--td-brand-color) !important;
    }

    &:hover:not(:disabled) {
      border-color: var(--td-brand-color-hover, #4dabf7) !important;
    }
  }
}

// 下拉选择器的特殊处理
.t-select__single,
.t-select__multiple {
  &:focus,
  &.t-is-focused {
    outline: none !important;
    box-shadow: none !important;
  }
}

// 日期选择器的特殊处理
.t-date-picker__input,
.t-date-range-picker__input {
  &:focus,
  &.t-is-focused {
    outline: none !important;
    box-shadow: none !important;
  }
}

// 文本域的特殊处理
.t-textarea__inner {
  &:focus,
  &.t-is-focused {
    outline: none !important;
    box-shadow: none !important;
    border-color: var(--td-brand-color) !important;
  }

  &:hover:not(:disabled) {
    border-color: var(--td-brand-color-hover, #4dabf7) !important;
  }
}

// 数字输入框的特殊处理
.t-input-number__input {
  &:focus,
  &.t-is-focused {
    outline: none !important;
    box-shadow: none !important;
  }
}
