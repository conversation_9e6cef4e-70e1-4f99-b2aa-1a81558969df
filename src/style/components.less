// ==================== TDesign 组件样式覆盖 ====================
// 统一处理所有TDesign组件的样式，去除不必要的视觉效果

// ==================== 输入框组件 ====================
.t-input,
.t-select,
.t-textarea,
.t-date-picker,
.t-time-picker,
.t-input-number,
.t-date-range-picker {
  border-radius: 6px;
  transition: all 0.2s ease;

  &:focus,
  &.t-is-focused {
    outline: none !important;
    box-shadow: none !important;
    border-color: var(--td-brand-color, #0052d9) !important;
  }

  &:hover:not(:disabled):not(.t-is-focused) {
    border-color: #9ca3af !important;
  }

  &:disabled {
    background-color: #f9fafb;
    border-color: #e5e7eb;
    color: #9ca3af;
    cursor: not-allowed;
  }
}

// ==================== 按钮组件 ====================
.t-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:focus {
    outline: none !important;
    box-shadow: none !important;
  }

  // 去掉按钮内部元素的 display: inline-flex
  .t-button__text,
  .t-icon {
    display: initial !important;
  }

  &.t-button--theme-primary {
    background: var(--td-brand-color, #0052d9);
    border-color: var(--td-brand-color, #0052d9);

    &:hover:not(:disabled) {
      background: var(--td-brand-color-hover, #266fe8);
      border-color: var(--td-brand-color-hover, #266fe8);
    }

    &:active {
      background: var(--td-brand-color-active, #0034a6);
      border-color: var(--td-brand-color-active, #0034a6);
    }
  }

  &.t-button--variant-outline {

    //background: #fff;
    //color: #374151;

    &:hover:not(:disabled) {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }

  &.t-button--variant-text {
    border: none;
    background: transparent;

    &:hover:not(:disabled) {
      background: rgba(0, 82, 217, 0.1);
    }
  }

  &.t-button--ghost {
    background: transparent;
    border: 1px solid var(--td-brand-color, #0052d9);
    color: var(--td-brand-color, #0052d9);

    &:hover:not(:disabled) {
      background: var(--td-brand-color, #0052d9);
      color: #fff;
      border-color: var(--td-brand-color, #0052d9);
    }

    &:active {
      background: var(--td-brand-color-active, #0034a6);
      color: #fff;
      border-color: var(--td-brand-color-active, #0034a6);
    }

    &:disabled {
      background: transparent;
      border-color: #d1d5db;
      color: #9ca3af;
    }
  }
}

// ==================== 表格组件 ====================
.t-table {
  border-radius: 8px;
  overflow: hidden;

  .t-table__header {
    th {
      background: #f8f9fa;
      border-bottom: 1px solid #e5e7eb;
      font-weight: 600;
      color: #374151;
    }
  }

  .t-table__body {
    tr {
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
      }

      td {
        border-bottom: 1px solid #f3f4f6;
        color: #374151;
      }
    }
  }
}

// ==================== 下拉菜单组件 ====================
.t-dropdown__menu {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  .t-dropdown__item {
    transition: all 0.2s ease;

    &:hover {
      background: #f8f9fa;
      color: var(--td-brand-color, #0052d9);
    }
  }
}

// ==================== 标签组件 ====================
.t-tag {
  border-radius: 4px;
  font-weight: 500;

  &.t-tag--theme-success {
    background: #dcfce7;
    border-color: #bbf7d0;
    color: #166534;
  }

  &.t-tag--theme-warning {
    background: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
  }

  &.t-tag--theme-danger {
    background: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
  }

  &.t-tag--theme-primary {
    background: #dbeafe;
    border-color: #bfdbfe;
    color: #1e40af;
  }
}

// ==================== 树形组件 ====================
.t-tree {
  .t-tree__item {
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: #f8f9fa;
    }

    &.t-is-active {
      background: #dbeafe;
      color: var(--td-brand-color, #0052d9);
    }
  }
}

// ==================== 分页组件 ====================
.t-pagination {
  .t-pagination__btn {
    border-radius: 4px;


    &:hover:not(:disabled) {
      border-color: var(--td-brand-color, #0052d9);
      color: var(--td-brand-color, #0052d9);
    }

    &.t-is-current {
      background: var(--td-brand-color, #0052d9);
      border-color: var(--td-brand-color, #0052d9);
      color: #fff;
    }
  }
}

// ==================== 对话框组件 ====================
.t-dialog {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

  .t-dialog__header {
    border-bottom: 1px solid #f0f0f0;
  }

  .t-dialog__footer {
    border-top: 1px solid #f0f0f0;
  }
}

// ==================== 卡片组件 ====================
.t-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;

  .t-card__header {
    border-bottom: 1px solid #f0f0f0;
  }
}

// ==================== 表单组件 ====================
.t-form-item {
  .t-form__label {
    font-weight: 500;
    color: #374151;
  }

  .t-form__help {
    color: #6b7280;
    font-size: 12px;
  }

  &.t-is-error {
    .t-input,
    .t-select,
    .t-textarea {
      border-color: #ef4444 !important;

      &:focus,
      &.t-is-focused {
        border-color: #ef4444 !important;
        box-shadow: none !important;
      }
    }
  }
}

// 内联表单样式优化
.t-form-inline .t-form__item {
  margin: 0 !important;
  margin-right: 0 !important;
  min-width: 200px;
  display: inline-block;
}

// ==================== 消息提示组件 ====================
.t-message {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

// ==================== 通知组件 ====================
.t-notification {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

// ==================== 加载组件 ====================
.t-loading {
  .t-loading__text {
    color: #6b7280;
  }

  .t-loading__indicator {
    color: var(--td-brand-color, #0052d9);
  }
}
