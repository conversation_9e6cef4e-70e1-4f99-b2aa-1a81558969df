/** 公共前缀 */
@prefix: tdesign-starter;

/* 颜色色板 */

@brand-color-1: var(--td-brand-color-1);
@brand-color-2: var(--td-brand-color-2);
@brand-color-3: var(--td-brand-color-3);
@brand-color-4: var(--td-brand-color-4);
@brand-color-5: var(--td-brand-color-5);
@brand-color-6: var(--td-brand-color-6);
@brand-color-7: var(--td-brand-color-7);
@brand-color-8: var(--td-brand-color-8);
@brand-color-9: var(--td-brand-color-9);
@brand-color-10: var(--td-brand-color-10);

@warning-color-1: var(--td-warning-color-1);
@warning-color-2: var(--td-warning-color-2);
@warning-color-3: var(--td-warning-color-3);
@warning-color-4: var(--td-warning-color-4);
@warning-color-5: var(--td-warning-color-5);
@warning-color-6: var(--td-warning-color-6);
@warning-color-7: var(--td-warning-color-7);
@warning-color-8: var(--td-warning-color-8);
@warning-color-9: var(--td-warning-color-9);
@warning-color-10: var(--td-warning-color-10);

@error-color-1: var(--td-error-color-1);
@error-color-2: var(--td-error-color-2);
@error-color-3: var(--td-error-color-3);
@error-color-4: var(--td-error-color-4);
@error-color-5: var(--td-error-color-5);
@error-color-6: var(--td-error-color-6);
@error-color-7: var(--td-error-color-7);
@error-color-8: var(--td-error-color-8);
@error-color-9: var(--td-error-color-9);
@error-color-10: var(--td-error-color-10);

@success-color-1: var(--td-success-color-1);
@success-color-2: var(--td-success-color-2);
@success-color-3: var(--td-success-color-3);
@success-color-4: var(--td-success-color-4);
@success-color-5: var(--td-success-color-5);
@success-color-6: var(--td-success-color-6);
@success-color-7: var(--td-success-color-7);
@success-color-8: var(--td-success-color-8);
@success-color-9: var(--td-success-color-9);
@success-color-10: var(--td-success-color-10);

@gray-color-1: var(--td-gray-color-1);
@gray-color-2: var(--td-gray-color-2);
@gray-color-3: var(--td-gray-color-3);
@gray-color-4: var(--td-gray-color-4);
@gray-color-5: var(--td-gray-color-5);
@gray-color-6: var(--td-gray-color-6);
@gray-color-7: var(--td-gray-color-7);
@gray-color-8: var(--td-gray-color-8);
@gray-color-9: var(--td-gray-color-9);
@gray-color-10: var(--td-gray-color-10);
@gray-color-11: var(--td-gray-color-11);
@gray-color-12: var(--td-gray-color-12);
@gray-color-13: var(--td-gray-color-13);
@gray-color-14: var(--td-gray-color-14);

/* 文字 & 图标 颜色 */
@font-white-1: var(--td-font-white-1);
@font-white-2: var(--td-font-white-2);
@font-white-3: var(--td-font-white-3);
@font-white-4: var(--td-font-white-4);

@font-gray-1: var(--td-font-gray-1);
@font-gray-2: var(--td-font-gray-2);
@font-gray-3: var(--td-font-gray-3);
@font-gray-4: var(--td-font-gray-4);

/* 基础颜色 */
@brand-color: var(--td-brand-color); // 色彩-品牌-可操作
@warning-color: var(--td-warning-color); // 色彩-功能-警告
@error-color: var(--td-error-color); // 色彩-功能-失败
@success-color: var(--td-success-color); // 色彩-功能-成功

// 基础颜色的扩展 用于 hover / 聚焦 / 禁用 / 点击 等状态
@brand-color-hover: var(--td-brand-color-hover); // hover态
@brand-color-focus: var(--td-brand-color-focus); // focus态，包括鼠标和键盘
@brand-color-active: var(--td-brand-color-active); // 点击态
@brand-color-disabled: var(--td-brand-color-disabled); // 禁用态
@brand-color-light: var(--td-brand-color-light); // 浅色的选中态

// 警告色扩展
@warning-color-hover: var(--td-warning-color-hover);
@warning-color-focus: var(--td-warning-color-focus);
@warning-color-active: var(--td-warning-color-active);
@warning-color-disabled: var(--td-warning-color-disabled);
@warning-color-light: var(--td-warning-color-light);

// 失败/错误色扩展
@error-color-hover: var(--td-error-color-hover);
@error-color-focus: var(--td-error-color-focus);
@error-color-active: var(--td-error-color-active);
@error-color-disabled: var(--td-error-color-disabled);
@error-color-light: var(--td-error-color-light);

// 成功色扩展
@success-color-hover: var(--td-success-color-hover);
@success-color-focus: var(--td-success-color-focus);
@success-color-active: var(--td-success-color-active);
@success-color-disabled: var(--td-success-color-disabled);
@success-color-light: var(--td-success-color-light);

// 遮罩
@mask-active: var(--td-mask-active); // 遮罩-弹出
@mask-disabled: var(--td-mask-disabled); // 遮罩-禁用

// 背景色
@bg-color-page: var(--td-bg-color-page); // 色彩 - page
@bg-color-container: var(--td-bg-color-container); // 色彩 - 容器
@bg-color-container-hover: var(--td-bg-color-container-hover); // 色彩 - 容器 - hover
@bg-color-container-active: var(--td-bg-color-container-active); // 色彩 - 容器 - active
@bg-color-container-select: var(--td-bg-color-container-select); // 色彩 - 容器 - select

@bg-color-secondarycontainer: var(--td-bg-color-secondarycontainer); // 色彩 - 次级容器
@bg-color-secondarycontainer-hover: var(--td-bg-color-secondarycontainer-hover); // 色彩 - 次级容器 - hover
@bg-color-secondarycontainer-active: var(--td-bg-color-secondarycontainer-active); // 色彩 - 次级容器 - active

@bg-color-component: var(--td-bg-color-component); // 色彩  - 组件
@bg-color-component-hover: var(--td-bg-color-component-hover); // 色彩 - 组件 - hover
@bg-color-component-active: var(--td-bg-color-component-active); // 色彩 - 组件 - active
@bg-color-component-disabled: var(--td-bg-color-component-disabled); // 色彩 - 组件 - disabled

// TODO: 考虑是否在组件内部做判断，不增加额外变量
// 特殊组件背景色，目前只用于 button、input 组件多主题场景，浅色主题下固定为白色，深色主题下为 transparent 适配背景颜色
@bg-color-specialcomponent: var(--td-bg-color-specialcomponent);

// 文本颜色
@text-color-primary: var(--td-text-color-primary); // 色彩-文字-主要
@text-color-secondary: var(--td-text-color-secondary); // 色彩-文字-次要
@text-color-placeholder: var(--td-text-color-placeholder); // 色彩-文字-占位符/说明
@text-color-disabled: var(--td-text-color-disabled); // 色彩-文字-禁用
@text-color-anti: var(--td-text-color-anti); // 色彩-文字-反色
@text-color-brand: var(--td-text-color-brand); // 色彩-文字-品牌
@text-color-link: var(--td-text-color-link); // 色彩-文字-链接

// 分割线
@border-level-1-color: var(--td-border-level-1-color);
@component-stroke: var(--td-component-stroke);
// 边框
@border-level-2-color: var(--td-border-level-2-color);
@component-border: var(--td-component-border);

// shadow

// 基础/下层 投影 hover 使用的组件包括：表格 /
@shadow-1: var(--td-shadow-1);
// 中层投影 下拉 使用的组件包括：下拉菜单 / 气泡确认框 / 选择器 /
@shadow-2: var(--td-shadow-2);
// 上层投影（警示/弹窗）使用的组件包括：全局提示 / 消息通知
@shadow-3: var(--td-shadow-3);

// 内投影 用于弹窗类组件（气泡确认框 / 全局提示 / 消息通知）的内描边
@shadow-inset-top: var(--td-shadow-inset-top);
@shadow-inset-right: var(--td-shadow-inset-right);
@shadow-inset-bottom: var(--td-shadow-inset-bottom);
@shadow-inset-left: var(--td-shadow-inset-left);
@shadow-inset: @shadow-inset-top, @shadow-inset-right, @shadow-inset-bottom, @shadow-inset-left;

// 融合阴影
@shadow-2-inset: @shadow-2, @shadow-inset;
@shadow-3-inset: @shadow-3, @shadow-inset;

// Spacer
@spacer: 8px;
@spacer-s: @spacer * 0.5; // 间距-4
@spacer-l: @spacer * 1.5; // 间距-12
@spacer-c: @spacer * 1.25; // 间距-10
@spacer-1: @spacer; // 间距-8
@spacer-2: @spacer * 2; // 间距-16
@spacer-3: @spacer * 3; // 间距-24
@spacer-4: @spacer * 4; // 间距-32
@spacer-5: @spacer * 5; // 间距-大-40
@spacer-6: @spacer * 6; // 间距-大-48
@spacer-7: @spacer * 7; // 间距-大-48
@spacer-8: @spacer * 8; // 间距-大-48
@spacer-9: @spacer * 9; // 间距-大-48
@spacer-10: @spacer * 10; // 间距-大-80

// Font
@font-size: 10px;
@font-size-s: @font-size * 1.2; // 字号-五级字号
@font-size-base: @font-size * 1.4; // 字号-四级字号
@font-size-l: @font-size * 1.6; // 字号-三级字号
@font-size-xl: @font-size * 2; // 字号-二级字号
@font-size-xxl: @font-size * 3.6; // 字号-一级字号

// Line Height
@text-line-height: 1.5; // 行高-常规
@text-line-height-s: 20px; // 行高-对应五级文字
@text-line-height-base: 22px; // 行高-对应四级文字
@text-line-height-l: 24px; // 行高-对应三级文字
@text-line-height-xl: 28px; // 行高-对应二级文字
@text-line-height-xxl: 44px; //行高-对应一级文字

@font-family: PingFang SC, Microsoft YaHei, Arial Regular; // 字体-磅数-常规
@font-family-medium: PingFang SC, Microsoft YaHei, Arial Medium; // 字体-磅数-粗体

// Border Radius
@border-radius: 3px; // 圆角-全局
@border-radius-50: 50%; // 圆角-全圆角

// 表单相关
@form-height: 30px;
@form-text-color: @text-color-primary;
@form-bg-color: @bg-color-container;
@form-border-color: @border-level-2-color;

// 图标尺寸
@icon-default: 16px;
@icon-l: 24px;

// 滚动条颜色
@scrollbar-color: var(--td-scrollbar-color);

// 响应式断点
@screen-sm: 768px;
@screen-md: 992px;
@screen-lg: 1200px;
@screen-xl: 1400px;

@screen-sm-min: @screen-sm;
@screen-md-min: @screen-md;
@screen-lg-min: @screen-lg;
@screen-xl-min: @screen-xl;

@screen-sm-max: (@screen-md-min - 1px);
@screen-md-max: (@screen-lg-min - 1px);
@screen-lg-max: (@screen-xl-min - 1px);

// 动画
@anim-time-fn-easing: cubic-bezier(0.38, 0, 0.24, 1);
@anim-time-fn-ease-out: cubic-bezier(0, 0, 0.15, 1);
@anim-time-fn-ease-in: cubic-bezier(0.82, 0, 1, 0.9);
@anim-duration-base: 0.2s;
@anim-duration-moderate: 0.24s;
@anim-duration-slow: 0.28s;

// 统一管理各组件层级关系
@z-index-affix: 500;
@z-index-drawer: 1500;
@z-index-dialog: 2500;
@z-index-loading: 3500;
@z-index-message: 5000;
@z-index-Popup: 5500;
@z-index-Notification: 6000;
