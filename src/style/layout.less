@import './variables.less';
@import './font-family.less';

// layout rewrite

.t-layout__sider {
  width: fit-content;
}

.t-button + .t-button {
  margin-left: @spacer;
}

.t-layout.t-layout--with-sider {
  > .t-layout {
    flex: 1;
    min-width: 760px;
  }
}

.t-layout.t-layout--with-sider {
  > .t-layout__content {
    flex: 1;
    min-width: 760px;
  }
}

.t-menu--dark .t-menu__operations .t-icon {
  color: rgba(255, 255, 255, 0.55);
  &:hover {
    cursor: pointer;
  }
}
.t-layout__header {
  height: 48px;
}

// 菜单项基础样式
.t-default-menu {
  background: #fff;
  border-right: 1px solid #e5e7eb;

  .t-menu__item {
    height: 48px;
    color: #374151;
    margin: 4px 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      background: #f8f9fa;
      color: #1565c0;

      .t-icon {
        color: #1565c0;
      }
    }

    // 选中状态样式
    &.t-is-active:not(.t-is-opened) {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
      color: #1565c0 !important;
      border: 1px solid #90caf9;
      box-shadow: 0 2px 8px rgba(25, 101, 192, 0.15);

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: #1565c0;
        border-radius: 0 2px 2px 0;
      }

      .t-icon {
        color: #1565c0 !important;
      }

      .t-menu__content {
        font-weight: 600;
        padding-left: 16px; // 因为有左边框，所以增加左内边距
      }
    }
  }

  // 子菜单样式
  .t-submenu {
    margin: 4px -16px;
    border-radius: 8px;

    .t-submenu__title {
      height: 48px;
      color: #374151;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f8f9fa;
        color: #1565c0;

        .t-icon {
          color: #1565c0;
        }
      }
    }

    &.t-is-active {
      .t-submenu__title {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        color: #1565c0;
        border: 1px solid #90caf9;
        box-shadow: 0 2px 8px rgba(25, 101, 192, 0.15);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: #1565c0;
          border-radius: 0 2px 2px 0;
        }

        .t-icon {
          color: #1565c0;
        }
      }
    }

    // 子菜单项样式
    .t-menu__item {
      margin: 2px 16px 2px 24px;
      height: 40px;
      font-size: 13px;

      &.t-is-active {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        color: #1565c0;
        border: 1px solid #90caf9;

        &::before {
          left: -8px;
        }

        .t-menu__content {
          padding-left: 12px;
        }
      }
    }
  }

  // 暗色主题
  &.t-menu--dark {
    background: var(--td-gray-color-13);
    border-right: 1px solid var(--td-gray-color-10);

    .t-menu__item {
      color: rgba(255, 255, 255, 0.75);

      &:hover {
        background: var(--td-gray-color-12);
        color: #fff;
      }

      &.t-is-active:not(.t-is-opened) {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
        color: #fff !important;
        border: 1px solid #3b82f6;

        &::before {
          background: #60a5fa;
        }
      }
    }

    .t-submenu {
      .t-submenu__title {
        color: rgba(255, 255, 255, 0.75);

        &:hover {
          background: var(--td-gray-color-12);
          color: #fff;
        }
      }

      &.t-is-active {
        .t-submenu__title {
          background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
          color: #fff;
          border: 1px solid #3b82f6;

          &::before {
            background: #60a5fa;
          }
        }
      }
    }
  }
}

.t-menu__logo {
  height: 48px !important;
}
//顶部面包屑导航定制演示
//.t-tabs__nav-scroll {
//  //height: 36px !important;
//  background-color: #eeeeee;
//  border-bottom: 1px solid #D7D7D7;
//}

.t-alert--info {
  background-color: var(--td-brand-color-1);
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height:50px;
  background: #ffffff;
  border: 1px solid #ffffff;
  margin: 16px 0;
  padding: 10px
}

/* 全局修改table斑马纹背景色 */
.t-table tr:nth-child(odd) td {
  background-color: #FFFFFF; /* 自定义奇数行背景色 */
}
.t-table tr:nth-child(even) td {
  background-color: #F7FAFC; /* 自定义偶数行背景色 */
}
//表头样式修改
.t-table thead td, .t-table th {
  background: #ffffff;
  color: #000000 !important;
}
.t-table th, .t-table td {
  //color: #333333;
}
.t-button-link {
  margin-right: 0 !important;
  color: #4094F3 !important;
}
.t-button-link.t-button-link--theme-danger {
  color: #ff4949 !important;
}
.t-table__cell--fixed-right .t-table__cell--fixed-right-first {
  display: flex;
  justify-content: space-between;
}

.t-table__pagination {
  padding: 20px !important;
}

.search-button {
  //color: #4093F2 !important;
  //font-size: 14px !important;
  //border-radius: 14px !important;
  //border: 1px solid #4093F2 !important;
  //background: #F0F6FF !important;
}
.reset-button {
  //color: #333333 !important;
  //font-size: 14px !important;
  //border-radius: 14px !important;
  //border: 1px solid #cccccc !important;
  //background: #ffffff !important;
}

.t-table__pagination {
  padding: 20px 0 !important;
}

.t-steps .t-steps-item__icon--number {
  height: 24px;
  width: 24px;
  font-weight: normal !important;
}
.t-steps .t-steps-item__title {
  font-size: 14px;
  font-weight: bold !important;
}
.t-steps-item.t-steps-item__finish {
  font-weight: bold;
  color: #333333;
}
.t-steps .t-steps-item__inner {
  color: #333333;
  font-weight: bold;
}
.t-dialog__header {
  background: #ffffff;
  color: #333333;
  font-weight: bold;
  font-size: 18px;
  height: 50px;
  padding: 0 20px;
  border-top-left-radius: 9px;
  border-top-right-radius: 9px;
}
.t-dialog--default {
  padding: 0 0 30px;
}
.t-dialog__body {
  padding: 20px 20px 0;
}
.t-dialog__footer {
  padding: 20px 20px 0;
}

.t-tabs__nav-item {
  color: #333333;
}

.@{prefix} {
  // 布局元素调整
  &-wrapper {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  &-main-wrapper {
    height: 500px;
    overflow: scroll;
  }

  &-side-nav-layout {
    &-relative {
      height: 100%;
    }
  }

  &-content-layout {
    background: var(--td-font-white-1);
    margin: @spacer-c;
    &-container {
      padding: 0 20px 20px 20px;
    }
  }

  &-layout {
    height: calc(100vh - 64px);
    overflow-y: scroll;

    &-tabs-nav {
      max-width: 100%;
      position: fixed;
      overflow: visible;
      z-index: 100;

    }
    &-tabs-nav + .@{prefix}-content-layout {
      //padding-top: @spacer-3;
    }
  }

  &-footer-layout {
    padding: 0;
    margin-bottom: @spacer-2;
  }

  // slideBar
  &-sidebar-layout {
    height: 100%;
  }

  &-sidebar-compact {
    width: 64px;
  }

  &-sidebar-layout-side {
    z-index: 100;
  }

  &-side-nav {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 200;
    transition: all 0.3s;
    min-height: 100%;

    .t-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
    [class*='dhcc-icon'] {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
    .t-icon + .t-menu__content,
    .t-icon + .t-menu__item-link {
      margin-left: 5px;
    }
    [class*='dhcc-icon'] + .t-menu__content,
    [class*='dhcc-icon'] + .t-menu__item-link {
      margin-left: 5px;
    }

    &-mix {
      top: 64px;

      &-fixed {
        top: 64px;
        z-index: 0;
      }
    }

    &-no-fixed {
      position: relative;
      z-index: 1;
    }

    &-no-logo {
      z-index: 1;
    }

    &-logo-wrapper {
      // display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 12px 0;
      &:hover {
        cursor: pointer;
      }
      .menu-logo-icon {
        height: 40px;
        width: 100%;
        text-align: center;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      height: 100%;
      width: 100%;
      text-align: center;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      .logo-area {
        height: 40px;
        .side-nav-logo-custom {
          height: 100%;
          position: relative;
          display: flex;
          justify-content: center;
          .custom-text {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            display: block;
            line-height: 40px;
            max-width: 180px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            &.no-logo {
              max-width: 200px;
            }
          }
          .custom-logo {
            height: 100%;
            padding: 5px 0;
          }
          .mr10 {
            margin-right: 10px;
          }
          .ml10 {
            margin-left: 10px;
          }
        }
      }
    }

    &-logo-t-logo {
      height: 34px;
      width: 100%;
    }

    &-logo-tdesign-logo {
      padding: 0 24px;
      height: 34px;
      width: 100%;
      color: @text-color-primary;
      margin: 3px 0;
    }

    &-logo-normal {
      color: @brand-color;
      font-size: @font-size-l;
      transition: all 0.3s;
    }
  }

  &-side-nav-placeholder {
    flex: 1 1 232px;
    min-width: 232px;
    transition: all 0.3s;

    &-hidden {
      flex: 1 1 72px;
      min-width: 72px;
      transition: all 0.3s;
    }
  }
}

.route-tabs-dropdown {
  .t-icon {
    margin-right: 8px;
  }
}

.logo-container {
  cursor: pointer;
  display: inline-flex;
  height: 64px;
  margin-left: 24px;
}

.version-container {
  color: @text-color-primary;
  opacity: 0.4;
  text-align: center;
  display: block;
  max-width: 220px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.t-table__affixed-header-elm-wrap,
.t-table__affixed-header-elm {
  // left: -1px;
}
