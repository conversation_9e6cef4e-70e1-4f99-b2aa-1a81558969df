/**
 * @description: 对象拷贝
 */
class BeanUtilsService {
  /**
   * @description: 对象拷贝
   */
  copy(obj: any): any {
    if (obj != null) {
      return JSON.parse(JSON.stringify(obj));
    }
    return "";
  }

  /**
   * @description: 字符串转对象
   */
  parse(str: string | null): any {
    if (str != null && str !== "") {
      return JSON.parse(str);
    }
    return null;
  }

  /**
   * @description: 对象转字符串
   */
  stringify(obj: any): string {
    if (obj != null) {
      return JSON.stringify(obj);
    }
    return "";
  }
}
const beanUtilsService = new BeanUtilsService();
export default beanUtilsService;
