import Vue from 'vue';
import TDesign, { DialogPlugin } from 'tdesign-vue';
// import { Pagination } from "tdesign-vue";
// 引入组件库全局样式资源
import 'tdesign-vue/es/style/index.css';

class TdesignVueService {
  init() {
    Vue.use(TDesign);
    Vue.prototype.$confirm = (options: any) => {
      const {
        title,
        icon,
        onOk = () => {},
        onCancel = () => {},
        okText,
        cancelText,
        content,
        closeOnOverlayClick,
        ...other
      } = options;
      const dialog: any = DialogPlugin.confirm({
        header: title,
        theme: icon,
        closeOnOverlayClick: closeOnOverlayClick || false,
        onConfirm: async () => {
          const result = await onOk();
          // console.log(result);
          if (result !== false) {
            dialog.hide(); // 只要是返回的是false  不关闭对话框
          }
          // const res = await onOk();
        },
        onClose: async () => {
          onCancel();
          dialog.hide(); // 只要是返回的是false  不关闭对话框
        },
        confirmBtn: okText,
        cancelBtn: cancelText,
        body: content,
        ...other,
      });
      return dialog;
    };
    Vue.prototype.$info = (options: any) => {
      this.showDialogAlert({ ...options, theme: 'info' });
    };
    Vue.prototype.$success = (options: any) => {
      this.showDialogAlert({ ...options, theme: 'success' });
    };
    Vue.prototype.$error = (options: any) => {
      this.showDialogAlert({ ...options, theme: 'danger' });
    };
    Vue.prototype.$warning = (options: any) => {
      this.showDialogAlert({ ...options, theme: 'warning' });
    };
  }
  showDialogAlert(options: any) {
    const { title, theme, onOk = () => {}, okText, content, closeOnOverlayClick, ...other } = options;
    const dialog: any = DialogPlugin.alert({
      header: title,
      theme,
      closeOnOverlayClick: closeOnOverlayClick || false,
      onConfirm: async () => {
        const result = await onOk();
        if (result !== false) {
          dialog.hide(); // 只要是返回的是false  不关闭对话框
        }
      },
      confirmBtn: okText,
      body: content,
      ...other,
    });
    return dialog;
  }
  generalPagination(options?: any): Partial<any> {
    return {
      showQuickJumper: true,
      showSizeChanger: true,
      hideOnSinglePage: false,
      // defaultPageSize: 50,
      // 每页数量
      pageSize: options?.pageSize || 10,
      // 默认当前页
      // defaultCurrent: 1,
      // 当前页
      current: options?.current || 1,
      // 总计
      total: 0,
      size: options?.size || 'medium',
      disableDataPage: false,
      showTotal(total: number, range: number[]) {
        let context = `总共${total}条记录`;
        if (range != null && range.length > 0) {
          context += `, 当前展示${range[0]}-${range[1]}条`;
        }
        return context;
      },
    };
  }
}
const tdesignVueService = new TdesignVueService();
export default tdesignVueService;
