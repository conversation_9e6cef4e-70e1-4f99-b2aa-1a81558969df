/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-18 09:59:19
 * @Last Modified by: kyr<PERSON><PERSON>
 * @Last Modified time: 2023-10-18 11:41:13
 * @Last Des:
 */
import Vue from 'vue';
import axios, { AxiosRequestConfig } from 'axios';
import VueAxios from 'vue-axios';
import { MethodType } from '@/constants/enum/general/method-type.enum';
import { HeaderType } from '@/constants/enum/general/header-type.enum';
import { Code } from '@/constants/enum/general/code.enum';
import { jsonToClass } from '@/utils/jsonUtils';
import {message} from "tdesign-vue";


interface Query {
  [key: string]: any;
}

class ApiService {
  logoutNoticeSign = false; // 登出通知标志

  init() {
    Vue.use(VueAxios, axios);

    const requestConfig: any = this.createBasicHeaders();
    Object.keys(requestConfig.headers).forEach((key) => {
      Vue.axios.defaults.headers.common[key] = requestConfig.headers[key];
    });

    Vue.axios.interceptors.request.use(
      (config) => {
        // Do something before request is sent
        if (this.verificationToken(config)) {
          return Promise.resolve(config);
        }
        if (!this.logoutNoticeSign) {
          this.logoutNoticeSign = true;
          return oauthService.refreshToken().then((response: any) => {
            this.logoutNoticeSign = false;
            if (response && response.code === Code.OK.code) {
              config.headers = config.headers ? config.headers : {};
              config.headers['token'] = tokenStore.token as string;
              return Promise.resolve(config);
            }
            return Promise.reject('会话已失效');
          });
        }
      },
      (_error: any) => Promise.reject('网络异常，请稍后再试'),
    );
  }

  /**
   * @description:  创建基础消息头
   */
  createBasicHeaders(): any {
    return {
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };
  }

  /**
   * @description: 创建认证消息头
   */
  createAuthHeaders(): any {
    return {
      headers: {
        'token': tokenStore.token,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };
  }

  /**
   * @description: 创建上传认证消息头
   */
  createFileUploadAuthorizationHeader() {
    return {
      headers: {
        Accept: 'application/json',
        'token': tokenStore.token
      },
    };
  }

  /**
   * @description: 创建认证下载消息头
   */
  createFileDownloadAuthorizationHeader() {
    return {
      headers: {
        'Content-Type': 'application/json',
        'token': tokenStore.token
      },
      responseType: 'blob',
    };
  }

  get(path: string, query: Query | undefined, requestConfig: AxiosRequestConfig, clazz?: any): any {
    path = query != null ? this.urlQueryConvert(path, query) : path;
    console.log(path);
    console.log(Vue.axios);
    return Vue.axios
      .get(`${path}`, requestConfig)
      .then(this.createBusCodeHander(clazz))
      .catch(this.createErrorHander());
  }

  post(path: string, params: any, query: Query | undefined, requestConfig: AxiosRequestConfig, clazz?: any): any {
    path = query != null ? this.urlQueryConvert(path, query) : path;
    return Vue.axios
      .post(`${path}`, params, requestConfig)
      .then(this.createBusCodeHander(clazz))
      .catch(this.createErrorHander());
  }

  put(path: string, params: any, query: Query | undefined, requestConfig: AxiosRequestConfig, clazz?: any): any {
    path = query != null ? this.urlQueryConvert(path, query) : path;
    return Vue.axios
      .put(`${path}`, params, requestConfig)
      .then(this.createBusCodeHander(clazz))
      .catch(this.createErrorHander());
  }

  delete(path: string, query: Query | undefined, requestConfig: AxiosRequestConfig, clazz?: any): any {
    path = query != null ? this.urlQueryConvert(path, query) : path;
    return Vue.axios.delete(path, requestConfig).then(this.createBusCodeHander(clazz))
.catch(this.createErrorHander());
  }

  /**
   * @description: 通用请求函数
   */
  general(
    api: any,
    query?: Query | undefined,
    params?: any | undefined,
    requestConfig?: AxiosRequestConfig | any,
    follow?: string | undefined,
    clazz?: any,
  ): any {
    if (!!api.url && !!api.method) {
      if (requestConfig == null) {
        switch (api.header) {
          case HeaderType.BASE.code:
            requestConfig = this.createBasicHeaders();
            break;
          case HeaderType.AUTH.code:
            requestConfig = this.createAuthHeaders();
            break;
          case HeaderType.UPLOAD_AUTH.code:
            requestConfig = this.createFileUploadAuthorizationHeader();
            break;
          case HeaderType.DOWNLOAD_AUTH.code:
            requestConfig = this.createFileDownloadAuthorizationHeader();
            break;
        }
      } else {
        let requestConfigBase: any = {};
        switch (api.header) {
          case HeaderType.BASE.code:
            requestConfigBase = this.createBasicHeaders();
            break;
          case HeaderType.AUTH.code:
            requestConfigBase = this.createAuthHeaders();
            break;
          case HeaderType.UPLOAD_AUTH.code:
            requestConfigBase = this.createFileUploadAuthorizationHeader();
            break;
          case HeaderType.DOWNLOAD_AUTH.code:
            requestConfigBase = this.createFileDownloadAuthorizationHeader();
            break;
        }
        Object.keys(requestConfig).forEach((value, _index, _array) => {
          requestConfigBase[value] = requestConfig[value];
        });
        requestConfig = requestConfigBase;
      }
      const url = follow ? api.url + follow : api.url;
      switch (api.method) {
        case MethodType.GET.code:
          return this.get(url, query, requestConfig, clazz);
          break;
        case MethodType.PUT.code:
          return this.put(url, params, query, requestConfig, clazz);
          break;
        case MethodType.POST.code:
          return this.post(url, params, query, requestConfig, clazz);
          break;
        case MethodType.DELETE.code:
          return this.delete(url, query, requestConfig, clazz);
          break;
      }
    }
  }

  /**
   * @description: 错误码处理器
   */
  createBusCodeHander(clazz?: any) {
    const _this = this;
    return function (response: any) {
      if (response.status === 200) {
        if (
          (response.data
            && response.data.meta
            && String(response.data.meta.statusCode) === StatusCode.USER_CACHE_DOES_NOT_EXIST.code)
          || String(response.data.code) === StatusCode.USER_CACHE_DOES_NOT_EXIST.code
        ) {
          // oauthService.refreshToken();
          message.error({
            content: response.msg || response.data.msg || '用户缓存不存在',
          });
        }
        const jsonData = response.data;
        jsonData.headers = response.headers;
        if (clazz && jsonData && jsonData.data) {
          const jsonObj = jsonToClass(jsonData.data, clazz);
          jsonData.data = jsonObj;
        }
        return jsonData;
      }

      return response;
    };
  }

  /**
   * @description: 异常处理器
   */
  createErrorHander() {
    return function (error: any) {
      let msg = '未知异常，稍后请重试或联系管理员';
      if (error.request) {
        msg = error.request?.data?.msg || '网络连接异常，稍后请重试或联系管理员';
      } else if (error.response) {
        msg = error.response?.data?.msg || '服务器异常，稍后请重试或联系管理员';
      } else if (error && typeof error === 'string') {
        msg = error;
      } else if (error && error.code && error.msg) {
        msg = error.msg;
      }
      return RestfulResponse.failure(msg);
    };
  }

  /**
   * @description: 校验令牌
   */
  verificationToken(config: AxiosRequestConfig) {
    if (
      config.headers
      && config.headers.Authorization
      && (config.headers.Authorization as string).indexOf(OauthService.BASE_TOKEN_TYPE) != 0
    ) {
      if (tokenStore.token) {
        return oauthService.isTokenExpiration;
      }
      return false;
    }
    return true;
  }

  /**
   * @description: 日期格式化
   */
  formatDate(date: any) {
    date = new Date(date);
    const YY = `${date.getFullYear()}-`;
    const MM = `${date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1}-`;
    const DD = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
    const hh = `${date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()}:`;
    const mm = `${date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()}:`;
    const ss = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds();
    return `${YY + MM + DD} ${hh}${mm}${ss}`;
  }

  /**
   * @description: json格式转表单格式
   */
  jsonToFormData(params: any) {
    if (params != null) {
      const formData = new FormData();
      Object.keys(params).forEach((key) => {
        formData.append(key, params[key]);
      });
      return formData;
    }
  }

  /**
   * @description: url请求参数组装
   */
  urlQueryConvert(url: string, query: Query) {
    let connectiveSymbol = '';
    if (url.indexOf('?') !== -1) {
      connectiveSymbol = '&';
    } else {
      connectiveSymbol = '?';
    }
    if (query) {
      Object.keys(query).forEach((key, idx) => {
        const val = query[key];
        if (idx === 0) {
          if (val != null && val !== 'null' && val !== 'undefined') {
            url += `${connectiveSymbol + key}=${val}`;
          } else {
            url += `${connectiveSymbol + key}=`;
          }
        } else {
          if (val != null && val !== 'null' && val !== 'undefined') {
            url += `&${key}=${val}`;
          } else {
            url += `&${key}=`;
          }
        }
      });
    }
    return url;
  }
}
const apiService = new ApiService();

export default apiService;
