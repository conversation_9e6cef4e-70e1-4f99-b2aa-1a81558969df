<template>
  <div v-if="!$route.meta.hideTitle" class="container-title" :class="mode === 'dark' ? 'container-title-dark' : ''">
    <template v-if="$route.meta.hasBack">
      <t-button variant="text" @click="onBack" size="small">
        <arrow-left-icon slot="icon"></arrow-left-icon>
      </t-button>
    </template>
    {{ title }}
  </div>
</template>

<script>
import { ArrowLeftIcon } from 'tdesign-icons-vue';
import store from '@/store';

export default {
  name: 'container-title',
  components: { ArrowLeftIcon },
  data() {
    return {
      title: '',
      isRedirect: false,
    };
  },
  computed: {
    mode() {
      return this.$store.state.setting.mode;
    },
  },
  watch: {
    //  routeTitle是每个页面自定义的title,如不设置，就是取route的meta
    $route: {
      handler(newVal, old) {
        setTimeout(() => {
          const activeRoute = newVal.matched.find(({ name }) => name === newVal.name)?.instances;
          if (activeRoute) {
            this.title = activeRoute.default?.routeTitle || this?.$route?.meta?.title;
          }
        }, 0);
      },
      immediate: true,
    },
  },
  created() {
    if (this.$route.meta.hasBack) {
      this.isRedirect = true;
    }
  },
  methods: {
    onBack() {
      //  刷新页面后，丢失路由栈，此时就跳转到matched的第一个路由
      if (this.isRedirect) {
        this.$router.replace(this.$route.matched[0]?.path || '/home');
        this.isRedirect = false;
      } else {
        this.$router.back();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container-title {
  position: relative;
  width: 90%;
  height: 48px;
  font-size: 18px;
  color: #333333;
  font-weight: 600;
  background: #fff;
  padding: 20px 0;
  // border-left: 1px solid #eee;
  display: flex;
  align-items: center;
  z-index: 10;
  .t-button {
    height: 22px;
  }
  &.container-title-dark {
    border-bottom: 1px solid #2d2d2d;
    background: transparent;
  }
}
</style>
