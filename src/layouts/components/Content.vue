<template>
  <!--   这里需要去掉动画，因为动画结束之后，才会渲染组件，这就导致ContainertTitle里监听$route变化之后拿不到组件-->
  <!--    先去掉keep alive， 否则一些页面的组件不会触发生命周期hook-->
  <div>
    <template>
      <transition name="slide-fade">
        <keep-alive :max="15">
          <router-view :key="`${routerViewKey}_${$route.meta.refreshFlag || 0}`" v-if="$route.meta.keepAlive"></router-view>
        </keep-alive>
      </transition>
      <transition name="slide-fade">
        <router-view :key="routerViewKey" v-if="!$route.meta.keepAlive && !isRefreshing"></router-view>
      </transition>
    </template>
    <!--    <router-view v-if="!isRefreshing" />-->
    <upload-list v-if="isShowTabsRouter"/>
  </div>
</template>
<script lang="ts">
import {mapGetters} from 'vuex';
import uploadList from "@/components/upload-list/index.vue";

export default {
  components: {uploadList},
  data() {
    return {
      multipleIns: [],
    };
  },
  computed: {
    ...mapGetters({
      tabRouterList: 'tabRouter/tabRouterList',
      isRefreshing: 'tabRouter/isRefreshing',
      isUseTabsRouter: 'setting/isUseTabsRouter',
    }),
    isShowTabsRouter() {
      return this.$store.getters.getShowUpload;
    },
    aliveViews() {
      return this.tabRouterList.filter((route: any) => route.isAlive).map((route: any) => route.name);
    },
    /**
     * @description: 路由缓存key
     * <AUTHOR>
     * @date 2021/2/26 10:20
     */
    routerViewKey() {
      return this.multipleIns.indexOf(this.$route.path) != -1 ? this.$route.fullPath : this.$route.path;
    }
  },
};
</script>
<style lang="less" scoped>
@import '@/style/variables';

.fade-leave-active,
.fade-enter-active {
  transition: opacity @anim-duration-slow @anim-time-fn-easing;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
