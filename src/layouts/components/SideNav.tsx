import Vue from 'vue';
import {prefix, userMenu} from '@/config/global';

import MenuContent from './MenuContent';
import { BaseMenuList } from '@/router';
import { SYSTEM_MENU_CONST } from "@/constants/enum/system/system-menu.enum";

// import pgk from '../../../package.json';

const MIN_POINT = 992 - 1;

export default Vue.extend({
  name: 'sideNav',
  components: {
    MenuContent,
  },
  props: {
    menu: Array,
    showLogo: {
      type: Boolean,
      default: true,
    },
    isFixed: {
      type: Boolean,
      default: true,
    },
    layout: String,
    headerHeight: {
      type: String,
      default: '64px',
    },
    theme: {
      type: String,
      default: 'light',
    },
    isCompact: {
      type: Boolean,
      default: false,
    },
    maxLevel: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      prefix,
      userMenu,
    };
  },
  mounted() {
    this.autoCollapsed();
    window.onresize = () => {
      this.autoCollapsed();
    };
    // 登录后用户具有的菜单已经存入了storage中，从中获取
    const remoteMenus: any[] = JSON.parse(window.localStorage.getItem('menu') || "");
    // console.log("用户菜单：", remoteMenus);
    // 为了防止退出登录后重新登录，每次加载菜单栏都需要先清空
    this.userMenu = [];
    if(remoteMenus && remoteMenus.length > 0){
      remoteMenus.map((item: any) => {
        if(item.menuType && item.menuType == SYSTEM_MENU_CONST.M.value){
          // 先收集所有菜单，然后统一过滤为树形结构
          this.userMenu.push(item);
        }
      })
    }
    console.log("收集到所有菜单：", this.userMenu);
    this.userMenu = this.getUserMenu(this.userMenu);
    // 需要保持菜单顺序，所以拼接后还需要排序下
    const systemMenu: any[] = this.menuToShow.concat(this.userMenu);
    this.userMenu = systemMenu.sort((a: any, b: any) => a.sortNo - b.sortNo);
    console.log("用户菜单转换为路由：", this.userMenu);
  },
  computed: {
    tenantParameterConfig() {
      return this.$store.getters['config/tenantParameterConfig'] || {};
    },
    appName() {
      return (
        import.meta.env.VITE_APP_NAME
      );
    },
    menuToShow() {
      const menus: any[] = [];
      // const { tenantParameterConfig = {} } = this.$store.state?.config || {};
      const tenantParameterConfig = this.$store.getters['config/tenantParameterConfig'] || {};
      // this.$store.state.config
      // const testMenu = [{
      //   path: "/home/<USER>",
      //   name: "首页",
      //   type: "base",
      //   sortNo: 0,
      //   meta: {
      //     icon: "t-icon-home",
      //     title: "首页"
      //   }
      // },
      // {
      //   name: "客户管理",
      //   path: "/customer-management/customer-management-main",
      //   sortNo: 2,
      //   meta: {
      //     title: "客户管理",
      //     icon: "t-icon-usergroup"
      //   },
      //   children: []
      // },
      // {
      //   name: "用户管理",
      //   path: "/user-management/user-management-main",
      //   sortNo: 3,
      //   meta: {
      //     title: "用户管理",
      //     icon: "t-icon-user"
      //   },
      //   children: []
      // },
      // {
      //   name: "组织管理",
      //   path: "/organization-management/organization-management-main",
      //   sortNo: 2,
      //   meta: {
      //     title: "组织管理",
      //     icon: "t-icon-fork"
      //   },
      //   children: []
      // },
      // {
      //   name: "视频中心",
      //   path: "/video-center/video-center-main",
      //   sortNo: 5,
      //   meta: {
      //     title: "视频中心",
      //     icon: "t-icon-video"
      //   },
      //   children: []
      // },
      // {
      //   name: "课程管理",
      //   path: "/video-management/video-management-main",
      //   sortNo: 6,
      //   meta: {
      //     title: "课程管理",
      //     icon: "t-icon-bulletpoint"
      //   },
      //   children: []
      // },
      // {
      //   name: "营销活动",
      //   path: "/marketing-campaign/marketing-campaign-main",
      //   sortNo: 7,
      //   meta: {
      //     title: "营销活动",
      //     icon: "t-icon-activity"
      //   },
      //   children: []
      // },
      // {
      //   name: "训练营",
      //   path: "/training-camp/training-camp-main",
      //   sortNo: 8,
      //   meta: {
      //     title: "训练营",
      //     icon: "t-icon-play-demo"
      //   },
      //   children: []
      // },
      // {
      //   name: "小程序管理",
      //   path: "/miniprogram-management/miniprogram-management-main",
      //   sortNo: 9,
      //   meta: {
      //     title: "小程序管理",
      //     icon: "t-icon-logo-wechat-stroke"
      //   },
      //   children: []
      // },
      // {
      //   name: "活动记录",
      //   path: "/activity-record/activity-record-main",
      //   sortNo: 9,
      //   meta: {
      //     title: "活动记录",
      //     icon: "t-icon-logo-wechat-stroke"
      //   },
      //   children: []
      // },
      //   {
      //     name: "红包领取记录",
      //     path: "/course-redpacket/course-redpacket-main",
      //     sortNo: 10,
      //     meta: {
      //       title: "红包领取记录",
      //       icon: "t-icon-logo-wechat-stroke"
      //     },
      //     children: []
      //   },
      // ]
      const menuCopy=[...[]];
      // console.log("系统菜单：", menuCopy)
      for (let i = 0; i < menuCopy.length; i++) {
        menus.push(menuCopy[i]);
      }
      return menus;
    },
    iconName(): string {
      return this.$store.state.setting.isSidebarCompact ? 'menu-fold' : 'menu-unfold';
    },
    collapsed(): boolean {
      return this.$store.state.setting.isSidebarCompact;
    },
    sideNavCls(): Array<ClassName> {
      return [
        `${this.prefix}-sidebar-layout`,
        {
          [`${this.prefix}-sidebar-compact`]: this.isCompact,
        },
      ];
    },
    menuCls(): Array<ClassName> {
      return [
        `${this.prefix}-side-nav`,
        {
          [`${this.prefix}-side-nav-no-logo`]: !this.showLogo,
          [`${this.prefix}-side-nav-no-fixed`]: !this.isFixed,
          [`${this.prefix}-side-nav-mix-fixed`]: this.layout === 'mix' && this.isFixed,
        },
      ];
    },
    layoutCls(): Array<ClassName> {
      return [`${this.prefix}-side-nav-${this.layout}`, `${this.prefix}-sidebar-layout`];
    },
    active(): string {
      return this.$route.path;
      // if (!this.$route.path) {
      //   return '';
      // }
      // const check = this.checkBaseMenuList(this.$route.path);
      // const menuFilter = this.$route.path
      //   .split('/')
      //   .filter((_item: string, index: number) => index <= this.maxLevel && index > 0);
      // let result;
      // if (check) {
      //   result = menuFilter.map((item) => `/${item}`);
      //   // check = [`/${menuFilter[0]}`, `${menuFilter[0] ? '/' + menuFilter[0] + '-main' : ''}`].join('');
      // } else {
      //   result = [`/${menuFilter[0]}`, `${menuFilter[0] ? `/${menuFilter[0]}-main` : ''}`];
      // }
      // return result.join('');
    },
    mode(): string {
      return this.$store.state.setting.mode;
    },
  },
  methods: {
    changeCollapsed(): void {
      this.$store.commit('setting/toggleSidebarCompact');
    },
    autoCollapsed(): void {
      const isCompact = window.innerWidth <= MIN_POINT;
      this.$store.commit('setting/showSidebarCompact', isCompact);
    },
    handleNav(url: string) {
      this.$router.push(url);
    },
    checkBaseMenuList(path: string): boolean {
      let result = false;
      BaseMenuList.forEach((ele) => {
        if (path === ele.path) {
          result = true;
        }
      });
      return result;
    },
    /**
    * @description: 用户菜单转化
    * <AUTHOR>
    * @date 2025/5/3 23:58
    */
    getUserMenu(elements: any[]): any[] {
      // 创建节点映射表（id -> 节点）
      const nodeMap = new Map();
      elements.forEach(element => {
        nodeMap.set(element.id, {
          ...element,
          children: [] // 初始化children数组
        });
      });
      // 构建树形结构
      const tree: any[] = [];
      nodeMap.forEach(node => {
        const {parentId} = node;
        if (parentId && nodeMap.has(parentId)) {
          // 有父节点：添加到父节点的children中
          const parent = nodeMap.get(parentId);
          parent.children.push(node);
        } else {
          // 无父节点：作为根节点
          tree.push(node);
        }
      });
      // 递归转换树形结构为菜单格式
      const convertToMenu = (node: any): any => ({
        name: node.name,
        path: node.menuUrl,
        sortNo: node.sortOrder,
        meta: {
          title: node.name,
          icon: node.icon,
        },
        children: node.children
          .sort((a: any, b: any) => a.sortOrder - b.sortOrder)
          .map((child: any) => convertToMenu(child))
      });
        // 返回排序后的菜单树
      return tree.map(root => convertToMenu(root));
    }
  },
  render() {
    return (
      <div class={this.sideNavCls}>
        <t-menu width="232px" class={this.menuCls} theme={this.theme} value={this.active} collapsed={this.collapsed}>
          {this.showLogo && (
            <span slot="logo" class={`${prefix}-side-nav-logo-wrapper`} onClick={() => this.handleNav('/home/<USER>')}>
              {this.collapsed ? (
                <div class="menu-logo-icon">
                  {this.tenantParameterConfig.systemLogo ?
                    <img
                      style="width:80%"
                      src={this.tenantParameterConfig.systemLogo}
                    />
                    :<Logo class={`${prefix}-side-nav-logo-t-logo`} />}
                </div>
              ) : (
                <div class="logo-area">
                  {(() => {
                    if (this.tenantParameterConfig.systemLogo || this.appName) {
                      return (
                        <div class="side-nav-logo-custom">
                          {this.tenantParameterConfig.systemLogo && (
                            <img
                              class={`custom-logo ${this.tenantParameterConfig.systemLogo && this.appName && 'mr10 ml10'}`}
                              src={this.tenantParameterConfig.systemLogo}
                            />
                          )}
                          {this.appName && (
                            <span class={`custom-text ${!this.tenantParameterConfig.systemLogo && 'no-logo'}`}>
                              {this.appName}
                            </span>
                          )}
                        </div>
                      );
                    }
                    return this.mode === 'dark' ? (
                      <LogoFullLight class={`${prefix}-side-nav-logo-tdesign-logo`} />
                    ) : (
                      <LogoFull class={`${prefix}-side-nav-logo-tdesign-logo`} />
                    );
                  })()}
                </div>
              )}
            </span>
          )}
          <menu-content navData={this.userMenu}></menu-content>
        </t-menu>
        <div class={`${prefix}-side-nav-placeholder${this.collapsed ? '-hidden' : ''}`}></div>
      </div>
    );
  },
});
