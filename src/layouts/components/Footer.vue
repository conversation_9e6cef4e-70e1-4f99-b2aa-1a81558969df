<template>
</template>

<script lang="ts">
import { prefix } from '@/config/global';
import Vue from 'vue';

export default Vue.extend({
  name: `${prefix}-footer`,
  data() {
    return {
      prefix,
      isHideFooter: import.meta.env.VITE_HIDE_FOOTER === '1',
    };
  },
});
</script>
<style lang="less" scoped>
@import '@/style/variables';

.@{prefix}-footer {
  color: @text-color-placeholder;
  line-height: 20px;
  text-align: center;
}
</style>
