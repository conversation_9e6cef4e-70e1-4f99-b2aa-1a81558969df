<template>
  <div :class="layoutCls">
    <t-head-menu :class="menuCls" :theme="theme" expandType="popup" :value="active">
      <template #logo>
        <span v-if="showLogo" class="header-logo-container" @click="handleNav('/dashboard/base')">
          <logo-full class="t-logo" />
        </span>
        <div v-else class="header-operate-left">
          <t-button theme="default" shape="square" variant="text" @click="changeCollapsed">
            <view-list-icon class="collapsed-icon" />
          </t-button>
          <!-- <search :layout="layout" /> -->
        </div>
      </template>
      <!-- <menu-content v-show="layout !== 'side'" class="header-menu" :navData="menu" /> -->
      <template #operations>
        <div class="operations-container">
          <!-- 搜索框 -->
          <!-- <search v-if="layout !== 'side'" :layout="layout" /> -->

          <!-- 全局通知 -->
          <!-- <notice /> -->

          <!-- <t-tooltip placement="bottom" content="代码仓库">
            <t-button theme="default" shape="square" variant="text" @click="navToGitHub">
              <logo-github-icon />
            </t-button>
          </t-tooltip> -->
          <!-- <t-tooltip placement="bottom" content="帮助文档">
            <t-button theme="default" shape="square" variant="text" @click="navToHelper">
              <help-circle-icon />
            </t-button>
          </t-tooltip> -->
          <t-dropdown :min-column-width="125" trigger="click">
            <template #dropdown>
              <t-dropdown-menu>
                <t-dropdown-item class="operations-dropdown-container-item" @click="handleNav('/user/index')">
                  <user-circle-icon />个人中心
                </t-dropdown-item>
                <t-dropdown-item class="operations-dropdown-container-item" @click="changePassword">
                  <user-talk-icon />修改密码
                </t-dropdown-item>
                <t-dropdown-item class="operations-dropdown-container-item" @click="handleLogout">
                  <poweroff-icon />退出登录
                </t-dropdown-item>
              </t-dropdown-menu>
            </template>
            <t-button class="header-user-btn" theme="default" variant="text">
              <template #icon>
                <div v-if="userInfo.headImg" class="head-img"><img :src="userInfo.headImg" /></div>
                <user-circle-icon v-else class="header-user-avatar" />
              </template>
              <div class="header-user-account">
                {{ userInfo.username }}
                <chevron-down-icon />
              </div>
            </t-button>
          </t-dropdown>
<!--          <t-tooltip placement="bottom" content="系统设置">-->
<!--            <t-button theme="default" shape="square" variant="text" @click="toggleSettingPanel">-->
<!--              <setting-icon />-->
<!--            </t-button>-->
<!--          </t-tooltip>-->
          <t-dialog :closeOnOverlayClick="false" header="修改密码" v-if="modalVisible" :visible="modalVisible" :width="500" :confirm-loading="confirmLoading" :maskClosable="false" @confirm="handleConfirmOk" @close="handleCancel">
            <t-form ref="formRef" @submit="onSubmit" :data="form" :rules="rules" :label-col="{ flex: '75px' }">
              <!-- 菜单名称 -->
              <t-form-item label="旧密码" name="oldPassword">
                <t-input v-model="form.oldPassword" :maxLength="10" type="password" />
              </t-form-item>
              <t-form-item label="新密码" name="newPassword">
                <t-input v-model="form.newPassword" :maxLength="10" type="password" />
              </t-form-item>
              <!-- 菜单名称 -->
              <t-form-item label="确认密码" name="repeatNewPassword">
                <t-input v-model="form.repeatNewPassword" :maxLength="10" type="password" />
              </t-form-item>
            </t-form>
          </t-dialog>
        </div>
      </template>
    </t-head-menu>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  ViewListIcon,
  // LogoGithubIcon,
  // HelpCircleIcon,
  UserCircleIcon,
  PoweroffIcon,
  UserTalkIcon,
  SettingIcon,
  ChevronDownIcon,
} from 'tdesign-icons-vue';
import { prefix } from '@/config/global';
import { BaseMenuList } from '@/router';
import { SM4, SM3, SM2 } from "gm-crypto";
import sysUserLoginApi from "@/constants/api/back/sys-user-login.api";

export default Vue.extend({
  components: {
    // MenuContent,
    // Notice,
    // Search,
    ViewListIcon,
    // LogoGithubIcon,
    // HelpCircleIcon,
    UserCircleIcon,
    PoweroffIcon,
    UserTalkIcon,
    SettingIcon,
    ChevronDownIcon,
  },
  props: {
    theme: String,
    layout: {
      type: String,
      default: 'top',
    },
    showLogo: {
      type: Boolean,
      default: true,
    },
    menu: {
      type: Array,
    },
    isFixed: {
      type: Boolean,
      default: false,
    },
    isCompact: {
      type: Boolean,
      default: false,
    },
    maxLevel: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      prefix,
      visibleNotice: false,
      isSearchFocus: false,
      BaseMenuList,
      modalVisible: false,
      confirmLoading: false, // 请求等待状态标识
      key: "7c4a8d09ca3762af61e59520943dc264", // 16 字节的密钥
      user: JSON.parse(window.localStorage.getItem('core:user') || '{}'),
      form: {
        newPassword: "",
        repeatNewPassword: "",
        oldPassword: "",
        userId: "",
        identityType: "",
      },
      rules: {
        oldPassword: [{ required: true, message: "请输入旧密码" }],
        newPassword: [
          { required: true, message: "请输入密码" },
          { validator: this.checkPasswordStrength, trigger: "change" },
        ],
        repeatNewPassword: [
          { required: true, message: "请再次输入密码", trigger: "blur" },
          { validator: this.validateRepeatPass, trigger: "change" },
        ],
      },
    };
  },
  computed: {
    userInfo() {
      let user = {};
      try {
        user = JSON.parse(window.localStorage.getItem('core:user') || '{}');
      } catch (e) {
        user = {};
      }
      return user;
    },
    active() {
      if (!this.$route.path) {
        return '';
      }
      const isBase = this.isBaseRoute(this.$route.path);
      if (!isBase) {
        const result = this.$route.path.split('/').filter((item, index) => index <= this.maxLevel && index > 0);
        const resultString = `/${result[0]}/${result[0]}-main`;
        console.log(resultString);
        return resultString;
      }
      return this.$route.path
        .split('/')
        .filter((item, index) => index <= this.maxLevel && index > 0)
        .map((item) => `/${item}`)
        .join('');
    },
    showMenu() {
      return !(this.layout === 'mix' && this.showLogo === 'side');
    },
    layoutCls() {
      return [`${this.prefix}-header-layout`];
    },
    menuCls() {
      return [
        {
          [`${this.prefix}-header-menu`]: !this.isFixed,
          [`${this.prefix}-header-menu-fixed`]: this.isFixed,
          [`${this.prefix}-header-menu-fixed-side`]: this.layout === 'side' && this.isFixed,
          [`${this.prefix}-header-menu-fixed-side-compact`]: this.layout === 'side' && this.isFixed && this.isCompact,
        },
      ];
    },
  },
  // mounted(){
  //   console.log(111);
  //   const user
  // },
  watch: {
    modalVisible: {
      handler(newValue) {
        if(!newValue ) {
          this.form.newPassword = "";
          this.form.repeatNewPassword = "";
        }
      },

      deep: true,
    },
  },
  methods: {
    // toggleSettingPanel() {
    //   this.$store.commit('setting/toggleSettingPanel', true);
    // },
    changePassword() {
      this.modalVisible = true;
    },
    validateRepeatPass(value) {
      if (value !== this.form.newPassword) {
        return {
          result: false,
          message: "两次密码不一致",
        };
      }
      return {
        result: true,
      };

    },
    checkPasswordStrength(password) {
      const hasUpperCase = /[A-Z]/.test(password);
      const hasLowerCase = /[a-z]/.test(password);
      const hasSpecialChar = /[!@#$%^&*~]/.test(password);
      const isLengthValid = password.length >= 8 && password.length <= 10;
      const hasNoSpaces = !/\s/.test(password);

      if (!hasNoSpaces) {
        return {
          result: false,
          message: "不能有空格",
        };
      }
      if (!hasUpperCase) {
        return {
          result: false,
          message: "密码必须包含大写字母",
        };
      }
      if (!hasLowerCase) {
        return {
          result: false,
          message: "密码必须包含小写字母",
        };
      }
      if (!hasSpecialChar) {
        return {
          result: false,
          message: "密码必须包含特殊字符(!@#$ %&*~)",
        };
      }
      if (!isLengthValid) {
        return {
          result: false,
          message: "密码长度需8 ~ 10位",
        };
      }
      return {
        result: true,
      };
    },
    handleConfirmOk() {
      this.$refs.formRef.submit();
    },
    arrayBufferToHex(buffer) {
      const hex = Array.from(new Uint8Array(buffer))
        .map((byte) => byte.toString(16).padStart(2, "0"))
        .join("");
      return hex;
    },
    encrypt(password) {
      const encrypted = SM4.encrypt(password, this.key, {
        inputEncoding: "utf8",
        outputEncoding: "base64",
      });
      return encrypted;
    },
    async handleOk() {
      this.confirmLoading = true;
      const formData = JSON.parse(JSON.stringify(this.form));

      const newPassword = this.encrypt(this.form.newPassword);
      const oldPassword = this.encrypt(this.form.oldPassword);
      formData.newPassword = newPassword;
      formData.repeatNewPassword = newPassword;
      formData.userId = this.user?.userId;
      formData.oldPassword = oldPassword;
      formData.identityType = "1"; // 密码模式登录 1 密码 2 手机 3 邮箱


      const res = await this.$request.put(sysUserLoginApi.updatePassword.url, formData);
      const { code, data = [], msg } = res;
      this.confirmLoading = false;
      if (code === 1) {
        this.$message.success({ content: msg });
        this.modalVisible = false;
      } else {
        this.$message.error({ content: msg });
      }
    },
    onSubmit(result) {
      if (result.validateResult === true) {
        this.handleOk();
      }
    },
    handleCancel(event) {
      this.modalVisible = false;
    },
    async handleLogout() {
      const loginOutUrl = `${import.meta.env.VITE_APP_AUTH_API  }/auth/logout`;
      const res = await this.$request.post(loginOutUrl);
      if (res.code === 0) {
        this.$message.success('登出成功');
      } else {
        this.$message.error(res.msg || '登录失败');
      }
      localStorage.removeItem('token');
      localStorage.removeItem('core:user');
      localStorage.removeItem('tabRouterList');
      this.$store.commit('tabRouter/removeTabRouterList');
      this.$router.push(`/login?redirect=${this.$router.history.current.fullPath}`);
    },
    changeCollapsed() {
      this.$store.commit('setting/toggleSidebarCompact');
    },
    handleNav(url) {
      this.$router.push(url);
    },
    isBaseRoute(path) {
      let result = false;
      this.BaseMenuList.forEach((e) => {
        if (e.path === path) {
          result = true;
        }
      });
      return result;
    },
    // navToGitHub() {
    //   window.open('https://github.com/Tencent/tdesign-vue-starter');
    // },
    // navToHelper() {
    //   window.open('http://tdesign.tencent.com/starter/docs/get-started');
    // },
  },
});
</script>
<style lang="less">
@import '@/style/variables.less';

.header-menu {
  flex: 1 1 1;
  display: inline-flex;
}

.operations-container {
  display: flex;
  align-items: center;
  margin-right: 12px;

  .t-popup__reference {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .t-button {
    margin: 0 8px;
    width: auto;
    &.header-user-btn {
      margin: 0;
    }
  }

  .t-icon {
    font-size: 20px;

    &.general {
      margin-right: 16px;
    }
  }
  .head-img {
    img {
      width: 25px;
      border-radius: 25px;
      margin: 0 5px;
      position: relative;
      top: 3px;
    }
  }
}

.header-operate-left {
  display: flex;
  margin-left: 20px;
  align-items: normal;
  line-height: 0;

  .collapsed-icon {
    font-size: 20px;
  }
}

.header-logo-container {
  width: 184px;
  height: 26px;
  display: flex;
  margin-left: 24px;
  color: @text-color-primary;

  .t-logo {
    width: 100%;
    height: 100%;

    &:hover {
      cursor: pointer;
    }
  }

  &:hover {
    cursor: pointer;
  }
}

.header-user-account {
  display: inline-flex;
  align-items: center;
  color: @text-color-primary;

  .t-icon {
    margin-left: 4px;
    font-size: 16px;
  }
}

.t-head-menu__inner {
  height: 48px;
  border-bottom: 1px solid @border-level-1-color;
}

.t-menu--light {
  .header-user-account {
    color: @text-color-primary;
  }
}

.t-menu--dark {
  .t-head-menu__inner {
    height: 48px;
    border-bottom: 1px solid var(--td-gray-color-10);
  }

  .header-user-account {
    color: rgba(255, 255, 255, 0.55);
  }

  .t-button {
    --ripple-color: var(--td-gray-color-10) !important;

    &:hover {
      background: var(--td-gray-color-12) !important;
    }
  }
}

.operations-dropdown-container-item {
  width: 100%;
  display: flex;
  align-items: center;

  .t-icon {
    margin-right: 8px;
  }

  .t-dropdown__item {
    .t-dropdown__item__content {
      display: flex;
      justify-content: center;
    }

    .t-dropdown__item__content__text {
      display: flex;
      align-items: center;
      font-size: 14px;
    }
  }

  .t-dropdown__item {
    width: 100%;
    margin-bottom: 0px;
  }

  &:last-child {
    .t-dropdown__item {
      margin-bottom: 8px;
    }
  }
}
</style>
