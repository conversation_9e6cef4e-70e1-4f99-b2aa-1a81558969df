<template>
  <router-view :class="[mode]"/>
</template>

<script>
import Vue from 'vue';
import config from '@/config/style';

export default Vue.extend({
  computed: {
    mode() {
      return this.$store.getters['setting/mode'];
    },
  },
  created() {
    // this.$store.dispatch('config/changeTenantParameterConfig')

  },
  mounted() {
    this.$store.dispatch('setting/changeTheme', {...config});
    this.$router.beforeEach((to, from, next) => {
      const fromPath = from.path;
      const toPath = to.path;
      console.error('fromPath', fromPath, 'toPath', toPath)
      if (toPath === '/video-management/new-video') {
        switch (to.params.type) {
        case 'edit':
          to.meta.title = '编辑视频';
          to.meta.refreshFlag = Date.now();
          break;
        case 'copy':
          to.meta.title = '复制视频';
          to.meta.refreshFlag = Date.now();
          break;
        case 'new':
          to.meta.title = '新建视频';
          to.meta.refreshFlag = Date.now();
          break;
        default:
          to.meta.title = '新建视频';
          break;
        }
      }
      if (toPath === '/video-management/video-management-main') {
        switch (to.params.type) {
        case 'refresh':
          to.meta.refreshFlag = Date.now();
          break;
        default:
          break;
        }
      }
      if (toPath === '/training-camp/training-camp-main') {
        switch (to.params.type) {
        case 'refresh':
          to.meta.refreshFlag = Date.now();
          break;
        default:
          break;
        }
      }
      if (toPath === '/marketing-campaign/marketing-campaign-main') {
        switch (to.params.type) {
        case 'refresh':
          to.meta.refreshFlag = Date.now();
          break;
        default:
          break;
        }
      }
      if (toPath === '/training-camp/new-training-camp') {
        switch (to.params.type) {
        case 'edit':
          to.meta.title = '编辑营期';
          to.meta.refreshFlag = Date.now();
          break;
        case 'copy':
          to.meta.title = '复制营期';
          to.meta.refreshFlag = Date.now();
          break;
        case 'new':
          to.meta.title = '新建营期';
          to.meta.refreshFlag = Date.now();
          break;
        default:
          to.meta.title = '新建营期';
          break;
        }
      }
      if (toPath === '/marketing-campaign/new-marketing-campaign') {
        switch (to.params.type) {
        case 'edit':
          to.meta.title = '编辑营销活动';
          to.meta.refreshFlag = Date.now();
          break;
        case 'new':
          to.meta.title = '新建营销活动';
          to.meta.refreshFlag = Date.now();
          break;
        default:
          to.meta.title = '新建营销活动';
          break;
        }
      }
      if (toPath === '/training-camp/training-camp-data') {
        switch (to.params.type) {
        case 'detailData':
          to.meta.title = '营期数据';
          to.meta.refreshFlag = Date.now();
          break;
        default:
          to.meta.title = '营期数据';
          break;
        }
      }
      if (toPath === '/enterprise-wechat-management/enterprise-wechat-message') {
        switch (to.params.type) {
        case 'new':
          to.meta.refreshFlag = Date.now();
          break;
        default:
          break;
        }
      }
      if (toPath.includes('/mall-management/product-form/edit')) {
        switch (to.params.type) {
        case 'copy':
          to.meta.title = '复制商品';
          break;
        default:
          to.meta.title = '编辑商品';
          break;
        }
      }
      if ((fromPath === '/login' || fromPath === '/') && toPath !== '/login') {
        this.$store.dispatch('config/changeTenantParameterConfig');

      }

      next()
    });
  },
});
</script>
