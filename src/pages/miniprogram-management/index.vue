<template>
  <div>
    <div class="search-input">
      <t-form :data="formData" layout="inline">
        <t-form-item label="小程序名称" name="name">
          <t-input-adornment>
            <t-input v-model="formData.name" type="search" placeholder="请输入小程序名称" :style="{ minWidth: '134px' }" />
          </t-input-adornment>
        </t-form-item>
        <t-form-item label="类型" name="type">
          <t-select v-model="formData.type" :options="typeOptions" clearable placeholder="请选择类型" :style="{ minWidth: '134px' }" />
        </t-form-item>
      </t-form>
      <div class="search-btn">
        <t-button theme="primary" @click="onSubmit">查询</t-button>
        <t-button theme="primary" @click="onReset" ghost>重置</t-button>
      </div>
    </div>
    <div class="operation-container">
      <t-button theme="primary" @click="handleAdd">新增小程序</t-button>
    </div>
    <t-card class="card-container">
      <t-table :columns="columns" :data="data" :rowKey="rowKey" :pagination="pagination"
        :selected-row-keys="selectedRowKeys" :loading="dataLoading" @change="rehandleChange"
        @select-change="rehandleSelectChange">
        <template #type="{ row }">
          <span v-if="row.type === '1'"> 微信小程序 </span>
          <span v-else-if="row.type === '2'"> 微信公众号 </span>
        </template>
        <template #status="{ row }">
          <t-tag v-if="row.status == 1" theme="success">启用</t-tag>
          <t-tag v-else-if="row.status == 0" theme="danger">禁用</t-tag>
        </template>
<!--        <template #status="{ row }">-->
<!--          <span v-if="row.status === '0'" style="color: red"> 禁用 </span>-->
<!--          <span v-else-if="row.status === '1'" style="color: green"> 启用 </span>-->
<!--        </template>-->
        <!-- 新增操作列插槽 -->
        <template #op="{ row }">
          <t-button variant="text" theme="primary" @click="handleEdit(row)">编辑</t-button>
          <t-button variant="text" theme="primary" @click="handleDetail(row)">详情</t-button>
          <t-button v-if="row.type === '1'" variant="text" theme="primary" @click="handlePermission(row)">权限设置</t-button>
          <t-button variant="text" theme="danger" @click="handleDelete(row)">删除</t-button>
        </template>
      </t-table>
      <t-dialog :visible="formDialog.visible" :header="formDialog.title" width="600px" @confirm="handleSubmit"
        @cancel="handleCancel" @close="handleCancel">
        <t-form ref="formRef" :data="formDialog.formData" :rules="formRules" label-align="right" :label-width="100">
          <t-form-item label="小程序名称" name="name" prop="name">
            <t-input v-model="formDialog.formData.name" placeholder="请输入名称" />
          </t-form-item>
          <t-form-item label="类型" name="type" prop="type">
            <t-select v-model="formDialog.formData.type" :options="typeOptions" />
          </t-form-item>
          <t-form-item label="APPID" name="appid" prop="appid">
            <t-input v-model="formDialog.formData.appid" placeholder="请输入APPID" />
          </t-form-item>
          <t-form-item label="SECRET" name="secret" prop="secret">
            <t-input v-model="formDialog.formData.secret" placeholder="请输入SECRET" />
          </t-form-item>
          <t-form-item label="备注" name="remark" prop="remark">
            <t-input v-model="formDialog.formData.remark" placeholder="请输入备注" />
          </t-form-item>
          <t-form-item label="状态" name="status" prop="status">
            <t-select v-model="formDialog.formData.status" :options="statusOptions" />
          </t-form-item>
        </t-form>
      </t-dialog>

      <t-dialog :visible="formDialog1.visible" :header="formDialog1.title" width="600px" @cancel="handleCancel"
        @close="handleCancel" :footer="null">
        <t-form ref="formRef1" :data="formDialog1.formData" label-align="right" :label-width="100" :disabled="true">
          <t-form-item label="小程序名称" name="name">
            <t-input v-model="formDialog1.formData.name" />
          </t-form-item>
          <t-form-item label="类型" name="type">
            <t-select v-model="formDialog1.formData.type" :options="typeOptions" />
          </t-form-item>
          <t-form-item label="APPID" name="appid">
            <t-input v-model="formDialog1.formData.appid" />
          </t-form-item>
          <t-form-item label="SECRET" name="secret">
            <t-input v-model="formDialog1.formData.secret" />
          </t-form-item>
          <t-form-item label="备注" name="remark">
            <t-input v-model="formDialog1.formData.remark" />
          </t-form-item>
          <t-form-item label="状态" name="status">
            <t-select v-model="formDialog1.formData.status" :options="statusOptions" />
          </t-form-item>
        </t-form>
      </t-dialog>

      <t-dialog :visible="deleteDialogVisible" header="删除确认" @confirm="handleConfirmDelete"
        @cancel="deleteDialogVisible = false" @close="deleteDialogVisible = false" >
        <p>确定要删除这条记录吗？</p>
      </t-dialog>

      <!-- 权限设置对话框 -->
      <t-dialog :visible="permissionDialog.visible" header="权限设置" width="600px" @confirm="handlePermissionSubmit"
        @cancel="handlePermissionCancel" @close="handlePermissionCancel">
        <div>
          <p class="permission-title">为"{{ permissionDialog.miniProgramName }}"设置可访问的公司</p>
          <t-transfer
            v-model="permissionDialog.selectedCompanyIds"
            :data="permissionDialog.companyList"
            :title="['可选公司', '已选公司']"
            :search="true"
            :empty="{ left: '暂无可选公司', right: '暂无已选公司' }"
          ></t-transfer>
        </div>
      </t-dialog>
    </t-card>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { ACTIVE_TYPE_OPTIONS } from '@/constants';
import dayjs from 'dayjs';
import sysMiniProgram from '@/constants/api/back/sys-miniprogram.api';
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";

export default Vue.extend({
  name: 'MiniProgramManagementMain',
  components: {
  },
  data() {
    return {
      ACTIVE_TYPE_OPTIONS,
      // 侧边栏
      drawerVar: {
        visible: false,
        data: {},
      },
      // 客户创建时间
      createTime: [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
      ],
      // 搜索条件
      formData: {
        name: '',
        status: '',
        type: '',
      },
      // 批量操作选项
      batchOperationOption: [
        {
          content: '暂未开放',
          value: '1',
        },
      ],
      // 新增弹窗相关
      formDialog: {
        visible: false,
        title: '新增',
        formData: {
          id: '',
          name: '',
          type: '1',
          appid: '',
          secret: '',
          remark: '',
          status: '0'
        }
      },
      // 详情弹窗相关
      formDialog1: {
        visible: false,
        title: '新增',
        formData: {
          id: '',
          name: '',
          type: '1',
          appid: '',
          secret: '',
          remark: '',
          status: '0'
        }
      },
      typeOptions: [
        { label: '微信小程序', value: '1' },
        { label: '微信公众号', value: '2' }
      ],
      statusOptions: [
        { label: '禁用', value: '0' },
        { label: '启用', value: '1' }
      ],
      formRules: {
        name: [{ required: true, message: '名称必填' }],
        type: [{ required: true }],
        appid: [{ required: true, message: 'APPID必填' }],
        secret: [{ required: true, message: 'SECRET必填' }]
      },
      columns: [
        // {
        //   colKey: 'row-select',
        //   type: 'multiple',
        //   width: 64,
        //   fixed: 'left',
        //   disabled: (data: any) => {
        //     const { row } = data;
        //     return !!row.superAdmin;
        //   },
        // },
        {
          title: '小程序APPID',
          align: 'left',
          width: 140,
          colKey: 'appid',
        },
        {
          title: '小程序名称',
          align: 'left',
          width: 140,
          colKey: 'name',
        },
        {
          title: '小程序类型',
          align: 'left',
          width: 120,
          colKey: 'type',
        },
        {
          title: '小程序状态',
          align: 'left',
          width: 140,
          colKey: 'status',
        },
        {
          title: '创建时间',
          align: 'left',
          width: 140,
          colKey: 'createdAt',
        },
        {
          title: '操作',
          align: 'center',
          width: 220,
          colKey: 'op',
          fixed: 'right',
          cell: 'op',
        },
      ],
      rowKey: 'customerId',
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      deleteDialogVisible: false,
      selectedRow: null,
      // 权限设置对话框相关
      permissionDialog: {
        visible: false,
        miniProgramId: '',
        miniProgramName: '',
        companyList: [],
        selectedCompanyIds: [],
        loading: false
      }
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
  },
  async mounted() {
    this.getList();
  },

  methods: {
    // 显示详情
    showDrawer(slotProps: any) {
      this.drawerVar.visible = true;
      this.drawerVar.data = slotProps?.row;
      console.log("slotProps", slotProps);
    },
    // 关闭详情
    drawerCancel() {
      this.drawerVar.visible = false;
    },
    rehandleChange(changeParams: any) {
      this.pagination.pageSize = changeParams.pagination.pageSize;
      this.pagination.current = changeParams.pagination.current;
      this.getList();
      // console.log('统一Change', changeParams);
    },
    onSubmit(result: any) {
      console.log("this.formData", this.formData);
      this.pagination.current = 1;
      this.getList();
    },
    onReset() {
      this.pagination.current = 1;
      this.formData = {
        name: '',
        status: '',
        type: '',
      };
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    // 获取初始表单数据方法
    getInitialFormData() {
      return {
        id: '',
        name: '',
        type: '1',
        appid: '',
        secret: '',
        remark: '',
        status: '2'
      }
    },
    handleAdd() {
      this.formDialog.title = '新增';
      this.formDialog.visible = true;
      this.$nextTick(() => {
        this.formDialog.formData = this.getInitialFormData();
        // if (this.$refs.formRef) {
        //   this.$refs.formRef.resetFields();
        // }
      });
    },
    handleEdit(row) {
      this.formDialog.title = '编辑';
      this.formDialog.formData = { ...row }; // 使用对象展开符深拷贝
      this.formDialog.visible = true;
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate(); // 清除可能残留的校验状态
      });
    },
    handleDetail(row) {
      this.formDialog1.title = '详情'
      this.formDialog1.formData = row;
      this.formDialog1.visible = true
    },
    handleCancel() {
      this.formDialog.visible = false
      this.formDialog1.visible = false
      this.formDialog.formData = this.getInitialFormData() // 重置表单数据
      this.formDialog1.formData = this.getInitialFormData() // 重置表单数据
    },
    handleDelete(row) {
      this.selectedRow = row;
      this.deleteDialogVisible = true;
    },
    async handleConfirmDelete() {
      console.log("this.selectedRow.id", this.selectedRow.id)
      try {
        await this.$request.post(sysMiniProgram.deleteMiniProgram.url, {
          id: this.selectedRow.id
        });
        this.$message.success('删除成功');
        this.getList();
      } catch (error) {
        this.$message.error('删除失败');
      } finally {
        this.deleteDialogVisible = false;
      }
    },
    // 打开权限设置对话框
    async handlePermission(row) {
      console.log("row", row)
      this.permissionDialog.miniProgramId = row.appid;
      this.permissionDialog.miniProgramName = row.name;
      this.permissionDialog.visible = true;
      this.permissionDialog.loading = true;

      try {
        // 获取所有公司列表
        await this.getCompanyList();

        // 获取小程序关联的公司列表
        await this.getCompaniesByMiniProgramId(row.appid);

      } catch (error) {
        console.error('获取权限数据失败', error);
        this.$message.error('获取权限数据失败');
      } finally {
        this.permissionDialog.loading = false;
      }
    },
    // 关闭权限设置对话框
    handlePermissionCancel() {
      this.permissionDialog.visible = false;
      this.permissionDialog.miniProgramId = '';
      this.permissionDialog.miniProgramName = '';
      this.permissionDialog.companyList = [];
      this.permissionDialog.selectedCompanyIds = [];
    },
    // 提交权限设置
    async handlePermissionSubmit() {
      try {
        const res = await this.$request.post(
          sysMiniProgram.assignCompaniesToMiniProgram.url,
          this.permissionDialog.selectedCompanyIds,
          {
            params: {
              miniProgramId: this.permissionDialog.miniProgramId,
              miniProgramName: this.permissionDialog.miniProgramName
            }
          }
        );

        if (res.code === 0) {
          this.$message.success('权限设置成功');
          this.handlePermissionCancel();
        } else {
          this.$message.error(res.msg || '权限设置失败');
        }
      } catch (error) {
        console.error('权限设置失败', error);
        this.$message.error('权限设置失败');
      }
    },
    // 获取所有公司列表
    async getCompanyList() {
      try {
        const params = {
          id: 31000, // 总公司ID
          level: 2, // 公司层级
        };
        const res = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });

        if (res.code === 0 && res.data && res.data.columns) {
          // 转换为transfer组件需要的格式，包含栏目信息
          const transferData = [];

          // 遍历所有栏目
          res.data.columns.forEach(column => {
            // 如果栏目有关联的公司
            if (column.companies && column.companies.length > 0) {
              // 将公司添加到结果列表中，包含栏目信息
              column.companies.forEach(company => {
                transferData.push({
                  value: company.id,
                  label: `${company.name} (${column.name})`, // 在公司名称后添加栏目名称
                  disabled: false
                });
              });
            }
          });

          this.permissionDialog.companyList = transferData;
        }
      } catch (error) {
        console.error('获取公司列表失败', error);
        throw error;
      }
    },
    // 将树形结构转换为平面列表
    flattenCompanyTree(tree) {
      let result = [];

      for (const node of tree) {
        result.push({
          id: node.id,
          name: node.name
        });

        if (node.children && node.children.length > 0) {
          result = result.concat(this.flattenCompanyTree(node.children));
        }
      }

      return result;
    },
    // 获取小程序关联的公司列表
    async getCompaniesByMiniProgramId(miniProgramId) {
      try {
        const res = await this.$request.get(
          `${sysMiniProgram.getCompaniesByMiniProgramId.url}/${miniProgramId}`
        );

        if (res.code === 0 && res.data) {
          // 设置已选择的公司ID
          this.permissionDialog.selectedCompanyIds = res.data.map(company => company.id);
        }
      } catch (error) {
        console.error('获取小程序关联公司失败', error);
        throw error;
      }
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs.formRef.validate()
        if (valid === true) {
          const isEdit = !!this.formDialog.formData.id
          const api = isEdit
            ? sysMiniProgram.updateMiniProgram
            : sysMiniProgram.addMiniProgram

          // 添加当前用户信息
          const formData = {
            ...this.formDialog.formData,
            // updatedBy: this.$store.state.user.userInfo.userId
          }

          // if (!isEdit) {
          //   formData.createdBy = this.$store.state.user.userInfo.userId
          // }

          const res = await this.$request[api.method](api.url, formData)

          if (res.code === 0) {
            this.$message.success(`${isEdit ? '修改' : '新增'}成功`)
            this.formDialog.visible = false
            this.getList()
          } else {
            this.$message.error('新增失败')
          }
        }
      } catch (e) {
        console.error(e)
      }
    },
    async getList() {
      const query = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
        ...this.formData // 合并查询条件
      };
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.get(
          sysMiniProgram.queryMiniProgramListByPage.url,
          {
            params: query,
          },
        );
        const { code, data, msg } = res;
        if (code === 0) {
          this.data = data.records || [];
          this.pagination.total = +data.total || 0;
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.dataLoading = false;
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.operation-container {
  margin: 15px 0;
}

/deep/ .t-table {
  height: 100%;
  overflow: hidden;

  /deep/ .t-table__content {
    height: 450px;
  }
}

/deep/ table {
  tr th {
    background: #fff;
  }
}

/deep/ .t-table__pagination {
  background: #f3f3f3;
  padding: 10px 20px;
  border-top: 1px solid #eee;
}

/deep/ .pla {
  color: #0052d9;
  padding: 0 3px;
}

/deep/ .t-table--striped {
  background: #999999;
  color: red;
}


::v-deep .t-button--variant-text {
  padding: 0 !important;
}

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

.left-operation-container {
  padding: 0 0 10px 0;

  .selected-count {
    display: inline-block;
    color: @text-color-secondary;
  }
}

.search-input {
  margin: 15px 0;

  .search-btn {
    text-align: right;
    margin-right: 20px;
    margin-top: -35px;
  }
}

.operator-title-tag {
  margin-right: 8px;
  margin-top: 8px;
  margin-left: unset;
  border: unset;
}

.operator-content {
  height: 320px;
  overflow: hidden;
  overflow-y: auto;
}

.permission-title {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
</style>
