<template>
  <div class="redpacket-page">
    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <div class="filter-content">
        <t-form :data="formData" layout="inline" class="filter-form">
          <div class="filter-row">
            <t-form-item label="课程名称" name="courseName" class="filter-item">
              <t-input
                v-model="formData.courseName"
                placeholder="请输入课程名称"
                clearable
                class="filter-input"
              >
                <template #prefixIcon>
                  <t-icon name="search" />
                </template>
              </t-input>
            </t-form-item>

            <t-form-item label="客户名称" name="customerName" class="filter-item">
              <t-input
                v-model="formData.customerName"
                placeholder="请输入客户名称"
                clearable
                class="filter-input"
              >
                <template #prefixIcon>
                  <t-icon name="user" />
                </template>
              </t-input>
            </t-form-item>

            <t-form-item label="领取时间" class="filter-item filter-item-wide">
              <t-date-range-picker
                v-model="createTime"
                :clearable="true"
                enable-time-picker
                allow-input
                :presets="presets"
                @change="handleChange"
                class="filter-date"
              />
            </t-form-item>

            <t-form-item label="栏目" name="columnId" class="filter-item">
              <t-select
                v-model="formData.columnId"
                placeholder="请选择栏目"
                :options="columnOptions"
                clearable
                :filter="filterColumns"
                @change="onColumnChange"
                class="filter-select"
              />
            </t-form-item>

            <t-form-item label="公司(训练营)" name="companyId" class="filter-item">
              <t-select
                :disabled="!formData.columnId ||formData.columnId === ''"
                :options="companyData"
                v-model="formData.companyId"
                clearable
                :filter="filterCompanies"
                placeholder="请选择公司(训练营)"
                @change="onCompanyChange"
                class="filter-select"
              />
            </t-form-item>

            <t-form-item label="营期" name="campPeriodId" class="filter-item">
              <t-select
                :disabled="!formData.companyId ||formData.companyId === ''"
                v-model="formData.campPeriodId"
                clearable
                placeholder="请选择营期"
                :filter="filterCampPeriods"
                :options="campPeriodOptions"
                class="filter-select"
              />
            </t-form-item>

            <t-form-item label="销售组" name="salesGroupId" v-if="hasAllocationAuth" class="filter-item">
              <t-select
                :disabled="!formData.companyId || formData.companyId === ''"
                v-model="formData.salesGroupId"
                clearable
                placeholder="请选择销售组"
                :filter="filterSalesGroups"
                :options="salesGroupOptions"
                @change="getSalesList(formData.salesGroupId)"
                class="filter-select"
              />
            </t-form-item>

            <t-form-item label="销售" name="salesId" v-if="hasAllocationAuth" class="filter-item">
              <t-select
                :disabled="!formData.salesGroupId || formData.salesGroupId === ''"
                v-model="formData.salesId"
                clearable
                placeholder="请选择销售"
                :filter="filterSales"
                :options="salesOptions"
                class="filter-select"
              />
            </t-form-item>

            <t-form-item label="单据状态" name="state" class="filter-item">
              <t-select
                v-model="formData.state"
                clearable
                placeholder="请选择单据状态"
                :options="stateOptions"
                class="filter-select"
              />
            </t-form-item>

            <div class="filter-buttons">
              <t-button theme="primary" @click="onSubmit" class="search-btn">
                <t-icon name="search" />
                查询
              </t-button>
              <t-button variant="outline" @click="onReset" class="reset-btn">
                <t-icon name="refresh" />
                重置
              </t-button>
            </div>
          </div>
        </t-form>
      </div>
    </div>
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="table-info">
          <t-icon name="money-circle" />
          <span>红包领取记录</span>
          <span class="count-info">（共 {{ pagination.total }} 条）</span>
        </div>
      </div>

      <div class="toolbar-right">
        <div class="total-amount">
          <span class="label">总金额：</span>
          <span class="amount">{{ formatCurrency(totalAmount) }}</span>
          <span class="unit">元</span>
        </div>
        <t-button variant="outline" @click="refreshData" class="refresh-btn">
          <t-icon name="refresh" />
          刷新
        </t-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <t-card class="table-card">
        <t-table
          :columns="columns"
          :data="data"
          :rowKey="rowKey"
          :pagination="pagination"
          :hover="true"
          :selected-row-keys="selectedRowKeys"
          :loading="dataLoading"
          @change="rehandleChange"
          @row-click="onRowClick"
          @select-change="rehandleSelectChange"
          stripe
        >
          <template #state="{ row }">
            <t-tag :theme="stateMap[row.state].theme" shape="round" variant="light-outline" class="status-pill">
              <template #icon>
                <component
                  :is="stateMap[row.state].icon"
                  :style="{ color: stateMap[row.state].theme }"
                />
              </template>
              {{ stateMap[row.state].label }}
            </t-tag>
          </template>

          <!-- 转账金额格式化 -->
          <template #transferAmount="{ row }">
            <span class="amount-text">{{ formatCurrency(row.transferAmount) }}</span>
          </template>
        </t-table>
      </t-card>
    </div>

    <customer-drawer :hasAllocationAuth="false" :visible="drawerVar.visible" :data="drawerVar.data" @cancel="drawerCancel"></customer-drawer>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { ACTIVE_TYPE_OPTIONS } from '@/constants';
import dayjs from 'dayjs';
import sysCourseRedPacket from '@/constants/api/back/sys-course-redpacket.api';

import {
  CheckCircleFilledIcon,
  LockOnIcon,
  HourglassIcon,
  ErrorCircleIcon,
  DiscountIcon,
  // 导入其他所需图标
} from 'tdesign-icons-vue';
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import sysCampGroupApi from "@/constants/api/back/sys-camp-group.api";
import salesGroupApi from "@/constants/api/back/sales-group.api";
import apiUser from "@/constants/api/back/system-user.api";
import { SYSTEM_ROLE_CONST } from "@/constants/enum/system/system-role.enum";
import CustomerDrawer from "@/pages/customer-management/components/customer-drawer.vue";
import customerApi from "@/constants/api/hxsy-admin/customer.api";

// 状态配置映射
const stateMap = {
  '0': { label: '未查询到状态', theme: 'warning', icon: LockOnIcon },
  '1': { label: '已受理', theme: 'info', icon: HourglassIcon },
  '2': { label: '锁定资金中', theme: 'warning', icon: LockOnIcon },
  '3': { label: '待用户确认', theme: 'primary', icon: HourglassIcon },
  '4': { label: '转账中', theme: 'primary', icon: HourglassIcon },
  '5': { label: '转账成功', theme: 'success', icon: CheckCircleFilledIcon },
  '6': { label: '转账失败', theme: 'error', icon: ErrorCircleIcon },
  '7': { label: '撤销中', theme: 'warning', icon: HourglassIcon },
  '8': { label: '已撤销', theme: 'default', icon: LockOnIcon },
  '9': { label: '已关闭', theme: 'default', icon: LockOnIcon },
};

export default Vue.extend({
  name: 'ActivityRecordMain',
  components: {
    CustomerDrawer,
    CheckCircleFilledIcon,
    LockOnIcon,
    HourglassIcon,
    ErrorCircleIcon,
    DiscountIcon,
  },
  data() {
    return {
      ACTIVE_TYPE_OPTIONS,
      // 侧边栏
      drawerVar: {
        visible: false,
        data: {},
      },
      // 创建时间
      createTime: [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 创建结束时间
      ],
      presets: {
        '最近7天': [
          dayjs().subtract(6, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '最近3天': [
          dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '今天': [
          dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ]
      },
      // 搜索条件
      formData: {
        courseName: '',
        customerName: '',
        columnId:  '', // 栏目Id
        campPeriodId: '', // 营期Id
        companyId: '', // 公司（训练营）Id
        createStartTime: "", // 创建开始时间
        createEndTime: "", // 创建结束时间
        state: '',
        salesGroupId: '', // 销售组Id
        salesId: '', // 销售Id
      },
      companyData: [],
      campPeriodOptions: [],
      columnOptions: [],
      salesGroupOptions: [], // 销售组选项
      salesOptions: [], // 销售选项
      hasAllocationAuth: false, // 是否有分配权限
      // 批量操作选项
      batchOperationOption: [
        {
          content: '暂未开放',
          value: '1',
        },
      ],
      stateOptions: [
        { label: '已受理', value: '1' },
        { label: '锁定资金中', value: '2' },
        { label: '待用户确认', value: '3' },
        { label: '转账中', value: '4' },
        { label: '转账成功', value: '5' },
        { label: '转账失败', value: '6' },
        { label: '撤销中', value: '7' },
        { label: '已撤销', value: '8' },
        { label: '已关闭', value: '9' },
      ],
      stateMap,
      columns: [
        {
          title: '用户ID',
          align: 'left',
          width: 140,
          colKey: 'customerId',
        },
        {
          title: '用户名称',
          align: 'left',
          width: 100,
          colKey: 'customerName',
        },
        {
          title: '课程名称',
          align: 'left',
          width: 120,
          colKey: 'courseName',
        },
        {
          title: '转账金额（元）',
          align: 'right',
          width: 120,
          colKey: 'transferAmount',
          cell: 'transferAmount',
        },
        {
          title: '商户号',
          align: 'left',
          width: 120,
          colKey: 'merchantId',
        },
        {
          title: '微信转账单号',
          align: 'left',
          width: 140,
          colKey: 'transferBillNo',
        },
        {
          title: '单据状态',
          align: 'left',
          width: 140,
          colKey: 'state',
        },
        // {
        //   title: '使用状态',
        //   align: 'left',
        //   width: 140,
        //   colKey: 'status',
        // },
        {
          title: '领取时间',
          align: 'left',
          width: 140,
          colKey: 'createdAt',
        }
      ],
      rowKey: 'transferBillNo',
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',
      totalAmount: 0, // 总金额
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      deleteDialogVisible: false,
      selectedRow: null
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
  },
  created() {
    this.formData.createStartTime = this.createTime[0];
    this.formData.createEndTime = this.createTime[1];

    // 设置权限
    const user = this.getUser();
    const roleType = user ? String(user.roleType) : '';

    // 设置是否有分配权限
    if (roleType === SYSTEM_ROLE_CONST.ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.COMMON_ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.COL_ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.COM_ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.SALE_ADMIN.value) {
      this.hasAllocationAuth = true;
    } else {
      this.hasAllocationAuth = false;
    }
  },
  async mounted() {
    await this.getList();
    await this.getColumnList();
  },

  methods: {
    // 关闭详情
    drawerCancel() {
      this.drawerVar.visible = false;
    },
    handleChange(value, context) {
      console.log('onChange:', context.trigger);
      if (context.trigger === "clear") {
        this.formData.createStartTime = "";
        this.formData.createEndTime = "";
        this.createTime = [];
      }
    },
    async onRowClick(row: any) {
      const query = {
        pageNum: 1,
        pageSize: 8,
      };
      const {customerId} = row.row;
      const {code, data, msg} = await (this as any).$request.post(
        customerApi.queryPage.url,
        {
          customerId,
        },
        {
          params: query,
        },
      );
      if (data.records && data.records.length > 0) {
        this.drawerVar.data = data.records[0];
        this.drawerVar.visible = true;
      } else {
        this.$message.warning("您暂时没有权限查看此客户的详细信息！");
      }
    },
    rehandleChange(changeParams: any) {
      this.pagination.pageSize = changeParams.pagination.pageSize;
      this.pagination.current = changeParams.pagination.current;
      this.getList();
    },
    onSubmit() {
      console.log("this.formData", this.formData);
      console.log( this.createTime.length > 0);
      if (this.createTime.length > 0) {
        this.formData.createStartTime = this.createTime[0];
        this.formData.createEndTime = this.createTime[1];
      }
      this.pagination.current = 1;
      this.getList();
    },
    onReset() {
      this.pagination.current = 1;
      this.formData = {
        courseName: '',
        customerName: '',
        columnId:  '', // 栏目Id
        campPeriodId: '', // 营期Id
        companyId: '', // 公司（训练营）Id
        state: '',
        salesGroupId: '', // 销售组Id
        salesId: '', // 销售Id
        createStartTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 创建开始时间
        createEndTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 创建结束时间
      };
      this.createTime = [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 创建结束时间
      ];
      this.getList();
      this.getColumnList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    // 获取初始表单数据方法
    async getList() {
      const query = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.post(
          sysCourseRedPacket.queryCourseRedPacketByPage.url,
          this.formData,
          {
            params: query,
          },
        );
        const { code, data, msg } = res;
        if (code === 0) {
          this.data = data.pageData.records || [];
          this.pagination.total = +data.pageData.total || 0;
          this.totalAmount = data.totalAmount || 0;
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.dataLoading = false;
      }
    },
    async getColumnList() {
      const params = {
        id: 31000, // 总公司ID
        level: 2, // 公司层级
      };
      const { code, data } = await (this as any).$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });
      if (code === 0 && data) {
        this.columnOptions = data.columns.map((item: any) => ({ label: item.name, value: item.id, child: item.companies }));
      }
    },
    filterColumns(input: any, option: any) {
      return option.label.indexOf(input) !== -1;
    },
    filterCompanies(input: any, option: any) {
      return option.label.indexOf(input) !== -1;
    },
    filterCampPeriods(input: any, option: any) {
      return option.label.indexOf(input) !== -1;
    },
    async onColumnChange(value: any, context: any) {
      console.log('onColumnChange:', value, context);
      this.companyData = [];
      this.campPeriodOptions = [];
      this.formData.companyId  = '';
      this.formData.campPeriodId  = '';
      this.companyData = context.selectedOptions[0].child.map((item: any) => ({ label: item.name, value: item.id }));
    },
    async onCompanyChange(value: any, context: any) {
      console.log('onCompanyChange:', value, context, context.node?.getPath());
      this.formData.campPeriodId  = '';
      this.formData.salesGroupId = '';
      this.formData.salesId = '';
      this.salesGroupOptions = [];
      this.salesOptions = [];

      const params = {
        companyId: value,
      }
      const { code, data } = await (this as any).$request.get(sysCampGroupApi.getCampPeriodsByCompanyId.url, { params });
      console.log("code", code, "data", data);
      if (code === 0 && data) {
        this.campPeriodOptions = data.map((item: any) => ({ label: item.campperiodName, value: item.id }));
        console.log("campPeriodOptions", this.campPeriodOptions);
      }

      // 获取销售组列表
      if (this.hasAllocationAuth) {
        await this.getSalesGroupList(value);
      }
    },

    // 获取当前用户信息
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    },

    // 过滤销售组
    filterSalesGroups(input: any, option: any) {
      return option.label.indexOf(input) !== -1;
    },

    // 过滤销售
    filterSales(input: any, option: any) {
      return option.label.indexOf(input) !== -1;
    },

    // 获取销售组列表
    async getSalesGroupList(companyId: any) {
      const params = {
        companyId
      };
      const { code, data } = await (this as any).$request.get(salesGroupApi.listByCompanyId.url, { params });
      if (code === 0 && data) {
        this.salesGroupOptions = data.map((item: any) => ({
          label: item.salesGroupName,
          value: item.id,
        }));
      }
    },

    // 获取销售列表
    async getSalesList(salesGroupId: any) {
      this.formData.salesId = '';
      this.salesOptions = [];
      const res = await (this as any).$request.post(
        apiUser.queryUserByPage.url,
        {
          current: 1,
          size: 100,
          companyId: this.formData.companyId || '',
          salesGroupId,
          columnId: this.formData.columnId,
          status: 1, // 启用状态
          auditStatus: 2, // 审核通过状态
        },
      );
      const { code, data, msg } = res;
      if (code === 0 && data) {
        this.salesOptions = data.records.map((item: any) => ({
          label: item.username,
          value: item.id,
        }));
      } else {
        await this.$message.error(msg || '请求失败');
      }
    },

    // 刷新数据
    refreshData() {
      this.getList();
    },

    // 格式化金额
    formatCurrency(amount: number) {
      if (!amount && amount !== 0) return '0.00';
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

// ==================== 页面主体样式 ====================
.redpacket-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 筛选区域
  .filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 16px;
    overflow: hidden;

    .filter-content {
      padding: 20px 24px;

      .filter-form {
        .filter-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;

          .filter-item {
            min-width: 200px;
            margin-bottom: 16px;

            .filter-input,
            .filter-select {
              width: 200px;
            }

            .filter-date {
              width: 380px;
            }

            &.filter-item-wide {
              min-width: 280px;
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;
            margin-left: auto;
            margin-bottom: 16px;

            .t-button {
              height: 32px;
              padding: 0 16px;
              border-radius: 6px;
              font-weight: 500;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }
          }
        }
      }
    }
  }

  // 工具栏
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 24px;

      .table-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #374151;

        .t-icon {
          color: #f59e0b;
        }

        .count-info {
          color: #6b7280;
        }
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .total-amount {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 16px;
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        border-radius: 8px;
        border: 1px solid #f59e0b;

        .label {
          font-size: 14px;
          color: #92400e;
        }

        .amount {
          font-size: 18px;
          font-weight: 700;
          color: #d97706;
        }

        .unit {
          font-size: 14px;
          color: #92400e;
        }
      }

      .t-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 表格区域
  .table-section {
    .table-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      overflow: hidden;

      ::v-deep .t-table {
        .t-table__header {
          background: #fafbfc;

          th {
            background: #fafbfc;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
            padding: 16px;
          }
        }

        .t-table__body {
          td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;

            &:last-child {
              border-bottom: none;
            }
          }

          tr {
            transition: all 0.2s ease;

            &:hover {
              background: #f9fafb;
            }
          }
        }
      }

      ::v-deep .t-table__pagination {
        background: #fafbfc;
        padding: 16px 20px;
        border-top: 1px solid #e5e7eb;
      }
    }
  }
}

// ==================== 状态标签样式 ====================
.status-pill {
  min-width: 80px;
  text-align: center;
  font-weight: 500;

  ::v-deep .t-icon {
    font-size: 14px;
    vertical-align: -1px;
  }

  // 成功状态特殊处理
  &.t-tag--success {
    background-color: #10b981 !important;
    color: #fff !important;
    border: none;

    ::v-deep .t-icon {
      fill: #fff;
    }
  }

  // 其他状态保持原样式
  &.t-tag--round {
    border-radius: 16px;
    padding: 6px 12px;
    line-height: 18px;
    font-size: 12px;
  }
}

// ==================== 金额文本样式 ====================
.amount-text {
  font-weight: 600;
  color: #d10202;
  font-family: 'Courier New', monospace;
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .redpacket-page {
    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            .filter-buttons {
              margin-left: 0;
              margin-top: 16px;
              width: 100%;
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .redpacket-page {
    padding: 16px;

    .toolbar {
      flex-direction: column;
      gap: 16px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }

      .toolbar-right {
        .total-amount {
          flex: 1;
          justify-content: center;
        }
      }
    }

    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            flex-direction: column;
            align-items: stretch;

            .filter-item {
              min-width: auto;

              .filter-input,
              .filter-select,
              .filter-date {
                width: 100%;
              }

              &.filter-item-wide {
                min-width: auto;
              }
            }

            .filter-buttons {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}
</style>
