<template>
  <!-- <t-card class="list-card-container" header-bordered> </t-card> -->
  <div>
    <t-form ref="formRef" :data="formData" :rules="rules" :label-width="200" @submit="onSubmit">
      <t-tabs :value="tabsValue" @change="(newValue) => (tabsValue = newValue)">
        <t-tab-panel value="first">
        </t-tab-panel>
      </t-tabs>
      <t-form-item style="margin-left: 100px; padding-top: 10px">
        <t-space size="10px">
          <!-- type = submit，表单中的提交按钮，原生行为 -->
          <t-button theme="primary" type="submit">保存</t-button>
          <!-- type = reset，表单中的重置按钮，原生行为 -->
          <t-button theme="default" variant="base" @click="handleBack">返回</t-button>
        </t-space>
      </t-form-item>
    </t-form>
  </div>
</template>

<script lang="ts">
import {Component, Vue, Ref, Watch} from 'vue-property-decorator';

import store from '@/store';
import { SystemSettingIcon, ServiceIcon, ShareIcon } from 'tdesign-icons-vue';

const { state } = store;

@Component({
  name: 'SysConfigManagement',
  components: { ShareIcon, ServiceIcon, SystemSettingIcon },
  })
export default class SysConfigManagement extends Vue {
  @Ref() formRef!: any; // 表单对象引用

  formData = {};

  rules = {
    systemName: [
      {
        validator: (val: any = '') => val.length <= 30,
        message: '不能超过30个字',
        type: 'error',
        trigger: 'change',
      },
      { pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change' }
    ],
  };

  loading = false;

  submitted = true;

  currentDisplayMap = new Map();

  config = (state as any).config;

  /**
   * created hook
   */
  created(): void {
  }

  onSubmit() {
    this.submitConfig();
  }

}
</script>

<style lang="less" scoped>
@import '@/style/variables';

::v-deep .t-tabs__nav-scroll {
  background: #ffffff;
  height: 44px !important;
}

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

.left-operation-container {
  padding: 0 0 6px 0;
  margin-bottom: 16px;

  .selected-count {
    display: inline-block;
    margin-left: 8px;
    color: @text-color-secondary;
  }
}
::v-deep .t-slider__container{
  justify-content: start !important;
}
::v-deep .t-slider {
  width: 60% !important;
}
.form-item-content-retryTimes {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
</style>
