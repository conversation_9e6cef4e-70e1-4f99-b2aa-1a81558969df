<template>
  <div class="login-wrapper">
    <div class="login-container">
      <t-card :bordered="false" hover-shadow :style="{ padding: '30px 20px' }">
        <div class="title-container">
          <h1 class="title margin-no">
            <div v-if="!appName" class="yun-logo"></div>
            {{ appName || '社群管理系统' }}
          </h1>
        </div>
        <login v-if="type === 'login'" />
      </t-card>
    </div>
  </div>
</template>
<script>
import Login from './components/components-login.vue';

export default {
  name: 'LoginIndex',
  components: {
    Login,
  },
  data() {
    return {
      type: 'login',
      appName: import.meta.env.VITE_APP_NAME,
    };
  },
  methods: {
    switchType(val) {
      this.type = val;
    },
  },
};
</script>
<style lang="less" scoped>
@import url('./index.less');

.login-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f0f2f5;
  padding: 20px;
  box-sizing: border-box;
}

.login-container {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: none;
  max-width: 100%;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.t-card {
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 12px !important;
}

.title-container {
  text-align: center;
  margin-bottom: 24px;
}

.title {
  font-weight: 600;
  font-size: 28px;
  color: #222;
  margin: 0;
}

.yun-logo {
  width: 48px;
  height: 48px;
  margin: 0 auto 12px;
  background: #4a90e2;
  border-radius: 8px;
  box-shadow: none;
}

button {
  background-color: #4a90e2;
  border: none;
  border-radius: 6px;
  color: #fff;
  font-weight: 600;
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  box-shadow: none;
  outline: none;
}

button:hover {
  background-color: #357ABD;
}

input, select, textarea {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  width: 100%;
}

input:focus, select:focus, textarea:focus {
  border-color: #4a90e2;
  outline: none;
  box-shadow: none;
}
</style>
