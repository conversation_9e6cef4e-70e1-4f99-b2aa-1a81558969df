@import '@/style/variables.less';

.light {
  &.login-wrapper {
    background-color: white;
    background-image: url('@/assets/assets-login-bg.png');
    background-repeat: no-repeat;  // 禁止图片重复
    background-size: cover;       // 等比例拉伸铺满容器
  }
}

.dark {
  &.login-wrapper {
    background-color: @bg-color-page;
  }
}

.login-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: 100%;
  position: relative;
}

.login-container {
  position: absolute;
  top: 32%;
  right: 15%;
  line-height: 22px;
  padding: 20px;
  .yun-logo {
    width: 50px;
    height: 37px;
    display: inline-block;
    background-size: 100%;
    background-repeat: no-repeat;
    position: relative;
    top: 8px;
    margin: 0 10px 0 0;
  }
}

.title-container {
  .title {
    font-size: 32px;
    line-height: 44px;
    color: @text-color-primary;
    margin-top: 4px;
    position: relative;

    &.margin-no {
      margin-top: 0;
    }
  }

  .sub-title {
    margin-top: 16px;

    .tip {
      display: inline-block;
      margin-right: 8px;
      font-size: 14px;

      &:first-child {
        color: @text-color-secondary;
      }

      &:last-child {
        color: @text-color-primary;
        cursor: pointer;
      }
    }
  }
}

.item-container {
  width: 400px;
  margin-top: 48px;

  &.login-qrcode {
    .tip-container {
      width: 192px;
      margin-bottom: 16px;
      font-size: 14px;
      display: flex;
      justify-content: space-between;

      .tip {
        color: @text-color-primary;
      }

      .refresh {
        display: flex;
        align-items: center;
        color: @brand-color;

        .t-icon {
          font-size: 14px;
        }

        &:hover {
          cursor: pointer;
        }
      }
    }

    .bottom-container {
      margin-top: 32px;
    }
  }

  &.login-phone {
    .bottom-container {
      margin-top: 66px;
    }
  }

  .check-container {
    display: flex;
    align-items: center;

    &.remember-pwd {
      margin-bottom: 16px;
      justify-content: space-between;
    }

    .t-checkbox__label {
      color: @text-color-secondary;
    }

    span {
      color: @brand-color;

      &:hover {
        cursor: pointer;
      }
    }
  }

  .verification-code {
    display: flex;
    align-items: center;

    .t-form__controls {
      width: 100%;

      button {
        flex-shrink: 0;
        width: 102px;
        height: 40px;
        margin-left: 11px;
      }
    }
  }

  .btn-container {
    margin-top: 48px;
  }
}

.switch-container {
  margin-top: 24px;

  .tip {
    font-size: 14px;
    color: @brand-color-8;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-right: 14px;

    &:last-child {
      &::after {
        display: none;
      }
    }

    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 12px;
      background: @gray-color-3;
      margin-left: 14px;
    }
  }
}

.check-container {
  font-size: 14px;
  color: @text-color-secondary;

  .tip {
    float: right;
    font-size: 14px;
    color: @brand-color-8;
  }
}

.copyright {
  font-size: 14px;
  position: absolute;
  left: 5%;
  bottom: 64px;
  color: @text-color-secondary;
}

@media screen and (max-height: 700px) {
  .copyright {
    display: none;
  }
}
