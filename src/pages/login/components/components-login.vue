<template>
  <t-form
    ref="form"
    :class="['item-container', `login-${type}`]"
    :data="formData"
    :rules="FORM_RULES"
    label-width="0"
    @submit="onSubmit"
  >
    <template v-if="type == 'password'">
      <t-form-item name="account">
        <t-input v-model="formData.account" size="large" placeholder="请输入账号">
          <template #prefix-icon>
            <user-icon />
          </template>
        </t-input>
      </t-form-item>

      <t-form-item name="password">
        <t-input
          v-model="formData.password"
          size="large"
          :type="showPsw ? 'text' : 'password'"
          :clearable="true"
          key="password"
          placeholder="请输入登录密码"
        >
          <template #prefix-icon>
            <lock-on-icon />
          </template>
          <template #suffix-icon>
            <browse-icon v-if="showPsw" @click="showPsw = !showPsw" key="browse" />
            <browse-off-icon v-else @click="showPsw = !showPsw" key="browse-off" />
          </template>
        </t-input>
      </t-form-item>
    </template>
    <t-form-item v-if="type !== 'qrcode'" class="btn-container">
      <t-button block size="large" type="submit" :loading="loading" :disabled="disabledLogin"> 登录 </t-button>
    </t-form-item>

  </t-form>
</template>
<script lang="ts">
import Vue from 'vue';
import QrcodeVue from 'qrcode.vue';
import { UserIcon, LockOnIcon, BrowseOffIcon, BrowseIcon, RefreshIcon } from 'tdesign-icons-vue';
import { getRemoteMenu } from '@/router';
import {sm2, sm4} from "sm-crypto";
import {MessagePlugin} from "tdesign-vue";

const INITIAL_DATA = {
  phone: '',
  account: '',
  password: '',
  verifyCode: '',
  checked: false,
};

const FORM_RULES = {
  phone: [{ required: true, message: '手机号必填', type: 'error' }],
  account: [{ required: true, message: '账号必填', type: 'error' }],
  password: [{ required: true, message: '密码必填', type: 'error' }],
  verifyCode: [{ required: true, message: '验证码必填', type: 'error' }],
};
/** 高级详情 */
export default Vue.extend({
  name: 'Login',
  components: {
    QrcodeVue,
    UserIcon,
    LockOnIcon,
    BrowseOffIcon,
    BrowseIcon,
    RefreshIcon,
  },
  data() {
    return {
      FORM_RULES,
      type: 'password',
      loading: false,
      formData: { ...INITIAL_DATA },
      showPsw: false,
      countDown: 0,
      KaptchaImage: "",
      intervalTimer: null,
      encryptedData: "",
      key: '7c4a8d09ca3762af61e59520943dc264', // 16 字节的密钥
      disabledLogin: false, // 不可登陆标志
      // aesSecret: 'b4b753c15b6ebefce0d4fc06f64b5f2d',
      aesSecret: '',
      // aesIv: 'a76222812a14f5ae9db55a80b9ab00aa',
      aesIv: '',
    };
  },
  beforeDestroy() {
    clearInterval((this as any).intervalTimer);
  },
  mounted() {
    // this.getKaptchaImage();
  },
  methods: {
    switchType(val: any) {
      this.type = val;
      (this as any).$refs.form.reset();
    },
    /**
     * @description: sm4加密处理
     * <AUTHOR>
     * @date 2025/4/20 10:59
     */
    encryptData(jsonStr:any) {
      let encryptData = {};
      try {
        // 设置密钥和初始向量，开始对数据做sm4加密
        const sm4Key = this.randomStr(16);
        const sm4KeyArr = this.strToOneByteArr(sm4Key)
        const iv = this.randomStr(16);
        const sm4InitIvArr = this.strToOneByteArr(iv)
        // 将对象数据转为json数据加密
        const sm4Data = JSON.stringify(jsonStr)
        // console.log("sm4待加密数据:", sm4Data);
        let encryptSM4 = sm4.encrypt(sm4Data, sm4KeyArr, {
          mode: 'cbc',
          iv: sm4InitIvArr
        });
        encryptSM4 = `{SM4}${encryptSM4}`;
        // console.log("sm4加密后数据:", encryptSM4);
        // 加密sm4的初始秘钥与向量，上送给服务器时需要加’04‘头，如果是服务器解密需要把返回的04头去掉
        encryptData = {
          encryptInitIv: iv,
          encryptKey: sm4Key,
          encryptData: encryptSM4
        };
      } catch (e) {
        console.error(e);
        MessagePlugin.error({ content: '数据加密失败' });
      }
      return encryptData;
    },
    /**
     * @description: 生成随机字符串，加密向量与秘钥使用
     * <AUTHOR>
     * @date 2025/4/20 11:06
     */
    randomStr(length:number) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let item = 0; item < length; item++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    },
    /**
     * @description: 将字符串转为字节数组
     * <AUTHOR>
     * @date 2025/4/20 11:06
     */
    strToOneByteArr(str:string) {
      const arr = [];
      for (let i = 0, len = str.length; i < len; i++) {
        arr.push(str.charCodeAt(i));
      }
      return arr;
    },
    async onSubmit({ validateResult }) {
      if (validateResult === true) {
        // await this.$store.dispatch('user/login', this.formData);
        // console.log(this.formData)
        this.loading = true;
        const encryptResult = this.encryptData(this.formData.password);
        // console.log("加密后信息", encryptResult);
        let params = {};
        try {
          params = {
            encryptKey: encryptResult.encryptKey,
            encryptInitIv: encryptResult.encryptInitIv,
            encryptData: encryptResult.encryptData,
            accountId: this.formData.account
          }
        }catch (e) {
          this.loading = false;
          this.$message.error('登录失败，请检查密码是否包含中文符号');
          return;
        }
        try {
          const loginUrl = import.meta.env.VITE_APP_AUTH_API + import.meta.env.VITE_APP_LOGIN_URL;
          const res = await this.$request.post(loginUrl, params);
          if (res.code === 0) {
            const { token, userInfoResponse } = res.data;
            window.localStorage.setItem('token', token);
            window.localStorage.setItem('core:user', JSON.stringify(userInfoResponse));
            // 获取用户具有菜单配置，存入storage中，每次登录都需要清除之前的菜单配置
            const userMenus: any[] = JSON.stringify(userInfoResponse.sysMenuResponses);
            window.localStorage.setItem('menu', userMenus);
            // console.log("获取到返回菜单：", userMenus);
            // 转为树形结构再存入localstorage
            const treeNode: any[] = this.listToTree(userInfoResponse.sysMenuResponses)
            // console.log("转换为树形结构后：", treeNode);
            window.localStorage.setItem('menu:tree', JSON.stringify(treeNode));
            await this.$store.dispatch('user/login', this.formData);
            this.$message.success('登录成功');
            // 判断权限跳转路由
            this.$router.replace('/home/<USER>').catch(() => '');
          } else {
            this.$message.error(res.msg || '登录失败');
          }
          this.loading = false;
        } catch (e) {
          console.log("异常：", e);
          // this.$message.error('登录失败，请稍后重试');
          this.loading = false;
        }
      }
    },
    handleCounter() {
      this.countDown = 60;
      this.intervalTimer = setInterval(() => {
        if (this.countDown > 0) {
          this.countDown -= 1;
        } else {
          clearInterval(this.intervalTimer);
          this.countDown = 0;
        }
      }, 1000);
    },
    /**
     * @description: list菜单转换树形结构
     * 根据parentId划分递归父级结构
     * 未传入parentId，则表示正在找出最顶层菜单，后续再开始递归
     * <AUTHOR>
     * @date 2025/5/8 23:24
     */
    listToTree(list: any [], parentId?: string, parentName?: string) {
      const tree: any[] = [];
      if (parentId) {
        // console.log("当前正在获取节点:", parentId , "的子级节点");
        const rootNodes = list.filter(nodeItem => {
          // console.log("当前正在获取节点:", nodeItem);
          if(nodeItem.parentId === parentId){
            nodeItem.parentName = parentName // 设置一下父节点名称，后面编辑时需要用
            return true;
          }
        });
        // console.log("获取到子级节点:", rootNodes);
        if (rootNodes) {
          rootNodes.forEach(rootNode => {
            const children = this.listToTree(list, rootNode.id, rootNode.menuName);
            if (children) {
              rootNode.children = children;
            }
            tree.push(rootNode);
          });
        }
        return tree;
      }else {
        let FirstNodes: any[] = [];
        list.forEach(item => {
          if (!item.parentId || item.parentId == 'null' || item.parentId == ''){
            FirstNodes.push(item)
            // 记录一下所有的父节点，用于提交时选择了其下的子节点，父节点也可以置为选中
          }
        })
        // console.log("获取到顶层节点:", FirstNodes);
        // 先寻找二级菜单，再开始递归其下的子菜单
        FirstNodes.forEach(item => {
          const rootNodes = list.filter(nodeItem => {
            if (nodeItem.parentId === item.id) {
              nodeItem.parentName = item.name; // 设置一下父节点名称，后面编辑时需要用
              return true;
            }
          });
          item.children = rootNodes;
          rootNodes.forEach(rootNode => {
            const children = this.listToTree(list, rootNode.id, rootNode.name);
            if (children) {
              rootNode.children = children;
            }
            tree.push(rootNode);
          });
        })
        return FirstNodes;
      }
    },
  },
});
</script>
<style scoped lang="less">
.verify-code-image {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-left: 20px;
  font-size: 12px;
  min-width: 150px;
  max-height: 40px;
  .verify-code-img {
    max-height: 40px;
  }
  .verify-code-text {
    position: absolute;
    bottom: 0;
  }
}
</style>
