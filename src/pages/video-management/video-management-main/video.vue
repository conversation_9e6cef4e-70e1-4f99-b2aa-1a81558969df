<template>
  <div class="video-management-page">
    <!-- 基础配置卡片 -->
    <div class="config-card">
      <div class="card-header">
        <h3 class="card-title">
          <t-icon name="setting" />
          基础配置
        </h3>
      </div>
      <div class="card-content">
        <t-form :data="videoInfo" :rules="rules" ref="videoForm" validate-trigger="blur" class="config-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <div class="form-row">
              <t-form-item class="form-item" label="课程名称" name="courseName" required>
                <t-input v-model="videoInfo.courseName" placeholder="请输入课程名称" maxlength="30"></t-input>
              </t-form-item>
              <t-form-item class="form-item" label="课程分组" name="groupId" required>
                <t-select v-model="videoInfo.groupId" :options="courseList" placeholder="请选择课程分组"></t-select>
              </t-form-item>
            </div>
            <t-form-item label="课程简介" name="courseIntroduction">
              <t-textarea v-model="videoInfo.courseIntroduction" placeholder="请输入课程简介" :autosize="{ minRows: 3, maxRows: 5 }"></t-textarea>
            </t-form-item>
          </div>

          <!-- 详细内容 -->
          <div class="form-section">
            <div class="section-title">详细内容</div>
            <div class="form-row">
              <t-form-item class="form-item" label="课程介绍" name="courseDescription" required>
                <rich-editor v-model="videoInfo.courseDescription" placeholder="请输入课程介绍"></rich-editor>
              </t-form-item>
              <t-form-item class="form-item" label="课程内容" name="courseContent" required>
                <rich-editor v-model="videoInfo.courseContent" placeholder="请输入课程内容"></rich-editor>
              </t-form-item>
            </div>
          </div>

          <!-- 媒体文件 -->
          <div class="form-section">
            <div class="section-title">媒体文件</div>
            <div class="form-row">
              <t-form-item label="课程封面" name="coverPath" required class="form-item">
                <div class="upload-container cover-upload" @click="uploadImage">
                  <div class="upload-area">
                    <div v-if="videoInfo.coverPath" class="preview-container">
                      <t-image :src="videoInfo.coverPath" fit="cover" class="cover-preview" />
                      <div class="upload-overlay">
                        <t-icon name="edit" />
                        <span>更换封面</span>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <t-icon name="image" size="32px" />
                      <div class="upload-text">
                        <div class="upload-title">点击上传课程封面</div>
                        <div class="upload-desc">课程封面将在课程详情页进行展示</div>
                      </div>
                    </div>
                  </div>
                </div>
              </t-form-item>
              <t-form-item label="课程视频" name="videoPath" required class="form-item">
                <div class="upload-container video-upload" @click="handleUploadVideo">
                  <div class="upload-area">
                    <div v-if="videoInfo.videoPath" class="preview-container">
                      <video :src="videoInfo.videoPath" :autoplay="false" class="video-preview" />
                      <div class="upload-overlay">
                        <t-icon name="edit" />
                        <span>更换视频</span>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <t-icon name="video" size="32px" />
                      <div class="upload-text">
                        <div class="upload-title">点击添加课程视频</div>
                      </div>
                    </div>
                  </div>
                  <div v-if="videoName" class="file-name">{{ videoName }}</div>
                </div>
              </t-form-item>
            </div>
          </div>
          <!-- 其他配置 -->
          <div class="form-section">
            <div class="section-title">其他配置</div>
            <div class="form-row">
              <t-form-item label="课程序号" name="field1" class="form-item">
                <t-input v-model="videoInfo.field1" type="number" placeholder="请输入课程序号"></t-input>
              </t-form-item>
              <t-form-item label="标签" name="courseTag" class="form-item">
                <div class="field-with-tips">
                  <t-select v-model="videoInfo.courseTag" :options="tagList" placeholder="请选择标签"></t-select>
                  <div class="field-tips">
                    <t-icon name="info-circle" />
                    报名或购买此课程的学员，将会被标记选择的标签
                  </div>
                </div>
              </t-form-item>
            </div>
            <t-form-item label="营销活动" name="activityInfo" required>
              <div class="activity-selector">
                <t-button theme="primary" variant="outline" @click="handleActivity">
                  <t-icon name="add" />
                  选择营销活动
                </t-button>
              </div>
            </t-form-item>

            <!-- 已选择的营销活动 -->
            <div class="activity-preview" v-if="Object.keys(choseActivity).length">
              <div class="activity-header">
                <h4>已选择的营销活动</h4>
              </div>
              <div class="activity-content">
                <div class="activity-info">
                  <div class="info-item" v-if="choseActivity.amount">
                    <span class="info-label">红包金额：</span>
                    <span class="info-value highlight">{{ choseActivity.amount }} 元</span>
                  </div>
                  <div class="info-item" v-if="choseActivity.activityType">
                    <span class="info-label">活动类型：</span>
                    <span class="info-value">{{getModality(choseActivity.activityType)}}</span>
                  </div>
                </div>
                <div v-if="choseActivity.activityType === '5'" class="question-activity">
                  <div class="question-header">
                    <div class="question-title" v-if="choseActivity.title">
                      <span class="info-label">活动名称：</span>
                      <span class="info-value">{{ choseActivity.title }}</span>
                    </div>
                    <div class="question-answer" v-if="choseActivity.correctAnswer">
                      <span class="info-label">正确答案：</span>
                      <span class="info-value answer-text">{{ choseActivity.correctAnswer }}</span>
                    </div>
                  </div>
                  <div class="question-content" v-if="choseActivity.questionText" v-html="choseActivity.questionText"></div>
                  <div v-if="choseActivity.answerOptions" class="answer-options">
                    <div class="options-title">答案选项：</div>
                    <div class="options-list">
                      <div class="option-item" v-if="choseActivity.optionA">
                        <span class="option-label">A：</span>
                        <span class="option-text">{{ choseActivity.optionA }}</span>
                      </div>
                      <div class="option-item" v-if="choseActivity.optionB">
                        <span class="option-label">B：</span>
                        <span class="option-text">{{ choseActivity.optionB }}</span>
                      </div>
                      <div class="option-item" v-if="choseActivity.optionC">
                        <span class="option-label">C：</span>
                        <span class="option-text">{{ choseActivity.optionC }}</span>
                      </div>
                      <div class="option-item" v-if="choseActivity.optionD">
                        <span class="option-label">D：</span>
                        <span class="option-text">{{ choseActivity.optionD }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </t-form>
      </div>
    </div>
    <!-- 售卖配置卡片 -->
    <div class="config-card">
      <div class="card-header">
        <h3 class="card-title">
          <t-icon name="money-circle" />
          售卖配置
        </h3>
      </div>
      <div class="card-content">
        <t-form :data="videoInfo" :rules="configRules" ref="configForm" class="config-form">
          <!-- 售卖设置 -->
          <div class="form-section">
            <div class="section-title">售卖设置</div>
            <div class="form-row">
              <t-form-item class="form-item" label="售卖方式" name="courseSalesmethod" required>
                <t-radio-group v-model="videoInfo.courseSalesmethod" :options="configList"></t-radio-group>
              </t-form-item>
              <t-form-item class="form-item" label="上架配置" name="courseStatus" required>
                <t-radio-group v-model="videoInfo.courseStatus" :options="shelfList"></t-radio-group>
              </t-form-item>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <t-button theme="primary" @click="submit">
              <t-icon name="check" />
              提交
            </t-button>
            <t-button variant="outline" @click="reset">
              <t-icon name="refresh" />
              重置
            </t-button>
          </div>
        </t-form>
      </div>
    </div>
    <!-- 模态框组件 -->
    <video-upload :visible="uploadVideoModal" @close="uploadVideoModal = false" @success="onVideoSuccess"></video-upload>
    <image-upload :visible="uploadImageModal" @close="uploadImageModal = false" @success="onImageSuccess"></image-upload>
    <activity-choose :visible="activityChooseModal" @close="activityChooseModal = false" @success="onActivitySuccess"></activity-choose>
  </div>
</template>

<script>

import noneImage from "@/pages/video-management/video-management-main/image/none-image";
import videoUpload from "@/pages/video-management/video-management-main/components/videoUpload.vue";
import imageUpload from "@/pages/video-management/video-management-main/components/imageUpload.vue";
import richEditor from "@/components/rich-editor/index.vue";
import sysCourseGroupApi from "@/constants/api/back/sys-course-group.api";
import sysCourseGroupUploadApi from "@/constants/api/back/sys-course-video.api";
import {COURSE_STATUS_ARRAY} from "@/constants/enum/business/course-status-array.enum";
import BeanUtilsService from "@/service/bean-utils.service";
import ActivityChoose from "@/pages/video-management/video-management-main/components/activityChoose.vue";
import {MARKETING_OPTIONS} from "@/constants/enum/business/marketing-campaign-array.enum";

export default {
  name: 'VideoManagementMainVideo',
  components: { videoUpload, imageUpload, richEditor, ActivityChoose },
  data() {
    return {
      videoInfo: {
        groupId: '',
        courseName: '',
        coverPath: "",
        courseIntroduction: '',
        courseDescription: '',
        courseContent: '',
        courseSalesmethod: '1',
        courseStatus: '1',
        courseTag: [],
        videoPath: '',
        field1: '',
        activityInfo: {},
      },
      choseActivity: {},
      videoName: '',
      videoType: '',
      uploadVideoModal: false,
      uploadImageModal: false,
      activityChooseModal: false,
      rules: {
        courseName: [
          { required: true, message: '请输入视频名称', type: 'error', trigger: 'blur' }, {message: '请输入有效信息', whitespace: true}
        ],
        groupId: [
          { required: true, message: '请选择分组' }
        ],
        coverPath: [
          { required: true, message: '请上传课程封面', type: 'error', trigger: 'blur' }
        ],
        courseDescription: [
          { required: true, message: '请输入课程介绍', type: 'error', trigger: 'blur' }
        ],
        videoPath: [
          { required: true, message: '请上传课程视频', type: 'error', trigger: 'blur' }
        ],
        activityInfo: [
          { required: true, message: '请选择营销活动', type: 'error', trigger: 'blur' }
        ],
        field1: [
          { required: true, message: '请输入课程序号', type: 'error', trigger: 'blur' }
        ]
      },
      categoryList: [],
      courseList: [],
      tagList: [],
      configList: [
        { label: '免费课程', value: '1' },
        { label: '付费课程', value: '2' }
      ],
      configFullScreenList: [
        { label: '横向旋转', value: '1' },
        { label: '纵向全屏', value: '2' }
      ],
      projectList: [
        { label: '项目1', value: '1' },
        { label: '项目2', value: '2' }
      ],
      sourceList: [
        { label: '来源1', value: '1' },
        { label: '来源2', value: '2' }
      ],
      headerList: [
        { label: '不设置', value: '1' },
        { label: '单独片头视频', value: '2' },
        { label: '指定时长作为片头', value: '3' }
      ],
      shelfList: COURSE_STATUS_ARRAY,
      configRules: {
        courseSalesmethod: [
          { required: true, message: '请选择售卖方式', type: 'error', trigger: 'blur' },
        ],
        shelfSettings: [
          { required: true, message: '请选择上架配置', type: 'error', trigger: 'blur' },
        ],
      }
    }
  },
  computed: {
    noneImage() {
      return noneImage
    }
  },
  created() {},
  mounted() {
    this.checkCourseType();
    this.getCategoryList();
  },
  methods: {
    handleActivity() {
      this.activityChooseModal = true;
    },
    async getCategoryList() {
      const { data } = await this.$request.get(sysCourseGroupApi.queryCourseGroupList.url);
      this.courseList = data.map((item) => ({ label: item.groupName, value: item.id }));
    },
    checkCourseType() {
      const data = this.$route.params;
      if (data && Object.keys(data).length > 0) {
        switch (data.type) {
        case 'edit':
          this.videoInfo = BeanUtilsService.copy(data.data);
          this.videoName = this.videoInfo.videoPath.split('/').pop();
          if (this.videoInfo.activityInfo && Object.keys(JSON.parse(this.videoInfo.activityInfo)).length > 0 ) {
            this.choseActivity = JSON.parse(this.videoInfo.activityInfo);
            this.dealActivityEditData(this.choseActivity);
          }
          this.videoType = data.type;
          break;
        case 'copy':
          this.videoInfo = BeanUtilsService.copy(data.data);
          // 删除课程id及活动信息
          delete this.videoInfo.id;
          delete this.videoInfo.activityInfo;
          this.videoName = this.videoInfo.videoPath.split('/').pop();
          this.videoType = data.type;
          break;
        default:
          break;
        }
      }
    },
    uploadImage() {
      this.uploadImageModal = true;
    },
    handleUploadVideo() {
      this.uploadVideoModal = true;
    },
    onImageSuccess(data) {
      this.uploadImageModal = false;
      const imageInfo = data[0];
      this.videoInfo.coverPath = imageInfo.imageUrl || "";
      this.videoInfo.coverId = imageInfo.id || "";
    },
    onActivitySuccess(data) {
      if (data && data.length > 0) {
        const activityInfo = data[0];
        this.choseActivity = BeanUtilsService.copy(activityInfo);
        this.dealActivityEditData(this.choseActivity);
        // this.videoInfo.activityInfo = activityInfo;
        this.$set(this.videoInfo, 'activityInfo', activityInfo)
      }
      this.activityChooseModal = false;
    },
    getModality(val) {
      return MARKETING_OPTIONS.find(item => item.value === val)?.label || '--';
    },
    onVideoSuccess(data) {
      this.uploadVideoModal = false;
      const videoInfo = data[0];
      this.videoInfo.videoPath = videoInfo.videoUrl;
      this.videoInfo.videoId = videoInfo.id;
      this.videoName = videoInfo.videoName;
      // 返回数据
    },
    submit() {
      // 需要同时校验两个表单
      Promise.all([
        this.$refs.videoForm.validate(),
        this.$refs.configForm.validate()
      ]).then(async (res) => {
        // res为数组，两个值都为true才表示校验成功
        if (res.every((item) => item === true)) {
          if (this.videoType === 'edit') {
            const {code, data, msg} = await this.$request.post(sysCourseGroupUploadApi.updateCourse.url, this.videoInfo);
            if (code === 0) {
              this.$message.success({ content: "编辑成功" });
              // 返回列表页
              this.$router.push({ name: 'video-management-main', params: { type: 'refresh' } });
              // this.$router.push("/video-management/video-management-main");
            } else {
              this.$message.error({content: `编辑失败${msg}`});
            }
          } else {
            // 新增和复制同一个接口
            const {code, data, msg} = await this.$request.post(sysCourseGroupUploadApi.addCourse.url, this.videoInfo);
            if (code === 0) {
              this.$message.success({content: "提交成功"});
              // 返回列表页
              this.$router.push({ name: 'video-management-main', params: { type: 'refresh' } });
              // this.$router.push("/video-management/video-management-main");
            } else {
              this.$message.error({content: `提交失败${msg}`});
            }
          }
        }
      }).catch(() => {
        console.log('校验失败')
      })
    },
    dealActivityEditData(data) {
      switch (data.activityType) {
      case '1':
      case '6':
        this.dealRedPacketEditData(data);
        break;
      case '2':
        this.dealCouponEditData(data);
        break;
      case '3':
        this.dealGiftEditData(data);
        break;
      case '4':
        this.dealIntegralEditData(data);
        break;
      case '5':
        this.dealQuestionEditData(data);
        break;
      default:
        return [];
      }
    },
    dealRedPacketEditData(data) {
      const config = JSON.parse(data.config);
      if (config.type === 'FIXED') {
        this.$set(data, 'type', 'FIXED');
        this.$set(data, 'amount', config.amount);
      } else if (config.type === 'RANDOM') {
        this.$set(data, 'type', 'RANDOM');
        this.$set(data, 'minAmount', config.minAmount);
        this.$set(data, 'maxAmount', config.maxAmount);
      }
    },
    dealCouponEditData(data) {
      const config = JSON.parse(data.config);
      if (config.discountType === 'PERCENT') {
        this.$set(data, 'type', 'PERCENT');
        this.$set(data, 'discountValue', config.discountValue);
        this.$set(data, 'minAmount', config.minAmount);
      } else if (config.discountType === 'FIXED') {
        this.$set(data, 'type', 'FIXED');
        this.$set(data, 'discountValue', config.discountValue);
      }
    },
    dealGiftEditData(data) {
      const config = JSON.parse(data.config);
      this.$set(data, 'type', config.giftType);
      this.$set(data, 'describe', config.describe);
    },
    dealIntegralEditData(data) {
      const config = JSON.parse(data.config);
      this.$set(data, 'points', config.points);
    },
    dealQuestionEditData(data) {
      const config = JSON.parse(data.config);
      this.$set(data, 'type', config.type);
      this.$set(data, 'amount', config.amount);
      // 处理问题答案把选项转换成数组对象
      const options = JSON.parse(data.answerOptions);
      const optionKeys = ['A', 'B', 'C', 'D'];
      optionKeys.forEach(key => {
        const option = options.find(item => item[key]);
        if (option) {
          this.$set(data, `option${key}`, option[key]);
        }
      });
      // 处理正确答案 如果数组只有一个值则转成字符串，否则直接赋值
      const correctAnswer = JSON.parse(data.correctAnswer);
      const dealAnswer = correctAnswer.join('');
      this.$set(data, 'correctAnswer', dealAnswer);
    },
    reset() {
      this.$refs.videoForm.reset();
      this.$refs.configForm.reset();
    }
  }
}
</script>

<style scoped lang="less">
.video-management-page {
  background: #f8f9fa;
  min-height: 100vh;

  .config-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    }

    .card-header {
      padding: 20px 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;

      .card-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        .t-icon {
          font-size: 20px;
        }
      }
    }

    .card-content {
      padding: 24px;
    }
  }

  .config-form {
    .form-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e5e7eb;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 40px;
          height: 2px;
          background: #3b82f6;
        }
      }

      .form-row {
        display: flex;
        gap: 24px;
        margin-bottom: 20px;

        .form-item {
          flex: 1;
          min-width: 0;
        }

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 16px;
        }
      }
    }
  }
}
  // 上传组件样式
  .upload-container {
    .upload-area {
      width: 100%;
      height: 200px;
      border: 2px dashed #d1d5db;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: #3b82f6;
        background: #f8fafc;
      }

      .preview-container {
        width: 100%;
        height: 100%;
        position: relative;

        .cover-preview,
        .video-preview {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .upload-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #fff;
          opacity: 0;
          transition: opacity 0.3s ease;

          .t-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }

          span {
            font-size: 14px;
            font-weight: 500;
          }
        }

        &:hover .upload-overlay {
          opacity: 1;
        }
      }

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        color: #6b7280;

        .t-icon {
          color: #9ca3af;
        }

        .upload-text {
          text-align: center;

          .upload-title {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
          }

          .upload-desc {
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
    }

    .file-name {
      margin-top: 8px;
      font-size: 12px;
      color: #6b7280;
      text-align: center;
    }

    &.cover-upload .upload-area {
      height: 160px;
    }

    &.video-upload .upload-area {
      height: 240px;
    }
  }

  .field-with-tips {
    .field-tips {
      margin-top: 8px;
      padding: 8px 12px;
      background: #f0f9ff;
      border-radius: 6px;
      border-left: 3px solid #3b82f6;
      font-size: 12px;
      color: #1e40af;
      display: flex;
      align-items: center;
      gap: 6px;

      .t-icon {
        font-size: 14px;
      }
    }
  }

  .activity-selector {
    .t-button {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .t-icon {
        margin-right: 6px;
      }
    }
  }

  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 16px;

    .t-button {
      border-radius: 8px;
      font-weight: 500;
      padding: 8px 24px;
      transition: all 0.3s ease;

      .t-icon {
        margin-right: 6px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  // 活动预览样式
  .activity-preview {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 8px;
    border: 1px solid #bae6fd;

    .activity-header {
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #0c4a6e;
      }
    }

    .activity-content {
      .activity-info {
        margin-bottom: 16px;

        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .info-label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            min-width: 80px;
          }

          .info-value {
            font-size: 14px;
            color: #1f2937;

            &.highlight {
              color: #dc2626;
              font-weight: 600;
            }

            &.answer-text {
              color: #dc2626;
              font-weight: 600;
              background: #fef2f2;
              padding: 2px 8px;
              border-radius: 4px;
            }
          }
        }
      }

      .question-activity {
        .question-header {
          margin-bottom: 12px;

          .question-title,
          .question-answer {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
          }
        }

        .question-content {
          margin-bottom: 12px;
          padding: 12px;
          background: #fff;
          border-radius: 6px;
          border: 1px solid #e5e7eb;
          font-size: 14px;
          line-height: 1.5;
        }

        .answer-options {
          margin-top: 12px;

          .options-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
          }

          .options-list {
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            padding: 12px;

            .option-item {
              display: flex;
              align-items: flex-start;
              margin-bottom: 8px;
              padding: 6px 0;

              &:last-child {
                margin-bottom: 0;
              }

              .option-label {
                font-weight: 600;
                color: #3b82f6;
                min-width: 24px;
                flex-shrink: 0;
              }

              .option-text {
                font-size: 14px;
                color: #1f2937;
                line-height: 1.4;
              }
            }
          }
        }
      }
    }
  }


// 全局表单组件优化
/deep/ .t-form-item {
  margin-bottom: 20px;

  .t-form__label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }

  .t-input,
  .t-select,
  .t-textarea {
    border-radius: 8px;

    transition: all 0.2s ease;

    &:hover {
      border-color: #9ca3af;
    }

    &:focus,
    &.t-is-focused {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  .t-textarea {
    resize: vertical;
  }
}

/deep/ .t-radio-group {
  .t-radio {
    margin-right: 16px;

    .t-radio__label {
      font-weight: 500;
      color: #374151;
    }

    &.t-is-checked {
      .t-radio__label {
        color: #3b82f6;
      }
    }
  }
}

/deep/ .t-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.t-button--theme-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }

  &.t-button--variant-outline {

    background: #fff;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .video-management-page {
    padding: 16px;

    .config-card {
      .card-header {
        padding: 16px 20px;

        .card-title {
          font-size: 16px;
        }
      }

      .card-content {
        padding: 16px 20px;
      }
    }

    .config-form {
      .form-section {
        margin-bottom: 24px;

        .section-title {
          font-size: 14px;
        }
      }

      .form-actions {
        flex-direction: column;
        gap: 12px;

        .t-button {
          width: 100%;
        }
      }
    }

    .upload-container {
      .upload-area {
        height: 150px;
      }

      &.video-upload .upload-area {
        height: 180px;
      }
    }
  }
}
</style>
