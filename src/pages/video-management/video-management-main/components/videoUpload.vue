<template>
  <div>
    <t-dialog
      width="65%"
      header="添加视频"
      :closeOnOverlayClick="false"
      :visible.sync="modalVisible"
      @close="closeModal"
      class="edit-task-dialog"
      @confirm="confirmVideo"
      :cancelBtn="null"
    >
      <div>
        <div class="search-input">
          <div>
            <t-button theme="primary" @click="openUploadModal">上传视频</t-button>
            <t-button variant="text" theme="primary" @click="refreshTable">刷新</t-button>
          </div>
          <t-form :data="form" ref="formSearch" layout="inline">
            <t-form-item label="视频名称">
              <t-input v-model="form.videoName" :clearable="true" placeholder="请输入图片名称"></t-input>
            </t-form-item>
            <t-form-item label="视频分组">
              <t-select v-model="activeId" :clearable="true" placeholder="请选择图片分组"
                        :options="videoGroupOptions" @change="clearSelected"></t-select>
            </t-form-item>
          </t-form>
        </div>

        <common-table
          ref="commonTable"
          :columns="columns"
          :url="url"
          rowKey="id"
          :isRequest="!!activeId"
          :params="tableParams"
          selectable
          :operations="operations"
          @getValue="selectedValue"
        >
        </common-table>
      </div>
    </t-dialog>
    <video-upload-child style="z-index: 3000" :visible="uploadModalVisible"
                        @close="uploadModalVisible = false"></video-upload-child>
    <preview-resource style="z-index: 3000" :visible="previewVisible" :src="previewResource"
                      @close="previewVisible = false"></preview-resource>
  </div>
</template>
<script lang="ts">
import CommonTable from "@/components/common-table/index.vue";
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import videoUploadChild from "@/pages/video-center/video-center-main/components/videoUpload.vue";
import PreviewResource from "@/components/preview-resource/index.vue";

export default {
  name: "videoUpload",
  components: {CommonTable, videoUploadChild, PreviewResource},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      url: sysVideoUploadApi.queryVideoListByGroupId.url,
      form: {
        videoName: "",
      },
      videoGroupOptions: [],
      modalVisible: false,
      uploadModalVisible: false,
      previewVisible: false,
      previewResource: '',
      columns: [
        {colKey: 'row-select', type: 'single'},
        {
          colKey: 'videoName',
          title: '视频名称',
          width: 150,
        },
        {
          colKey: 'videoGroupid',
          title: '视频分组',
          width: 80,
          cell: (h, {col, row}) => {
            const val = row[col.colKey];
            return this.getModality(val);
          }
        },
        // {
        //   colKey: 'videoDuration',
        //   title: '视频时长',
        //   width: 80,
        // },
        {
          colKey: 'videoSize',
          title: '视频大小',
          width: 80,
          cell: (h, {col, row}) => {
            const val = row[col.colKey];
            return val ? `${val.toFixed(1)}MB` : '';
          },
        },
        {
          colKey: 'op',
          title: '操作',
          width: 100,
          cell: 'op',
        },
      ],
      activeId: 'ALL',
      operations: [
        {
          type: 'preview',
          onClick: this.onPreview,
        },
      ],
      actionTypeList: [],
    };
  },
  computed: {
    tableParams() {
      return {
        id: this.activeId,
        videoName: this.form.videoName,
      };
    },
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    }
  },
  mounted() {
    this.getVideoGroupList();
  },
  methods: {
    async getVideoGroupList() {
      const {data} = await this.$request.get(sysVideoUploadApi.queryVideoGroupList.url);
      this.actionTypeList = data;
      this.videoGroupOptions = data.map((item: any) => ({
        label: item.groupName,
        value: item.id,
      }));
    },
    getModality(val: any) {
      const data = this.actionTypeList;
      return data.find((item: any) => item.id === val)?.groupName || '';
    },
    onPreview(row: any) {
      this.previewVisible = true;
      this.previewResource = row.videoUrl;
    },
    refreshTable() {
      this.$refs.commonTable && this.$refs.commonTable.getList();
    },
    openUploadModal() {
      this.uploadModalVisible = true;
    },
    closeModal() {
      this.$emit('close');
    },
    confirmVideo() {
      const {selectedRows} = this.$refs.commonTable;
      this.$emit('success', selectedRows);
    },
    clearSelected(e: any, context: any) {
      if (context.trigger === "clear") {
        this.activeId = 'ALL';
      }
    },
    selectedValue(data: any) {
    },
  },
};
</script>
<style lang="less" scoped>
.search-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 15px 0;

  .refresh-btn {
    cursor: pointer;
    color: #409eff;
    font-size: 14px;
    margin-left: 10px;
  }
}
</style>
