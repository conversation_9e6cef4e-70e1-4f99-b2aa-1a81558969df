<template>
  <div>
    <t-dialog
      width="65%"
      header="添加图片"
      :closeOnOverlayClick="false"
      :visible="modalVisible"
      @close="closeModal"
      class="edit-task-dialog"
      @confirm="confirmVideo"
      :cancelBtn="null"
    >
      <div>
        <div>
          <t-button theme="primary" @click="openModal">上传图片</t-button>
        </div>

        <common-table
          ref="commonTable"
          :selectable="true"
          :columns="columns"
          :url="url"
          :isRequest="!!activeId"
          :params="tableParams"
          :operations="operations"
        >
        </common-table>
      </div>
    </t-dialog>
    <image-upload-child :visible="uploadModalVisible" @close="uploadModalVisible = false" @success="closeImageUpload" @error="closeImageUpload"></image-upload-child>
    <preview-resource style="z-index: 3000" :visible="previewVisible" :src="resourceUrl" @close="previewVisible = false"></preview-resource>
  </div>
</template>
<script lang="ts">
import CommonTable from "@/components/common-table/index.vue";
import ImageUploadChild from "@/pages/video-management/video-management-main/components/imageUploadChild.vue";
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import PreviewResource from "@/components/preview-resource/index.vue";

export default {
  name: "imageUpload",
  components: { CommonTable, ImageUploadChild, PreviewResource },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      url: sysVideoUploadApi.queryCoverList.url,
      form: {
        imageGroup: "",
        imageName: "",
        createDate: [],
      },
      imageGroupOptions: [{ label: "默认分组", value: "1" }],
      modalVisible: false,
      uploadModalVisible: false,
      resourceUrl: "",
      previewVisible: false,
      columns: [
        { colKey: "row-select", type: "single" },
        {
          colKey: 'imageName',
          title: '图片名称',
          width: 150,
        },
        {
          colKey: 'imageSize',
          title: '图片大小',
          width: 80,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val? `${val.toFixed(1)}MB` : '';
          }
        },
        {
          colKey: 'op',
          title: '操作',
          width: 100,
          cell: 'op',
        },
      ],
      activeId: 'ALL',
      operations: [
        {
          type: 'preview',
          onClick: this.onPreview,
        },
      ],
    };
  },
  computed: {
    tableParams() {
      return {};
    },
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    }
  },
  methods: {
    onPreview(row) {
      this.previewVisible = true;
      this.resourceUrl = row.imageUrl;
    },
    openModal() {
      this.uploadModalVisible = true;
    },
    closeImageUpload() {
      this.uploadModalVisible = false;
      this.$refs.commonTable.getList();
    },
    confirmVideo() {
      const { selectedRows } = this.$refs.commonTable;
      this.$emit("success", selectedRows);
    },
    closeModal() {
      this.modalVisible = false;
      this.$emit("close");
    },
  },
};
</script>
<style lang="less" scoped>
.search-input {
  margin: 15px 0;
  .search-btn {
    text-align: right;
    margin-right: 20px;
    margin-top: -35px;
  }
}
</style>
