<template>
  <div>
    <t-dialog
      width="65%"
      header="选择营销活动"
      :closeOnOverlayClick="false"
      :visible.sync="modalVisible"
      @close="closeModal"
      class="edit-task-dialog"
      @confirm="confirmVideo"
      :cancelBtn="null"
    >
      <div>
        <div class="search-input">
          <t-form :data="params" ref="formSearch" layout="inline">
            <t-form-item label="营销活动名称">
              <t-input v-model="params.title" :clearable="true" placeholder="请输入营销活动名称"></t-input>
            </t-form-item>
            <t-form-item label="活动类型">
              <t-select v-model="params.type" :clearable="true" :options="activeOptions" allow-input />
            </t-form-item>
            <t-form-item label="活动分组">
              <t-select v-model="activeId" :clearable="true" @change="clearChange" :options="activityGroupOptions" allow-input />
            </t-form-item>
          </t-form>
        </div>
        <common-table
          ref="commonTable"
          :columns="columns"
          :url="url"
          rowKey="id"
          :isRequest="!!activeId"
          :params="tableParams"
          selectable
          :operations="operations"
        >
        </common-table>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
import CommonTable from "@/components/common-table/index.vue";
import {MARKETING_OPTIONS, MARKETING_STATE_OPTIONS} from "@/constants/enum/business/marketing-campaign-array.enum";
import sysCampaignGroupApi from "@/constants/api/back/sys-campaign-group.api";

export default {
  name: 'activityChoose',
  components: { CommonTable },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      url: sysCampaignGroupApi.queryCampaignGroupListByGroupId.url,
      modalVisible: false,
      activeId: 'ALL',
      actionTypeList: [],
      statusList: MARKETING_STATE_OPTIONS,
      activeOptions: MARKETING_OPTIONS,
      activityGroupOptions: [],
      columns: [
        { colKey: 'row-select', type: 'single' },
        {
          title: '活动名称',
          colKey: 'title',
          align: 'center',
          width: 200,
        },
        {
          title: '活动类型',
          colKey: 'activityType',
          align: 'center',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getModality(val);
          }
        },
        {
          title: '活动分组',
          colKey:'groupId',
          align: 'center',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.actionTypeList.find((item: any) => item.id === val)?.groupName || '';
          }
        },
        {
          title: '活动状态',
          colKey: 'status',
          align: 'center',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.statusList.find((item: any) => item.value === val)?.label || '';
          }
        }
      ],
      operations: [],
      form: {
        title: '',
        type: '',
      },
      params: {
        title: '',
        type: '',
      },
    };
  },
  computed: {
    tableParams() {
      return {
        id: this.activeId,
        title: this.params.title,
        type: this.params.type,
      };
    },
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    }
  },
  mounted() {
    this.getCampaignList();
  },
  methods: {
    async getCampaignList() {
      const { data } = await this.$request.get(sysCampaignGroupApi.queryCampaignGroupList.url);
      this.actionTypeList = data;
      this.activityGroupOptions = data.map((item: any) => ({
        label: item.groupName,
        value: item.id,
      }));
    },
    closeModal() {
      this.$emit('close');
    },
    confirmVideo() {
      const { selectedRows } = this.$refs.commonTable;
      this.$emit('success', selectedRows);
    },
    getModality(val: any) {
      return MARKETING_OPTIONS.find(item => item.value === val)?.label || '';
    },
    clearChange(e: any, context: any) {
      if (context.trigger === "clear") {
        this.activeId = 'ALL';
      }
    },
  },
};

</script>
<style lang="less" scoped>
.search-input {
  margin: 15px 0;
  .search-btn {
    text-align: right;
    margin-right: 20px;
    margin-top: -35px;
  }
}
</style>
