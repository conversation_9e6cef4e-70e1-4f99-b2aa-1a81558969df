<template>
  <t-dialog :visible.sync='modalVisible' width="35%" @close='closeModal' header="上传图片"
            class='action-type-edit-dialog' @confirm='closeModal' @cancel='closeModal'>

    <t-upload
      :key="uploadKey"
      ref="uploadRef"
      theme="file-flow"
      placeholder="支持批量上传文件"
      :multiple="true"
      :autoUpload="false"
      @change="onSubmit"
    >
      <t-button theme="primary" size="small">上传图片</t-button>
    </t-upload>

  </t-dialog>
</template>

<script>

import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";

export default {
  name: 'ImageUploadChild',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    initForm: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      uploadKey: Date.now(),
      modalVisible: false,
    };
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    },
  },
  methods: {
    closeModal() {
      this.uploadKey = Date.now();
      this.$emit('close');
    },
    async onSubmit(data) {
      const imagePath = import.meta.env.VITE_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      await Promise.all(promises).then(() => { // 上传成功 关闭弹窗
        this.$message.success({ content: '操作成功' });
        this.$emit('success');
      }).catch(() => { // 上传失败 关闭弹窗
        this.closeModal();
        this.$message.error({ content: '操作失败' });
      });
    },
  },
};
</script>

<style lang='less' scoped>
.action-type-edit-dialog {
  /deep/ .t-dialog__body {
    padding-top: 30px;
  }
}
/deep/ .t-form__controls {
  margin-left: 125px !important;
}
/deep/ .t-upload__flow-bottom {
  display: none !important;
}
</style>

