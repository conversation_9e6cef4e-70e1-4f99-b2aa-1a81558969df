<template>
  <div class="video-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="video" />
          视频课程管理
        </div>
        <div class="page-desc">管理视频课程内容，设置营销活动和上架状态</div>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="addModel" size="large">
          <t-icon name="add" />
          新建视频课程
        </t-button>
      </div>
    </div>



    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧分组列表 -->
      <div class="sidebar">
        <action-list ref="actionListRef" @select="onSelected"></action-list>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索筛选区域 -->
        <div class="filter-section">
          <div class="filter-content">
            <t-form :data="form" ref="formSearch" layout="inline" class="filter-form">
              <div class="filter-row">
                <t-form-item label="课程名称" name="courseName" class="filter-item">
                  <t-input
                    v-model="form.courseName"
                    :clearable="true"
                    placeholder="请输入课程名称"
                    class="filter-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="search" />
                    </template>
                  </t-input>
                </t-form-item>

                <t-form-item label="创建日期" name="createDate" class="filter-item">
                  <t-date-range-picker
                    v-model="searchDate"
                    :clearable="true"
                    allow-input
                    placeholder="请选择创建日期"
                    class="filter-date"
                  />
                </t-form-item>

                <t-form-item label="上架状态" name="courseStatus" class="filter-item">
                  <t-select
                    v-model="form.courseStatus"
                    :clearable="true"
                    placeholder="请选择上架状态"
                    :options="shelfOptions"
                    class="filter-select"
                  >
                    <template #prefixIcon>
                      <t-icon name="layers" />
                    </template>
                  </t-select>
                </t-form-item>

                <!-- 操作按钮 -->
                <div class="filter-buttons">
                  <t-button theme="primary" @click="onSubmit" class="search-btn">
                    <t-icon name="search" />
                    查询
                  </t-button>
                  <t-button variant="outline" @click="onReset" class="reset-btn">
                    <t-icon name="refresh" />
                    重置
                  </t-button>
                </div>
              </div>
            </t-form>
          </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <div class="selection-info" v-if="selectedData.length > 0">
              <t-icon name="check-circle" />
              <span class="selected-count">已选择 {{ selectedData.length }} 项</span>
              <t-button size="small" variant="text" @click="clearSelection">
                <t-icon name="close" />
                清空
              </t-button>
            </div>
            <div class="table-info" v-else>
              <t-icon name="view-list" />
              <span>课程列表</span>
            </div>
          </div>

          <div class="toolbar-right">
            <t-button
              :disabled="disableButton"
              theme="default"
              variant="outline"
              @click="openDailog"
              class="batch-btn"
            >
              <t-icon name="file-copy" />
              批量复制
            </t-button>
            <t-button variant="outline" @click="refreshData" class="refresh-btn">
              <t-icon name="refresh" />
              刷新
            </t-button>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-section">
          <common-table
            ref="commonTable"
            :columns="columns"
            :url="url"
            :isRequest="!!activeId"
            :params="tableParams"
            :operations="operations"
            @getValue="handleSelectedData"
            class="course-table"
          >
          </common-table>
        </div>
      </div>
    </div>

    <!-- 新建/编辑课程对话框 -->
    <new-video
      ref="editModal"
      :visible="addTaskModalVisible"
      @close="addTaskModalVisible = false"
      @success="onSuccess"
    ></new-video>

    <!-- 批量复制对话框 -->
    <t-dialog
      class="batch-copy-dialog"
      placement="center"
      :visible="visibleCenter"
      :onConfirm="confirmDialog"
      :onClose="closeDialog"
      :width="480"
    >
      <template #header>
        <div class="dialog-header">
          <t-icon name="file-copy" class="dialog-icon" />
          <span class="dialog-title">批量复制课程</span>
        </div>
      </template>

      <div class="dialog-content">
        <div class="selected-info">
          <t-icon name="info-circle" />
          <span>已选择 {{ selectedData.length }} 个课程进行复制</span>
        </div>

        <t-form :data="formDialogData" layout="vertical" class="copy-form">
          <t-form-item label="目标分组" name="groupId" required>
            <t-select
              clearable
              filterable
              v-model="formDialogData.groupId"
              placeholder="请选择目标分组"
              @change="handleChange"
              class="group-select"
            >
              <template #prefixIcon>
                <t-icon name="folder" />
              </template>
              <t-option
                v-for="item in options"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </t-select>
          </t-form-item>
        </t-form>

        <div class="copy-tips">
          <t-icon name="info-circle" />
          <span>复制后的课程将保持原有配置，但需要重新设置上架状态</span>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script>
import NewVideo from './components/new-video.vue';
import ActionList from './components/action-list.vue';
import CommonTable from '@/components/common-table/index.vue';
import sysCourseGroupApi from "@/constants/api/back/sys-course-group.api";
import {COURSE_STATUS_TABLE} from "@/constants/enum/business/course-status-array.enum";
import sysCourseVideoApi from "@/constants/api/back/sys-course-video.api";
import {CAMPAIGN_TYPE_OPTIONS} from "@/constants/enum/business/marketing-campaign-array.enum";
import {getMenuChildrenList, getOperationTypeList} from "@/utils/utils";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";

export default {
  name: 'video-list',
  components: { ActionList, NewVideo, CommonTable },
  data() {
    return {
      formDialogData:{
        groupName:'',
        groupId:'',
      },
      options:{},
      visibleCenter: false,
      disableButton: true,
      url: sysCourseGroupApi.queryCourseGroupListByGroupId.url,
      searchDate: [],
      params: {
        courseName: '',
        startDate: '',
        endDate: '',
        courseStatus: '',
      },
      form: {
        courseName: '',
        activeId: '',
        startDate: '',
        endDate: '',
        courseStatus: '',
      },
      activeOptions: [],
      shelfOptions: [
        { label: '全部', value: 'ALL' },
        { label: '已上架', value: '1' },
        { label: '已下架', value: '0' },
        { label: '暂未上架', value: '2' },
      ],
      questionTypeOptions: [
        { label: '视频课', value: '1' },
        { label: '红包视频课', value: '2' },
      ],
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 50,
        },
        {
          colKey: 'courseName',
          title: '课程名称',
          width: 120,
        },
        {
          colKey: 'groupId',
          title: '课程分组',
          width: 80,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getModality(val, row);
          },
        },
        {
          colKey: 'courseStatus',
          title: '上架状态',
          width: 60,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getCourseStatus(val);
          },
        },
        {
          colKey: 'field1',
          title: '课程序号',
          width: 50,
        },
        {
          colKey: 'activityInfo',
          title: '活动信息',
          width: 120,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getConfigInfo(val);
          },
        },
        {
          colKey: 'op',
          title: '操作',
          width: 80,
          cell: 'op',
        },
      ],
      addModalVisible: false,
      addTaskModalVisible: false,
      activeId: 'ALL',
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      },
      userOperationTab: [], // 用户可操作按钮
      selectedData: [],
    };
  },
  computed: {
    tableParams() {
      return {
        id: this.activeId === 'ALL' ? 'ALL' : this.activeId,
        courseName: this.params.courseName,
        startDate: this.params.startDate,
        endDate: this.params.endDate,
        courseStatus: this.params.courseStatus,
      };
    },
    operations() {
      return [
        {
          type: 'copy',
          onClick: this.onCopy,
        },
        {
          type: 'removed',
          onClick: this.onRemoved,
        },
        {
          type: 'edit',
          onClick: this.onEdit,
          visible: this.userOperation.hasEdit,
        },
        {
          type: 'delete',
          onClick: this.onDel,
          visible: this.userOperation.hasDelete,
        },
      ]
    }
  },
  mounted() {
    // 获取用户操作类型按钮列表
    this.getOperationTypeList();
  },
  methods: {


    // 清空选择
    clearSelection() {
      this.selectedData = [];
      this.disableButton = true;
      if (this.$refs.commonTable?.clearSelection) {
        this.$refs.commonTable.clearSelection();
      }
    },

    // 刷新数据
    refreshData() {
      this.$refs.commonTable.getList();
    },

    openDailog(){
      this.visibleCenter = true;
      this.getList();
    },
    handleChange(value){
      console.log("value",value);
    },
    async getList() {
      const { data } = await this.$request.get(sysCourseGroupApi.queryCourseGroupList.url);
      this.options = data.map(item => ({
        label: item.groupName,
        value: item.id
      }));
    },
    closeDialog() {
      this.visibleCenter = false;
    },
    async confirmDialog() {
      try {
        this.visibleCenter = false;

        // 1. 深拷贝并移除ID字段
        const newData = JSON.parse(JSON.stringify(this.selectedData)).map(item => {
          // 解构排除id字段
          const { id, ...rest } = item;
          return {
            ...rest,  // 保留除id外的所有字段
            groupId: this.formDialogData.groupId
          };
        });

        // 2. 发送批量更新请求
        const { code } = await this.$request.post(
          sysCourseVideoApi.batchCourseByGroupId.url,
          newData  // 使用处理后的数据
        );

        // 3. 处理请求结果
        if (code === 0) {
          this.$message.success({ content: '批量操作成功' });

          // 4. 清除表格选中状态
          if (this.$refs.commonTable?.clearSelection) {
            this.$refs.commonTable.clearSelection();
          }

          // 5. 重置本地状态
          this.selectedData = [];
          this.formDialogData = { groupName: '', groupId: '' };
          this.disableButton = true;

          // 6. 刷新表格数据
          this.$refs.commonTable.getList();
        } else {
          throw new Error('API返回非0状态码');
        }
      } catch (error) {
        console.error('批量操作失败:', error);
        this.$message.error({ content: '操作失败，请重试' });

        // 7. 失败时恢复对话框显示
        this.visibleCenter = true;
      }
    },
    handleSelectedData(selectedRows) {
      console.log("选中的数据：", selectedRows);
      if(selectedRows.length >= 1){ this.disableButton = false}
      else {this.disableButton = true}
      this.selectedData = selectedRows;
    },
    /**
     * @description: 获取操作类型按钮列表
     * <AUTHOR>
     * @date 2025/6/2 11:29
     */
    getOperationTypeList() {
      // 登录后用户具有的菜单已经存入了storage中，从中获取
      this.childrenButton = getMenuChildrenList();
      // console.log("获取到具有按钮菜单：", this.childrenButton);
      if(this.childrenButton && this.childrenButton.length > 0) {
        this.childrenButton.map((item) => {
          if (item.menuType && item.menuType == SYSTEM_MENU_CONST.O.value && item.menuUrl) {
            // 截取操作按钮，区分类型
            const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
            this.userOperationTab.push(operationType);
          }
        })
      }
      this.userOperation = getOperationTypeList(this.userOperationTab)
      // console.log("获取到可操作按钮类型：", this.userOperation);
      // console.log("操作栏：", this.operations);
    },
    addModel() {
      this.$router.push({ name: 'new-video', params: { type: 'new' } });
      // this.addTaskModalVisible = true;
    },
    onSuccess() {
      this.addTaskModalVisible = false;
      this.$refs.commonTable.getList();
    },
    onSelected(val) {
      this.activeId = val;
    },
    async onDel(data) {
      console.error(data);
      // 根据id删除数据
      const { code } = await this.$request.post(sysCourseVideoApi.deleteCourse.url, { ...data });
      if (code === 0) {
        this.$message.success({ content: '操作成功' });
        this.$refs.commonTable.getList();
      } else {
        this.$message.error({ content: '操作失败' });
      }
    },

    getModality(val, row) {
      const data = this.$refs.actionListRef.actionTypeList;
      return data.find((item) => item.id === val)?.groupName || '';
    },
    getCourseStatus(val) {
      const data = COURSE_STATUS_TABLE;
      return data.find((item) => item.value === val)?.label || '暂不上架';
    },
    getConfigInfo(val) {
      if (!val) return '暂未配置';
      // 后续需优化展示
      const activityInfo = JSON.parse(val);
      const { activityType, config } = activityInfo;
      const configInfo = JSON.parse(config);
      const typeLabel = CAMPAIGN_TYPE_OPTIONS[activityType]?.find(
        item => item.value === configInfo.type
      )?.label || '';
      const formatMoney = (value, prefix) =>
        value ? `${prefix}：${value}元` : '';
      return [
        typeLabel && `类型：${typeLabel}`,
        formatMoney(configInfo.amount, '金额'),
        formatMoney(configInfo.minAmount, '最小金额'),
        formatMoney(configInfo.maxAmount, '最大金额')
      ].filter(Boolean).join(' | ');
    },
    onCopy(row ) {
      this.$router.push({ name: 'new-video', params: { data: row, type: 'copy' } });
    },
    async onRemoved(data) {
      const { code } = await this.$request.post(sysCourseVideoApi.removeCourseById.url, { id: data.id });
      if (code === 0) {
        this.$message.success({ content: '操作成功' });
        this.$refs.commonTable.getList();
      } else {
        this.$message.error({ content: '操作失败' });
      }
    },

    onEdit(row) {
      this.$router.push({ name: 'new-video', params: { data: row, type: 'edit' } });
    },
    onSubmit() {
      if (this.searchDate) {
        if (this.searchDate.length > 0) {
          this.form.startDate = this.searchDate[0];
        }
        if (this.searchDate.length > 1) {
          this.form.startDate = this.searchDate[0];
          this.form.endDate = this.searchDate[1];
        }
      }
      this.params = {
        courseName: this.form.courseName,
        startDate: this.form.startDate,
        endDate: this.form.endDate,
        courseStatus: this.form.courseStatus,
      };
    },
    onReset() {
      this.searchDate = [];
      this.form = {
        courseName: '',
        activeId: '',
        startDate: '',
        endDate: '',
        courseStatus: '',
      };
      this.onSubmit();
    },
  },
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面主体样式 ====================
.video-management-page {
  padding: 12px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      .t-button {
        height: 44px;
        padding: 0 24px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }



  // 主要内容区域
  .main-content {
    display: flex;
    gap: 24px;
    min-height: calc(100vh - 300px);
  }

  // 左侧边栏
  .sidebar {
    width: 250px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
  }

  // 右侧内容区域
  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 0;
  }

  // 筛选区域
  .filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .filter-content {
      padding: 16px 20px;

      .filter-form {
        .filter-row {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          align-items: flex-end;

          .filter-item {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            margin-bottom: 16px;

            .filter-input,
            .filter-select,
            .filter-date {
              width: 100%;
              border-radius: 8px;

              transition: all 0.2s ease;

              &:hover {
                border-color: #9ca3af;
              }

              &:focus,
              &.t-is-focused {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-left: auto;
            flex-shrink: 0;

            .t-button {
              padding: 0 16px;
              border-radius: 8px;
              font-weight: 500;
              transition: all 0.3s ease;

              .t-icon {
                margin-right: 4px;
              }

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }

            .search-btn {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              border: none;
              color: #fff;

              &:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
              }
            }

            .reset-btn {

              background: #fff;
              color: #374151;

              &:hover {
                background: #f9fafb;
                border-color: #9ca3af;
              }
            }
          }
        }
      }
    }
  }

  // 工具栏
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .selection-info {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: #dbeafe;
        border-radius: 8px;
        color: #1e40af;

        .selected-count {
          font-weight: 500;
        }

        .t-button {
          padding: 0 8px;
          height: 24px;
          font-size: 12px;
        }
      }

      .table-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #6b7280;

        .t-icon {
          font-size: 16px;
        }
      }
    }

    .toolbar-right {
      display: flex;
      gap: 12px;

      .t-button {
        height: 36px;
        padding: 0 16px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
        }
      }

      .batch-btn {
        border: 1px solid #3b82f6;
        color: #3b82f6;

        &:hover:not(:disabled) {
          background: #3b82f6;
          color: #fff;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .refresh-btn {

        color: #374151;

        &:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }
      }
    }
  }

  // 表格区域
  .table-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    flex: 1;
    min-height: 0;
    overflow: hidden;

    .course-table {
      height: 100%;

      /deep/ .t-table {
        height: 100%;

        .t-table__header {
          th {
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 12px;
          }
        }

        .t-table__body {
          tr {
            transition: all 0.2s ease;

            &:hover {
              background: #f8f9fa;
            }

            td {
              border-bottom: 1px solid #f3f4f6;
              color: #374151;
            }
          }
        }
      }
    }
  }

  // 批量复制对话框
  .batch-copy-dialog {
    /deep/ .t-dialog {
      border-radius: 16px;
      overflow: hidden;
    }

    .dialog-header {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 4px 0;

      .dialog-icon {
        font-size: 20px;
        color: #3b82f6;
      }

      .dialog-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .dialog-content {
      padding: 8px 0;

      .selected-info {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background: #f0f9ff;
        border-radius: 8px;
        border-left: 4px solid #3b82f6;
        margin-bottom: 20px;

        .t-icon {
          color: #3b82f6;
        }

        span {
          color: #1e40af;
          font-weight: 500;
        }
      }

      .copy-form {
        .group-select {
          height: 44px;
          border-radius: 8px;

          transition: all 0.2s ease;

          &:hover {
            border-color: #9ca3af;
          }

          &:focus,
          &.t-is-focused {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }

      .copy-tips {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background: #fef3c7;
        border-radius: 8px;
        border-left: 4px solid #f59e0b;
        margin-top: 16px;

        .t-icon {
          color: #d97706;
        }

        span {
          color: #92400e;
          font-size: 13px;
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .video-management-page {
    .main-content {
      flex-direction: column;

      .sidebar {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .video-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            flex-direction: column;

            .filter-item {
              min-width: 100%;
              max-width: 100%;
            }

            .filter-buttons {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 12px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
