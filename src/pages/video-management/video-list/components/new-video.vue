<template>
  <t-dialog
    width="600"
    header="创建视频课"
    :closeOnOverlayClick="false"
    :visible.sync="modalVisible"
    @close="closeModal"
    class="edit-task-dialog"
    :confirmBtn="null"
    :cancelBtn="null"
  >
 <div>
   <span>请选择视频课类型：</span>
   <div class="choose-list">
     <div class="choose-item" @click="chooseType(1)">
       <div class="video-container"></div>
       <div class="text-container">
         <span class="text-title">视频课</span>
         <span class="text-desc">观看学习视频类课程</span>
       </div>
     </div>
     <div class="choose-item" @click="chooseType(2)">
       <div class="red-video-container"></div>
       <div class="text-container">
         <span class="text-title">红包视频课</span>
         <span class="text-desc">观看视频答题领红包</span>
       </div>
     </div>
   </div>
 </div>
  </t-dialog>
</template>

<script>

export default {
  name: 'new-video',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    expose: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {},
      actionTypes: [],
      modalVisible: false,
      meetSceneType: [],
      actionAbilityTypeEnums: [],
      loading: false,
    };
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    }
  },
  mounted() {},
  methods: {
    chooseType(index) {
      if (index) {
        const path = index === 1 ? '/video-management/new-video' : '/video-management/new-video-red';
        this.$router.push({ path });
      }
      this.$emit('close');
    },
    closeModal() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="less" scoped>
.edit-task-dialog {
  /deep/ .small-input {
    width: 300px;
  }

  .example-form /deep/ .t-form__controls-content {
    flex-direction: column;
    align-items: flex-start;
  }
}
.choose-list {
  display: flex;
  justify-content: space-evenly;
  margin-top: 20px;
  .choose-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 10px;
    cursor: pointer;
    border: 1px solid #ffffff;
    border-radius: 6px;
    &:hover {
      border: 1px solid #1890ff;
    }
  }
  .text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    .text-title {
       font-size: 16px;
       color: #333333;
       margin-bottom: 5px;
     }
    .text-desc {
       font-size: 14px;
       color: #999999;
       margin-bottom: 10px;
    }
  }
}
.red-video-container {
  background: url('@/assets/png/red-bag-video.png') no-repeat center center;
  width: 50px;
  height: 50px;
  background-size: 100% 100%;
}
.video-container {
  background: url('@/assets/png/video-icon.png') no-repeat center;
  width: 50px;
  height: 50px;
  background-size: 100% 100%;
  margin: 0 20px;
}
</style>
