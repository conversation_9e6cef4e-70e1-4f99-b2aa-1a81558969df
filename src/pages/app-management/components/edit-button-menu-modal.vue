<!-- @format -->

<template>
  <t-dialog
    :header="data.isAddChildren ? '新增菜单/按钮' : data.menuId ? '编辑菜单/按钮' : '新增菜单/按钮'"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <t-form :data="form" :rules="rules" :labelWidth="140" :statusIcon="true" ref="formRef" @submit="onSubmit">
        <t-form-item label="菜单名称" name="menuName">
          <t-input v-model="form.menuName" placeholder="请输入菜单名称"></t-input>
        </t-form-item>

        <t-form-item label="菜单类型" name="menuType">
          <t-select v-model="form.menuType" placeholder="请选择"
                    :disabled="!data.parentId">
            <t-option
              v-for="item in menuOptions"
              :value="item.value"
              :label="item.label"
              :key="item.value"
              :disabled="item.disabled"
            ></t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="上级目录" name="parentId">
          <t-tree-select
            :disabled="true"
            :data="allMenu"
            v-model="form.parentName"
            filterable
            clearable
            placeholder="请选择"
          />
        </t-form-item>
        <!-- 上级目录 -->
        <t-form-item label="菜单图标" name="menuIcon">
          <t-select
            v-model="form.menuIcon"
            placeholder="请选择"
            clearable
            :popupProps="{  overlayInnerStyle: { width: '495px' } }"
          >
            <t-option-group label="自定义图标">
              <t-option
                v-for="item in ticonOptions"
                :value="item.stem"
                :label="item.stem"
                :key="item.stem"
                class="overlay-options"
              >
                <div>
                  <icon-font class="dhcc-icon" :name="item.stem" :url="`${iconUrl}/style.css`"></icon-font>
                </div>
              </t-option>
            </t-option-group>
          </t-select>
        </t-form-item>
        <template v-if="form.menuType == SYSTEM_MENU_CONST.M.value">
          <t-form-item label="路由地址" name="menuUrl">
            <t-input v-model="form.menuUrl" placeholder="请输入路由地址"></t-input>
          </t-form-item>
        </template>
        <t-form-item label="排序" name="sortNo">
          <t-input-number
            class="t-input-number-item"
            v-model="form.sortNo"
            :max="999"
            :min="1"
            placeholder="请输入排序"
          ></t-input-number>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import {
  USER_STATE_OPTIONS,
} from '@/constants';

import { prefix } from '@/config/global';
import { manifest } from 'tdesign-icons-vue/lib/manifest'; // 获取全部图标的列表
import { IconFont } from 'tdesign-icons-vue';

import axios from 'axios';
import {SYSTEM_MENU_CONST, SYSTEM_MENU_CONST_OPTIONS} from "@/constants/enum/system/system-menu.enum";
import systemMenuApi from "@/constants/api/hxsy-admin/system-menu.api";

export default Vue.extend({
  name: 'EditModal',
  components: {
    IconFont,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
    allMenu: {
      type: Array,
      default() {
        return [];
      },
      required: false,
    },
  },

  data() {
    return {
      USER_STATE_OPTIONS,
      prefix,
      loading: false,
      iconUrl: import.meta.env.VITE_APP_ICON_URL,
      ticonOptions: [],
      iconOptions: [],
      form: {
        menuType: '',
        status: '1',
        connectType: '',
        parentId: undefined,
        parentName: undefined,
        menuIcon: '',
        menuName: '',
        menuUrl: '',
        sortNo: undefined,
        isAddChildren: undefined,
      },
      sysPostInfos: [],
      rules: {
        menuName: [{ required: true }, { min: 2 }, { max: 50, type: 'error' }],
        menuType: [{ required: true }],
        menuUrl: [{ max: 150,type: 'error' }],
      },
    };
  },
  computed: {
    SYSTEM_MENU_CONST() {
      return SYSTEM_MENU_CONST
    },
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
    menuOptions: {
      get() {
        const result = SYSTEM_MENU_CONST_OPTIONS.map((item) => {
          // 区分父节点与子节点
          if ((this.form.parentId || this.form.isAddChildren) && item.value == SYSTEM_MENU_CONST.M.value) {
            let disabled = true;
            return { ...item, disabled };
          }else {
            let disabled = false;
            return { ...item, disabled };
          }
        });
        return result;
      },
    },
  },
  watch: {
    data: {
      handler(newValue) {
        // console.log(newValue);
        if (newValue.isAddChildren && !newValue.parentId) {
          // 新增节点
          (this as any).form = {
            menuIcon: '',
            menuName: '',
            menuType: newValue.menuType || SYSTEM_MENU_CONST.M.value,
            menuUrl: '',
            sortNo: undefined,
            isAddChildren: newValue.isAddChildren,
          };
        } else if (newValue.isAddChildren && newValue.parentId) {
          // 新增子节点
          (this as any).form = {
            menuIcon: newValue.menuIcon || '',
            menuName: newValue.menuName || '',
            parentId: newValue.parentId || '',
            parentName: newValue.parentName || '',
            menuType: newValue.menuType || '',
            menuUrl: newValue.menuUrl || '',
            sortNo: newValue.sortNo,
            isAddChildren: newValue.isAddChildren,
          };
        } else {
          // 修改节点
          (this as any).form = {
            menuIcon: newValue.menuIcon || '',
            menuName: newValue.menuName || '',
            parentId: newValue.parentId || undefined,
            parentName: newValue.parentName || undefined,
            menuType: newValue.menuType || '',
            menuUrl: newValue.menuUrl || '',
            sortNo: newValue.sortNo,
            isAddChildren: newValue.isAddChildren,
          };
        }
        // console.log("this.form", this.form);
        (this as any).$refs.formRef.reset();
      },

      deep: true,
    },
  },

  async mounted() {
    this.getIconInfo();
    // console.log("传入菜单：", this.data);
  },

  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      (this as any).$refs.formRef.submit();
    },
    getIconInfo() {
      // console.log(manifest);
      const tdesignIcons = manifest.map((item) => ({ ...item, stem: `t-icon-${item.stem}` }));
      this.$set(this, 'ticonOptions', [...tdesignIcons]);
    },
    async saveData() {
      this.loading = true;
      let params = {
        id: this.data.menuId || '',
        name: this.form.menuName,
        appFrom: this.data.appFrom || '0',
        menuType: this.form.menuType || '',
        menuUrl: this.form.menuUrl || '',
        icon: this.form.menuIcon || '',
        sortOrder: this.form.sortNo || '0',
        parentId: this.form.parentId || '',
        status: this.data.status || '1',
      };
      let url;
      let method = 'post';
      url = systemMenuApi.saveOrUpdateMenu.url;
      params = {
        ...params,
      };
      try {
        const res = await this.$request[method](url, params);
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success('保存成功');
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.loading = false;
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
        this.loading = false;
      }
    },
    onSubmit({ validateResult }) {
      if (validateResult === true) {
        this.saveData();
      }
    },
  },
});
</script>

<style lang="less">
@import '@/style/variables';
.t-input-number.t-input-number-item {
  width: 200px;
}
.t-select-option.overlay-options {
  display: inline-block;
  font-size: 20px;
}
.menu-icon-area {
  width: 400px;
}
</style>
