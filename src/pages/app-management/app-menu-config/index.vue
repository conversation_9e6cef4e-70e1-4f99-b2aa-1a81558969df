<template>
  <div>
    <t-card class="list-card-container" header-bordered>
    <t-row justify="space-between">
      <div class="left-operation-container">
        <t-button @click="handleAdd"> 新增 <add-circle-icon slot="icon" /></t-button>
      </div>
    </t-row>
    <div :key="JSON.stringify(treeNodeOptions)">
      <t-enhanced-table
        ref="table"
        row-key="menuId"
        drag-sort="row-handler"
        :loading="dataLoading"
        :columns="columns"
        :data="treeNodeOptions"
        :tree="{
          childrenKey: 'children',
          defaultExpandAll: true,
          indent: 24,
        }"
        @select-change="rehandleSelectChange"
        :stripe="true"
        :bordered="true"
        :hover="true"
      >
        <template #menuType="{ row }">
          <span v-if="row.menuType === SYSTEM_MENU_CONST.M.value">目录</span>
          <span v-else-if="row.menuType === SYSTEM_MENU_CONST.C.value">菜单</span>
          <span v-else-if="row.menuType === SYSTEM_MENU_CONST.F.value">按钮</span>
          <span v-else-if="row.menuType === SYSTEM_MENU_CONST.O.value">操作</span>
        </template>
        <template #connectType="{ row }">
          <span v-if="row.connectType === '1'">内部链接</span>
          <span v-else-if="row.connectType === '2'">外部链接</span>
        </template>
        <template #op="{ row }">
          <a v-if="row.menuType == SYSTEM_MENU_CONST.M.value" class="t-button-link tdesign-table-demo__table-operations" @click="handleAddChild(row)">新增</a>
          <a class="t-button-link tdesign-table-demo__table-operations" @click="handleEditRow(row)">编辑</a>
          <a class="t-button-link t-button-link--theme-danger tdesign-table-demo__table-operations" @click="handleClickDelete(row)">删除</a>
        </template>
      </t-enhanced-table>
    </div>
    <edit-button-menu-modal
      :data="editModalVar.data"
      :subData="editModalVar.subData"
      :visible="editModalVar.visible"
      :allMenu="transferTreeNodeOptions"
      @cancel="handelCloseEditModal"
      @success="handelEditModalSuccess"
    ></edit-button-menu-modal>
    <t-dialog
      theme="warning"
      header="确认要删除该项？"
      :visible.sync="confirmVisible"
      @confirm="onConfirmDelete"
      :onCancel="onCancel"
    >
    </t-dialog>
  </t-card>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import {AddCircleIcon} from 'tdesign-icons-vue'
import EditButtonMenuModal from '../components/edit-button-menu-modal.vue';
import systemMenuApi from "@/constants/api/hxsy-admin/system-menu.api";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";

export default Vue.extend({
  name: 'TenantManagementMain',
  components: {
    AddCircleIcon,
    EditButtonMenuModal,
  },
  data() {
    return {
      tabValue: '1',
      pagination: {
        // defaultPageSize: 10,
        total: 0,
        // defaultCurrent: 1,
        current: 1,
        pageSize: 10,
      },
      dataLoading: false,

      columns: [
        // {
        //   colKey: 'row-select',
        //   type: 'multiple',
        // },
        {
          colKey: 'menuName',
          title: '菜单名称',
          ellipsis: true,
        },
        {
          colKey: 'menuId',
          title: '菜单ID',
        },
        {
          colKey: 'menuType',
          title: '菜单类型',
          cell: 'menuType',
        },
        {
          colKey: 'sortNo',
          title: '序号',
        },

        {
          colKey: 'op',
          title: '操作',
          cell: 'op',
        },
      ],
      changeStatusOption: [
        {
          content: '有效',
          value: '1',
        },
        {
          content: '禁用',
          value: '2',
        },
      ],
      editModalVar: {
        visible: false,
        data: {},
        subData: {},
      },
      selectedRowKeys: [],
      sysMenuInfos: [],
      treeNodeOptions: [],
      transferTreeNodeOptions: [],
      deleteIdx: '',
      confirmVisible: false,
      nodeContrast: { id: 'menuId', parentId: 'menuSupId', children: 'subSysMenuInfo' }, // 平级转树形结构映射
      nzNodeContrast: {
        key: 'menuId',
        title: 'menuName',
        label: 'menuName',
        value: 'menuId',
        children: 'subSysMenuInfo',
      },
    };
  },
  computed: {
    SYSTEM_MENU_CONST() {
      return SYSTEM_MENU_CONST
    }
  },

  watch: {
    // 'query.appId': function (newValue) {
    //   // debugger;
    //   if (newValue) {
    //     this.getList();
    //   }
    // },
  },
  async mounted() {
    this.getList();
  },

  methods: {
    handleAdd() {
      this.editModalVar.visible = true;
      this.editModalVar.data = {
        isAddChildren: true,
        menuType: SYSTEM_MENU_CONST.M.value,
      };
    },
    /**
     * 新增子节点
     */
    handleAddChild(row: any) {
      this.editModalVar.visible = true;
      // console.log(row);
      this.editModalVar.data = {
        parentId: row.id || '',
        parentName: row.menuName || '',
        isAddChildren: true,
      };
    },
    handelCloseEditModal() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
    },
    handelEditModalSuccess() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
      this.getList();
    },
    handleEditRow(row: any) {
      this.editModalVar.visible = true;
      // console.log(row)
      this.editModalVar.data = {
        ...row,
      };
    },
    handleClickDelete(data: any) {
      this.deleteIdx = data.menuId;
      this.confirmVisible = true;
    },
    async onConfirmDelete() {
    },
    async changeStatusId(menuId: string) {
      // try {
      //   const res = await (this as any).$request.delete(apiMenuInfo.deleteMenu.url, {
      //     params: {
      //       menuId,
      //     },
      //   });
      //   const { code, msg } = res;
      //   if (code === 1) {
      //     this.$message.success(msg || '删除成功');
      //     this.getList();
      //     return 'success';
      //   }
      //   this.$message.error(msg || '删除失败');
      // } catch (e) {
      //   // this.$message.error('请求失败');
      // }
    },
    async changeStatusIds(ids: any, state: string) {
      // try {
      //   const res = await (this as any).$request.put(apiComp.updateUseStateBatch.url, ids, {
      //     params: {
      //       useState: state,
      //     },
      //   });
      //   const { code, msg } = res;
      //   if (code === 1) {
      //     this.$message.success(msg || '状态改变成功');
      //     this.getList();
      //     return 'success';
      //   }
      //   this.$message.error(msg || '状态改变失败');
      // } catch (e) {
      //   // this.$message.error('请求失败');
      // }
    },
    onCancel() {
      this.deleteIdx = '';
    },
    /**
     * @description: 获取系统菜单
     * <AUTHOR>
     * @date 2025/5/11 22:59
     */
    async getList() {
      try {
        this.dataLoading = true;
        const query = {
          appFrom: '0', // 0:pc端菜单，1：小程序端菜单
        };
        const res = await (this as any).$request.post(systemMenuApi.queryPage.url, query);
        const { code, data = [], msg } = res;
        // 每次都需要清空数据，防止用到之前的菜单
        this.treeNodeOptions = [];
        // console.log("获取到响应菜单：", data);
        if (code === 0) {
          // 根据后端返回的菜单数据，转换成组件需要的树形结构
          data.forEach((item: any) => {
            const noAuthTreeNodeOption = {
              id: item.id,
              parentId: item.parentId, // 用于树形结构转换
              menuId: item.id,
              menuName: item.name,
              menuUrl: item.menuUrl,
              menuIcon: item.icon,
              menuType: item.menuType,
              sortNo: item.sortOrder,
              expanded: true,
            };
            this.treeNodeOptions.push(noAuthTreeNodeOption);
          });
          const treeNode: any[] = this.listToTree(this.treeNodeOptions);
          this.treeNodeOptions = treeNode;
          console.log("转换为树形结构后：", this.treeNodeOptions);
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        this.dataLoading = false;
        console.log(e);
      }
    },
    /**
     * @description: list菜单转换树形结构
     * 根据parentId划分递归父级结构
     * 未传入parentId，则表示正在找出最顶层菜单，后续再开始递归
     * <AUTHOR>
     * @date 2025/5/8 23:24
     */
    listToTree(list: any [], parentId?: string, parentName?: string) {
      const tree: any[] = [];
      if (parentId) {
        // console.log("当前正在获取节点:", parentId , "的子级节点");
        const rootNodes = list.filter(nodeItem => {
          // console.log("当前正在获取节点:", nodeItem);
          if(nodeItem.parentId === parentId){
            nodeItem.parentName = parentName // 设置一下父节点名称，后面编辑时需要用
            return true;
          }
        });
        // console.log("获取到子级节点:", rootNodes);
        if (rootNodes) {
          rootNodes.forEach(rootNode => {
            const children = this.listToTree(list, rootNode.id, rootNode.menuName);
            if (children) {
              rootNode.children = children;
            }
            tree.push(rootNode);
          });
        }
        return tree;
      }else {
        let FirstNodes: any[] = [];
        list.forEach(item => {
          if (!item.parentId || item.parentId == 'null' || item.parentId == ''){
            FirstNodes.push(item)
            // 记录一下所有的父节点，用于提交时选择了其下的子节点，父节点也可以置为选中
          }
        })
        // console.log("获取到顶层节点:", FirstNodes);
        // 先寻找二级菜单，再开始递归其下的子菜单
        FirstNodes.forEach(item => {
          const rootNodes = list.filter(nodeItem => {
            if (nodeItem.parentId === item.id) {
              nodeItem.parentName = item.menuName; // 设置一下父节点名称，后面编辑时需要用
              return true;
            }
          });
          item.children = rootNodes;
          rootNodes.forEach(rootNode => {
            const children = this.listToTree(list, rootNode.id, rootNode.menuName);
            if (children) {
              rootNode.children = children;
            }
            tree.push(rootNode);
          });
        })
        return FirstNodes;
      }
    },
    /**
     * 设置状态
     */
    clickHandlerChangeStatus(status: any) {
      this.changeStatusIds(this.selectedRowKeys, status.value);
      this.selectedRowKeys = [];
    },

    rehandleSelectChange(value: any) {
      this.selectedRowKeys = value;
      // console.log(value, selectedRowData);
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.left-operation-container {
  padding: 0 0 6px 0;
  margin-bottom: 16px;

  .selected-count {
    display: inline-block;
    margin-left: 8px;
    color: @text-color-secondary;
  }
}

.tdesign-table-demo__table-operations{
  padding: 0 8px;
}
</style>
