<template>
  <div class='product-model-edit'>
    <div style="margin: 20px;">
      <t-steps class='step' v-model="current" :options="steps" readonly></t-steps>
    </div>

    <step-one v-if='current === 1' :form='form' :mode="campType" @success='handleStepOne' :companyId='companyId'></step-one>

    <step-two v-if='current === 2' :mode="campType" :form='form' @change='handleStepTwo' @prev='current--' @save='save'></step-two>

  </div>
</template>

<script>
import StepOne from './components/step-one.vue';
import StepTwo from './components/step-two.vue';
import BeanUtilsService from "@/service/bean-utils.service";

export default {
  name: 'product-model-edit',
  components: { StepOne, StepTwo },
  data() {
    return {
      current: 1,
      steps: [
        { title: '营期信息', value: 1 },
        { title: '关联课程', value: 2 }
      ],
      appList: [],
      campType: '',
      companyId: '',
      form: {
        campperiodName: '',
        campperiodIntroduction: '',
        campperiodCoverpath: '',
        campperiodContent: '',
        campperiodTag: [],
        startingFlag: '1',
        campperiodSalesmethod: '1',
        startingTime: [],
        campperiodStatus: '1',
        salesGroupId: [],
        visualFlag: '',
      },
    };
  },
  created() {
    if (this.$route.params.id) {
      this.companyId = this.$route.params.id;
    }
    this.checkCampType();
  },
  methods: {
    checkCampType() {
      const data = this.$route.params;
      if (data && Object.keys(data).length > 0) {
        switch (data.type) {
        case 'edit':
          this.form = BeanUtilsService.copy(data.data);
          this.campType = data.type;
          this.companyId = data.data.companyId;
          break;
        case 'copy':
          this.form = BeanUtilsService.copy(data.data);
          this.campType = data.type;
          this.companyId = data.data.companyId;
          break;
        default:
          break;
        }
      }
    },

    async save(){
      this.$message.success({content: '操作成功'});
      this.$router.push({ name: 'training-camp-main', params: { type: 'refresh' } });
    },
    handleStepOne(data){
      this.form = data.data;
      if (!data.saveFlag) {
        this.current += 1;
      }
    },
    handleStepTwo(data){
      this.form = data;
    },
  },
};
</script>

<style lang="less">
.product-model-edit{
  position: relative;
  .next{
    margin-top: 20px;
  }
  .t-steps{
    width: 300px;
  }

}
</style>
