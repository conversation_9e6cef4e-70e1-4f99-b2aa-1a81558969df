<template>
  <div class="detail-data-page">
    <t-tabs :value="value1" @change="(newValue) => (value1 = newValue)" class="main-tabs">
      <t-tab-panel value="first" label="营期数据">
        <!-- 数据概览 - 横向紧凑布局 -->
        <div class="data-overview-compact">
          <div class="overview-header">
            <h3 class="overview-title">
              <t-icon name="chart-line" />
              数据概览
            </h3>
          </div>

          <!-- 横向统计卡片 -->
          <div class="stats-horizontal">
            <!-- 会员数据 -->
            <div class="stat-card member-stats">
              <div class="card-icon">
                <t-icon name="user" />
              </div>
              <div class="card-content">
                <div class="card-title">会员数据</div>
                <div class="stats-list">
                  <div class="stat-mini">
                    <span class="stat-label">总会员</span>
                    <span class="stat-value">{{ campPeriodData.campPeriodTotalMembers || 0 }}</span>
                  </div>
                  <div class="stat-mini">
                    <span class="stat-label">昨日会员</span>
                    <span class="stat-value">{{ campPeriodData.campPeriodLastDayMembers || 0 }}</span>
                  </div>
                  <div class="stat-mini">
                    <span class="stat-label">昨日新增</span>
                    <span class="stat-value highlight">{{ campPeriodData.campPeriodLastDayNewMembers || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 活跃数据 -->
            <div class="stat-card active-stats">
              <div class="card-icon">
                <t-icon name="chart-line" />
              </div>
              <div class="card-content">
                <div class="card-title">活跃数据</div>
                <div class="stats-list">
                  <div class="stat-mini">
                    <span class="stat-label">上线人数</span>
                    <span class="stat-value">{{ campPeriodData.campPeriodLastDayLoginMembers || 0 }}</span>
                  </div>
                  <div class="stat-mini">
                    <span class="stat-label">上线率</span>
                    <span class="stat-value highlight">{{ campPeriodData.campPeriodLastDayLoginPercentMembers || 0 }}%</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 红包数据 -->
            <div class="stat-card redpack-stats">
              <div class="card-icon">
                <t-icon name="wallet" />
              </div>
              <div class="card-content">
                <div class="card-title">红包数据</div>
                <div class="stats-list">
                  <div class="stat-mini">
                    <span class="stat-label">发放数量</span>
                    <span class="stat-value">{{ campPeriodData.campPeriodTotalRedPacket || 0 }}</span>
                  </div>
                  <div class="stat-mini">
                    <span class="stat-label">发放金额</span>
                    <span class="stat-value highlight">¥{{ campPeriodData.campPeriodAmountRedPacket || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 课节排行卡片 -->
        <div class="table-card">
          <div class="card-header">
            <h3 class="card-title">
              <t-icon name="chart-bar" />
              课节排行
            </h3>
          </div>
          <div class="card-content">
            <div class="search-section">
              <div class="search-container">
                <t-form :data="formData" layout="inline" class="search-form">
                  <t-form-item label="课程名称" name="courseName">
                    <t-select
                      filterable
                      placeholder="请选择课程名称"
                      v-model="value2"
                      @change="handleChange"
                      class="form-select"
                    >
                      <t-option
                        v-for="item in options"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      />
                    </t-select>
                  </t-form-item>
                </t-form>
                <div class="search-actions">
                  <t-button theme="primary" @click="onSubmit">查询</t-button>
                  <t-button theme="primary" @click="onReset">重置</t-button>
                </div>
              </div>
            </div>
            <div class="table-container">
              <t-table
                :columns="columns"
                :data="data"
                :rowKey="rowKey"
                :pagination="pagination"
                :selected-row-keys="selectedRowKeys"
                :loading="dataLoading"
                @change="rehandleChange"
                @select-change="rehandleSelectChange"
                class="data-table"
              >
              </t-table>
            </div>
          </div>
        </div>
      </t-tab-panel>
      <t-tab-panel value="second" label="营期会员数据">
        <!-- 会员数据概览 - 横向紧凑布局 -->
        <div class="data-overview-compact">
          <div class="overview-header">
            <h3 class="overview-title">
              <t-icon name="user" />
              会员数据概览
            </h3>
          </div>

          <!-- 横向统计卡片 -->
          <div class="stats-horizontal">
            <!-- 会员基础数据 -->
            <div class="stat-card member-stats">
              <div class="card-icon">
                <t-icon name="user-add" />
              </div>
              <div class="card-content">
                <div class="card-title">会员基础</div>
                <div class="stats-list">
                  <div class="stat-mini">
                    <span class="stat-label">总会员</span>
                    <span class="stat-value">{{ campPeriodData.campPeriodTotalMembers || 0 }}</span>
                  </div>
                  <div class="stat-mini">
                    <span class="stat-label">今日新增</span>
                    <span class="stat-value highlight">{{ campPeriodData.campPeriodToDayNewMembers || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 观看数据 -->
            <div class="stat-card watch-stats">
              <div class="card-icon">
                <t-icon name="play-circle" />
              </div>
              <div class="card-content">
                <div class="card-title">观看数据</div>
                <div class="stats-list">
                  <div class="stat-mini">
                    <span class="stat-label">观看次数</span>
                    <span class="stat-value">{{ campCourseData.countCustomerCourse || 0 }}</span>
                  </div>
                  <div class="stat-mini">
                    <span class="stat-label">完播次数</span>
                    <span class="stat-value highlight">{{ campCourseData.countCustomerComplete || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 红包数据 -->
            <div class="stat-card redpack-stats">
              <div class="card-icon">
                <t-icon name="wallet" />
              </div>
              <div class="card-content">
                <div class="card-title">红包数据</div>
                <div class="stats-list">
                  <div class="stat-mini">
                    <span class="stat-label">发放数量</span>
                    <span class="stat-value">{{ campCourseData.countCustomerRedPacket || 0 }}</span>
                  </div>
                  <div class="stat-mini">
                    <span class="stat-label">发放金额</span>
                    <span class="stat-value highlight">¥{{ campCourseData.amountCustomerRedPacket || 0 }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 会员详情表格卡片 -->
        <div class="table-card">
          <div class="card-header">
            <h3 class="card-title">
              <t-icon name="table" />
              会员详情数据
            </h3>
          </div>
          <div class="card-content">
            <div class="search-section">
              <div class="search-container">
                <t-form :data="formData" layout="inline" class="search-form">
                  <t-form-item label="课程名称" name="courseName">
                    <t-select
                      clearable
                      filterable
                      :disabled="isTimeRangeSelected"
                      v-model="formData.courseId"
                      placeholder="请选择课程名称"
                      @change="handleChange1"
                      class="form-select"
                    >
                      <t-option
                        v-for="item in options"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      />
                    </t-select>
                  </t-form-item>
                  <t-form-item label="销售组" name="salesGroupId">
                    <t-select
                      :disabled="isOther"
                      v-model="formData.salesGroupId"
                      filterable
                      clearable
                      placeholder="请选择销售组"
                      :options="salesGroupOptions"
                      @change="getSalesList(formData.salesGroupId)"
                      class="form-select"
                    />
                  </t-form-item>
                  <t-form-item label="销售" name="salesId">
                    <t-select
                      :disabled="isOther"
                      v-model="formData.salesId"
                      filterable
                      clearable
                      placeholder="请选择销售"
                      :options="salesOptions"
                      class="form-select"
                    />
                  </t-form-item>
                  <t-form-item label="活跃时间">
                    <t-date-range-picker
                      :disabled="isCourseSelected"
                      v-model="createTime"
                      :clearable="true"
                      enable-time-picker
                      allow-input
                      :presets="presets"
                      @change="handleDateRangeChange"
                      class="form-date-picker"
                    />
                  </t-form-item>
                  <t-form-item label="课程状态">
                    <t-select v-model="formData.isFlag" clearable class="form-select">
                      <t-option key="0" label="未到课" value="0" />
                      <t-option key="1" label="到课" value="1" />
                      <t-option key="2" label="完播" value="2" />
                      <t-option key="3" label="已到课未完播" value="3" />
                    </t-select>
                  </t-form-item>
                </t-form>
                <div class="search-actions">
                  <t-button theme="primary" @click="onSubmit1">查询</t-button>
                  <t-button theme="primary" @click="onReset1">重置</t-button>
                </div>
              </div>
            </div>
            <div class="table-container">
              <t-table
                :columns="columns1"
                :data="data1"
                :rowKey="rowKey1"
                :pagination="pagination1"
                :selected-row-keys="selectedRowKeys1"
                :loading="dataLoading1"
                @change="rehandleChange1"
                @select-change="rehandleSelectChange1"
                class="data-table"
              >
                <template #avatarUrl="{ row }">
                  <t-comment :avatar="row.avatarUrl">
                  </t-comment>
                </template>
                <template #statusDesc="{ row }">
                  <t-tag :theme="row.statusDesc === '1' ? 'success' : 'danger'">
                    {{ row.statusDesc === '1' ? '有效' : '无效' }}
                  </t-tag>
                </template>
                <template #viewCourseTime="{ row }">
                  {{ secondsToTime(row.viewCourseTime) }}
                </template>
              </t-table>
            </div>
          </div>
        </div>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<script>
import BeanUtilsService from "@/service/bean-utils.service";
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import sysCampPeriodDataApi from "@/constants/api/back/sys-camp-period-data.api";
import apiUser from "../../constants/api/back/system-user.api";
import dayjs from "dayjs";
import sysCampGroupApi from "../../constants/api/back/sys-camp-group.api";
import salesGroupApi from "../../constants/api/back/sales-group.api";
import {SYSTEM_ROLE_CONST} from "@/constants/enum/system/system-role.enum";

export default {
  name: 'DetailData',
  components: { },
  data() {
    return {
      isCourseSelected: false,
      isTimeRangeSelected: false,
      courseState:'',
      createTime: [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
      ],
      presets: {
        '最近7天': [
          dayjs().subtract(6, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '最近3天': [
          dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '今天': [
          dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ]
      },
      activeTime: [],
      salesGroupOptions: [], // 销售组
      salesOptions: [], // 销售
      options1:[],
      options:[],
      loadingSearch:false,
      rowKey:'',
      rowKey1:'',
      data:[],
      data1:[],
      url: sysCampPeriodDataApi.queryCampPeriodData.url,
      url1: sysCampPeriodDataApi.queryCampPeriodTableData.url,
      url2: sysCampPeriodDataApi.queryCustomerDataTableData.url,
      url3: sysCampPeriodDataApi.queryCampCourseListData.url,
      url4: sysCampPeriodDataApi.queryCustomerCourseListData.url,
      columns1: [
        {
          title: '头像',
          align: 'left',
          width: 140,
          colKey: 'avatarUrl',
        },
        {
          title: '会员ID',
          align: 'left',
          width: 140,
          colKey: 'memberId',
        },
        {
          title: '昵称',
          align: 'left',
          width: 120,
          colKey: 'nickname',
        },
        {
          title: '姓名',
          align: 'left',
          width: 140,
          colKey: 'realName',
        },
        {
          title: '状态',
          align: 'left',
          width: 140,
          colKey: 'statusDesc',
        },
        {
          title: '手机号',
          align: 'left',
          width: 140,
          colKey: 'mobile',
        },
        {
          title: '观看次数',
          align: 'left',
          width: 140,
          colKey: 'videoViewCount',
        },
        {
          title: '观看课节数',
          align: 'left',
          width: 140,
          colKey: 'viewCourseCount',
        },
        {
          title: '完播课节数',
          align: 'left',
          width: 140,
          colKey: 'completeCourseCount',
        },
        {
          title: '所属销售',
          align: 'left',
          width: 140,
          colKey: 'salesMan',
        },
        {
          title: '红包领取次数',
          align: 'left',
          width: 140,
          colKey: 'redPacketCount',
        },
        {
          title: '客户创建时间',
          align: 'left',
          width: 140,
          colKey: 'createdAt',
        },
        {
          title: '最后活跃时间',
          align: 'left',
          width: 140,
          colKey: 'lastActiveTime',
        },
        {
          title: '观看时长',
          align: 'left',
          width: 140,
          colKey: 'viewCourseTime',
        },
        {
          title: '完播时间',
          align: 'left',
          width: 140,
          colKey: 'completeCourseTime',
        },
      ],
      columns: [
        {
          title: '课程ID',
          colKey: 'courseId',
          width: 180
        },
        {
          title: '课程名称',
          colKey: 'courseName',
          ellipsis: true // 超出显示省略号
        },
        {
          title: '观看人数',
          colKey: 'viewCount',
          align: 'right'
        },
        {
          title: '完播人数',
          colKey: 'completeCount',
          align: 'right'
        },
        {
          title: '完播率',
          colKey: 'completionRate',
          align: 'right'
        },
        {
          title: '红包数量',
          colKey: 'redPacketCount',
          align: 'right'
        },
        {
          title: '红包金额(元)',
          colKey: 'redPacketAmount',
          align: 'right'
        }
      ],
      start: false,
      value: 56.32,
      value1: 'first',
      value2:'',
      tSelectSearch:'',
      columnOptions: [],
      companyData: [],
      campPeriodData:{
        campPeriodTotalMembers:0,
        campPeriodLastDayMembers: 0,
        campPeriodLastDayNewMembers: 0,
        campPeriodLastDayLoginMembers: 0,
        campPeriodLastDayLoginPercentMembers: 0,
        campPeriodToDayNewMembers: 0,
        campPeriodTotalRedPacket: 0,
        campPeriodAmountRedPacket: 0
      },
      campCourseData:{
        countCustomerCourse:0,
        countCustomerComplete: 0,
        countCustomerRedPacket: 0,
        amountCustomerRedPacket: 0
      },
      campPeriodOptions: [],
      selectedRowKeys: [],
      selectedRowKeys1: [],
      dataLoading: false,
      dataLoading1: false,
      campType: 'detailData',
      form: {
        companyId: '',
        campPeriodId: '', // 营期Id
      },
      rowData: {},
      columnId:'',
      formData: {
        courseId:'',
        courseName: '',
        columnId:  '',
        campPeriodId: '',
        companyId: '',
        startDate: "",
        endDate: "",
        salesGroupId:'',
        salesId:'',
        isFlag: '',
      },
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      pagination1: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      isOther: false,
      currentUserSalesGroupId: null,
      currentUserSalesGroupName: '',
      currentUserSalesId: null,
      currentUserSalesName: '',
    };
  },
  created() {
    this.checkCampType();
    this.remoteMethod();
    this.getCampPeriodData();
    this.getCampPeriodTableData();
    this.getCustomerTableData();
    this.getCustomerCourseData();
  },
  methods: {
    // 将秒转为时分秒
    secondsToTime(seconds) {
      if (!seconds) {
        return '';
      }
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      let result = '';
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分`;
      if (remainingSeconds > 0 || result === '') result += `${remainingSeconds}秒`;
      return result;
    },
    async getSalesGroupList(value) {
      const params = {
        companyId:value
      };
      const {
        code,
        data
      } = await this.$request.get(salesGroupApi.listByCompanyId.url, { params });
      if (code === 0 && data) {
        this.salesGroupOptions = data.map((item) => ({
          label: item.salesGroupName,
          value: item.id,
        }));
      }
    },
    async getSalesList(salesGroupId) {
      this.formData.salesId = '';
      this.salesOptions = [];
      const res = await this.$request.post(
        apiUser.queryUserByPage.url,
        {
          current: 1,
          size: 100,
          companyId: this.companyId || '',
          salesGroupId,
          columnId: this.columnId,
          status: 1, // 启用状态
          auditStatus: 2, // 审核通过状态
        },
      );
      const { code, data, msg } = res;
      if (code === 0 && data) {
        this.salesOptions = data.records.map((item) => ({
          label: item.username,
          value: item.id,
        }));
      } else {
        await this.$message.error(msg || '请求失败');
      }
    },
    handleChange(value){
      // console.log("value",value);
      this.value2 = value;
    },
    handleChange1(value){
      // console.log("value",value);
      this.value2 = value;
      if(value){
        this.createTime = [];
        this.isTimeRangeSelected = false;
        this.isCourseSelected = true;
      }
      else {
        this.createTime = [
          dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
        ];
        this.isCourseSelected = false;
      }
    },
    handleDateRangeChange(value){
      // console.log("value",value);
      if(value && value.length === 2){
        this.isTimeRangeSelected = true;
        this.isCourseSelected = false;
      }
      else {
        this.isTimeRangeSelected = false;
        this.formData.startDate = "";
        this.formData.endDate = "";
      }
    },
    async remoteMethod() {
      // console.log('search', search);
      if (!this.companyId) return;
      this.loadingSearch = true; // 开启加载状态
      try {
        const { code, data, msg } = await this.$request.post(this.url3, {
          companyId: this.companyId,
          campPeriodId: this.campPeriodId
        });

        if (code === 0) {
          this.options = data;
        } else {
          this.$message.error({ content: msg });
        }
      } catch (error) {
        this.$message.error("数据加载异常");
      } finally {
        this.loadingSearch = false; // 关闭加载状态
      }
    },
    async getCampPeriodData() {
      if (!this.companyId) return;
      const { code, data, msg } = await this.$request.post(this.url, {
        companyId: this.companyId,
        campPeriodId: this.campPeriodId,
      });
      if (code === 0) {
        // 正确获取数组中的第一个对象
        const result = data[0];

        // 转换数据类型并移除单位
        this.campPeriodData = {
          campPeriodTotalMembers: parseFloat(result.campPeriodTotalMembers),
          campPeriodLastDayMembers: parseFloat(result.campPeriodLastDayMembers),
          campPeriodLastDayNewMembers: parseFloat(result.campPeriodLastDayNewMembers),
          campPeriodLastDayLoginMembers: parseFloat(result.campPeriodLastDayLoginMembers),
          campPeriodLastDayLoginPercentMembers: parseFloat(result.campPeriodLastDayLoginPercentMembers),
          campPeriodTotalRedPacket: parseFloat(result.campPeriodTotalRedPacket),
          campPeriodAmountRedPacket: parseFloat(result.campPeriodAmountRedPacket)
        };

        // 触发动画重新播放
        this.start = false;
        this.$nextTick(() => {
          this.start = true;
        });
      } else {
        this.$message.error({ content: msg });
      }
    },
    async getCampPeriodTableData() {
      if (!this.companyId) return;

      this.dataLoading = true; // 开启加载状态
      try {
        const { code, data, msg } = await this.$request.post(this.url1, {
          companyId: this.companyId,
          campPeriodId: this.campPeriodId,
          courseId: this.value2,
          pageNum: this.pagination.current, // 传递当前页码
          pageSize: this.pagination.pageSize // 传递每页条数
        });

        if (code === 0) {
          this.data = data.records;

          this.pagination = {
            ...this.pagination,
            total: parseInt(data.total) // 确保转换为数字类型
          };
        } else {
          this.$message.error({ content: msg });
        }
      } catch (error) {
        // console.error("表格数据加载失败:", error);
        this.$message.error("数据加载异常");
      } finally {
        this.dataLoading = false; // 关闭加载状态
      }
    },
    async getCustomerCourseData() {
      if (!this.companyId) return;
      [this.formData.startDate, this.formData.endDate] = this.createTime;
      this.dataLoading = true; // 开启加载状态
      try {
        const { code, data, msg } = await this.$request.post(this.url4, {
          companyId: this.companyId,
          campPeriodId: this.campPeriodId,
          courseId: this.formData.courseId,
          salesGroupId: !this.isOther ? this.formData.salesGroupId : this.currentUserSalesGroupId,
          salesId: !this.isOther ? this.formData.salesId : this.currentUserSalesId,
          startDate: this.formData.startDate,
          isFlag: this.formData.isFlag,
          endDate: this.formData.endDate,
          columnId: this.columnId,
        });

        if (code === 0) {
          // console.log("data::::",data);
          // 正确获取数组中的第一个对象
          const result = data[0];

          // 转换数据类型并移除单位
          this.campCourseData = {
            countCustomerCourse: parseFloat(result.countCustomerCourse),
            countCustomerComplete: parseFloat(result.countCustomerComplete),
            countCustomerRedPacket: parseFloat(result.countCustomerRedPacket),
            amountCustomerRedPacket: parseFloat(result.amountCustomerRedPacket),
          };
        } else {
          this.$message.error({ content: msg });
        }
      } catch (error) {
        // console.error("数据加载失败:", error);
        this.$message.error("数据加载异常");
      } finally {
        this.dataLoading = false; // 关闭加载状态
      }
    },
    async getCustomerTableData() {
      if (!this.companyId) return;
      [this.formData.startDate, this.formData.endDate] = this.createTime;
      this.dataLoading = true; // 开启加载状态
      try {
        const { code, data, msg } = await this.$request.post(this.url2, {
          companyId: this.companyId,
          campPeriodId: this.campPeriodId,
          courseId: this.formData.courseId,
          // salesGroupId: this.isOther ? this.formData.salesGroupId : this.currentUserSalesGroupId,
          salesGroupId: "",
          salesId: !this.isOther ? this.formData.salesId : this.currentUserSalesId,
          startDate: this.formData.startDate,
          isFlag: this.formData.isFlag,
          endDate: this.formData.endDate,
          columnId: this.columnId,
          pageNum: this.pagination1.current, // 传递当前页码
          pageSize: this.pagination1.pageSize // 传递每页条数
        });

        if (code === 0) {
          this.data1 = data.records;
          this.pagination1 = {
            ...this.pagination1,
            total: parseInt(data.total) // 确保转换为数字类型
          };
        } else {
          this.$message.error({ content: msg });
        }
      } catch (error) {
        // console.error("表格数据加载失败:", error);
        this.$message.error("数据加载异常");
      } finally {
        this.dataLoading = false; // 关闭加载状态
      }
    },
    checkCampType() {

      // 从 LocalStorage 获取 JSON 字符串
      const userData = window.localStorage.getItem('core:user');

      if (userData) {
        try {
          const user = JSON.parse(userData);
          this.currentUserSalesId = user.id;
          this.currentUserSalesGroupId = user.salesGroupId;
          const roleType = user ? String(user.roleType) : '';
          console.log('roleType', roleType)
          // 否为普通销售
          this.isOther = roleType === SYSTEM_ROLE_CONST.OTHER.value;
        } catch (error) {
          console.error('JSON解析失败:', error);
        }
      } else {
        console.warn('core:user 不存在于 LocalStorage');
      }

      const data = this.$route.params;
      // console.log("data:::",data);
      if (data && Object.keys(data).length > 0) {
        switch (data.type) {
        case 'detailData':
          this.form = BeanUtilsService.copy(data.data);
          this.companyId = this.form.companyId;
          this.campPeriodId = this.form.id;
          this.rowData = { ...this.$route.params.rowData };
          this.columnId = this.rowData.columnId;
          this.campType = data.type;
          this.getSalesGroupList(this.companyId);
          break;
        default:
          break;
        }
      }
    },
    onSubmit() {
      // console.log("this.value2", this.value2);
      this.pagination.current = 1;
      this.getCampPeriodTableData();
    },
    onReset() {
      this.pagination.current = 1;
      this.value2 = '';
      this.getCampPeriodTableData();
    },
    onSubmit1() {
      // 1. 直接检查 createTime 是否为空
      // if (!this.createTime || this.createTime.length === 0) {
      //   this.$message.error('活跃时间为必选项');
      //   return;
      // }

      this.pagination1.current = 1;
      this.getCustomerTableData();
      this.getCustomerCourseData();
    },
    onReset1() {
      this.pagination1.current = 1;
      this.formData = {
        courseName: '',
        startDate: "",
        endDate: "",
        salesGroupId:'',
        salesId:'',
        isFlag: '',
      }
      this.createTime = [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
      ]
      this.getCustomerTableData();
      this.getCustomerCourseData();
      this.isTimeRangeSelected = false;
      this.isCourseSelected = false;
    },
    async onColumnChange(value, context) {
      // console.log('onColumnChange:', value, context);
    },
    async onCompanyChange(value, context) {
      // console.log('onCompanyChange:', value, context, context.node?.getPath());
      this.formData.campPeriodId = '';
      this.formData.salesGroupId = '';
      this.formData.salesId = '';
      this.salesGroupOptions = [];
      this.salesOptions = [];
      const params = {
        companyId: value,
      }
      const {code, data} = await this.$request.get(sysCampGroupApi.getCampPeriodsByCompanyId.url, {params});
      // console.log("code", code, "data", data);
      if (code === 0 && data) {
        this.campPeriodOptions = data.map((item) => ({label: item.campperiodName, value: item.id}));
        // console.log("campPeriodOptions", this.campPeriodOptions);
      }
      await this.getSalesGroupList(value);
    },
    rehandleChange(changeParams) {
      const { pagination } = changeParams;
      this.pagination = {
        ...this.pagination,
        current: pagination.current,
        pageSize: pagination.pageSize
      };
      this.getCampPeriodTableData(); // 重新加载数据
    },
    rehandleSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    rehandleChange1(changeParams) {
      const { pagination } = changeParams;
      this.pagination1 = {
        ...this.pagination1,
        current: pagination.current,
        pageSize: pagination.pageSize
      };
      this.getCustomerTableData(); // 重新加载数据
    },
    rehandleSelectChange1(selectedRowKeys) {
      this.selectedRowKeys1 = selectedRowKeys;
    },
  },
};
</script>

<style lang="less" scoped>
.detail-data-page {
  background: #f8f9fa;
  min-height: 100vh;

  .main-tabs {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;

    /deep/ .t-tabs__nav {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 0 24px;

      .t-tabs__nav-item {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        border: none;

        &.t-is-active {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 8px 8px 0 0;
        }

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.05);
        }
      }
    }

    /deep/ .t-tabs__content {
      padding: 24px;
    }
  }

  // 紧凑型数据概览
  .data-overview-compact {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;

    .overview-header {
      padding: 12px 16px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      color: #fff;

      .overview-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 6px;

        .t-icon {
          font-size: 16px;
        }
      }
    }

    .stats-horizontal {
      display: flex;
      padding: 0;
      gap: 0;

      .stat-card {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 16px;
        border-right: 1px solid #f0f0f0;
        transition: all 0.2s ease;
        position: relative;

        &:last-child {
          border-right: none;
        }

        &:hover {
          background: #f8f9fa;
        }

        .card-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;

          .t-icon {
            font-size: 20px;
            color: #fff;
          }
        }

        .card-content {
          flex: 1;
          min-width: 0;

          .card-title {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 8px;
          }

          .stats-list {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .stat-mini {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .stat-label {
                font-size: 11px;
                color: #9ca3af;
                white-space: nowrap;
              }

              .stat-value {
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;

                &.highlight {
                  color: #dc2626;
                  font-weight: 700;
                }
              }
            }
          }
        }

        // 不同类型的卡片图标背景色
        &.member-stats .card-icon {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        &.active-stats .card-icon {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        &.watch-stats .card-icon {
          background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        &.redpack-stats .card-icon {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
      }
    }
  }

  // 表格卡片
  .table-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }

    .card-header {
      padding: 14px 20px;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid #e5e7eb;

      .card-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 6px;

        .t-icon {
          color: #3b82f6;
          font-size: 18px;
        }
      }
    }

    .card-content {
      padding: 20px;

      .search-section {
        margin-bottom: 20px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e5e7eb;

        .search-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 20px;

          .search-form {
            flex: 1;

            .t-form-item {
              margin-bottom: 12px;

              .t-form__label {
                font-weight: 500;
                color: #374151;
                font-size: 13px;
              }

              .form-select,
              .form-date-picker {
                min-width: 160px;
                border-radius: 6px;

                transition: all 0.2s ease;

                &:hover {
                  border-color: #9ca3af;
                }

                &:focus,
                &.t-is-focused {
                  border-color: #3b82f6;
                  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
              }
            }
          }

          .search-actions {
            display: flex;
            gap: 10px;
            flex-shrink: 0;
            align-self: flex-start;
            margin-top: 28px; // 对齐表单项的高度

            .t-button {
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }
          }
        }
      }

      .table-container {
        .data-table {
          border-radius: 8px;
          overflow: hidden;
          border: 1px solid #e5e7eb;

          /deep/ .t-table__header {
            background: #f8f9fa;

            th {
              font-weight: 600;
              color: #374151;
              border-bottom: 1px solid #e5e7eb;
            }
          }

          /deep/ .t-table__body {
            tr {
              transition: background-color 0.2s ease;

              &:hover {
                background: #f8f9fa;
              }

              td {
                border-bottom: 1px solid #f0f0f0;
              }
            }
          }
        }
      }
    }
  }
}

// 全局组件优化
/deep/ .t-button {
  &.t-button--theme-primary {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }
}

/deep/ .t-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 12px;
  font-size: 13px;

  &.t-tag--theme-success {
    background: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;
  }

  &.t-tag--theme-danger {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .detail-data-page {
    padding: 16px;

    .main-tabs {
      /deep/ .t-tabs__content {
        padding: 16px;
      }
    }

    .data-overview-compact {
      .overview-header {
        padding: 10px 12px;

        .overview-title {
          font-size: 13px;
        }
      }

      .stats-horizontal {
        flex-direction: column;

        .stat-card {
          border-right: none;
          border-bottom: 1px solid #f0f0f0;
          padding: 12px;

          &:last-child {
            border-bottom: none;
          }

          .card-icon {
            width: 36px;
            height: 36px;
            margin-right: 10px;

            .t-icon {
              font-size: 18px;
            }
          }

          .card-content {
            .card-title {
              font-size: 11px;
            }

            .stats-list {
              .stat-mini {
                .stat-label {
                  font-size: 10px;
                }

                .stat-value {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }

    .table-card {
      .card-header {
        padding: 12px 16px;

        .card-title {
          font-size: 14px;
        }
      }

      .card-content {
        padding: 12px 16px;
      }
    }

    .search-section {
      .search-container {
        flex-direction: column;
        gap: 16px;

        .search-form {
          .t-form-item {
            .form-select,
            .form-date-picker {
              min-width: 100%;
            }
          }
        }

        .search-actions {
          margin-top: 0;
          justify-content: flex-end;
          gap: 8px;

          .t-button {
            min-width: 80px;
          }
        }
      }
    }
  }
}
</style>
