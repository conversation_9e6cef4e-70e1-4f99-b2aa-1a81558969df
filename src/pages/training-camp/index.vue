<template>
  <div class="training-camp-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="play-demo" />
          训练营管理
        </div>
        <div class="page-desc">创建训练营，关联营期便于分享训练链接</div>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="addModel" v-if="userOperation.hasAdd" size="large">
          <t-icon name="add" />
          新建营期
        </t-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧分组列表 -->
      <div class="sidebar">
        <action-list @select="onSelected" ref="actionList"></action-list>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索筛选区域 -->
        <div class="filter-section">
          <div class="filter-content">
            <t-form :data="form" ref="formSearch" layout="inline" class="filter-form">
              <div class="filter-row">
                <t-form-item label="营期名称" name="name" class="filter-item">
                  <t-input
                    v-model="form.name"
                    :clearable="true"
                    placeholder="请输入营期名称"
                    class="filter-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="search" />
                    </template>
                  </t-input>
                </t-form-item>

                <!-- 操作按钮 -->
                <div class="filter-buttons">
                  <t-button theme="primary" @click="onSubmit" class="search-btn">
                    <t-icon name="search" />
                    查询
                  </t-button>
                  <t-button variant="outline" @click="onReset" class="reset-btn">
                    <t-icon name="refresh" />
                    重置
                  </t-button>
                </div>
              </div>
            </t-form>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-section">
          <common-table
            ref="commonTable"
            :columns="columns"
            :url="url"
            :isRequest="!!activeId"
            :params="tableParams"
            :operations="operations"
            class="camp-table"
          >
          </common-table>
        </div>
      </div>
    </div>

    <course-sharing :visible="shareVisible" :shareData="rowData" @close="shareVisible = false"></course-sharing>
    <copy-camp :visible="copyVisible" :allSysCompanyInfos="companyTreeData" :companyId="companyId" @close="copyVisible = false"></copy-camp>
  </div>
</template>

<script>
import ActionList from './components/action-list.vue';
import CommonTable from '@/components/common-table/index.vue';
import sysCampGroupApi from "@/constants/api/back/sys-camp-group.api";
import {COURSE_STATUS_ARRAY} from "@/constants/enum/business/course-status-array.enum";
import courseSharing from "@/components/course-sharing/index.vue";
import search from "tdesign-icons-vue/lib/components/search";
import copyCamp from "./components/copy-camp.vue";
import {getMenuChildrenList, getOperationTypeList} from "@/utils/utils";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";

export default {
  name: 'task-library',
  components: { ActionList, courseSharing, CommonTable, copyCamp },
  data() {
    return {
      url: sysCampGroupApi.queryCampGroupListByPage.url,
      shareVisible: false,
      copyVisible: false,
      companyId: '',
      companyTreeData: [],
      rowData: {},
      params: {
        name: '',
      },
      form: {
        name: '',
        activeId: '',
        createDate: [],
        questionType: '',
      },
      activeOptions: [],
      columns: [
        {
          colKey: 'campperiodName',
          title: '营期名称',
          width: 150,
        },
        {
          colKey: 'field1',
          title: '营期外显名称',
          width: 150,
        },
        {
          colKey: 'startingTime',
          title: '开营时间',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val ? val.join(' - ') : '';
          }
        },
        {
          colKey: 'campperiodStatus',
          title: '上架状态',
          width: 80,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getCourseStatus(val);
          },
        },
        {
          colKey: 'campperiodRedPack',
          title: '自定义红包',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val ? '已开启' : '未开启';
          }
        },
        {
          colKey: 'campperiodRedPackAmount',
          title: '红包金额（元）',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val && row.campperiodRedPack ? val : '未设置';
          }
        },
        {
          colKey: 'op',
          title: '操作',
          width: 200,
          cell: 'op',
        },
      ],
      activeId: '',
      parentId: '',
      childrenButton: [], // 子节点按钮
      userOperationTab: [], // 用户可操作按钮
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      },
    };
  },
  computed: {
    search() {
      return search
    },
    operations(){
      return [
        {
          type: 'share',
          onClick: this.onShare,
        },
        {
          type: 'edit',
          onClick: this.onEdit,
          visible: this.userOperation.hasEdit,
        },
        {
          type: 'detail',
          onClick: this.onDetail,
        },
        {
          type: 'viewdata',
          onClick: this.onViewData,
        },
        {
          type: 'delete',
          onClick: this.onDel,
          visible: this.userOperation.hasDelete,
        },
      ]
    },
    tableParams() {
      return {
        id: this.activeId === 'ALL' ? '' : this.activeId,
        name: this.params.name,
      };
    },
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    }
  },
  mounted() {
    // 登录后用户具有的菜单已经存入了storage中，从中获取
    this.childrenButton = getMenuChildrenList();
    // console.log("获取到具有按钮菜单：", this.childrenButton);
    if(this.childrenButton && this.childrenButton.length > 0) {
      this.childrenButton.map((item) => {
        if (item.menuType && item.menuType == SYSTEM_MENU_CONST.O.value && item.menuUrl) {
          // 截取操作按钮，区分类型
          const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
          this.userOperationTab.push(operationType);
        }
      })
    }
    // console.log("获取到可操作按钮：", this.userOperationTab);
    this.userOperation = getOperationTypeList(this.userOperationTab)
    // console.log("获取到可操作按钮类型：", this.userOperation);
    // console.log("操作栏：", this.operations);
  },
  methods: {
    addModel() {
      if (!this.activeId || this.activeId === 'ALL') {
        this.$message.error({ content: '请选择公司' });
        return;
      }
      const id = this.activeId;
      this.companyTreeData = this.$refs.actionList.getCompanyTreeData();
      this.companyId = id;
      this.copyVisible = true;
    },
    getCourseStatus(val) {
      const data = COURSE_STATUS_ARRAY;
      return data.find((item) => item.value === val)?.label || '';
    },
    onSelected(data) {
      this.activeId = data.companyId;
      this.parentId = data.parentId;
    },
    async onDel(row) {
      const { code } = await this.$request.post(sysCampGroupApi.deleteCampGroup.url, { ...row });
      if (code === 0) {
        this.$message.success({ content: '操作成功' });
        this.$refs.commonTable.getList();
      } else {
        this.$message.error({ content: '删除失败' });
      }

    },
    onDetail(row) {
      const rowData = {
        salesId: this.getUser.id,
        campPeriodId: row.id,
        columnId: this.parentId,
        companyId: row.companyId,
        salesGroupId: String(this.getUser.salesGroupId),
      };
      this.$router.push({ name: 'training-camp-detail', params: { data: row, type: 'detail', rowData  } });
    },
    onViewData(row) {
      const rowData = {
        salesId: this.getUser.id,
        campPeriodId: row.id,
        columnId: this.parentId,
        companyId: row.companyId,
        salesGroupId: String(this.getUser.salesGroupId),
      };
      this.$router.push({ name: 'training-camp-data', params: { data: row, type: 'detailData', rowData  } });
    },
    onShare(row) {
      if (this.getUser.roleType === 1) {
        this.$message.warning({content: "超管不可以进行分享，请更换账号后重试！"});
        return;
      }
      this.shareVisible = true;
      console.error(row);
      this.rowData = {
        salesId: this.getUser.id,
        // courseId: row.courseId,
        campPeriodId: row.id,
        columnId: this.parentId,
        companyId: row.companyId,
        salesGroupId: String(this.getUser.salesGroupId),
      };
    },
    onEdit(row) {
      this.$router.push({ name: 'new-training-camp', params: { data: row, type: 'edit' } });
    },
    onSubmit() {
      this.params.name = this.form.name;
    },
    onReset() {
      this.form.name = "";
      this.params.name = this.form.name;
    },

    // 刷新数据
    refreshData() {
      this.$refs.commonTable.getList();
    },
  },
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面主体样式 ====================
.training-camp-page {
  padding: 12px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      .t-button {
        height: 44px;
        padding: 0 24px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 20px;
    min-height: calc(100vh - 300px);
  }

  // 左侧边栏
  .sidebar {
    width: 250px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
  }

  // 右侧内容区域
  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 0;
  }

  // 筛选区域
  .filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .filter-content {
      padding: 20px 24px;

      .filter-form {
        .filter-row {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          align-items: flex-end;

          .filter-item {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            margin-bottom: 16px;

            .filter-input {
              width: 100%;
              border-radius: 8px;

              transition: all 0.2s ease;

              &:hover {
                border-color: #9ca3af;
              }

              &:focus,
              &.t-is-focused {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-left: auto;
            flex-shrink: 0;

            .t-button {
              padding: 0 16px;
              border-radius: 8px;
              font-weight: 500;
              transition: all 0.3s ease;

              .t-icon {
                margin-right: 4px;
              }

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }

            .search-btn {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              border: none;
              color: #fff;

              &:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
              }
            }

            .reset-btn {

              background: #fff;
              color: #374151;

              &:hover {
                background: #f9fafb;
                border-color: #9ca3af;
              }
            }
          }
        }
      }
    }
  }

  // 表格区域
  .table-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    flex: 1;
    min-height: 0;
    overflow: hidden;

    .camp-table {
      height: 100%;

      /deep/ .t-table {
        height: 100%;

        .t-table__header {
          th {
            background: #f8f9fa;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
          }
        }

        .t-table__body {
          tr {
            transition: all 0.2s ease;

            &:hover {
              background: #f8f9fa;
            }

            td {
              border-bottom: 1px solid #f3f4f6;
              color: #374151;
            }
          }
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .training-camp-page {
    .main-content {
      flex-direction: column;

      .sidebar {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .training-camp-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            flex-direction: column;

            .filter-item {
              min-width: 100%;
              max-width: 100%;
            }

            .filter-buttons {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 12px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
