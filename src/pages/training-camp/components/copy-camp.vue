<template>
  <t-dialog
    width="35%"
    header="新建营期"
    :closeOnOverlayClick="false"
    :visible.sync="modalVisible"
    @close="closeModal"
    class="edit-task-dialog"
    @confirm="confirmCampData"
    :cancelBtn="null"
  >
    <div class="copy-camp-content">
      <t-form>
        <t-form-item label="复制营期方式" name="copyType">
          <t-radio-group v-model="copyType" @change="handleCopyType">
            <t-radio value="1">从当前营期复制</t-radio>
            <t-radio value="2">从其他营期复制</t-radio>
            <t-radio value="3">新建一个营期</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="选择营期" name="selectedCamp" v-if="copyType !== '3'">
          <div v-if="copyType === '1'">
            <t-select v-if="copyType === '1'" v-model="selectedCurrentCamp" placeholder="请选择营期" :options="campList"></t-select>
          </div>
          <div v-else>
            <t-tree-select
              :data="sysCompanyInfos"
              v-model="form.companyId"
              filterable
              :clearable="true"
              placeholder="请选择公司"
              :treeProps="{
              checkStrictly: true,
            }"
              @change="handleSelectCompany"
            />
            <t-select class="mt-10" :loading="selectLoading" v-model="selectedCamp" :options="otherCampList" placeholder="请选择营期"></t-select>
          </div>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>
<script>
import sysCampGroupApi from "@/constants/api/back/sys-camp-group.api";
import BeanUtilsService from "@/service/bean-utils.service";

export default {
  name: 'CopyCamp',
  props: {
    companyId: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: false
    },
    allSysCompanyInfos: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      modalVisible: false,
      selectedCurrentCamp: null,
      selectedCamp: null,
      campList: [],
      otherCampList: [],
      selectedTask: null,
      copyType: "1",
      selectLoading: false,
      form: {
        companyId: "",
      }
    }
  },
  computed: {
    sysCompanyInfos: {
      get() {
        if (this.allSysCompanyInfos.length) {
          const companyData = BeanUtilsService.copy(this.allSysCompanyInfos);
          const sysCompanyInfos = companyData.map((item) => ({
            label: item.label,
            value: item.value,
            disabled: true,
            children: item.children.map((item) => ({
              label: item.label,
              value: item.value,
            })),
          }));
          return sysCompanyInfos;
        }
        return [];
      },
    }
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    },
    'companyId': {
      async handler(val) {
        // 初始化当前营期
        if (val) {
          const params = {
            companyId: val,
          }
          const { code, data } = await this.$request.get(sysCampGroupApi.getCampPeriodsByCompanyId.url, { params }).catch((err) => err);
          if (code === 0) {
            this.campList = data.map(item => ({
              value: item,
              label: item.campperiodName,
            }))
          }
        }
      },
      deep: true,
    }
  },
  methods: {
    handleSelectCompany(val) {
      this.selectedCamp = null;
      this.otherCampList = [];
      if (val) {
        const params = {
          companyId: val,
        }
        this.selectLoading = true;
        this.$request.get(sysCampGroupApi.getCampPeriodsByCompanyId.url, { params }).then((res) => {
          this.selectLoading = false;
          console.error(res.data)
          if (res.code === 0) {
            const otherCampList = res.data.map(item => ({
              value: item,
              label: item.campperiodName,
            }));
            this.$set(this, 'otherCampList', otherCampList);
          }
        });
      }
    },
    async handleCopyType(val) {
      if (val === "1") {
        if (this.campList.length === 0) {
          console.error(this.companyId, this.copyType)
          const params = {
            companyId: this.companyId,
          }
          const { code, data } = await this.$request.get(sysCampGroupApi.getCampPeriodsByCompanyId.url, { params }).catch((err) => err);
          if (code === 0) {
            this.campList = data.map(item => ({
              value: item,
              label: item.campperiodName,
            }))
          }
        }
      }
    },
    closeModal() {
      this.$emit('close');
    },
    confirmCampData() {
      // 根据类型判断跳转
      if (this.copyType === "3") {
        this.$router.push({ name: 'new-training-camp', params: { id: this.companyId, type: 'new' } });
        this.closeModal();
      } else if (this.copyType === "1") {
        if (!this.selectedCurrentCamp) return this.$message.error("请选择营期");
        this.$router.push({ name: 'new-training-camp', params: { data: this.selectedCurrentCamp, type: 'copy' } });
        this.closeModal();
      } else if (this.copyType === "2") {
        if (!this.selectedCamp) return this.$message.error("请选择营期");
        this.selectedCamp.companyId = this.companyId;
        this.$router.push({ name: 'new-training-camp', params: { data: this.selectedCamp, type: 'copy' } });
        this.closeModal();
      }
    },
  }
}
</script>
<style scoped lang="less">
.copy-camp-content {
  position: relative;
  padding: 20px;
  .copy-camp-head {
    display: flex;
    justify-content: center;
    align-items: center;
  }
 .copy-camp-body {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
 }
  .mt-10 {
    margin-top: 10px;
  }
}
</style>
