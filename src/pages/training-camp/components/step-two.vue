<template>

  <div class='step-two'>
    <div class='step-two-container'>
      <t-card class='right'>
        <p class='title-step-two'>{{ form.campperiodName ? `${form.campperiodName}-` : '' }}关联课程</p>
        <div class='right-container'>
          <div class='right-left'>
            <div class="header-right-left">
              <!-- 批量操作栏 -->
              <div class="batch-operation" v-if="taskList.length && mode !== 'detail'">
                <t-checkbox :checked="isAllSelected" :disabled="availableTasks.length === 0" @change="handleSelectAll">
                  全选
                  <span class="count-tips">
                  <span class="available">可选 {{ availableTasks.length }} 节</span>
                  <span class="divider">|</span>
                  <span class="selected">已选 {{ selectedTasks.length }} 节</span>
                </span>
                </t-checkbox>
                <t-popconfirm content="确认删除选中课程吗" @confirm="deleteTasks(selectedTasks)">
                  <t-button variant="outline" theme="primary" :disabled="!selectedTasks.length">批量删除</t-button>
                </t-popconfirm>
              </div>
              <div class='right-left-title' v-if='taskList.length'>已关联{{ taskList.length }}节课程</div>
            </div>
            <!-- 课程列表容器 -->
            <div class="course-list-container">
              <template v-for='(item, index) in taskList'>
                <div class='scene-item' :key='item.id'>
                  <p class='item-header'>
                    <span><t-checkbox v-if="mode !== 'detail'"
                                      :disabled="mode === 'edit' && isDateExpired(item.startTime)"
                                      :checked="selectedTasks.includes(item.id)" @change="handleSelectItem(item.id)"
                    />{{ item.courseName }}<span class="item-header-preview"
                                                 @click="handlePreview(item)">预览</span></span>
                    <span class='item-operation'>
                      <t-popconfirm @confirm="handleSortConfirm(item)" placement="top-left">
                        <t-input-adornment prepend="点击排序">
                          <t-input v-model="item.orderNumber" disabled label="当前序号：" type="number" min="1"
                                   :max="taskList.length" @change="handleSort($event, item)"/>
                        </t-input-adornment>
                        <template #icon>
                          <span></span>
                        </template>
                        <template #content>
                          <t-input
                            v-model="tempOrderNumber"
                            type="number"
                            :min="1"
                            :max="taskList.length"
                            placeholder="请输入排序序号"
                            style="margin: 8px 0"
                            @change="handleTempInput"
                          />
                        </template>
                      </t-popconfirm>
                      <!--                    <t-popconfirm class="operation-item" :key="index" content="确认删除该课程吗" @confirm="deleteTask(item)">-->
                      <!--                    <delete1-icon size="22px" style="color: #f31414" v-if="mode !== 'detail' && !(mode === 'edit' && isDateExpired(item.startTime))" />-->
                      <!--                  </t-popconfirm>-->

                  </span>
                  </p>
                  <div>
                    <t-date-picker v-if='isShowTimeSelect'
                                   :disabled="mode === 'detail' || (mode === 'edit' && isDateExpired(item.startTime))"
                                   @change="(value) => handleStartTime(value, index)"
                                   v-model='item.startTime'></t-date-picker>
                  </div>
                </div>
              </template>

              <p class='task-tip' v-if='taskList.length === 0'>请选择课程</p>
            </div>

          </div>
          <div class='right-right'>
            <p class='right-right-title'>选择课程</p>
            <t-input clearable class='search-input' v-model='searchValue' :disabled="mode === 'detail'"
                     placeholder='输入课程名称搜索'>
              <template #suffix-icon>
                <search-icon size='20px'/>
              </template>
            </t-input>
            <t-checkbox-group class='scene-list' v-model='sceneValue' :disabled="mode === 'detail'">
              <div class="scene-list-content">
                <div class="scene-list-item" v-for='item in sceneList.filter(v => v.groupName.includes(searchValue))'
                     :key='item.id'>
                  <t-checkbox :value='item.id' :disabled="mode === 'detail'">
                    <span>{{ item.groupName }}</span>
                  </t-checkbox>
                </div>
              </div>
            </t-checkbox-group>
          </div>
        </div>

      </t-card>
    </div>


    <div style="text-align: right" v-if="mode !== 'detail'">
      <t-button class='next' theme='default' variant="outline" @click='$emit("prev")'>上一步</t-button>
      <t-button class='next' @click='onSave'>保存</t-button>
    </div>
    <preview-resource :visible="previewVisible" :src="previewResource"
                      @close="previewVisible = false"></preview-resource>
  </div>
</template>

<script>
import {SearchIcon, Delete1Icon} from 'tdesign-icons-vue';
import sysCourseGroupApi from "@/constants/api/back/sys-course-group.api";
import sysCampCourseUploadApi from "@/constants/api/back/sys-camp-course.api";
import dayjs from "dayjs";
import PreviewResource from "@/components/preview-resource/index.vue";

export default {
  name: 'step-two',
  components: {PreviewResource, SearchIcon, Delete1Icon},
  props: ['form', 'mode'],
  data() {
    return {
      activeId: '',
      searchValue: '',
      hasAction: false,
      sceneList: [],
      actionList: [],
      sceneValue: [],
      sceneDetailList: [],
      taskList: [],
      choseCourseId: [],
      tempOrderNumber: null,
      previewResource: '',
      previewVisible: false,
      selectedTasks: [],
    };
  },
  computed: {
    isShowTimeSelect() {
      return this.form.startingFlag === '1';
    },
    isAllSelected() {
      return this.selectedTasks.length === this.availableTasks.length && this.availableTasks.length > 0;
    },
    availableTasks() {
      return this.taskList.filter(item =>
        !(this.mode === 'edit' && this.isDateExpired(item.startTime))
      );
    }
  },
  watch: {
    sceneValue: {
      async handler(val, oldVal) {
        // 在编辑模式下检查被移除的过期课程
        if (this.mode === 'edit') {
          // 获取被取消选择的课程ID
          const removedIds = oldVal.filter(id => !val.includes(id))
          // 检查被移除的是否包含过期课程
          const hasExpired = this.taskList.some(
            item => {
              const today = new Date().setHours(0, 0, 0, 0)
              const courseDate = new Date(item.startTime).setHours(0, 0, 0, 0)
              return removedIds.includes(item.groupId) && courseDate < today
            }
          )

          if (hasExpired) {
            const waitConfirm = new Promise((resolve) => {
              const dialog = this.$dialog.confirm({
                theme: 'danger',
                header: '温馨提示',
                body: '取消的课程中包含过期课程，后续课程需重新编辑，确认继续？',
                onConfirm: () => {
                  dialog.destroy()
                  resolve(true)
                },
                onCancel: () => {
                  dialog.destroy()
                  resolve(false)
                }
              })
            })

            // 等待用户操作结果
            const confirmResult = await waitConfirm
            if (!confirmResult) {
              this.sceneValue = oldVal
              return;
            }
          }
        }
        // 过滤掉已选择的课程或取消选择的课程
        this.taskList = this.taskList.filter(v => val.includes(v.groupId));
        this.choseCourseId = this.choseCourseId.filter(v => val.includes(v));
        this.taskList = this.taskList.map((item, index) => ({
          ...item,
          orderNumber: item.orderNumber || index + 1
        }))
        // 根据ID查询课程详情
        this.queryCourseDetail(val);
      },
      deep: true,
    },
    mode: {
      handler(val) {
        if (val === 'edit') {
          this.getTaskListByCampId(this.form.campId);
        }
        if (val === 'copy') {
          this.getTaskListByCampId(this.form.copyCampId);
        }
        if (val === 'detail') {
          this.getTaskListByCampId(this.form.id);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getScenes();
  },
  methods: {
    handleTempInput(value) {
      if (value < 1 || value > this.taskList.length) {
        this.$message.error(`序号需在1-${this.taskList.length}之间`)
        this.tempOrderNumber = null
      }
    },
    // 禁用今天之前的日期（单日期选择器）
    disableDate(date) {
      return date < new Date(new Date().setHours(0, 0, 0, 0))
    },
    async getScenes() {
      const {data} = await this.$request.get(sysCourseGroupApi.queryCourseGroupList.url);
      //  自定义场景
      this.sceneList = data;
    },
    deleteTask(item) {
      const index = this.taskList.findIndex(v => v.id === item.id);
      this.taskList.splice(index, 1);
      // 更新全局序号
      this.taskList.forEach((item, index) => {
        item.orderNumber = index + 1;
      });
    },
    handleStartTime(newDate, currentIndex) {
      // 遍历后续所有项目
      for (let i = currentIndex; i < this.taskList.length - 1; i++) {
        const nextIndex = i + 1
        if (this.taskList[nextIndex]) {
          const nextDate = dayjs(newDate).add(nextIndex - currentIndex, 'day').format('YYYY-MM-DD')
          this.$set(this.taskList[nextIndex], 'startTime', nextDate)
        }
      }
    },
    deleteTasks(ids) {
      this.taskList = this.taskList.filter(item => !ids.includes(item.id));
      // 更新全局序号
      this.taskList.forEach((item, index) => {
        item.orderNumber = index + 1;
      });
      this.selectedTasks = []
    },
    // 全选/全不选
    handleSelectAll(checked) {
      if (checked) {
        this.selectedTasks = this.availableTasks.map(item => item.id);
      } else {
        this.selectedTasks = [];
      }
    },
    // 单选操作
    handleSelectItem(id) {
      const item = this.taskList.find(item => item.id === id);
      // 检查是否允许选择
      if (this.mode === 'edit' && this.isDateExpired(item.startTime)) return;

      const index = this.selectedTasks.indexOf(id);
      if (index === -1) {
        this.selectedTasks.push(id);
      } else {
        this.selectedTasks.splice(index, 1);
      }
    },
    isDateExpired(date) {
      if (!date) return false;
      return dayjs(date).isBefore(dayjs().startOf('day'));
    },
    handlePreview(data) {
      const {videoPath} = data;
      this.previewResource = videoPath;
      this.previewVisible = true;
    },
    handleSortConfirm(item) {
      if (!this.tempOrderNumber) {
        this.$message.error('请输入有效序号')
        return
      }
      this.handleSort(this.tempOrderNumber, item);
      this.tempOrderNumber = null
    },
    handleSort(value, task) {
      if (value < 1 || value > this.taskList.length) {
        this.$message.error(`序号需在1-${this.taskList.length}之间`)
        return
      }
      // 过期课程锁定校验
      if (this.mode === 'edit' && this.isDateExpired(task.startTime)) {
        this.$message.error('过期课程不可调整顺序');
        task.orderNumber = this.taskList.findIndex(t => t.id === task.id) + 1;
        return;
      }

      const inputPosition = Number(value);
      const maxPosition = this.taskList.length;
      const currentIndex = this.taskList.findIndex(t => t.id === task.id);

      // 输入范围校验
      if (inputPosition < 1 || inputPosition > maxPosition) {
        this.$message.error(`排序位置需在1-${maxPosition}之间`);
        task.orderNumber = currentIndex + 1;
        return;
      }

      // 过期课程位置校验
      const expiredCourses = this.taskList.filter(item => this.isDateExpired(item.startTime));
      if (expiredCourses.length > 0) {
        // 获取所有过期课程的位置
        const expiredPositions = new Set(expiredCourses.map(c => c.orderNumber));

        // 检查输入位置是否在任意过期位置之前
        const isBeforeAnyExpired = Array.from(expiredPositions).some(pos =>
          inputPosition <= pos
        );

        if (isBeforeAnyExpired) {
          const sortedPositions = Array.from(expiredPositions).sort((a, b) => a - b);
          this.$message.error(`不能排在已过期课程前（过期位置：${sortedPositions.join(',')}）`);
          task.orderNumber = currentIndex + 1;
          return;
        }
      }

      // 位置调整逻辑
      if (currentIndex !== -1 && currentIndex !== inputPosition - 1) {
        const [movedTask] = this.taskList.splice(currentIndex, 1);
        this.taskList.splice(inputPosition - 1, 0, movedTask);
      }

      // 更新全局序号
      this.taskList.forEach((item, index) => {
        item.orderNumber = index + 1;
      });
    },
    async getTaskListByCampId(id) {
      const {
        code,
        data
      } = await this.$request.post(sysCampCourseUploadApi.getCampCourseListByCampId.url, {campId: `${id}`});
      if (code === 0) {
        const courseList = data.map((item) => item.courseInfo);
        const taskList = courseList.map((item) => JSON.parse(item));
        this.taskList = taskList.flat(1);
        // 过滤出taskList中不同的groupId并去重
        const groupIdList = [...new Set(this.taskList.map(v => v.groupId))];
        this.sceneValue = groupIdList;
        this.choseCourseId = groupIdList;
      } else {
        this.$message.error("获取课程列表失败");
      }
    },
    queryCourseDetail(sceneIds) {
      // 过滤掉已选择的课程
      if (this.choseCourseId.length > 0) {
        sceneIds = sceneIds.filter(v => !this.choseCourseId.includes(v));
        this.choseCourseId.push(...sceneIds.flat(1));
      }
      if (this.choseCourseId.length === 0) this.choseCourseId = sceneIds;
      // 遍历ID，查询课程详情
      if (sceneIds.length === 0) return;
      const courseData = sceneIds.map(sceneId => this.$request.get(sysCourseGroupApi.queryCourseGroupListByGroupId.url, {
        params: {
          pageNum: 1,
          pageSize: 9999,
          id: sceneId
        }
      }));
      Promise.all(courseData).then(res => {
        const courseDetailList = res.map(item => item.code === 0 && item.data.records);
        // 去除courseDetailList中的对象中的startTime字段
        courseDetailList.forEach(item => {
          item.forEach(v => {
            delete v.startTime;
          });
        });
        // 合并数据并过滤courseStatus为0的数据
        const courseDetailListFilter = courseDetailList.flat(1).filter(v => v.courseStatus !== "0");
        this.taskList.push(...courseDetailListFilter);
        // 更新全局序号
        this.taskList.forEach((item, index) => {
          item.orderNumber = item.field1 || index + 1;
        });
      })
    },
    async onSave() {
      if (this.isShowTimeSelect) {
        // 检查每个数据是否都有开始时间
        const taskList = this.taskList.filter(v => !v.startTime);
        if (taskList.length > 0) {
          this.$message.error('请选择开始时间');
          return;
        }
      }
      // 检查每个数据是否都有orderNumber
      const taskList = this.taskList.filter(v => !v.orderNumber);
      if (taskList.length > 0) {
        this.$message.error('请设置排序');
        return;
      }
      // 处理数据
      this.taskList = this.taskList.map((v, i) => ({
        ...v,
      }));
      const groupMap = new Map();
      this.taskList.forEach(task => {
        const groupId = task.groupId || '';
        if (!groupMap.has(groupId)) {
          groupMap.set(groupId, {
            groupId,
            courseVideoDTOS: []
          });
        }
        groupMap.get(groupId).courseVideoDTOS.push(task);
      });

      const params = {
        campId: this.form.campId,
        groupCoursesRequestDTOS: Array.from(groupMap.values())
      };
      console.error(params);
      // 处理数据
      const {
        msg,
        data,
        code
      } = await this.$request.post(sysCampCourseUploadApi.saveCampCourse.url, JSON.stringify(params), {headers: {'Content-Type': 'application/json'}});
      if (code === 0) {
        this.$emit("save");
      } else {
        this.$message.error(msg);
      }
    },
  },
};
</script>

<style lang='less' scoped>
.step-two {
  background: #f8f9fa;
  min-height: 100vh;

  .step-two-container {
    display: flex;
    gap: 24px;
    height: 700px;
  }

  .left {
    width: 380px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    }

    /deep/ .t-card__body {
      height: 100%;
      overflow: auto;
      padding: 0;
    }

    .container {
      .associate-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #f8f9fa;
        }

        &.active {
          background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
          border-left: 4px solid #3b82f6;
        }

        & > div {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex: 1;

          & > span {
            padding: 0 12px;
            font-weight: 500;
            color: #374151;
          }

          & > div {
            span {
              margin: 4px;
              padding: 2px 8px;
              background: #f3f4f6;
              border-radius: 4px;
              font-size: 12px;
              color: #6b7280;
            }
          }
        }

        & > p {
          padding: 0 12px;
          width: 80px;
          font-size: 13px;
          color: #9ca3af;
        }
      }
    }
  }

  .right {
    flex: 1;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    }

    .right-container {
      display: flex;
      padding: 20px;
      gap: 20px;
      height: calc(100% - 40px);

      .right-left {
        flex: 1;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        height: 580px;
        display: flex;
        flex-direction: column;
        background: #fff;
        overflow: hidden;

        .header-right-left {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border-bottom: 1px solid #e5e7eb;
          z-index: 99;
          padding: 16px 20px;
          flex-shrink: 0;

          .right-left-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
          }
        }

        // 课程列表容器
        .course-list-container {
          flex: 1;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f5f9;
          }

          &::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;

            &:hover {
              background: #94a3b8;
            }
          }
        }

        .task-tip {
          text-align: center;
          color: #9ca3af;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15px;
          flex-direction: column;
          gap: 8px;

          &::before {
            content: "📚";
            font-size: 48px;
            opacity: 0.5;
          }
        }
      }

      .right-right {
        width: 280px;
        padding: 0 0 0 20px;
        border-left: 1px solid #e5e7eb;

        .right-right-title {
          margin-bottom: 16px;
          font-weight: 600;
          font-size: 16px;
          color: #1f2937;
        }

        .search-input {
          margin-bottom: 16px;
          border-radius: 8px;
        }

        .scene-list {
          width: 100%;

          .scene-list-content {
            overflow-y: auto;
            height: 480px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;

            &::-webkit-scrollbar {
              width: 6px;
            }

            &::-webkit-scrollbar-track {
              background: #f1f5f9;
            }

            &::-webkit-scrollbar-thumb {
              background: #cbd5e1;
              border-radius: 3px;

              &:hover {
                background: #94a3b8;
              }
            }

            .scene-list-item {
              font-size: 14px;
              padding: 16px;
              border-bottom: 1px solid #f0f0f0;
              transition: all 0.2s ease;

              &:hover {
                background: #f8f9fa;
              }

              &:last-child {
                border-bottom: none;
              }
            }

            .scene-list-taskType {
              font-size: 12px;
              margin-left: 12px;
              color: #6b7280;
            }
          }

          .t-list {
            width: 100%;
            height: 480px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
          }
        }
      }

      .scene-item {
        padding: 20px;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s ease;

        &:hover {
          background: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .item-header-preview {
            cursor: pointer;
            margin-left: 12px;
            color: #3b82f6;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
              background: #eff6ff;
              color: #1d4ed8;
            }
          }

          .item-operation {
            display: flex;
            align-items: center;
            gap: 12px;

            .operation-item {
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                transform: scale(1.1);
              }
            }
          }
        }

        .item-box {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
        }

        .scene-card {
          width: 120px;
          height: 120px;
          background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 12px;
          flex-direction: column;
          cursor: pointer;
          transition: all 0.3s ease;
          border: 2px solid transparent;

          .hlight {
            display: none;
          }

          .normal {
            display: block;
          }

          &:hover {
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

            .hlight {
              display: block;
            }

            .normal {
              display: none;
            }
          }

          & > span {
            margin: 0 12px;
            text-align: center;
            font-weight: 500;
          }

          &.icon_1:hover, &.icon_2:hover {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
          }

          &.icon_3:hover, &.icon_4:hover {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
          }

          &.icon_5:hover, &.icon_6:hover, &.icon_7:hover {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
          }

          &.icon_8:hover, &.icon_9:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          }
        }
      }
    }

  }

  .left, .right {
    /deep/ .t-card__body {
      padding: 0;
    }

    .title-step-two {
      padding: 20px 24px;
      font-weight: 600;
      font-size: 18px;
      color: #1f2937;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-bottom: 1px solid #e5e7eb;
      margin: 0;
    }
  }
}

// 滚动条优化已移至 .course-list-container 中

// 批量操作区域优化
.batch-operation {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0;

  .t-checkbox {
    font-weight: 500;
    color: #374151;
  }

  .t-button {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

.scene-item .t-checkbox {
  margin-right: 12px;

  .t-checkbox__label {
    font-weight: 500;
    color: #374151;
  }
}

.count-tips {
  margin-left: 12px;
  font-weight: 400;
  color: #6b7280;
  font-size: 13px;

  .divider {
    margin: 0 8px;
    color: #d1d5db;
  }

  .available {
    color: #3b82f6;
    font-weight: 500;
  }

  .selected {
    color: #10b981;
    font-weight: 500;
  }
}

// 全局组件优化
/deep/ .t-input {
  border-radius: 8px;

  transition: all 0.2s ease;

  &:hover {
    border-color: #9ca3af;
  }

  &:focus,
  &.t-is-focused {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

/deep/ .t-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.t-button--theme-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }

  &.t-button--theme-default {
    background: #fff;

    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

/deep/ .t-date-picker {
  border-radius: 8px;

  transition: all 0.2s ease;

  &:hover {
    border-color: #9ca3af;
  }

  &:focus,
  &.t-is-focused {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

/deep/ .t-checkbox {
  .t-checkbox__label {
    font-weight: 500;
    color: #374151;
  }

  &.t-is-checked {
    .t-checkbox__label {
      color: #3b82f6;
    }
  }
}
</style>
