<template>
  <t-dialog
    width="65%"
    header="选择部门"
    :closeOnOverlayClick="false"
    :visible.sync="modalVisible"
    @close="closeModal"
    class="edit-task-dialog"
    @confirm="confirmVideo"
    :cancelBtn="null"
  >
    <div>
      <t-table
        rowKey="id"
        :columns="columns"
        :data="companyList"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        @page-change="onPageChange"
        @select-change="handleRowSelectChange"
      ></t-table>
    </div>
  </t-dialog>
</template>
<script lang="ts">

export default {
  name: "ChooseDept",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    salasGroup: {
      type: Array,
      default: () => [],
    }
  },
  data() {
    return {
      modalVisible: false,
      columns: [
        { colKey: 'row-select', type: 'multiple' },
        {
          colKey: 'id',
          title: '部门ID',
          width: 150,
        },
        {
          colKey: 'name',
          title: '部门名称',
          width: 80,
        },
      ],
      companyList: [],
      selectedRowKeys: [],
      selectedRows: [],
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
        showJumper: true,
      },
    };
  },
  computed: {},
  watch: {
    visible(val) {
      this.modalVisible = val;
    },
    salasGroup(val) {
      if (val.length > 0) {
        this.companyList = val;
        this.pagination.total = val.length;
      }
    },
  },
  methods: {
    onPageChange(pageInfo: any, newData: any) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
    },
    closeModal() {
      this.$emit('close');
    },
    confirmVideo() {
      this.$emit('success', this.selectedRows);
    },
    handleRowSelectChange(selectedRowKeys, { selectedRowData }) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRowData;
    },
  },
};
</script>
<style lang="less" scoped>
.search-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 15px 0;
  .refresh-btn {
    cursor: pointer;
    color: #409eff;
    font-size: 14px;
    margin-left: 10px;
  }
}
</style>
