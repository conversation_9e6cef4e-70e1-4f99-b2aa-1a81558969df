<template>
  <div class='step-one'>
    <t-card :bordered="false" header="基础配置">
      <t-form :data="baseInfo" :rules="rules" ref="baseForm">
        <div class="form-box">
          <t-form-item class="form-item" label="营期名称" name="campperiodName" required>
            <t-input v-model="baseInfo.campperiodName" placeholder="请输入营期名称" maxlength="30"></t-input>
          </t-form-item>
          <t-form-item class="form-item" label="营期简介" name="campperiodIntroduction" required>
            <t-textarea v-model="baseInfo.campperiodIntroduction" placeholder="请输入营期简介"></t-textarea>
          </t-form-item>
        </div>
        <div style="margin-top: 10px;margin-bottom: 10px">
          <t-form-item class="form-item" label="营期外显名称" name="field1" required>
            <t-input v-model="baseInfo.field1" placeholder="请输入营期外显名称" maxlength="60"></t-input>
          </t-form-item>
        </div>
        <t-form-item label="封面" name="campperiodCoverpath" required>
          <div class="cover-box" @click="handleUpload">
            <div class="cover-img">
              <t-image v-if="baseInfo.campperiodCoverpath" :src="baseInfo.campperiodCoverpath" fit="contain"
                       position="center" class="uncompressed-image"/>
              <img class="none-image" v-else :src="noneImage" alt="courseCover"/>
              <span class="prompt" v-if="!baseInfo.campperiodCoverpath">点击添加封面</span>
              <span class="prompt-two" v-if="!baseInfo.campperiodCoverpath">课程封面将在课程详情页中展示</span>
            </div>
          </div>
        </t-form-item>
        <t-form-item label="营期详情" name="campperiodContent" required>
          <rich-editor v-model="baseInfo.campperiodContent" placeholder="请输入营期详情"></rich-editor>
        </t-form-item>
        <t-form-item label="标签" name="campperiodTag">
          <div class="tips-box">
            <t-switch style="width: 50px" v-model="openTag"></t-switch>
            <!--      打开时内容显示  baseInfo.campperiodTag      -->
            <span class="tips">
          * 可根据客户在课程中不同的行为为其打上对应的标签
        </span>
          </div>
        </t-form-item>
        <t-form-item label="可见范围" name="visualFlag" required>
          <div class="form-box-vertical" @click="handleSalesGroupChange('3')">
            <t-radio-group v-model="baseInfo.visualFlag" :options="rangeList"
                           @change="handleSalesGroupChange"></t-radio-group>
            <t-tag-input :min-collapsed-num="8" class="form-item" v-if="baseInfo.salesGroupId.length" disabled
                         :value="salesGroupName">
              <template #collapsedItems="{ collapsedTags }">
                <t-popup>
                  <t-tag>More({{ collapsedTags.length }})</t-tag>
                  <template #content>
                    <t-tag v-for="item in collapsedTags" :key="item" style="margin-right: 4px">
                      {{ item }}
                    </t-tag>
                  </template>
                </t-popup>
              </template>
            </t-tag-input>
          </div>
        </t-form-item>
        <t-form-item labelWidth="150px" label="是否自定义红包金额" name="campperiodRedPack">
          <t-switch v-model="baseInfo.campperiodRedPack" @change="handleRedPackChange"></t-switch>
        </t-form-item>
        <t-form-item labelWidth="120px" label="红包金额范围" name="redPackRange" v-if="baseInfo.campperiodRedPack">
          <t-slider
            v-model="baseInfo.redPackRange"
            range
            :min="0.1"
            :max="1"
            :step="0.1"
            :marks="rangeMarks"
          />
        </t-form-item>
        <t-form-item label="红包金额" name="campperiodRedPackAmount" v-if="baseInfo.campperiodRedPack">
          <t-input-number suffix="元" v-model="baseInfo.campperiodRedPackAmount" :min="currentRange[0]"
                          :max="currentRange[1]" :step="0.1" @change="handleAmountChange"></t-input-number>
        </t-form-item>

      </t-form>
    </t-card>
    <t-card :bordered="false" header="开课设置">
      <t-form label-width="120px" :data="baseInfo" :rules="courseConfigFormRules" ref="courseConfigForm">
        <t-form-item label="开课时间" name="startingTime" required>
          <div class="open-time-box">
            <t-radio-group style="margin-bottom: 10px" v-model="baseInfo.startingFlag"
                           :options="configList"></t-radio-group>
            <div class="tips-box" v-if="baseInfo.startingFlag === '1'">
              <t-date-range-picker :presets="presets" v-model="baseInfo.startingTime" :disable-date="disableRangeDate"
                                   enable-time-picker></t-date-range-picker>
              <span
                class="tips">* 开课时间范围内学员可以进入直播或查看回放，开课时间结束之前，学员通过营期链接均可以报名。</span>
            </div>
            <div class="tips-box" v-else>
              <t-date-picker :presets="presets" :disable-date="disableDate" v-model="baseInfo.startingTime[0]"
                             enable-time-picker></t-date-picker>
              <span
                class="tips">* 开课时间范围内学员可以进入直播或查看回放，开课时间结束之前，学员通过营期链接均可以报名。</span>
            </div>
          </div>
        </t-form-item>
        <t-form-item label="新用户自动注册" name="autoRegister" required>
          <div class="tips-box">
            <t-radio-group v-model="baseInfo.autoRegister" :options="registerOptions"></t-radio-group>
            <span class="tips">* 开启后，新用户无需账号审核即可注册看课</span>
          </div>
        </t-form-item>
        <t-form-item label="获取用户手机号" name="needPhone" required>
          <div class="tips-box">
            <t-radio-group v-model="baseInfo.needPhone" :options="phoneOptions"></t-radio-group>
            <span class="tips">* 开启后，用户领取红包时需要绑定手机号码</span>
          </div>
        </t-form-item>
        <t-form-item label="是否校验添加企微" name="needAddWechat" required>
          <div class="tips-box">
            <t-radio-group v-model="baseInfo.needAddWechat" :options="wechatOptions"></t-radio-group>
            <span class="tips">* 开启后，用户需要添加企微才能参与营期看课</span>
          </div>
        </t-form-item>
      </t-form>
    </t-card>
    <t-card :bordered="false" header="售卖设置">
      <t-form :data="baseInfo" :rules="configFormRules" ref="configForm">
        <div class="form-box">
          <t-form-item class="form-item" label="上架设置" name="campperiodStatus" required>
            <t-radio-group v-model="baseInfo.campperiodStatus" :options="shelfList"></t-radio-group>
          </t-form-item>
          <t-form-item class="form-item" label="付费类型" name="campperiodSalesmethod" required>
            <t-radio-group v-model="baseInfo.campperiodSalesmethod" :options="cashList"></t-radio-group>
          </t-form-item>
        </div>
      </t-form>
    </t-card>
    <div style="text-align: right">
      <!--   仅在编辑模式下显示保存按钮   -->
      <t-button class='next' @click='onSubmit(true)' v-if="mode === 'edit'">保存</t-button>
      <t-button class='next' @click='onSubmit(false)'>下一步，课程配置</t-button>
    </div>
    <image-upload :visible="uploadImageModal" @close="uploadImageModal = false"
                  @success="onImageSuccess"></image-upload>
    <choosed-department :salasGroup="companyList" :visible="openDepartmentModal" @close="openDepartmentModal = false"
                        @success="onDepartmentSuccess"></choosed-department>
  </div>
</template>

<script>
import noneImage from "@/pages/video-management/video-management-main/image/none-image";
import imageUpload from "@/pages/video-management/video-management-main/components/imageUpload.vue";
import richEditor from "@/components/rich-editor/index.vue";
import ChoosedDepartment from "./chooseDept.vue";
import {COURSE_STATUS_ARRAY} from "@/constants/enum/business/course-status-array.enum";
import BeanUtilsService from "@/service/bean-utils.service";
import sysCampGroupApi from "@/constants/api/back/sys-camp-group.api";
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import merchantKeyApi from "@/constants/api/back/merchant-key";
import {Code} from "@/constants/enum/general/code.enum";

export default {
  name: 'step-one',
  components: {richEditor, imageUpload, ChoosedDepartment},
  props: ['form', 'companyId', 'mode'],
  data() {
    return {
      url: sysCompanyGroupApi.queryCompanyListByCompanyId.url,
      baseInfo: {},
      // 如果visualList有值则是员工，salesGroupId有值则是部门
      uploadImageModal: false,
      openDepartmentModal: false,
      settingList: [],
      modelTypes: [],
      companyList: [],
      openTag: false,
      campId: '',
      presets: {
        今天: [new Date(), new Date()],
      },
      rules: {
        campperiodName: [{required: true, message: '营期名称必填'}, {
          message: '营期名称必填',
          whitespace: true
        }, {pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change'}],
        field1: [{required: true, message: '营期外显名称必填'}, {
          message: '营期外显名称必填',
          whitespace: true
        }, {pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change'}],
        campperiodIntroduction: [{required: true, message: '营期简介必填', trigger: 'blur'}],
        campperiodCoverpath: [{required: true, message: '营期封面必填', trigger: 'blur'}],
        campperiodContent: [{required: true, message: '营期详情必填', trigger: 'blur'}],
        visualFlag: [{required: true, message: '可见范围必选', trigger: 'blur'}],
        campperiodRedPackAmount: [{required: true, message: '红包金额必填', trigger: 'blur'}],
        redPackRange: [{required: true, message: '红包金额范围必填', trigger: 'blur'}],
      },
      courseConfigFormRules: {
        startingTime: [{required: true, message: '开课时间必选', whitespace: true, trigger: 'blur'}],
        needPhone: [{required: true, message: '获取用户手机号必选', trigger: 'blur'}],
        autoRegister: [{required: true, message: '新用户自动注册必选', trigger: 'blur'}],
        needAddWechat: [{required: true, message: '新用户自动注册必选', trigger: 'blur'}],
      },
      configFormRules: {
        campperiodSalesmethod: [{required: true, message: '付费类型必选', trigger: 'blur'}],
        campperiodStatus: [{required: true, message: '上架设置必选', trigger: 'blur'}],
      },
      treeProps: {
        disableCheck: (node) => !!node.children
      },
      rangeList: [
        {label: '按部门设置', value: '3'},
      ],
      rangeMarks: {
        0.1: '0.1元',
        0.2: '0.2元',
        0.3: '0.3元',
        0.4: '0.4元',
        0.5: '0.5元',
        0.6: '0.6元',
        0.7: '0.7元',
        0.8: '0.8元',
        0.9: '0.9元',
        1: '1元',
      },
      shelfList: COURSE_STATUS_ARRAY,
      cashList: [
        {label: '免费', value: '1'},
        {label: '付费', value: '2'},
      ],
      catalogMode: [
        {label: '课节模式', value: '1'},
        {label: '章节模式', value: '2'},
      ],
      catalogUnlockMode: [
        {label: '自由模式', value: '1'},
        {label: '日期解锁模式', value: '2'},
      ],
      configList: [
        {label: '期限', value: '1'},
        {label: '永久', value: '0'},
      ],
      phoneOptions: [
        {label: '是', value: true},
        {label: '否', value: false},
      ],
      wechatOptions: [
        {label: '是', value: true},
        {label: '否', value: false},
      ],
      registerOptions: [
        {label: '是', value: true},
        {label: '否', value: false},
      ],
      columns: [
        {
          colKey: 'courseName',
          title: '系列课名称',
          width: 150,
        },
        {
          colKey: 'courseId',
          title: '系列课ID',
          width: 150,
        },
        {
          colKey: 'action',
          title: '操作',
          width: 150,
        }
      ]
    };
  },
  computed: {
    salesGroupName() {
      let data = [];
      if (this.companyList.length) {
        data = this.baseInfo.salesGroupId.map((item) => this.companyList.find((i) => i.id === item)).filter(Boolean);
      }
      return data.length > 0 ? data.map((item) => item.name) : [];
    },
    noneImage() {
      return noneImage
    },
    currentRange() {
      return this.baseInfo.redPackRange?.length ?
        this.baseInfo.redPackRange : [0.1, 1];
    }
  },
  watch: {
    companyId: {
      handler() {
        this.getCompanyList();
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.baseInfo = {...this.form, companyId: this.companyId};
    console.error(this.baseInfo);
    if (this.baseInfo.salesGroupId.length > 0) this.baseInfo.visualFlag = '3';
    if (this.baseInfo.campperiodRedPack) {
      // 将红包范围['0.1','1'] 转换为 [0.1,1]
      this.baseInfo.redPackRange = this.baseInfo.redPackRange.map((item) => parseFloat(item));
      // 将红包金额转换为数字
      this.baseInfo.campperiodRedPackAmount = parseFloat(this.baseInfo.campperiodRedPackAmount);
    }
    // 设置新用户自动注册的默认值
    if (this.baseInfo.autoRegister === undefined) {
      this.$set(this.baseInfo, 'autoRegister', true);
    }
    // 设置获取用户手机号的默认值为否
    if (this.baseInfo.needPhone === undefined) {
      this.$set(this.baseInfo, 'needPhone', false);
    }
  },
  methods: {
    handleUpload() {
      this.uploadImageModal = true;
    },
    handleSalesGroupChange(data) {
      if (data === '3') {
        // 按部门设置
        this.openDepartmentModal = true;
      }
    },
    handleAmountChange(value) {
      const [min, max] = this.currentRange;

      if (value && (value < min || value > max)) {
        this.$set(this.baseInfo, 'campperiodRedPackAmount', min);
        this.$message.warning(`输入值需在${min}-${max}之间`);
      } else if (value === 0) {
        this.$message.warning(`输入值需大于0`);
      }
    },
    async getCompanyList() {
      if (!this.companyId) return;
      const {code, data, msg} = await this.$request.get(this.url, {
        params: {
          id: this.companyId,
        },
      });
      if (code === 0) {
        this.companyList = data.salesGroups;
      } else {
        this.$message.error({content: msg});
      }
    },
    onDepartmentSuccess(data) {
      this.openDepartmentModal = false;
      this.baseInfo.salesGroupId = data.map((item) => item.id);
    },
    handleRedPackChange(data) {
      this.getMerchantKeyInfo();
      if (!data) {
        this.$set(this.baseInfo, 'campperiodRedPackAmount', 0.1);
        this.$set(this.baseInfo, 'redPackRange', []);
      } else {
        this.$set(this.baseInfo, 'campperiodRedPackAmount', 0.1);
        this.$set(this.baseInfo, 'redPackRange', [0.1, 1]);
      }
    },
    async getMerchantKeyInfo() {
      try {
        const {code, data} = await this.$request.get(
          `${merchantKeyApi.queryMerchantKey.url}/${this.companyId}`
        );
        if (code === Code.OK.code && !data) {
          this.$message.warning({content: "当前公司暂未绑定微信支付商户信息，不可以进行红包设置"});
          this.$set(this.baseInfo, 'campperiodRedPack', false);
          return;
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
      }
    },
    onImageSuccess(data) {
      this.uploadImageModal = false;
      const imageData = data[0];
      this.baseInfo.campperiodCoverpath = imageData.imageUrl;
    },
    async onSubmit(saveFlag) {
      // 需要同时校验三个表单
      Promise.all([
        this.$refs.baseForm.validate(),
        this.$refs.courseConfigForm.validate(),
        this.$refs.configForm.validate(),
      ]).then(async (res) => {
        console.error(res, this.baseInfo);
        if (res.every((item) => item === true)) {
          // 判断部门值及数据转换
          if (this.baseInfo.visualFlag === '3') {
            if (!this.baseInfo.salesGroupId.length || !this.salesGroupName.length) return this.$message.error({content: "请选择可见范围"});
          }
          const baseInfo = BeanUtilsService.copy(this.baseInfo);
          if (baseInfo.campperiodRedPack) {
            // 将红包范围 [0.1,1] 转换为 ['0.1','1']
            baseInfo.redPackRange = baseInfo.redPackRange.map((item) => item.toFixed(1));
            if (baseInfo.campperiodRedPackAmount === 0) {
              const [firstAmount] = baseInfo.redPackRange;
              baseInfo.campperiodRedPackAmount = firstAmount;
            }
            // 将红包金额转换为字符串
            baseInfo.campperiodRedPackAmount = baseInfo.campperiodRedPackAmount.toFixed(1);
          }
          if (this.mode === 'copy') {
            this.campId = this.campId || baseInfo.id; // copy时点击上一步，父campId为空问题
            delete baseInfo.id;
          }
          delete baseInfo.visualFlag;
          delete baseInfo.campId;
          if (this.mode === 'edit') {
            const {code, data, msg} = await this.$request.post(sysCampGroupApi.updateCampGroup.url, baseInfo);
            if (code === 0) {
              if (saveFlag) this.$message.success({content: "保存成功"});
              // 提交成功后才能下一步
              this.$emit('success', {
                data: {
                  ...this.baseInfo,
                  campId: data.id
                }, saveFlag,
              });
            } else {
              this.$message.error({content: `修改失败${msg}`});
            }
          } else {
            const {code, data, msg} = await this.$request.post(sysCampGroupApi.addCampGroup.url, baseInfo);
            if (code === 0) {
              this.$message.success({content: "提交成功"});
              // 提交成功后才能下一步
              this.$emit('success', {
                data: {
                  ...this.baseInfo,
                  campId: data.id,
                  copyCampId: this.campId
                }, saveFlag
              });
            } else {
              this.$message.error({content: `提交失败${msg}`});
            }
          }
        }
      }).catch((e) => {
        this.$message.error({content: `表单校验不通过，请检查${e.message}`});
      })
    },
    // 禁用今天之前的日期（单日期选择器）
    disableDate(date) {
      return date < new Date(new Date().setHours(0, 0, 0, 0))
    },
    // 禁用范围选择器的逻辑
    disableRangeDate(date) {
      return date < new Date(new Date().setHours(0, 0, 0, 0))
    },
  },
};
</script>

<style lang='less' scoped>
.step-one {
  background: #f8f9fa;
  min-height: 100vh;

  .basic-info {
    margin-bottom: 24px;
  }

  .setting {
    margin-top: 24px;

    .setting-container {
      width: 100%;
      max-width: 500px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
      }

      .header {
        padding: 16px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        font-weight: 600;
        font-size: 16px;

        span {
          display: inline-block;

          &:nth-child(1) {
            width: 60px;
            text-align: center;
            font-weight: 500;
          }

          &:nth-child(2) {
            width: 320px;
            font-weight: 500;
          }

          &:nth-child(3) {
            width: 60px;
            text-align: center;
            font-weight: 500;
          }
        }
      }

      .setting-item {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;
        transition: background-color 0.2s ease;

        &:hover {
          background: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .item-one {
          width: 60px;
          text-align: center;
          font-weight: 500;
          color: #666;
        }

        .item-two {
          flex: 1;
          padding-right: 60px;
        }
      }

      .footer {
        padding: 16px 20px;
        background: #f8f9fa;
        border-top: 1px solid #f0f0f0;

        .t-button {
          min-width: 120px;
          height: 36px;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }
}

.open-time-box {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tips-box {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .tips {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.6;
    padding: 8px 12px;
    background: #f3f4f6;
    border-radius: 6px;
    border-left: 3px solid #3b82f6;
    margin: 0;

    a {
      color: #3b82f6;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s ease;

      &:hover {
        color: #1d4ed8;
        text-decoration: underline;
      }
    }
  }
}

.cover-box {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
  }

  .cover-img {
    width: 320px;
    height: 180px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 2px dashed #d1d5db;
    position: relative;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3b82f6;
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    }

    .prompt {
      position: absolute;
      top: 35%;
      left: 50%;
      transform: translateX(-50%);
      color: #6b7280;
      font-size: 14px;
      font-weight: 500;
    }

    .prompt-two {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%);
      color: #9ca3af;
      font-size: 12px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 6px;
    }

    .none-image {
      width: 48px;
      height: 48px;
      opacity: 0.5;
    }
  }

  .cover-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 16px;

    .cover-tips {
      color: #6b7280;
      font-size: 13px;
      line-height: 1.6;
      padding: 12px 16px;
      background: #f9fafb;
      border-radius: 8px;
      border-left: 3px solid #10b981;
      margin: 0;
    }
  }
}

.uncompressed-image {
  width: 320px;
  height: 180px;
  border-radius: 8px;
}

.form-box {
  display: flex;
  gap: 20px;
  align-items: flex-start;

  .form-item {
    flex: 1;
    min-width: 0;
  }
}

.form-box-vertical {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .form-item {
    width: 100%;
  }
}

// 全局样式优化
/deep/ .t-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  }

  .t-card__header {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;
    padding: 20px 24px;
  }

  .t-card__body {
    padding: 24px;
  }
}

// 表单项优化
/deep/ .t-form-item {
  margin-bottom: 24px;

  .t-form__label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }

  .t-input,
  .t-select,
  .t-textarea,
  .t-date-picker {
    border-radius: 8px;

    transition: all 0.2s ease;

    &:hover {
      border-color: #9ca3af;
    }

    &:focus,
    &.t-is-focused {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

// 按钮优化
/deep/ .t-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.t-button--theme-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }

  &.t-button--theme-default {
    background: #fff;

    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 单选按钮组优化
/deep/ .t-radio-group {
  .t-radio {
    margin-right: 16px;

    .t-radio__label {
      font-weight: 500;
      color: #374151;
    }

    &.t-is-checked {
      .t-radio__label {
        color: #3b82f6;
      }
    }
  }
}

</style>

<style lang='less'>
.tree-pop {
  .t-is-disabled .t-checkbox__label {
    color: #000;
  }
}
</style>
