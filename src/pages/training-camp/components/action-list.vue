<template>
  <div class="training-camp-container">
    <!-- 头部标题区域 -->
    <div class="group-header">
      <h3 class="group-title">
        <t-icon name="play-demo" />
        训练营
      </h3>
    </div>

    <!-- 公司树形列表区域 -->
    <div class="group-list">
      <t-tree
        class="company-tree"
        :data="companyTreeData"
        activable
        hover
        transition
        :expandLevel="1"
        @active="companyTreeChange"
      />
    </div>
  </div>
</template>

<script>
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import {treeNodeConvertService} from "@/service/tree-node-convert.service";

export default {
  name: 'type-list',
  components: {},
  data() {
    return {
      actionTypeList: [],
      initForm: {},
      companyTreeData: [],
      nodeContrast: {
        id: 'id',
        children: 'companies',
        cascadeField: 'companies',

        subNodeContrast: { id: 'id', parentId: 'columnId', children: 'salesGroups', cascadeField: 'salesGroups' },
      },
      nzNodeContrast: {
        key: 'id',
        title: 'name',
        label: 'name',
        children: 'companies',
        value: 'id',
        cascadeField: 'companies',
        id: 'id',
        parentId: 'columnId',
      },
    };
  },
  created() {
    this.getList();
  },

  methods: {
    getCompanyTreeData() {
      return this.companyTreeData;
    },
    companyTreeChange(value, context) {
      // 判断是否为叶子节点
      if (!context.node.isLeaf()) {
        this.$message.error('请选择公司');
        this.$emit('select', "");
      } else {
        this.$emit('select', {companyId: value[0], parentId: context.node.data.parentId});
      }
    },
    async getList() {
      const params = {
        id: 31000, // 总公司ID
        level: 2, // 公司层级
      };
      const { code, data } = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });
      if (code === 0 && data) {
        const companyTreeData = data.columns.map(column => ({
          ...column,
          companies: column.companies?.map(company => ({
            ...company,
            columnId: column.id // 将总公司的id作为columnId带下去
          }))
        }));
        const treeData = treeNodeConvertService.arrayConvertToNzTreeNode(
          companyTreeData,
          this.nodeContrast,
          this.nzNodeContrast,
        );

        this.companyTreeData = treeData;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.training-camp-container {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;

  // 头部标题区域
  .group-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .group-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      .t-icon {
        font-size: 18px;
      }
    }
  }

  // 公司树形列表区域
  .group-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;

    .company-tree {
      padding: 0;

      /deep/ .t-tree {
        background: transparent !important;

        .t-tree__item {
          margin: 2px 8px !important;
          border-radius: 8px !important;
          transition: all 0.2s ease !important;
          position: relative !important;

          &:hover {
            background: #f8f9fa !important;
          }

          .t-tree__item--content {
            padding: 12px 16px !important;
            border-radius: 8px !important;
            transition: all 0.2s ease !important;

            .t-tree__item--label {
              font-size: 14px !important;
              color: #374151 !important;
              font-weight: 500 !important;
              transition: all 0.2s ease !important;
            }

            .t-tree__item--icon {
              color: #6b7280 !important;
              transition: all 0.2s ease !important;
            }
          }

          // 选中状态样式 - 与用户管理保持一致
          &.t-is-active {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
            color: #1565c0 !important;
            border: 1px solid #90caf9 !important;
            box-shadow: 0 2px 8px rgba(25, 101, 192, 0.15) !important;

            &::before {
              content: '' !important;
              position: absolute !important;
              left: 0 !important;
              top: 50% !important;
              transform: translateY(-50%) !important;
              width: 4px !important;
              height: 20px !important;
              background: #1565c0 !important;
              border-radius: 0 2px 2px 0 !important;
            }

            .t-tree__item--content {
              background: transparent !important;
              padding-left: 20px !important;

              .t-tree__item--label {
                font-weight: 600 !important;
                color: #1565c0 !important;
              }

              .t-tree__item--icon {
                color: #1565c0 !important;
              }
            }
          }

          // 子节点样式
          .t-tree__item--children {
            .t-tree__item {
              margin: 2px 8px 2px 16px !important;

              .t-tree__item--content {
                padding-left: 24px !important;

                .t-tree__item--label {
                  font-size: 13px !important;
                  color: #6b7280 !important;
                }
              }

              &.t-is-active {
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
                color: #1565c0 !important;
                border: 1px solid #90caf9 !important;
                box-shadow: 0 2px 8px rgba(25, 101, 192, 0.15) !important;
                padding-left: 20px; // 因为有左边框，所以增加左内边距
                border-left: 4px solid #1565c0 !important;

                &::before {
                  content: '' !important;
                  position: absolute !important;
                  left: 0 !important;
                  top: 50% !important;
                  transform: translateY(-50%) !important;
                  width: 4px !important;
                  height: 20px !important;
                  background: #1565c0 !important;
                  border-radius: 0 2px 2px 0 !important;
                }

                .t-tree__item--content {
                  background: transparent !important;
                  padding-left: 28px !important;

                  .t-tree__item--label {
                    font-weight: 600 !important;
                    color: #1565c0 !important;
                  }

                  .t-tree__item--icon {
                    color: #1565c0 !important;
                  }
                }
              }
            }
          }
        }
      }

      // 额外的选择器确保样式生效
      /deep/ .t-tree__item.t-is-active,
      /deep/ .t-tree__item[data-active="true"],
      /deep/ .t-tree__item.active {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
        color: #1565c0 !important;
        border: 1px solid #90caf9 !important;
        box-shadow: 0 2px 8px rgba(25, 101, 192, 0.15) !important;
        position: relative !important;

        &::before {
          content: '' !important;
          position: absolute !important;
          left: 0 !important;
          top: 50% !important;
          transform: translateY(-50%) !important;
          width: 4px !important;
          height: 20px !important;
          background: #1565c0 !important;
          border-radius: 0 2px 2px 0 !important;
          z-index: 1 !important;
        }

        .t-tree__item--content {
          background: transparent !important;
          padding-left: 20px !important;

          .t-tree__item--label {
            font-weight: 600 !important;
            color: #1565c0 !important;
          }

          .t-tree__item--icon {
            color: #1565c0 !important;
          }
        }
      }
    }
  }
}
</style>
