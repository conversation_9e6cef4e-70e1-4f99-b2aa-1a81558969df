<template>
  <div>
    <t-space>
        <t-button variant='text' shape='square' @click.stop='onSort("up")' v-if='index > 0 && editState'>
          <arrow-up-icon />
        </t-button>
        <t-button variant='text' shape='square'  @click.stop='onSort("down")' v-if='index < length - 1 && editState'>
          <arrow-down-icon />
        </t-button>
      <t-tooltip v-if="editState" content="编辑话术" theme="light">
        <t-button variant='text' shape='square' @click.stop='onEdit'>
          <edit-icon />
        </t-button>
      </t-tooltip>
      <t-tooltip v-if="editState" content="删除话术" theme="light">
        <t-popconfirm content="确认删除吗" @confirm='onConfirm'>
          <t-button variant='text' shape='square'>
            <delete-icon />
          </t-button>
        </t-popconfirm>
      </t-tooltip>
    </t-space>
  </div>
</template>

<script>
import { ArrowUpIcon, ArrowDownIcon, EditIcon, DeleteIcon } from 'tdesign-icons-vue';

export default {
  name: 'actions',
  components: { ArrowUpIcon, ArrowDownIcon, EditIcon, DeleteIcon },
  props:['item', 'index', 'length', 'rightLabel', 'taskType', 'editState', 'viewProcess'],
  data(){
    return {
      meetSceneEnum: JSON.parse(window.localStorage.getItem('dictEnum') || '{}'),
    }
  },
  methods:{
    onConfirm(){
      this.$emit('delete', this.item);
    },
    onEdit(){
      this.$emit('edit', this.item);
    },
    onSort(dir){
      this.$emit('sort', {dir, item:this.item});
    },
    onLink(){
      this.$emit('link', this.item);
    },
    onCorrect() {
      this.$emit('correct', this.item);
    },
    onWrong() {
      this.$emit('wrong', this.item);
    },
    onView() {
      this.$emit('view');
    }
  }
}
</script>
