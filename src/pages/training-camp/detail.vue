<template>
  <div class='product-model-edit'>
    <!-- 营期信息卡片 -->
    <div class="camp-info-section">
      <div class="camp-info-card">
        <div class="card-header">
          <h3 class="page-title">营期详情</h3>
          <div class="header-actions">
            <t-button theme="primary" variant="outline" size="small" @click="shareVisible = true">
              <t-icon name="share" />
              分享营期
            </t-button>
          </div>
        </div>

        <div class="card-content">
          <div class="step-info">
            <div class="cover-section">
              <div class="cover-img">
                <t-image
                  v-if="form.campperiodCoverpath"
                  :src="form.campperiodCoverpath"
                  fit="cover"
                  position="center"
                  class="camp-cover-image"
                />
                <div v-else class="no-cover-placeholder">
                  <t-icon name="image" size="48px" />
                  <span>暂无封面</span>
                </div>
              </div>
            </div>

            <div class="camp-period-info">
              <div class="camp-period-name">{{ form.campperiodName }}</div>

              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">
                    <t-icon name="time" />
                    开营时间
                  </div>
                  <div class="info-value">{{ getCampTime(form.startingTime) }}</div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <t-icon name="money-circle" />
                    付费类型
                  </div>
                  <div class="info-value">
                    <t-tag :theme="form.campperiodSalesmethod === '1' ? 'success' : 'warning'" variant="light">
                      {{ getCampType(form.campperiodSalesmethod) }}
                    </t-tag>
                  </div>
                </div>

                <div class="info-item">
                  <div class="info-label">
                    <t-icon name="wallet" />
                    自定义红包
                  </div>
                  <div class="info-value">
                    <t-tag :theme="form.campperiodRedPack ? 'success' : 'default'" variant="light">
                      {{ form.campperiodRedPack ? '已开启' : '未开启' }}
                    </t-tag>
                  </div>
                </div>

                <div class="info-item" v-if="form.campperiodRedPack">
                  <div class="info-label">
                    <t-icon name="money-circle" />
                    红包金额
                  </div>
                  <div class="info-value amount">
                    ¥{{ form.campperiodRedPackAmount || '未设置' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程关联区域 -->
    <div class="course-section">
      <step-two :mode="campType" :form='form' @change='handleStepTwo'></step-two>
    </div>

    <course-sharing :visible="shareVisible" :shareData="rowData" @close="shareVisible = false"></course-sharing>
  </div>
</template>

<script>
import StepTwo from './components/step-two.vue';
import BeanUtilsService from "@/service/bean-utils.service";
import noneImage from "@/pages/video-management/video-management-main/image/none-image";
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import courseSharing from "@/components/course-sharing/index.vue";

export default {
  name: 'Detail',
  components: { courseSharing, StepTwo },
  data() {
    return {
      shareVisible: false,
      url: sysCompanyGroupApi.queryCompanyListByCompanyId.url,
      campType: 'detail',
      companyId: '',
      rowData: {},
      companyList: [],
      form: {
        campperiodName: '',
        campperiodIntroduction: '',
        campperiodCoverpath: '',
        campperiodContent: '',
        campperiodTag: [],
        startingFlag: '1',
        campperiodSalesmethod: '1',
        campperiodRedPack: false,
        campperiodRedPackAmount: "",
        startingTime: [],
        campperiodStatus: '1',
        salesGroupId: [],
        visualFlag: '',
      },
    };
  },
  created() {
    this.checkCampType();
  },
  methods: {
    noneImage() {
      return noneImage
    },
    getCampTime(time) {
      if (time && time.length === 1) return '永久';
      if (time && time.length === 2) return `${time[0]}-${time[1]}`;
      return '';
    },
    getCampType(type) {
      switch (type) {
      case '1':
        return '免费';
      case '2':
        return '付费';
      default:
        return '';
      }
    },
    checkCampType() {
      const data = this.$route.params;
      if (data && Object.keys(data).length > 0) {
        switch (data.type) {
        case 'detail':
          this.form = BeanUtilsService.copy(data.data);
          this.companyId = this.form.companyId;
          this.rowData = { ...this.$route.params.rowData };
          // this.getCompanyList();
          this.campType = data.type;
          break;
        default:
          break;
        }
      }
    },
    async getCompanyList() {
      if (!this.companyId) return;
      const { code, data, msg } = await this.$request.get(this.url, {
        params: {
          id: this.companyId,
        },
      });
      if (code === 0) {
        this.companyList = data.salesGroups;
      } else {
        this.$message.error({ content: msg });
      }
    },

    handleStepTwo(data){
      this.form = data;
    },
  },
};
</script>

<style lang="less" scoped>
.product-model-edit {
  background: #f8f9fa;
  min-height: 100vh;

  .camp-info-section {
    margin-bottom: 20px;

    .camp-info-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      overflow: hidden;
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;

        .page-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #fff;
        }

        .header-actions {
          .t-button {
            border-radius: 6px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .t-icon {
              margin-right: 4px;
            }
          }
        }
      }

      .card-content {
        padding: 20px 24px;
      }
    }
  }

  .course-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
  }

  .step-info {
    display: flex;
    align-items: flex-start;
    gap: 20px;
  }

  .cover-section {
    flex-shrink: 0;

    .cover-img {
      width: 180px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #e5e7eb;
      transition: all 0.3s ease;

      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
      }

      .camp-cover-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .no-cover-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        color: #9ca3af;
        font-size: 12px;
        gap: 6px;

        .t-icon {
          opacity: 0.6;
          font-size: 32px;
        }
      }
    }
  }

  .camp-period-info {
    flex: 1;
    min-width: 0;

    .camp-period-name {
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16px;
      line-height: 1.3;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;

      .info-item {
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid #3b82f6;
        transition: all 0.2s ease;

        &:hover {
          background: #f1f5f9;
          transform: translateX(1px);
        }

        .info-label {
          display: flex;
          align-items: center;
          font-size: 12px;
          font-weight: 500;
          color: #6b7280;
          margin-bottom: 6px;
          gap: 4px;

          .t-icon {
            font-size: 14px;
            color: #3b82f6;
          }
        }

        .info-value {
          font-size: 14px;
          font-weight: 600;
          color: #1f2937;

          &.amount {
            font-size: 16px;
            color: #dc2626;
          }
        }
      }
    }
  }
}

// 全局组件优化
/deep/ .t-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.t-button--theme-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }

  &.t-button--variant-outline {
    border: 1px solid currentColor;
    background: transparent;

    &:hover {
      background: currentColor;
      color: #fff;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

/deep/ .t-tag {
  border-radius: 6px;
  font-weight: 500;
  padding: 4px 12px;
  font-size: 13px;

  &.t-tag--theme-success {
    &.t-tag--variant-light {
      background: #f0fdf4;
      color: #16a34a;
      border-color: #bbf7d0;
    }
  }

  &.t-tag--theme-warning {
    &.t-tag--variant-light {
      background: #fffbeb;
      color: #d97706;
      border-color: #fed7aa;
    }
  }

  &.t-tag--theme-default {
    &.t-tag--variant-light {
      background: #f3f4f6;
      color: #6b7280;
      border-color: #d1d5db;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .product-model-edit {
    padding: 16px;

    .camp-info-card {
      .card-header {
        padding: 16px 20px;
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .page-title {
          font-size: 16px;
        }
      }

      .card-content {
        padding: 16px 20px;
      }
    }

    .step-info {
      flex-direction: column;
      gap: 16px;
    }

    .cover-section .cover-img {
      width: 100%;
      max-width: 200px;
      height: 100px;
    }

    .camp-period-info {
      .camp-period-name {
        font-size: 18px;
      }

      .info-grid {
        grid-template-columns: 1fr;
        gap: 10px;
      }
    }
  }
}
</style>
