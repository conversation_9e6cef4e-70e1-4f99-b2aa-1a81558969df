<template>
  <view class="batch-audit-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <view class="fixed-header">
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="批量审核"></navigation-bar>
    </view>

    <scroll-view scroll-y class="audit-content">
      <!-- 操作提示 -->
      <view class="tips-section">
        <view class="tips-card">
          <van-icon name="info-o" size="16" color="#1890ff" />
          <text class="tips-text">选择需要审核的退款申请，可进行批量同意或拒绝操作</text>
        </view>
      </view>

      <!-- 筛选和操作栏 -->
      <view class="toolbar">
        <view class="filter-section">
          <picker :range="statusOptions" :value="selectedStatusIndex" @change="onStatusChange">
            <view class="filter-btn">
              <text>{{ selectedStatus || "全部状态" }}</text>
              <van-icon name="arrow-down" size="12" />
            </view>
          </picker>
        </view>

        <view class="batch-actions">
          <view 
            class="batch-btn" 
            :class="{ disabled: selectedRefunds.length === 0 }"
            @click="handleBatchApprove"
          >
            <van-icon name="passed" size="14" />
            <text>批量同意({{ selectedRefunds.length }})</text>
          </view>
          <view 
            class="batch-btn reject" 
            :class="{ disabled: selectedRefunds.length === 0 }"
            @click="handleBatchReject"
          >
            <van-icon name="close" size="14" />
            <text>批量拒绝({{ selectedRefunds.length }})</text>
          </view>
        </view>
      </view>

      <!-- 退款列表 -->
      <view class="refund-list">
        <view v-if="loading" class="loading-state">
          <van-loading type="spinner" color="#007aff" />
          <text class="loading-text">加载中...</text>
        </view>

        <view v-else-if="refundList.length === 0" class="empty-state">
          <van-empty description="暂无待审核的退款申请" />
        </view>

        <view v-else>
          <!-- 全选操作 -->
          <view class="select-all">
            <van-checkbox 
              :value="isAllSelected" 
              @change="handleSelectAll"
              icon-size="16"
            >
              全选 ({{ refundList.length }}项)
            </van-checkbox>
          </view>

          <!-- 退款项目 -->
          <view 
            v-for="refund in refundList" 
            :key="refund.id" 
            class="refund-item"
            :class="{ selected: isSelected(refund.id) }"
          >
            <!-- 选择框 -->
            <view class="select-section">
              <van-checkbox 
                :value="isSelected(refund.id)" 
                @change="handleSelectRefund(refund)"
                icon-size="16"
              />
            </view>

            <!-- 退款信息 -->
            <view class="refund-content" @click="toggleSelect(refund)">
              <!-- 头部信息 -->
              <view class="refund-header">
                <view class="refund-status">
                  <van-tag 
                    :color="getStatusColor(refund.refundStatus)"
                    plain
                    size="small"
                  >
                    {{ getStatusText(refund.refundStatus) }}
                  </van-tag>
                  <text v-if="getUrgentLevel(refund.applyTime)" class="urgent-tag">
                    {{ getUrgentLevel(refund.applyTime) }}
                  </text>
                </view>
                <view class="refund-amount">
                  <text class="amount-value">¥{{ refund.refundAmount }}</text>
                </view>
              </view>

              <!-- 详细信息 -->
              <view class="refund-info">
                <view class="info-row">
                  <text class="info-label">退款单号</text>
                  <text class="info-value">{{ refund.refundNo }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">订单号</text>
                  <text class="info-value">{{ refund.orderNumber }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">申请时间</text>
                  <text class="info-value">{{ formatTime(refund.applyTime) }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">退款原因</text>
                  <text class="info-value">{{ refund.refundReason }}</text>
                </view>
              </view>
            </view>

            <!-- 单独操作 -->
            <view class="item-actions">
              <view class="action-btn" @click="viewDetail(refund)">
                <van-icon name="eye-o" size="14" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
        <text>加载更多</text>
      </view>
    </scroll-view>

    <!-- 批量审核对话框 -->
    <van-popup 
      :show="batchDialogVisible" 
      position="bottom" 
      :close-on-click-overlay="false"
      @close="closeBatchDialog"
      class="batch-dialog"
    >
      <view class="dialog-content">
        <view class="dialog-header">
          <text class="dialog-title">
            批量{{ batchType === 'approve' ? '同意' : '拒绝' }}退款
          </text>
          <view class="close-btn" @click="closeBatchDialog">
            <van-icon name="cross" size="18" />
          </view>
        </view>

        <view class="batch-summary">
          <text class="summary-text">
            将对 {{ selectedRefunds.length }} 个退款申请进行{{ batchType === 'approve' ? '同意' : '拒绝' }}操作
          </text>
          <text class="summary-amount">
            总金额：¥{{ totalSelectedAmount }}
          </text>
        </view>

        <view class="batch-form">
          <view class="form-item">
            <text class="form-label" :class="{ required: batchType === 'reject' }">
              {{ batchType === 'approve' ? '审核备注' : '拒绝原因' }}
            </text>
            <textarea
              v-model="batchRemark"
              :placeholder="batchType === 'approve' ? '请输入审核备注（可选）' : '请输入拒绝原因（必填）'"
              class="remark-input"
              :maxlength="200"
            />
            <view class="char-count">{{ batchRemark.length }}/200</view>
          </view>
        </view>

        <view class="dialog-actions">
          <view class="action-btn cancel-btn" @click="closeBatchDialog">
            <text>取消</text>
          </view>
          <view 
            class="action-btn confirm-btn" 
            :class="{ disabled: !canBatchSubmit }"
            @click="handleBatchSubmit"
          >
            <van-loading v-if="batchSubmitting" type="spinner" size="16" color="#fff" />
            <text v-else>确认{{ batchType === 'approve' ? '同意' : '拒绝' }}</text>
          </view>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";
import { getRefundAuditList, batchAuditRefunds } from "@/api/refund.api";
import { RefundOrder, REFUND_STATUS_MAP } from "@/types/refund.types";

@Component({
  name: "RefundBatchAudit",
  components: {
    NavigationBar,
  },
})
export default class RefundBatchAudit extends Vue {
  topHeight = 88;
  contentHeight = 0;
  loading = false;
  hasMore = true;
  
  // 数据
  refundList: RefundOrder[] = [];
  selectedRefunds: RefundOrder[] = [];
  
  // 筛选条件
  statusOptions = ["全部状态", "待审核"];
  selectedStatusIndex = 1; // 默认选择待审核
  
  // 分页
  pageNum = 1;
  pageSize = 20;
  
  // 批量操作
  batchDialogVisible = false;
  batchType: "approve" | "reject" = "approve";
  batchRemark = "";
  batchSubmitting = false;

  get selectedStatus(): string {
    return this.selectedStatusIndex > 0 ? this.statusOptions[this.selectedStatusIndex] : "";
  }

  get isAllSelected(): boolean {
    return this.refundList.length > 0 && this.selectedRefunds.length === this.refundList.length;
  }

  get totalSelectedAmount(): string {
    const total = this.selectedRefunds.reduce((sum, refund) => sum + refund.refundAmount, 0);
    return total.toFixed(2);
  }

  get canBatchSubmit(): boolean {
    if (this.batchSubmitting) return false;
    if (this.batchType === "reject" && !this.batchRemark.trim()) return false;
    return true;
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    
    this.loadRefundList();
  }

  // 加载退款列表
  async loadRefundList(refresh = true) {
    if (this.loading) return;
    
    this.loading = true;
    
    if (refresh) {
      this.pageNum = 1;
      this.refundList = [];
      this.selectedRefunds = [];
    }

    try {
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        refundStatus: this.getStatusValue(this.selectedStatus),
      };

      const response = await getRefundAuditList(params);
      
      if (response.code === 0) {
        const data = response.data;
        if (refresh) {
          this.refundList = data.records;
        } else {
          this.refundList.push(...data.records);
        }
        
        this.hasMore = this.refundList.length < data.total;
      } else {
        uni.showToast({
          title: response.msg || "加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载退款列表失败:", error);
      uni.showToast({
        title: "网络错误",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  // 状态筛选
  onStatusChange(e: any) {
    this.selectedStatusIndex = e.detail.value;
    this.loadRefundList();
  }

  // 加载更多
  loadMore() {
    this.pageNum++;
    this.loadRefundList(false);
  }

  // 选择相关方法
  isSelected(refundId: string): boolean {
    return this.selectedRefunds.some(r => r.id === refundId);
  }

  handleSelectRefund(refund: RefundOrder) {
    const index = this.selectedRefunds.findIndex(r => r.id === refund.id);
    if (index > -1) {
      this.selectedRefunds.splice(index, 1);
    } else {
      this.selectedRefunds.push(refund);
    }
  }

  toggleSelect(refund: RefundOrder) {
    this.handleSelectRefund(refund);
  }

  handleSelectAll() {
    if (this.isAllSelected) {
      this.selectedRefunds = [];
    } else {
      this.selectedRefunds = [...this.refundList];
    }
  }

  // 批量操作
  handleBatchApprove() {
    if (this.selectedRefunds.length === 0) return;
    this.batchType = "approve";
    this.batchRemark = "";
    this.batchDialogVisible = true;
  }

  handleBatchReject() {
    if (this.selectedRefunds.length === 0) return;
    this.batchType = "reject";
    this.batchRemark = "";
    this.batchDialogVisible = true;
  }

  closeBatchDialog() {
    this.batchDialogVisible = false;
  }

  async handleBatchSubmit() {
    if (!this.canBatchSubmit) return;

    this.batchSubmitting = true;

    try {
      const params = {
        refundIds: this.selectedRefunds.map(r => r.id),
        auditResult: this.batchType === "approve" ? "APPROVED" : "REJECTED",
        auditRemark: this.batchRemark.trim(),
      };

      const response = await batchAuditRefunds(params);

      if (response.code === 0) {
        uni.showToast({
          title: `批量${this.batchType === "approve" ? "同意" : "拒绝"}成功`,
          icon: "success",
        });
        
        this.closeBatchDialog();
        this.loadRefundList();
      } else {
        uni.showToast({
          title: response.msg || "操作失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("批量审核失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.batchSubmitting = false;
    }
  }

  // 查看详情
  viewDetail(refund: RefundOrder) {
    uni.navigateTo({
      url: `/pages/refund/refund-audit-detail?refundId=${refund.id}`,
    });
  }

  // 工具方法
  getStatusValue(statusText: string): string {
    const statusMap: Record<string, string> = {
      "待审核": "PENDING",
    };
    return statusMap[statusText] || "";
  }

  getStatusText(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.label || status;
  }

  getStatusColor(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.color || "#999";
  }

  getUrgentLevel(applyTime: string): string {
    const now = new Date().getTime();
    const apply = new Date(applyTime).getTime();
    const hours = (now - apply) / (1000 * 60 * 60);
    
    if (hours > 24) return "紧急";
    if (hours > 12) return "较急";
    return "";
  }

  formatTime(time: string): string {
    return new Date(time).toLocaleString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  }
}
</script>

<style lang="scss" scoped>
.batch-audit-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.audit-content {
  flex: 1;
  overflow-y: auto;
}

/* 提示区域 */
.tips-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}

.tips-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
}

.tips-text {
  font-size: 12px;
  color: #1890ff;
  line-height: 1.4;
}

/* 工具栏 */
.toolbar {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f5f5f5;
}

.filter-section {
  margin-bottom: 15px;
}

.filter-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  width: 120px;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.batch-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  background-color: #52c41a;
  color: #fff;

  &.reject {
    background-color: #ff4d4f;
  }

  &.disabled {
    background-color: #d9d9d9;
    color: #999;
  }
}

/* 退款列表 */
.refund-list {
  padding: 0 15px;
}

.select-all {
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  border: 1px solid #f0f0f0;
}

.refund-item {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  border: 1px solid #f0f0f0;

  &.selected {
    border-color: #007aff;
    background-color: #f0f9ff;
  }
}

.select-section {
  padding: 15px;
  border-right: 1px solid #f5f5f5;
}

.refund-content {
  flex: 1;
  padding: 15px;
}

.refund-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.refund-status {
  display: flex;
  align-items: center;
}

.urgent-tag {
  background-color: #ff4d4f;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.amount-value {
  font-size: 16px;
  font-weight: bold;
  color: #ff4444;
}

.refund-info {
  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-label {
    color: #666;
    width: 60px;
    flex-shrink: 0;
  }

  .info-value {
    color: #333;
    flex: 1;
    text-align: right;
  }
}

.item-actions {
  padding: 15px;
  border-left: 1px solid #f5f5f5;
}

.action-btn {
  padding: 8px;
  color: #666;
}

/* 批量对话框 */
.batch-dialog {
  ::v-deep .van-popup {
    border-radius: 16px 16px 0 0;
    max-height: 60vh;
  }
}

.dialog-content {
  padding: 20px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.close-btn {
  padding: 5px;
  color: #666;
}

.batch-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.summary-text {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.summary-amount {
  font-size: 16px;
  font-weight: bold;
  color: #ff4444;
}

.batch-form {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 10px;

  &.required::after {
    content: "*";
    color: #ff4d4f;
    margin-left: 4px;
  }
}

.remark-input {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.dialog-actions {
  display: flex;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #f5f5f5;
}

.action-btn {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #52c41a;
  color: #fff;

  &.disabled {
    background-color: #d9d9d9;
    color: #999;
  }
}

/* 加载状态 */
.loading-state {
  padding: 50px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;
}

.load-more {
  padding: 20px;
  text-align: center;
  color: #007aff;
  font-size: 14px;
}
</style>
