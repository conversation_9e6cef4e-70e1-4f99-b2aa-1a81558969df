# 商城订单退款功能完整设计方案（基于现有架构优化）

## 1. 现有架构分析

### 1.1 现有订单状态枚举
基于现有的 `OrderStateType` 枚举，需要扩展退款相关状态：

```java
public enum OrderStateType {
    pending("pending", "待付款"),
    paid("paid", "已付款"),
    shipped("shipped", "待收货"),
    completed("completed", "已完成"),
    cancelled("cancelled", "已取消"),
    refund("refund", "退款中"),
    
    // 扩展退款状态
    refund_processing("refund_processing", "退款处理中"),
    refund_success("refund_success", "退款成功"),
    refund_failed("refund_failed", "退款失败");
}
```

### 1.2 现有微信支付集成
项目已集成微信支付V3 API，具备以下能力：
- `WxCustomerPayUtils.createOrder()` - 统一下单
- `WxCustomerPayUtils.queryOrderByTradeNo()` - 订单查询
- `WxCustomerPayUtils.wxPayRefund()` - 退款申请
- `WxCustomerPayUtils.handlePayCallback()` - 支付回调处理

### 1.3 现有数据库字段
`MallOrder` 实体已包含必要字段：
- `orderNumber` - 订单号
- `customerId` - 用户ID
- `companyId` - 归属公司ID（支持多商户）
- `paymentMethod` - 支付方式
- `totalAmount` - 订单总金额
- `prepayId` - 微信预支付ID
- `paymentExpireTime` - 支付过期时间
- `isExpired` - 是否过期

### 1.4 现有请求响应对象模式
项目采用统一的请求响应对象设计模式：
- Request对象：如 `MallOrderRequest`
- Response对象：如 `MallOrderResponse`
- 内部类设计：如 `MallOrderRequest.OrderQueryRequest`

## 2. 功能需求（基于现有架构）

### 2.1 功能描述
- 支持用户对已支付订单申请退款
- **仅支持微信支付订单的退款，原路退回到用户支付账户**
- 支持全额退款（暂不开放部分退款）
- 退款申请需要上传图片凭证（最多5张，参考现有评论功能的图片处理方式）
- 商家审核机制
- 自动审核：未发货订单自动审核通过
- 人工审核：已发货或已完成订单需要人工审核
- **不支持退款到钱包余额或其他账户**

### 2.2 适用订单状态
- `paid`：已付款（自动审核通过）
- `shipped`：已发货（需人工审核）
- `completed`：已完成（需人工审核）

### 2.3 退款流程
```
用户申请退款 → 系统验证 → 自动/人工审核 → 微信原路退款 → 退款完成
```

### 2.4 退款方式限制
- **仅支持微信支付原路退款**：退款金额将原路退回到用户的微信支付账户
- **不支持钱包余额退款**：系统不提供退款到平台钱包余额的功能
- **不支持其他支付方式**：非微信支付的订单不支持退款申请

## 3. 数据库设计（基于现有结构优化）

### 3.1 订单退款主表 `mall_order_refunds`

```sql
CREATE TABLE mall_order_refunds (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    refund_no VARCHAR(32) UNIQUE NOT NULL COMMENT '退款单号',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    order_number VARCHAR(32) NOT NULL COMMENT '订单号（冗余）',
    customer_id BIGINT NOT NULL COMMENT '用户ID',
    company_id BIGINT COMMENT '归属公司ID（预留字段，暂不开放多商户功能）',
    
    -- 退款基本信息
    refund_type VARCHAR(20) NOT NULL DEFAULT 'FULL' COMMENT '退款类型：FULL-全额退款',
    refund_reason VARCHAR(200) NOT NULL COMMENT '退款原因',
    refund_description VARCHAR(500) COMMENT '退款详细说明',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '申请退款金额',
    refund_images JSON COMMENT '退款凭证图片（JSON数组，最多5张，存储格式参考评论功能）',
    
    -- 退款状态管理
    refund_status VARCHAR(20) NOT NULL COMMENT '退款状态：PENDING-待审核，APPROVED-已同意，REJECTED-已拒绝，PROCESSING-退款处理中，SUCCESS-退款成功，FAILED-退款失败',
    status_text VARCHAR(20) NOT NULL COMMENT '状态文本',
    
    -- 审核信息
    audit_result VARCHAR(20) COMMENT '审核结果：APPROVED-同意退款，REJECTED-拒绝退款',
    audit_remark VARCHAR(500) COMMENT '审核备注',
    audit_user_id BIGINT COMMENT '审核人ID',
    audit_user_name VARCHAR(50) COMMENT '审核人姓名',
    audit_time DATETIME COMMENT '审核时间',
    
    -- 微信退款信息（与现有微信支付集成保持一致）
    wx_refund_id VARCHAR(64) COMMENT '微信退款单号',
    wx_transaction_id VARCHAR(32) COMMENT '微信支付订单号',
    wx_out_refund_no VARCHAR(64) COMMENT '商户退款单号',
    wx_refund_account VARCHAR(50) COMMENT '退款入账账户',
    wx_success_time DATETIME COMMENT '退款成功时间',
    wx_refund_channel VARCHAR(20) DEFAULT 'ORIGINAL' COMMENT '退款渠道：ORIGINAL-原路退回（微信支付）',
    
    -- 时间字段（与现有订单表风格保持一致）
    apply_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    process_time DATETIME COMMENT '处理时间',
    complete_time DATETIME COMMENT '完成时间',
    
    -- 系统字段（与现有表结构保持一致）
    remark VARCHAR(500) COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_order_id (order_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_company_id (company_id),
    INDEX idx_refund_status (refund_status),
    INDEX idx_refund_no (refund_no),
    INDEX idx_apply_time (apply_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单退款申请表';
```

### 3.2 退款进度记录表 `mall_refund_progress`

```sql
CREATE TABLE mall_refund_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    refund_id BIGINT NOT NULL COMMENT '退款ID',
    progress_status VARCHAR(20) NOT NULL COMMENT '进度状态',
    status_text VARCHAR(50) NOT NULL COMMENT '状态文本',
    description VARCHAR(500) NOT NULL COMMENT '进度描述',
    operate_time DATETIME NOT NULL COMMENT '操作时间',
    operator VARCHAR(50) COMMENT '操作人',
    
    -- 系统字段（与现有表结构保持一致）
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    
    INDEX idx_refund_id (refund_id),
    INDEX idx_operate_time (operate_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款进度记录表';
```

## 4. 实体类设计（基于现有BaseEntity）

### 4.1 MallOrderRefund 实体类

```java
package cn.hxsy.entity;

import cn.hxsy.datasource.model.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单退款实体类
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MallOrderRefund", description = "订单退款信息")
@TableName("mall_order_refunds")
public class MallOrderRefund extends BaseEntity {

    @ApiModelProperty(value = "退款单号")
    @TableField("refund_no")
    private String refundNo;

    @ApiModelProperty(value = "订单ID")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "订单号")
    @TableField("order_number")
    private String orderNumber;

    @ApiModelProperty(value = "用户ID")
    @TableField("customer_id")
    private Long customerId;

    @ApiModelProperty(value = "归属公司ID（预留字段）")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty(value = "退款类型")
    @TableField("refund_type")
    private String refundType;

    @ApiModelProperty(value = "退款原因")
    @TableField("refund_reason")
    private String refundReason;

    @ApiModelProperty(value = "退款详细说明")
    @TableField("refund_description")
    private String refundDescription;

    @ApiModelProperty(value = "申请退款金额")
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款凭证图片JSON数组")
    @TableField("refund_images")
    private String refundImages;

    @ApiModelProperty(value = "退款状态")
    @TableField("refund_status")
    private String refundStatus;

    @ApiModelProperty(value = "状态文本")
    @TableField("status_text")
    private String statusText;

    @ApiModelProperty(value = "审核结果")
    @TableField("audit_result")
    private String auditResult;

    @ApiModelProperty(value = "审核备注")
    @TableField("audit_remark")
    private String auditRemark;

    @ApiModelProperty(value = "审核人ID")
    @TableField("audit_user_id")
    private Long auditUserId;

    @ApiModelProperty(value = "审核人姓名")
    @TableField("audit_user_name")
    private String auditUserName;

    @ApiModelProperty(value = "审核时间")
    @TableField("audit_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "微信退款单号")
    @TableField("wx_refund_id")
    private String wxRefundId;

    @ApiModelProperty(value = "微信支付订单号")
    @TableField("wx_transaction_id")
    private String wxTransactionId;

    @ApiModelProperty(value = "商户退款单号")
    @TableField("wx_out_refund_no")
    private String wxOutRefundNo;

    @ApiModelProperty(value = "退款入账账户")
    @TableField("wx_refund_account")
    private String wxRefundAccount;

    @ApiModelProperty(value = "退款成功时间")
    @TableField("wx_success_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime wxSuccessTime;

    @ApiModelProperty(value = "退款渠道")
    @TableField("wx_refund_channel")
    private String wxRefundChannel;

    @ApiModelProperty(value = "申请时间")
    @TableField("apply_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "处理时间")
    @TableField("process_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processTime;

    @ApiModelProperty(value = "完成时间")
    @TableField("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completeTime;

    // 非数据库字段
    @ApiModelProperty(value = "退款凭证图片列表")
    @TableField(exist = false)
    private List<String> refundImageList;
}
```

## 5. 请求响应对象设计（与现有风格保持一致）

### 5.1 ApplyRefundRequest（基于现有请求对象风格）

```java
package cn.hxsy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 用户申请退款请求（与现有MallOrderRequest风格保持一致）
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@ApiModel("申请退款请求")
public class ApplyRefundRequest {

    @ApiModelProperty("订单ID")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @ApiModelProperty("用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long customerId;

    @ApiModelProperty("退款类型：FULL-全额退款")
    @NotBlank(message = "退款类型不能为空")
    private String refundType = "FULL";

    @ApiModelProperty("退款原因")
    @NotBlank(message = "退款原因不能为空")
    @Size(max = 200, message = "退款原因不能超过200个字符")
    private String refundReason;

    @ApiModelProperty("退款详细说明")
    @Size(max = 500, message = "退款说明不能超过500个字符")
    private String refundDescription;

    @ApiModelProperty("退款凭证图片URL列表（最多5张，参考评论功能模式）")
    @Size(max = 5, message = "退款凭证图片最多5张")
    private List<String> refundImages;
}
```

### 5.2 RefundQueryRequest（基于现有查询对象模式）

```java
package cn.hxsy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 退款查询请求（与MallOrderRequest.OrderQueryRequest风格保持一致）
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@ApiModel("退款查询请求")
public class RefundQueryRequest {

    @ApiModelProperty("用户ID（用户端必填，商家端可选）")
    private Long customerId;

    @ApiModelProperty("退款单号")
    private String refundNo;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("退款状态")
    private String refundStatus;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;
}
```

### 5.3 RefundResponse（基于现有响应对象风格）

```java
package cn.hxsy.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 退款响应对象（与MallOrderResponse风格保持一致）
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Data
@ApiModel("退款响应")
public class RefundResponse {

    @ApiModelProperty("退款ID")
    private Long id;

    @ApiModelProperty("退款单号")
    private String refundNo;

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("用户ID")
    private Long customerId;

    @ApiModelProperty("退款类型")
    private String refundType;

    @ApiModelProperty("退款原因")
    private String refundReason;

    @ApiModelProperty("退款说明")
    private String refundDescription;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("退款凭证图片列表")
    private List<String> refundImages;

    @ApiModelProperty("退款状态")
    private String refundStatus;

    @ApiModelProperty("状态文本")
    private String statusText;

    @ApiModelProperty("审核结果")
    private String auditResult;

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("审核人姓名")
    private String auditUserName;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime auditTime;

    @ApiModelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;

    @ApiModelProperty("处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processTime;

    @ApiModelProperty("完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completeTime;

    @ApiModelProperty("微信退款成功时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime wxSuccessTime;
}
```

## 6. 控制器设计（基于现有MallOrderController风格）

### 6.1 MallRefundController

```java
package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.request.ApplyRefundRequest;
import cn.hxsy.request.RefundQueryRequest;
import cn.hxsy.response.RefundResponse;
import cn.hxsy.response.RefundDetailResponse;
import cn.hxsy.service.MallRefundService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 退款管理控制器（与MallOrderController风格保持一致）
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Api(tags = "退款管理")
@RestController
@RequestMapping("/api/v1/mall/refund")
@RequiredArgsConstructor
@Slf4j
public class MallRefundController {

    private final MallRefundService mallRefundService;

    /**
     * 申请退款
     *
     * @param request 退款申请请求
     * @return 退款单号
     */
    @ApiOperation("申请退款")
    @PostMapping("/apply")
    public Result<String> applyRefund(
            @ApiParam("退款申请请求") @Valid @RequestBody ApplyRefundRequest request) {
        try {
            String refundNo = mallRefundService.applyRefund(request);
            return Result.ok(refundNo);
        } catch (Exception e) {
            log.error("申请退款失败，请求参数：{}，异常信息：", request, e);
            return Result.error("申请退款失败：" + e.getMessage());
        }
    }

    /**
     * 查询我的退款列表
     *
     * @param request 查询条件
     * @return 退款列表
     */
    @ApiOperation("查询我的退款列表")
    @PostMapping("/my-list")
    public Result<IPage<RefundResponse>> getMyRefundList(
            @ApiParam("查询条件") @RequestBody RefundQueryRequest request) {
        try {
            IPage<RefundResponse> result = mallRefundService.getRefundList(request);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("查询退款列表失败，请求参数：{}，异常信息：", request, e);
            return Result.error("查询退款列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取退款详情
     *
     * @param refundId 退款ID
     * @param customerId 用户ID
     * @return 退款详情
     */
    @ApiOperation("获取退款详情")
    @GetMapping("/detail/{refundId}")
    public Result<RefundDetailResponse> getRefundDetail(
            @ApiParam("退款ID") @PathVariable String refundId,
            @ApiParam("用户ID") @RequestParam String customerId) {
        try {
            Long refundIdLong = Long.valueOf(refundId);
            Long customerIdLong = Long.valueOf(customerId);
            RefundDetailResponse result = mallRefundService.getRefundDetail(refundIdLong, customerIdLong);
            return Result.ok(result);
        } catch (NumberFormatException e) {
            log.error("获取退款详情参数格式错误，refundId：{}，customerId：{}，异常信息：", refundId, customerId, e);
            return Result.error("参数格式错误：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取退款详情失败，refundId：{}，customerId：{}，异常信息：", refundId, customerId, e);
            return Result.error("获取退款详情失败：" + e.getMessage());
        }
    }

    /**
     * 注意：不需要单独开发图片上传接口
     * 直接复用现有的文件上传接口，参考评论功能的图片上传方式
     * 前端调用现有的文件上传接口获取图片URL，然后在申请退款时传入URL列表
     */
    
    // @ApiOperation("上传退款凭证图片 - 复用现有文件上传接口")
    // 无需实现，直接使用现有的通用图片上传接口
}
```

## 7. 服务层设计（基于评论功能的图片处理模式）

### 7.1 MallRefundService 接口

```java
package cn.hxsy.service;

import cn.hxsy.entity.MallOrderRefund;
import cn.hxsy.request.ApplyRefundRequest;
import cn.hxsy.request.RefundQueryRequest;
import cn.hxsy.response.RefundResponse;
import cn.hxsy.response.RefundDetailResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 退款服务接口
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
public interface MallRefundService extends IService<MallOrderRefund> {

    /**
     * 申请退款
     *
     * @param request 退款申请请求
     * @return 退款单号
     */
    String applyRefund(ApplyRefundRequest request);

    /**
     * 查询退款列表
     *
     * @param request 查询条件
     * @return 退款列表
     */
    IPage<RefundResponse> getRefundList(RefundQueryRequest request);

    /**
     * 获取退款详情
     *
     * @param refundId 退款ID
     * @param customerId 用户ID
     * @return 退款详情
     */
    RefundDetailResponse getRefundDetail(Long refundId, Long customerId);
}
```

### 7.2 MallRefundServiceImpl 实现类（参考评论功能的图片处理）

```java
package cn.hxsy.service.impl;

import cn.hxsy.entity.MallOrder;
import cn.hxsy.entity.MallOrderRefund;
import cn.hxsy.mapper.MallOrderMapper;
import cn.hxsy.mapper.MallOrderRefundMapper;
import cn.hxsy.request.ApplyRefundRequest;
import cn.hxsy.request.RefundQueryRequest;
import cn.hxsy.response.RefundDetailResponse;
import cn.hxsy.response.RefundResponse;
import cn.hxsy.service.MallRefundService;
import cn.hxsy.utils.WxCustomerPayUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 退款服务实现类
 * 参考 MallProductReviewServiceImpl 的图片处理模式
 *
 * <AUTHOR>
 * @date 2025-09-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MallRefundServiceImpl extends ServiceImpl<MallOrderRefundMapper, MallOrderRefund> 
        implements MallRefundService {

    private final MallOrderRefundMapper refundMapper;
    private final MallOrderMapper orderMapper;
    private final WxCustomerPayUtils wxCustomerPayUtils;
    // 使用与评论功能相同的ObjectMapper
    private final ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String applyRefund(ApplyRefundRequest request) {
        try {
            // 1. 验证订单是否存在且属于当前用户
            MallOrder order = orderMapper.selectById(request.getOrderId());
            if (order == null || !order.getCustomerId().equals(request.getCustomerId())) {
                log.warn("订单不存在或不属于当前用户: orderId={}, customerId={}", 
                        request.getOrderId(), request.getCustomerId());
                throw new RuntimeException("订单不存在或不属于当前用户");
            }

            // 2. 验证订单状态是否可以申请退款
            if (!canApplyRefund(order.getOrderStatus())) {
                log.warn("订单状态不允许申请退款: orderId={}, status={}",
                        request.getOrderId(), order.getOrderStatus());
                throw new RuntimeException("当前订单状态不支持退款申请");
            }

            // 3. 验证是否已存在退款申请
            LambdaQueryWrapper<MallOrderRefund> existQuery = new LambdaQueryWrapper<>();
            existQuery.eq(MallOrderRefund::getOrderId, request.getOrderId())
                     .eq(MallOrderRefund::getCustomerId, request.getCustomerId())
                     .notIn(MallOrderRefund::getRefundStatus, "FAILED", "REJECTED");
            long existCount = count(existQuery);
            if (existCount > 0) {
                log.warn("订单已存在退款申请: orderId={}, customerId={}",
                        request.getOrderId(), request.getCustomerId());
                throw new RuntimeException("该订单已存在退款申请，请勿重复申请");
            }

            // 4. 创建退款记录
            MallOrderRefund refund = new MallOrderRefund();
            refund.setRefundNo(generateRefundNo());
            refund.setOrderId(request.getOrderId());
            refund.setOrderNumber(order.getOrderNumber());
            refund.setCustomerId(request.getCustomerId());
            // 预留多商户字段，暂时设置为null或使用默认值
            refund.setCompanyId(order.getCompanyId());
            refund.setRefundType(request.getRefundType());
            refund.setRefundReason(request.getRefundReason());
            refund.setRefundDescription(request.getRefundDescription());
            refund.setRefundAmount(order.getTotalAmount());
            refund.setWxRefundChannel("ORIGINAL"); // 固定为原路退回
            refund.setApplyTime(LocalDateTime.now());

            // 5. 处理退款凭证图片列表（参考评论功能的图片处理模式）
            if (!CollectionUtils.isEmpty(request.getRefundImages())) {
                try {
                    // 验证图片数量（最多5张，比评论功能少1张）
                    if (request.getRefundImages().size() > 5) {
                        throw new RuntimeException("退款凭证图片最多只能上传5张");
                    }
                    
                    // 使用ObjectMapper将List<String>序列化为JSON存储
                    // 这与MallProductReviewServiceImpl中的处理方式完全一致
                    refund.setRefundImages(objectMapper.writeValueAsString(request.getRefundImages()));
                    log.info("退款凭证图片处理成功，图片数量: {}", request.getRefundImages().size());
                } catch (JsonProcessingException e) {
                    log.error("序列化退款凭证图片列表失败", e);
                    refund.setRefundImages("[]");
                }
            } else {
                refund.setRefundImages("[]");
            }

            // 6. 根据订单状态确定退款状态和审核结果
            if ("paid".equals(order.getOrderStatus())) {
                // 未发货订单，自动审核通过
                refund.setRefundStatus("APPROVED");
                refund.setStatusText("已同意退款");
                refund.setAuditResult("APPROVED");
                refund.setAuditRemark("未发货订单，自动审核通过");
                refund.setAuditUserName("系统自动审核");
                refund.setAuditTime(LocalDateTime.now());
            } else {
                // 已发货或已完成订单，需要人工审核
                refund.setRefundStatus("PENDING");
                refund.setStatusText("待审核");
            }

            // 7. 保存退款记录
            refundMapper.insert(refund);

            log.info("退款申请提交成功: refundNo={}, orderId={}, customerId={}", 
                    refund.getRefundNo(), request.getOrderId(), request.getCustomerId());
            
            return refund.getRefundNo();

        } catch (Exception e) {
            log.error("申请退款失败: orderId={}, customerId={}", 
                    request.getOrderId(), request.getCustomerId(), e);
            throw new RuntimeException("申请退款失败: " + e.getMessage());
        }
    }

    @Override
    public IPage<RefundResponse> getRefundList(RefundQueryRequest request) {
        Page<RefundResponse> page = new Page<>(request.getPageNum(), request.getPageSize());
        return refundMapper.selectRefundPage(page, request);
    }

    @Override
    public RefundDetailResponse getRefundDetail(Long refundId, Long customerId) {
        // 验证退款记录是否属于当前用户
        LambdaQueryWrapper<MallOrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MallOrderRefund::getId, refundId)
                   .eq(MallOrderRefund::getCustomerId, customerId);
        
        MallOrderRefund refund = getOne(queryWrapper);
        if (refund == null) {
            throw new RuntimeException("退款记录不存在或不属于当前用户");
        }

        RefundDetailResponse response = new RefundDetailResponse();
        // 复制基本信息
        // BeanUtils.copyProperties(refund, response);
        
        // 处理退款凭证图片列表（参考评论功能的图片反序列化）
        if (refund.getRefundImages() != null && !"[]".equals(refund.getRefundImages())) {
            try {
                // 使用ObjectMapper将JSON字符串反序列化为List<String>
                // 这与评论功能在前端展示时的处理方式一致
                List<String> imageList = objectMapper.readValue(
                    refund.getRefundImages(), 
                    new TypeReference<List<String>>() {}
                );
                response.setRefundImages(imageList);
            } catch (JsonProcessingException e) {
                log.error("反序列化退款凭证图片列表失败: refundId={}", refundId, e);
                response.setRefundImages(new ArrayList<>());
            }
        } else {
            response.setRefundImages(new ArrayList<>());
        }

        return response;
    }

    /**
     * 验证订单状态是否可以申请退款
     */
    private boolean canApplyRefund(String orderStatus) {
        return "paid".equals(orderStatus) || 
               "shipped".equals(orderStatus) || 
               "completed".equals(orderStatus);
    }

    /**
     * 生成退款单号
     */
    private String generateRefundNo() {
        return "RF" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + 
               String.valueOf(System.currentTimeMillis() % 10000);
    }
}
```

## 8. 配置说明（基于现有配置结构）

## 9. 基于现有架构的核心优化点

### 9.1 复用现有微信支付工具类
直接使用现有的 `WxCustomerPayUtils.wxPayRefund()` 方法，无需重复开发

### 9.2 继承现有BaseEntity
所有新增实体类都继承现有的 `BaseEntity`，保持数据结构一致性

### 9.3 遵循现有命名规范
- 实体类：`Mall` + 功能 + 实体类型
- 请求对象：功能 + `Request`
- 响应对象：功能 + `Response`
- 控制器：`Mall` + 功能 + `Controller`

### 9.4 预留多商户扩展能力
数据库表设计中预留 `company_id` 字段，与现有订单系统保持一致，暂不开放多商户功能，为将来扩展做准备

### 9.5 统一异常处理
复用现有的异常处理机制和日志记录方式

### 9.6 参考评论功能的图片处理模式
- **实体设计**：使用 `String images` 字段存储JSON格式的图片数组
- **请求对象**：使用 `List<String> images` 接收图片URL列表
- **服务层处理**：使用 `ObjectMapper.writeValueAsString()` 序列化存储
- **验证约束**：使用 `@Size(max = 5)` 限制图片数量
- **图片上传**：复用现有文件上传接口，无需单独开发

### 9.7 完全复用现有文件上传基础设施
- 图片上传接口已存在，无需修改配置
- 图片存储和访问的COSClient配置无需变更
- 图片验证规则复用现有逻辑
- 前端交互模式与评论功能保持一致

## 8. 配置说明（基于现有配置结构）

### 8.1 复用现有文件上传配置

**注意：不需要修改任何配置文件，完全复用现有的文件上传接口和配置**

基于现有评论功能的图片上传实现：
- ✅ 图片上传接口已存在，无需重新开发
- ✅ 图片URL获取方式与评论功能保持一致
- ✅ 图片存储和访问配置复用现有COSClient配置
- ✅ 图片验证规则复用现有逻辑
- ✅ 业务层面控制：最多5张图片（比评论功能少1张）

### 8.2 微信支付退款配置

**注意：不需要修改微信支付相关配置，直接使用现有配置**

- ✅ 复用现有微信支付V3 API配置
- ✅ 使用现有的WxCustomerPayUtils工具类
- ✅ 退款回调复用现有的支付回调处理机制
- ✅ 退款渠道固定为ORIGINAL（原路退回）
- ✅ 超时和重试机制使用现有微信支付配置

## 10. 开发优势

### 10.1 最小化代码改动
- 复用现有微信支付集成
- 继承现有实体类结构
- 遵循现有设计模式
- **完全复用现有文件上传接口**

### 10.2 保持架构一致性
- 统一的数据表设计风格
- 一致的API接口风格
- 相同的异常处理机制
- **与评论功能一致的图片处理模式**

### 10.3 支持现有业务场景
- 预留多商户架构扩展能力
- 支持分库分表扩展
- 保持现有权限控制
- **无需修改任何配置文件和基础设施**

### 10.4 便于维护和扩展
- 清晰的模块划分
- 完整的文档说明
- 标准化的开发流程
- **参考成熟的评论功能实现，降低开发风险**

## 11. 开发清单（优先级排序）

### 11.1 第一阶段：核心功能
- [ ] 创建退款相关数据库表
- [ ] 实现MallOrderRefund实体类
- [ ] 实现基础的请求响应对象
- [ ] 开发MallRefundController核心接口
- [ ] 集成现有微信支付退款功能
- [ ] **实现基于评论功能模式的图片处理逻辑**

### 11.2 第二阶段：业务完善
- [ ] 实现退款申请业务逻辑
- [ ] 开发退款审核机制
- [ ] **验证图片上传和存储功能是否正常工作**
- [ ] 实现退款状态流转
- [ ] **测试图片序列化和反序列化功能**

### 11.3 第三阶段：系统集成
- [ ] 微信退款回调处理
- [ ] 退款状态同步机制
- [ ] 完善异常处理
- [ ] 单元测试和集成测试
- [ ] **测试图片数量限制和数据校验功能**

### 11.4 第四阶段：优化部署
- [ ] 性能优化
- [ ] 安全加固
- [ ] 部署配置
- [ ] 监控告警
- [ ] **图片存储和访问性能优化**

## 12. 总结

此优化方案基于您现有的代码架构，特别参考了评论功能的文件上传实现模式，实现了：

### 12.1 核心优化点
- **零修改配置文件**：不需要修改任何yaml配置文件
- **零修改文件上传配置**：直接复用现有文件上传接口
- **一致的图片处理模式**：与评论功能保持相同的实现方式
- **最小化架构变更**：最大程度复用现有组件
- **低风险实现**：参考成熟的功能模块实现
- **预留扩展能力**：适当预留多商户等扩展字段

### 12.2 技术特点
- **JSON字段存储图片**：与`MallProductReview.images`字段保持一致
- **ObjectMapper序列化**：复用`MallProductReviewServiceImpl`的图片处理逻辑
- **数量限制验证**：参考`SubmitReviewRequest`的验证模式
- **完整的数据一致性**：保证前端展示和后端存储的一致性
- **零配置修改**：不需要修改任何现有配置文件
- **预留字段设计**：为未来多商户扩展做准备

### 12.3 业务优势
- **无缝集成**：与现有系统完全兼容
- **零学习成本**：与评论功能相同的使用方式
- **快速上线**：减少开发和测试工作量
- **稳定可靠**：基于成熟的技术方案实现

该方案确保了新功能与现有系统的完美集成，同时最大化利用了现有的技本组件和业务逻辑。**特别注意：本方案不需要修改任何现有配置文件，保证了实施的低风险性。**