<template>
  <view class="refund-audit-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <view class="fixed-header">
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="退款审核"></navigation-bar>
    </view>

    <scroll-view scroll-y class="audit-content">
      <!-- 统计卡片 -->
      <view class="stats-section">
        <view class="stats-card">
          <view class="stats-item">
            <text class="stats-number">{{ statistics.pendingCount }}</text>
            <text class="stats-label">待审核</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ statistics.todayCount }}</text>
            <text class="stats-label">今日申请</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ statistics.totalAmount }}</text>
            <text class="stats-label">总金额</text>
          </view>
        </view>
      </view>

      <!-- 筛选条件 -->
      <view class="filter-section">
        <view class="filter-row">
          <picker :range="statusOptions" :value="selectedStatusIndex" @change="onStatusChange">
            <view class="filter-item">
              <text class="filter-label">状态</text>
              <view class="filter-value">
                <text>{{ selectedStatus || "全部状态" }}</text>
                <van-icon name="arrow-down" size="12" />
              </view>
            </view>
          </picker>
          
          <view class="filter-item" @click="showDatePicker = true">
            <text class="filter-label">日期</text>
            <view class="filter-value">
              <text>{{ selectedDate || "选择日期" }}</text>
              <van-icon name="arrow-down" size="12" />
            </view>
          </view>
        </view>
        
        <view class="search-row">
          <view class="search-input">
            <van-icon name="search" size="16" />
            <input 
              type="text" 
              placeholder="搜索退款单号或订单号" 
              v-model="searchKeyword"
              @confirm="handleSearch"
            />
          </view>
          <view class="search-btn" @click="handleSearch">
            <text>搜索</text>
          </view>
        </view>
      </view>

      <!-- 退款列表 -->
      <view class="refund-list">
        <view v-if="loading" class="loading-state">
          <van-loading type="spinner" color="#007aff" />
          <text class="loading-text">加载中...</text>
        </view>

        <view v-else-if="refundList.length === 0" class="empty-state">
          <van-empty description="暂无退款申请" />
        </view>

        <view v-else>
          <view 
            v-for="refund in refundList" 
            :key="refund.id" 
            class="refund-item"
            @click="viewRefundDetail(refund)"
          >
            <!-- 退款状态和金额 -->
            <view class="refund-header">
              <view class="refund-status">
                <van-tag 
                  :color="getStatusColor(refund.refundStatus)"
                  plain
                  size="small"
                >
                  {{ getStatusText(refund.refundStatus) }}
                </van-tag>
                <text v-if="refund.refundStatus === 'PENDING'" class="urgent-tag">
                  {{ getUrgentLevel(refund.applyTime) }}
                </text>
              </view>
              <view class="refund-amount">
                <text class="amount-value">¥{{ refund.refundAmount }}</text>
              </view>
            </view>

            <!-- 退款信息 -->
            <view class="refund-info">
              <view class="info-row">
                <text class="info-label">退款单号</text>
                <text class="info-value">{{ refund.refundNo }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">订单号</text>
                <text class="info-value">{{ refund.orderNumber }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">申请时间</text>
                <text class="info-value">{{ formatTime(refund.applyTime) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">退款原因</text>
                <text class="info-value">{{ refund.refundReason }}</text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view v-if="refund.refundStatus === 'PENDING'" class="refund-actions">
              <view class="action-btn reject-btn" @click.stop="handleReject(refund)">
                <text>拒绝</text>
              </view>
              <view class="action-btn approve-btn" @click.stop="handleApprove(refund)">
                <text>同意</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
        <text>加载更多</text>
      </view>
    </scroll-view>

    <!-- 日期选择器 -->
    <van-datetime-picker
      v-model="currentDate"
      type="date"
      :show="showDatePicker"
      @confirm="onDateConfirm"
      @cancel="showDatePicker = false"
    />

    <!-- 审核对话框 -->
    <refund-audit-dialog
      :visible.sync="auditDialogVisible"
      :refund-info="selectedRefund"
      :audit-type="auditType"
      @audit-success="handleAuditSuccess"
    />
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import RefundAuditDialog from "./components/RefundAuditDialog.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";
import { getRefundAuditList, getRefundStatistics } from "@/api/refund.api";
import { RefundOrder, REFUND_STATUS_MAP } from "@/types/refund.types";

interface RefundStatistics {
  pendingCount: number;
  todayCount: number;
  totalAmount: string;
}

@Component({
  name: "RefundAuditList",
  components: {
    NavigationBar,
    RefundAuditDialog,
  },
})
export default class RefundAuditList extends Vue {
  topHeight = 88;
  contentHeight = 0;
  loading = false;
  hasMore = true;
  
  // 数据
  refundList: RefundOrder[] = [];
  statistics: RefundStatistics = {
    pendingCount: 0,
    todayCount: 0,
    totalAmount: "0.00",
  };
  
  // 筛选条件
  statusOptions = ["全部状态", "待审核", "已同意", "已拒绝"];
  selectedStatusIndex = 0;
  selectedDate = "";
  searchKeyword = "";
  
  // 分页
  pageNum = 1;
  pageSize = 10;
  
  // 弹窗
  showDatePicker = false;
  currentDate = new Date();
  auditDialogVisible = false;
  selectedRefund: RefundOrder | null = null;
  auditType: "approve" | "reject" = "approve";

  get selectedStatus(): string {
    return this.selectedStatusIndex > 0 ? this.statusOptions[this.selectedStatusIndex] : "";
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    
    this.loadRefundList();
    this.loadStatistics();
  }

  // 加载退款列表
  async loadRefundList(refresh = true) {
    if (this.loading) return;
    
    this.loading = true;
    
    if (refresh) {
      this.pageNum = 1;
      this.refundList = [];
    }

    try {
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        refundStatus: this.getStatusValue(this.selectedStatus),
        searchKeyword: this.searchKeyword,
        applyDate: this.selectedDate,
      };

      const response = await getRefundAuditList(params);
      
      if (response.code === 0) {
        const data = response.data;
        if (refresh) {
          this.refundList = data.records;
        } else {
          this.refundList.push(...data.records);
        }
        
        this.hasMore = this.refundList.length < data.total;
      } else {
        uni.showToast({
          title: response.msg || "加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载退款列表失败:", error);
      uni.showToast({
        title: "网络错误",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  // 加载统计数据
  async loadStatistics() {
    try {
      const response = await getRefundStatistics();
      if (response.code === 0) {
        this.statistics = response.data;
      }
    } catch (error) {
      console.error("加载统计数据失败:", error);
    }
  }

  // 状态筛选
  onStatusChange(e: any) {
    this.selectedStatusIndex = e.detail.value;
    this.loadRefundList();
  }

  // 日期选择
  onDateConfirm() {
    this.selectedDate = this.formatDate(this.currentDate);
    this.showDatePicker = false;
    this.loadRefundList();
  }

  // 搜索
  handleSearch() {
    this.loadRefundList();
  }

  // 加载更多
  loadMore() {
    this.pageNum++;
    this.loadRefundList(false);
  }

  // 查看详情
  viewRefundDetail(refund: RefundOrder) {
    uni.navigateTo({
      url: `/pages/refund/refund-audit-detail?refundId=${refund.id}`,
    });
  }

  // 同意退款
  handleApprove(refund: RefundOrder) {
    this.selectedRefund = refund;
    this.auditType = "approve";
    this.auditDialogVisible = true;
  }

  // 拒绝退款
  handleReject(refund: RefundOrder) {
    this.selectedRefund = refund;
    this.auditType = "reject";
    this.auditDialogVisible = true;
  }

  // 审核成功回调
  handleAuditSuccess() {
    this.loadRefundList();
    this.loadStatistics();
  }

  // 工具方法
  getStatusValue(statusText: string): string {
    const statusMap: Record<string, string> = {
      "待审核": "PENDING",
      "已同意": "APPROVED",
      "已拒绝": "REJECTED",
    };
    return statusMap[statusText] || "";
  }

  getStatusText(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.label || status;
  }

  getStatusColor(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.color || "#999";
  }

  getUrgentLevel(applyTime: string): string {
    const now = new Date().getTime();
    const apply = new Date(applyTime).getTime();
    const hours = (now - apply) / (1000 * 60 * 60);
    
    if (hours > 24) return "紧急";
    if (hours > 12) return "较急";
    return "";
  }

  formatTime(time: string): string {
    return new Date(time).toLocaleString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  formatDate(date: Date): string {
    return date.toISOString().split("T")[0];
  }
}
</script>

<style lang="scss" scoped>
.refund-audit-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.audit-content {
  flex: 1;
  overflow-y: auto;
}

/* 统计卡片 */
.stats-section {
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}

.stats-card {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-number {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

/* 筛选区域 */
.filter-section {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.filter-row {
  display: flex;
  margin-bottom: 15px;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-right: 10px;

  &:last-child {
    margin-right: 0;
  }
}

.filter-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.filter-value {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.search-row {
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-right: 10px;

  input {
    flex: 1;
    border: none;
    background: none;
    font-size: 14px;
    margin-left: 10px;
  }
}

.search-btn {
  padding: 10px 20px;
  background-color: #007aff;
  color: #fff;
  border-radius: 6px;
  font-size: 14px;
}

/* 退款列表 */
.refund-list {
  padding: 0 15px;
}

.refund-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.refund-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.refund-status {
  display: flex;
  align-items: center;
}

.urgent-tag {
  background-color: #ff4d4f;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.amount-value {
  font-size: 18px;
  font-weight: bold;
  color: #ff4444;
}

.refund-info {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 14px;
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
  text-align: right;
}

.refund-actions {
  display: flex;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #f5f5f5;
}

.action-btn {
  flex: 1;
  padding: 8px 0;
  text-align: center;
  border-radius: 6px;
  font-size: 14px;
}

.reject-btn {
  background-color: #f5f5f5;
  color: #666;
}

.approve-btn {
  background-color: #52c41a;
  color: #fff;
}

/* 加载状态 */
.loading-state {
  padding: 50px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.empty-state {
  padding: 50px 20px;
  text-align: center;
}

.load-more {
  padding: 20px;
  text-align: center;
  color: #007aff;
  font-size: 14px;
}
</style>
