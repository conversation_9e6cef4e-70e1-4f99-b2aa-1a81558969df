<template>
  <view class="refund-detail-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <view class="fixed-header">
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="退款详情"></navigation-bar>
    </view>

    <scroll-view scroll-y class="detail-content">
      <view v-if="loading" class="loading-state">
        <van-loading type="spinner" color="#007aff" />
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="refundDetail" class="detail-container">
        <!-- 退款状态卡片 -->
        <view class="status-section">
          <view class="status-header">
            <view class="status-info">
              <van-tag 
                :color="getStatusColor(refundDetail.refundStatus)"
                plain
                size="large"
              >
                {{ getStatusText(refundDetail.refundStatus) }}
              </van-tag>
              <text class="status-desc">{{ getStatusDescription(refundDetail.refundStatus) }}</text>
            </view>
            <view class="refund-amount">
              <text class="amount-label">退款金额</text>
              <text class="amount-value">¥{{ refundDetail.refundAmount }}</text>
            </view>
          </view>
        </view>

        <!-- 订单信息 -->
        <view class="order-section">
          <view class="section-title">订单信息</view>
          <view class="info-list">
            <view class="info-item">
              <text class="info-label">订单号</text>
              <text class="info-value">{{ refundDetail.orderNumber }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">客户</text>
              <text class="info-value">{{ refundDetail.customerName || '未知客户' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">订单金额</text>
              <text class="info-value">¥{{ refundDetail.orderAmount || refundDetail.refundAmount }}</text>
            </view>
          </view>
        </view>

        <!-- 退款信息 -->
        <view class="refund-section">
          <view class="section-title">退款信息</view>
          <view class="info-list">
            <view class="info-item">
              <text class="info-label">退款单号</text>
              <text class="info-value">{{ refundDetail.refundNo }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">申请时间</text>
              <text class="info-value">{{ formatTime(refundDetail.applyTime) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">退款类型</text>
              <text class="info-value">{{ getRefundTypeText(refundDetail.refundType) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">退款原因</text>
              <text class="info-value">{{ refundDetail.refundReason }}</text>
            </view>
            <view v-if="refundDetail.refundDescription" class="info-item">
              <text class="info-label">详细说明</text>
              <text class="info-value">{{ refundDetail.refundDescription }}</text>
            </view>
          </view>
        </view>

        <!-- 退款凭证 -->
        <view v-if="refundDetail.refundImages && refundDetail.refundImages.length > 0" class="images-section">
          <view class="section-title">退款凭证</view>
          <view class="images-grid">
            <view 
              v-for="(image, index) in refundDetail.refundImages" 
              :key="index"
              class="image-item"
              @click="previewImage(image, index)"
            >
              <image :src="image" mode="aspectFill" class="refund-image" />
            </view>
          </view>
        </view>

        <!-- 审核信息 -->
        <view v-if="refundDetail.auditTime" class="audit-section">
          <view class="section-title">审核信息</view>
          <view class="info-list">
            <view class="info-item">
              <text class="info-label">审核结果</text>
              <text class="info-value" :class="getAuditResultClass(refundDetail.auditResult)">
                {{ getAuditResultText(refundDetail.auditResult) }}
              </text>
            </view>
            <view class="info-item">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ formatTime(refundDetail.auditTime) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">审核人</text>
              <text class="info-value">{{ refundDetail.auditUserName || '系统' }}</text>
            </view>
            <view v-if="refundDetail.auditRemark" class="info-item">
              <text class="info-label">审核备注</text>
              <text class="info-value">{{ refundDetail.auditRemark }}</text>
            </view>
          </view>
        </view>

        <!-- 退款进度 -->
        <view v-if="refundProgressSteps.length > 0" class="progress-section">
          <view class="section-title">退款进度</view>
          <van-steps 
            direction="vertical" 
            :steps="refundProgressSteps" 
            :active="refundActiveStep"
            active-color="#52c41a"
            inactive-color="#d9d9d9"
            active-icon="checked"
            class="refund-steps"
          />
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view v-if="refundDetail && refundDetail.refundStatus === 'PENDING'" class="bottom-actions">
      <view class="action-btn reject-btn" @click="handleReject">
        <text>拒绝退款</text>
      </view>
      <view class="action-btn approve-btn" @click="handleApprove">
        <text>同意退款</text>
      </view>
    </view>

    <!-- 审核对话框 -->
    <refund-audit-dialog
      :visible.sync="auditDialogVisible"
      :refund-info="refundDetail"
      :audit-type="auditType"
      @audit-success="handleAuditSuccess"
    />
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import RefundAuditDialog from "./components/RefundAuditDialog.vue";
import { getLayoutHeights } from "@/utils/get-page-height.util";
import { getRefundDetail } from "@/api/refund.api";
import { RefundOrder, REFUND_STATUS_MAP } from "@/types/refund.types";

interface StepData {
  text: string;
  desc?: string;
}

@Component({
  name: "RefundAuditDetail",
  components: {
    NavigationBar,
    RefundAuditDialog,
  },
})
export default class RefundAuditDetail extends Vue {
  topHeight = 88;
  contentHeight = 0;
  loading = false;
  refundId = "";
  
  refundDetail: RefundOrder | null = null;
  
  // 弹窗
  auditDialogVisible = false;
  auditType: "approve" | "reject" = "approve";

  onLoad(options: any) {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
    
    if (options.refundId) {
      this.refundId = options.refundId;
      this.loadRefundDetail();
    } else {
      uni.showToast({
        title: "缺少退款ID",
        icon: "none",
      });
      uni.navigateBack();
    }
  }

  // 加载退款详情
  async loadRefundDetail() {
    this.loading = true;
    
    try {
      const response = await getRefundDetail(this.refundId);
      
      if (response.code === 0) {
        this.refundDetail = response.data;
      } else {
        uni.showToast({
          title: response.msg || "加载失败",
          icon: "none",
        });
        uni.navigateBack();
      }
    } catch (error) {
      console.error("加载退款详情失败:", error);
      uni.showToast({
        title: "网络错误",
        icon: "none",
      });
      uni.navigateBack();
    } finally {
      this.loading = false;
    }
  }

  // 获取退款进度步骤
  get refundProgressSteps(): StepData[] {
    if (!this.refundDetail) return [];

    const steps: StepData[] = [
      {
        text: "提交退款申请",
        desc: this.formatTime(this.refundDetail.applyTime),
      },
    ];

    if (this.refundDetail.auditTime) {
      const auditResultText = this.refundDetail.auditResult === "APPROVED" ? "已同意" : "已拒绝";
      steps.push({
        text: `商家审核${auditResultText}`,
        desc: this.formatTime(this.refundDetail.auditTime),
      });
    }

    if (this.refundDetail.refundStatus === "PROCESSING") {
      steps.push({
        text: "退款处理中",
        desc: "正在处理退款，请耐心等待",
      });
    }

    if (this.refundDetail.wxSuccessTime) {
      steps.push({
        text: "退款成功",
        desc: this.formatTime(this.refundDetail.wxSuccessTime),
      });
    }

    return steps;
  }

  // 获取当前激活的步骤索引
  get refundActiveStep(): number {
    if (!this.refundDetail) return 0;

    let activeStep = 0;

    if (this.refundDetail.auditTime) {
      activeStep = 1;
    }

    if (this.refundDetail.refundStatus === "PROCESSING") {
      activeStep = 2;
    }

    if (this.refundDetail.wxSuccessTime) {
      activeStep = 3;
    }

    return activeStep;
  }

  // 同意退款
  handleApprove() {
    this.auditType = "approve";
    this.auditDialogVisible = true;
  }

  // 拒绝退款
  handleReject() {
    this.auditType = "reject";
    this.auditDialogVisible = true;
  }

  // 审核成功回调
  handleAuditSuccess() {
    this.loadRefundDetail();
  }

  // 预览图片
  previewImage(current: string, index: number) {
    uni.previewImage({
      current: index,
      urls: this.refundDetail?.refundImages || [],
    });
  }

  // 工具方法
  getStatusText(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.label || status;
  }

  getStatusColor(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.color || "#999";
  }

  getStatusDescription(status: string): string {
    const descriptions: Record<string, string> = {
      PENDING: "等待商家审核",
      APPROVED: "商家已同意退款",
      REJECTED: "商家已拒绝退款",
      PROCESSING: "退款正在处理中",
      SUCCESS: "退款已成功",
      FAILED: "退款失败",
    };
    return descriptions[status] || "";
  }

  getRefundTypeText(type: string): string {
    const types: Record<string, string> = {
      FULL: "全额退款",
      PARTIAL: "部分退款",
    };
    return types[type] || type;
  }

  getAuditResultText(result?: string): string {
    const results: Record<string, string> = {
      APPROVED: "同意",
      REJECTED: "拒绝",
    };
    return results[result || ""] || "未审核";
  }

  getAuditResultClass(result?: string): string {
    return result === "APPROVED" ? "approved" : result === "REJECTED" ? "rejected" : "";
  }

  formatTime(time: string): string {
    return new Date(time).toLocaleString("zh-CN");
  }
}
</script>

<style lang="scss" scoped>
.refund-detail-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
}

.detail-container {
  padding-bottom: 80px; // 为底部按钮留空间
}

/* 状态区域 */
.status-section {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 10px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info {
  flex: 1;
}

.status-desc {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 8px;
}

.refund-amount {
  text-align: right;
}

.amount-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 20px;
  font-weight: bold;
  color: #ff4444;
}

/* 信息区域 */
.order-section,
.refund-section,
.audit-section,
.progress-section {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 20px;
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 15px;
}

.info-list {
  border-top: 1px solid #f5f5f5;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid #f8f8f8;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 14px;
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  font-size: 14px;
  color: #333;
  flex: 1;
  text-align: right;

  &.approved {
    color: #52c41a;
  }

  &.rejected {
    color: #ff4d4f;
  }
}

/* 图片区域 */
.images-section {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 20px;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  width: calc(33.333% - 7px);
  aspect-ratio: 1;
  border-radius: 6px;
  overflow: hidden;
}

.refund-image {
  width: 100%;
  height: 100%;
}

/* 进度区域 */
.refund-steps {
  padding: 0 10px;
  
  ::v-deep .van-step {
    font-size: 12px;
    line-height: 1.4;
  }
  
  ::v-deep .van-step__title {
    font-size: 12px;
    line-height: 1.4;
  }
  
  ::v-deep .desc-class {
    font-size: 10px;
    color: #999;
    margin-top: 2px;
    line-height: 1.3;
  }
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 15px 20px;
  display: flex;
  gap: 15px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.action-btn {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
}

.reject-btn {
  background-color: #f5f5f5;
  color: #666;
}

.approve-btn {
  background-color: #52c41a;
  color: #fff;
}

/* 加载状态 */
.loading-state {
  padding: 100px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}
</style>
