<!-- 退款详情对话框组件 -->
<template>
  <el-dialog
    title="退款详情"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    class="refund-detail-dialog"
  >
    <div v-if="refundDetail" class="detail-content">
      <!-- 退款状态卡片 -->
      <el-card class="status-card" shadow="never">
        <div class="status-header">
          <div class="status-info">
            <el-tag
              :type="refundManager.getStatusConfig(refundDetail.refundStatus).type"
              size="large"
            >
              {{ refundManager.getStatusConfig(refundDetail.refundStatus).label }}
            </el-tag>
            <div class="status-desc">{{ getStatusDescription(refundDetail.refundStatus) }}</div>
          </div>
          <div class="refund-amount">
            <span class="amount-label">退款金额</span>
            <span class="amount-value">{{ refundManager.formatAmount(refundDetail.refundAmount) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>基本信息</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">退款单号：</span>
              <span class="info-value">{{ refundDetail.refundNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">订单号：</span>
              <span class="info-value">{{ refundDetail.orderNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">退款类型：</span>
              <span class="info-value">{{ refundManager.getTypeConfig(refundDetail.refundType).label }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">申请时间：</span>
              <span class="info-value">{{ refundManager.formatTime(refundDetail.applyTime) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 退款原因 -->
      <el-card class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>退款原因</span>
        </div>
        <div class="reason-content">
          <div class="reason-text">{{ refundDetail.refundReason }}</div>
          <div v-if="refundDetail.refundDescription" class="description-text">
            {{ refundDetail.refundDescription }}
          </div>
        </div>
      </el-card>

      <!-- 退款凭证 -->
      <el-card v-if="refundImages.length > 0" class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>退款凭证</span>
        </div>
        <div class="images-gallery">
          <div
            v-for="(image, index) in refundImages"
            :key="index"
            class="image-item"
            @click="previewImage(index)"
          >
            <el-image
              :src="image"
              :preview-src-list="refundImages"
              fit="cover"
              class="evidence-image"
            />
          </div>
        </div>
      </el-card>

      <!-- 审核信息 -->
      <el-card v-if="refundDetail.auditResult" class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>审核信息</span>
        </div>
        <div class="audit-content">
          <div class="audit-result">
            <el-tag
              :type="refundDetail.auditResult === 'APPROVED' ? 'success' : 'danger'"
              size="medium"
            >
              {{ refundDetail.auditResult === 'APPROVED' ? '审核通过' : '审核拒绝' }}
            </el-tag>
          </div>
          <el-row :gutter="20" class="audit-details">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">审核人：</span>
                <span class="info-value">{{ refundDetail.auditUserName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">审核时间：</span>
                <span class="info-value">{{ refundManager.formatTime(refundDetail.auditTime) }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <span class="info-label">审核备注：</span>
                <span class="info-value">{{ refundDetail.auditRemark || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 微信退款信息 -->
      <el-card v-if="refundDetail.wxSuccessTime" class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>退款详情</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">退款渠道：</span>
              <span class="info-value">微信支付原路退回</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">退款到账时间：</span>
              <span class="info-value">{{ refundManager.formatTime(refundDetail.wxSuccessTime) }}</span>
            </div>
          </el-col>
        </el-row>
        <div class="wx-tips">
          <el-alert
            title="退款已原路退回到用户的微信支付账户"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>
    </div>

    <!-- 加载状态 -->
    <div v-else-if="loading" class="loading-content">
      <el-loading :active="true" text="加载中..." />
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-content">
      <el-empty description="加载失败，请重试" />
    </div>

    <!-- 对话框底部操作按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button
        v-if="canAudit"
        type="success"
        @click="handleAudit('APPROVED')"
      >
        同意退款
      </el-button>
      <el-button
        v-if="canAudit"
        type="danger"
        @click="handleAudit('REJECTED')"
      >
        拒绝退款
      </el-button>
    </div>

    <!-- 审核对话框 -->
    <el-dialog
      title="审核退款申请"
      :visible.sync="auditDialogVisible"
      width="500px"
      append-to-body
    >
      <el-form :model="auditForm" :rules="auditRules" ref="auditForm">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.auditResult">
            <el-radio label="APPROVED">同意退款</el-radio>
            <el-radio label="REJECTED">拒绝退款</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditRemark">
          <el-input
            v-model="auditForm.auditRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见..."
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { refundManager } from '@/utils/refundManager'

export default {
  name: 'RefundDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    refundId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      refundManager,
      dialogVisible: false,
      loading: false,
      refundDetail: null,
      refundImages: [],
      // 审核对话框
      auditDialogVisible: false,
      auditForm: {
        auditResult: 'APPROVED',
        auditRemark: ''
      },
      auditRules: {
        auditRemark: [
          { required: true, message: '请输入审核意见', trigger: 'blur' },
          { min: 5, max: 500, message: '审核意见长度在 5 到 500 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    canAudit() {
      return this.refundDetail && refundManager.canAudit(this.refundDetail.refundStatus)
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val && this.refundId) {
          this.loadRefundDetail()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 加载退款详情
    async loadRefundDetail() {
      if (!this.refundId) return

      this.loading = true
      this.refundDetail = null

      try {
        const result = await refundManager.getRefundDetail(this.refundId)
        
        if (result.success) {
          this.refundDetail = result.data
          this.parseRefundImages()
        } else {
          this.$message.error(result.message || '加载退款详情失败')
        }
      } catch (error) {
        console.error('加载退款详情失败:', error)
        this.$message.error('加载退款详情失败')
      } finally {
        this.loading = false
      }
    },

    // 解析退款凭证图片
    parseRefundImages() {
      if (!this.refundDetail.refundImages) {
        this.refundImages = []
        return
      }

      try {
        if (typeof this.refundDetail.refundImages === 'string') {
          this.refundImages = JSON.parse(this.refundDetail.refundImages)
        } else if (Array.isArray(this.refundDetail.refundImages)) {
          this.refundImages = this.refundDetail.refundImages
        } else {
          this.refundImages = []
        }
      } catch (error) {
        console.error('解析退款图片失败:', error)
        this.refundImages = []
      }
    },

    // 获取状态描述
    getStatusDescription(status) {
      const descriptions = {
        'PENDING': '退款申请正在审核中，请耐心等待',
        'APPROVED': '退款申请已通过审核',
        'REJECTED': '退款申请已被拒绝',
        'PROCESSING': '退款正在处理中，请耐心等待',
        'SUCCESS': '退款已成功，已原路退回到用户账户',
        'FAILED': '退款处理失败'
      }
      return descriptions[status] || '未知状态'
    },

    // 预览图片
    previewImage(index) {
      // El-image 组件会自动处理预览
    },

    // 处理审核
    handleAudit(auditResult) {
      this.auditForm.auditResult = auditResult
      this.auditForm.auditRemark = auditResult === 'APPROVED' ? '同意退款' : '拒绝退款'
      this.auditDialogVisible = true
    },

    // 提交审核
    async submitAudit() {
      try {
        await this.$refs.auditForm.validate()

        const auditData = {
          refundId: this.refundId,
          auditResult: this.auditForm.auditResult,
          auditRemark: this.auditForm.auditRemark,
          auditUserId: 1, // 从用户信息获取
          auditUserName: '管理员' // 从用户信息获取
        }

        const result = await refundManager.auditRefund(auditData)
        
        if (result.success) {
          this.auditDialogVisible = false
          this.$emit('audit-success', result.data)
          // 重新加载详情
          this.loadRefundDetail()
        }
      } catch (error) {
        // 表单验证失败或审核失败
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.refundDetail = null
      this.refundImages = []
      
      // 重置审核表单
      if (this.$refs.auditForm) {
        this.$refs.auditForm.resetFields()
      }
      this.auditDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-detail-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
  }
}

.detail-content {
  .status-card {
    margin-bottom: 20px;
    
    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .status-info {
        .status-desc {
          margin-top: 10px;
          color: #909399;
          font-size: 14px;
        }
      }
      
      .refund-amount {
        text-align: right;
        
        .amount-label {
          display: block;
          color: #909399;
          font-size: 14px;
          margin-bottom: 5px;
        }
        
        .amount-value {
          color: #f56c6c;
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
  }
  
  .info-card {
    margin-bottom: 20px;
    
    .card-header {
      font-weight: bold;
      color: #303133;
    }
    
    .info-item {
      margin-bottom: 15px;
      line-height: 1.5;
      
      .info-label {
        color: #606266;
        display: inline-block;
        width: 100px;
      }
      
      .info-value {
        color: #303133;
      }
    }
    
    .reason-content {
      .reason-text {
        color: #303133;
        margin-bottom: 10px;
        font-size: 16px;
      }
      
      .description-text {
        color: #666;
        line-height: 1.6;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #409eff;
      }
    }
    
    .images-gallery {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      
      .image-item {
        cursor: pointer;
        
        .evidence-image {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          border: 1px solid #dcdfe6;
          
          &:hover {
            border-color: #409eff;
          }
        }
      }
    }
    
    .audit-content {
      .audit-result {
        margin-bottom: 20px;
      }
      
      .audit-details {
        margin-top: 15px;
      }
    }
    
    .wx-tips {
      margin-top: 15px;
    }
  }
}

.loading-content {
  height: 200px;
  position: relative;
}

.error-content {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}
</style>