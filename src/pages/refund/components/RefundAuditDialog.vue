<template>
  <van-popup 
    :show="visible" 
    position="bottom" 
    :close-on-click-overlay="false"
    @close="handleClose"
    class="audit-dialog"
  >
    <view class="dialog-content">
      <!-- 标题 -->
      <view class="dialog-header">
        <text class="dialog-title">
          {{ auditType === 'approve' ? '同意退款' : '拒绝退款' }}
        </text>
        <view class="close-btn" @click="handleClose">
          <van-icon name="cross" size="18" />
        </view>
      </view>

      <!-- 退款信息摘要 -->
      <view v-if="refundInfo" class="refund-summary">
        <view class="summary-item">
          <text class="summary-label">退款单号</text>
          <text class="summary-value">{{ refundInfo.refundNo }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">退款金额</text>
          <text class="summary-value amount">¥{{ refundInfo.refundAmount }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">退款原因</text>
          <text class="summary-value">{{ refundInfo.refundReason }}</text>
        </view>
      </view>

      <!-- 审核表单 -->
      <view class="audit-form">
        <!-- 审核结果 -->
        <view class="form-item">
          <text class="form-label required">审核结果</text>
          <view class="audit-result">
            <view 
              class="result-option" 
              :class="{ active: auditForm.auditResult === 'APPROVED' }"
              @click="auditForm.auditResult = 'APPROVED'"
            >
              <van-icon name="success" size="16" />
              <text>同意</text>
            </view>
            <view 
              class="result-option" 
              :class="{ active: auditForm.auditResult === 'REJECTED' }"
              @click="auditForm.auditResult = 'REJECTED'"
            >
              <van-icon name="close" size="16" />
              <text>拒绝</text>
            </view>
          </view>
        </view>

        <!-- 审核备注 -->
        <view class="form-item">
          <text class="form-label" :class="{ required: auditForm.auditResult === 'REJECTED' }">
            审核备注
          </text>
          <textarea
            v-model="auditForm.auditRemark"
            :placeholder="getRemarkPlaceholder()"
            class="remark-input"
            :maxlength="200"
            show-confirm-bar
          />
          <view class="char-count">{{ auditForm.auditRemark.length }}/200</view>
        </view>

        <!-- 快速备注选项 -->
        <view v-if="quickRemarks.length > 0" class="quick-remarks">
          <text class="quick-title">快速选择</text>
          <view class="remarks-grid">
            <view 
              v-for="remark in quickRemarks" 
              :key="remark"
              class="remark-tag"
              @click="selectQuickRemark(remark)"
            >
              <text>{{ remark }}</text>
            </view>
          </view>
        </view>

        <!-- 提醒信息 -->
        <view class="audit-tips">
          <van-icon name="info-o" size="14" />
          <text class="tips-text">
            {{ auditForm.auditResult === 'APPROVED' ? 
                '同意后将自动发起退款流程，请确认无误后提交' : 
                '拒绝后客户将收到通知，请填写拒绝原因' }}
          </text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <view class="action-btn cancel-btn" @click="handleClose">
          <text>取消</text>
        </view>
        <view 
          class="action-btn confirm-btn" 
          :class="{ disabled: !canSubmit }"
          @click="handleSubmit"
        >
          <van-loading v-if="submitting" type="spinner" size="16" color="#fff" />
          <text v-else>{{ auditForm.auditResult === 'APPROVED' ? '同意退款' : '拒绝退款' }}</text>
        </view>
      </view>
    </view>
  </van-popup>
</template>

<script lang="ts">
import Vue from "vue";
import { Component, Prop, Watch } from "vue-property-decorator";
import { auditRefund } from "@/api/refund.api";
import { RefundOrder } from "@/types/refund.types";

interface AuditForm {
  auditResult: "APPROVED" | "REJECTED" | "";
  auditRemark: string;
}

@Component({
  name: "RefundAuditDialog",
})
export default class RefundAuditDialog extends Vue {
  @Prop({ type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: Object, default: null }) refundInfo!: RefundOrder | null;
  @Prop({ type: String, default: "approve" }) auditType!: "approve" | "reject";

  submitting = false;
  
  auditForm: AuditForm = {
    auditResult: "",
    auditRemark: "",
  };

  // 快速备注选项
  get quickRemarks(): string[] {
    if (this.auditForm.auditResult === "APPROVED") {
      return [
        "符合退款条件，同意退款",
        "商品确有质量问题，同意退款",
        "客户要求合理，同意退款",
      ];
    } else if (this.auditForm.auditResult === "REJECTED") {
      return [
        "商品无质量问题，不符合退款条件",
        "超出退款时限",
        "商品已使用，不支持退款",
        "退款原因不充分",
        "需要提供更多证明材料",
      ];
    }
    return [];
  }

  get canSubmit(): boolean {
    if (this.submitting) return false;
    if (!this.auditForm.auditResult) return false;
    if (this.auditForm.auditResult === "REJECTED" && !this.auditForm.auditRemark.trim()) {
      return false;
    }
    return true;
  }

  @Watch("visible")
  onVisibleChange(val: boolean) {
    if (val) {
      this.initForm();
    }
  }

  @Watch("auditType")
  onAuditTypeChange(val: "approve" | "reject") {
    this.auditForm.auditResult = val === "approve" ? "APPROVED" : "REJECTED";
  }

  // 初始化表单
  initForm() {
    this.auditForm = {
      auditResult: this.auditType === "approve" ? "APPROVED" : "REJECTED",
      auditRemark: "",
    };
  }

  // 获取备注占位符
  getRemarkPlaceholder(): string {
    if (this.auditForm.auditResult === "APPROVED") {
      return "请输入同意退款的备注（可选）";
    } else if (this.auditForm.auditResult === "REJECTED") {
      return "请输入拒绝退款的原因（必填）";
    }
    return "请输入审核备注";
  }

  // 选择快速备注
  selectQuickRemark(remark: string) {
    this.auditForm.auditRemark = remark;
  }

  // 关闭对话框
  handleClose() {
    this.$emit("update:visible", false);
  }

  // 提交审核
  async handleSubmit() {
    if (!this.canSubmit || !this.refundInfo) return;

    this.submitting = true;

    try {
      const params = {
        refundId: this.refundInfo.id,
        auditResult: this.auditForm.auditResult,
        auditRemark: this.auditForm.auditRemark.trim(),
      };

      const response = await auditRefund(params);

      if (response.code === 0) {
        uni.showToast({
          title: this.auditForm.auditResult === "APPROVED" ? "已同意退款" : "已拒绝退款",
          icon: "success",
        });
        
        this.$emit("audit-success");
        this.handleClose();
      } else {
        uni.showToast({
          title: response.msg || "审核失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("审核失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.submitting = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.audit-dialog {
  ::v-deep .van-popup {
    border-radius: 16px 16px 0 0;
    max-height: 80vh;
  }
}

.dialog-content {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.close-btn {
  padding: 5px;
  color: #666;
}

/* 退款摘要 */
.refund-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.summary-label {
  font-size: 14px;
  color: #666;
}

.summary-value {
  font-size: 14px;
  color: #333;

  &.amount {
    color: #ff4444;
    font-weight: bold;
  }
}

/* 审核表单 */
.audit-form {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 10px;

  &.required::after {
    content: "*";
    color: #ff4d4f;
    margin-left: 4px;
  }
}

/* 审核结果选择 */
.audit-result {
  display: flex;
  gap: 15px;
}

.result-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
  color: #666;
  font-size: 14px;
  gap: 6px;

  &.active {
    border-color: #52c41a;
    background-color: #f6ffed;
    color: #52c41a;
  }

  &:first-child.active {
    border-color: #52c41a;
    color: #52c41a;
  }

  &:last-child.active {
    border-color: #ff4d4f;
    background-color: #fff2f0;
    color: #ff4d4f;
  }
}

/* 备注输入 */
.remark-input {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  box-sizing: border-box;

  &:focus {
    border-color: #007aff;
    outline: none;
  }
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 快速备注 */
.quick-remarks {
  margin-top: 15px;
}

.quick-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
}

.remarks-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.remark-tag {
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  border: 1px solid transparent;

  &:active {
    background-color: #e6f7ff;
    border-color: #007aff;
    color: #007aff;
  }
}

/* 提醒信息 */
.audit-tips {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
  margin-top: 15px;
}

.tips-text {
  font-size: 12px;
  color: #1890ff;
  line-height: 1.4;
}

/* 操作按钮 */
.dialog-actions {
  display: flex;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #f5f5f5;
}

.action-btn {
  flex: 1;
  padding: 12px 0;
  text-align: center;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #52c41a;
  color: #fff;

  &.disabled {
    background-color: #d9d9d9;
    color: #999;
  }
}
</style>
