<template>
  <view class="refund-detail-page" :style="{ paddingTop: topHeight + 'px' }">
    <!-- 导航栏 -->
    <view class="fixed-header">
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="退款详情"></navigation-bar>
    </view>

    <scroll-view scroll-y class="detail-content" v-if="refundDetail">
      <!-- 退款状态卡片 -->
      <view class="status-card">
        <view class="status-header">
          <view class="status-icon" :class="getStatusIconClass(refundDetail.refundStatus)">
            <van-icon :name="getStatusIcon(refundDetail.refundStatus)" size="24" color="#fff" />
          </view>
          <view class="status-info">
            <text class="status-title">{{ getStatusText(refundDetail.refundStatus) }}</text>
            <text class="status-desc">{{ getStatusDescription(refundDetail.refundStatus) }}</text>
          </view>
        </view>
        <view class="refund-amount">
          <text class="amount-label">退款金额</text>
          <text class="amount-value">¥{{ refundDetail.refundAmount }}</text>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="info-card">
        <view class="card-title">基本信息</view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">退款单号：</text>
            <text class="info-value">{{ refundDetail.refundNo }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">关联订单：</text>
            <text class="info-value">{{ refundDetail.orderNumber }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">申请时间：</text>
            <text class="info-value">{{ formatTime(refundDetail.applyTime) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">退款类型：</text>
            <text class="info-value">退货退款（全额退款）</text>
          </view>
        </view>
      </view>

      <!-- 退款原因 -->
      <view class="info-card">
        <view class="card-title">退款原因</view>
        <view class="reason-content">
          <text class="reason-text">{{ refundDetail.refundReason }}</text>
          <text v-if="refundDetail.refundDescription" class="description-text">
            {{ refundDetail.refundDescription }}
          </text>
        </view>
      </view>

      <!-- 退款凭证 -->
      <view v-if="refundImages.length > 0" class="info-card">
        <view class="card-title">退款凭证</view>
        <view class="images-gallery">
          <view v-for="(image, index) in refundImages" :key="index" class="image-item" @click="previewImage(index)">
            <image :src="image" class="evidence-image" mode="aspectFill" />
          </view>
        </view>
      </view>

      <!-- 审核信息 -->
      <view v-if="refundDetail.auditResult" class="info-card">
        <view class="card-title">审核信息</view>
        <view class="audit-content">
          <view class="audit-result">
            <text class="audit-label">审核结果：</text>
            <text class="audit-value" :class="getAuditResultClass(refundDetail.auditResult)">
              {{ getAuditResultText(refundDetail.auditResult) }}
            </text>
          </view>
          <view class="info-list">
            <view v-if="refundDetail.auditUserName" class="info-item">
              <text class="info-label">审核人：</text>
              <text class="info-value">{{ refundDetail.auditUserName }}</text>
            </view>
            <view v-if="refundDetail.auditTime" class="info-item">
              <text class="info-label">审核时间：</text>
              <text class="info-value">{{ formatTime(refundDetail.auditTime) }}</text>
            </view>
            <view v-if="refundDetail.auditRemark" class="info-item">
              <text class="info-label">审核备注：</text>
              <text class="info-value">{{ refundDetail.auditRemark }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 退款详情 -->
      <view v-if="refundDetail.wxSuccessTime" class="info-card">
        <view class="card-title">退款详情</view>
        <view class="refund-success-info">
          <view class="success-icon">
            <van-icon name="checked" size="20" color="#52c41a" />
          </view>
          <view class="success-content">
            <text class="success-title">退款成功</text>
            <text class="success-desc">已原路退回到您的微信支付账户</text>
            <text class="success-time">退款时间：{{ formatTime(refundDetail.wxSuccessTime) }}</text>
          </view>
        </view>
      </view>

      <!-- 进度时间轴 -->
      <view class="info-card">
        <view class="card-title">退款进度</view>
        <view class="timeline">
          <view v-for="(step, index) in timelineSteps" :key="index" class="timeline-item" :class="{ active: step.active, completed: step.completed }">
            <view class="timeline-dot">
              <van-icon v-if="step.completed" name="success" size="12" color="#fff" />
            </view>
            <view class="timeline-content">
              <text class="timeline-title">{{ step.title }}</text>
              <text v-if="step.time" class="timeline-time">{{ step.time }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 加载状态 -->
    <view v-else-if="loading" class="loading-state">
      <van-loading type="spinner" color="#007aff" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-state">
      <van-empty description="加载失败" />
      <van-button type="primary" @click="loadRefundDetail">重试</van-button>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { RefundOrder, REFUND_STATUS_MAP } from "@/types/refund.types";
import { getRefundDetail } from "@/api/refund.api";
import { getLayoutHeights } from "@/utils/get-page-height.util";

interface TimelineStep {
  title: string;
  time?: string;
  active: boolean;
  completed: boolean;
}

@Component({
  name: "RefundDetail",
  components: {
    NavigationBar,
  },
})
export default class RefundDetail extends Vue {
  topHeight = 88;
  refundId = "";
  customerId = "";
  loading = false;

  refundDetail: RefundOrder | null = null;
  refundImages: string[] = [];

  async onLoad(options: any) {
    const { topHeight } = getLayoutHeights();
    this.topHeight = topHeight;

    this.refundId = options.refundId || "";
    this.customerId = options.customerId || "";

    if (!this.refundId || !this.customerId) {
      uni.showToast({
        title: "缺少必要参数",
        icon: "none",
      });
      uni.navigateBack({});
      return;
    }

    await this.loadRefundDetail();
  }

  // 加载退款详情
  async loadRefundDetail() {
    this.loading = true;
    this.refundDetail = null;

    try {
      const response = await getRefundDetail(this.refundId, this.customerId);

      if (response.code === 0) {
        this.refundDetail = response.data;
        this.parseRefundImages();
      } else {
        uni.showToast({
          title: response.msg || "加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载退款详情失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  // 解析退款凭证图片
  parseRefundImages() {
    if (!this.refundDetail?.refundImages) {
      this.refundImages = [];
      return;
    }

    try {
      if (typeof this.refundDetail.refundImages === "string") {
        this.refundImages = JSON.parse(this.refundDetail.refundImages);
      } else if (Array.isArray(this.refundDetail.refundImages)) {
        this.refundImages = this.refundDetail.refundImages;
      } else {
        this.refundImages = [];
      }
    } catch (error) {
      console.error("解析退款图片失败:", error);
      this.refundImages = [];
    }
  }

  // 预览图片
  previewImage(index: number) {
    uni.previewImage({
      urls: this.refundImages,
      current: index,
    });
  }

  // 格式化时间
  formatTime(time: string): string {
    if (!time) return "-";

    return new Date(time).toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  }

  // 获取状态文本
  getStatusText(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.label || "未知状态";
  }

  // 获取状态描述
  getStatusDescription(status: string): string {
    const descriptions: Record<string, string> = {
      PENDING: "退款申请正在审核中，请耐心等待",
      APPROVED: "退款申请已通过审核",
      REJECTED: "退款申请已被拒绝",
      PROCESSING: "退款正在处理中，请耐心等待",
      SUCCESS: "退款已成功，已原路退回到您的账户",
      FAILED: "退款处理失败",
    };
    return descriptions[status] || "未知状态";
  }

  // 获取状态图标
  getStatusIcon(status: string): string {
    const icons: Record<string, string> = {
      PENDING: "clock",
      APPROVED: "success",
      REJECTED: "cross",
      PROCESSING: "clock",
      SUCCESS: "success",
      FAILED: "cross",
    };
    return icons[status] || "info";
  }

  // 获取状态图标样式类
  getStatusIconClass(status: string): string {
    const classes: Record<string, string> = {
      PENDING: "icon-pending",
      APPROVED: "icon-approved",
      REJECTED: "icon-rejected",
      PROCESSING: "icon-processing",
      SUCCESS: "icon-success",
      FAILED: "icon-failed",
    };
    return classes[status] || "icon-default";
  }

  // 获取审核结果文本
  getAuditResultText(result: string): string {
    const textMap: Record<string, string> = {
      APPROVED: "已同意",
      REJECTED: "已拒绝",
    };
    return textMap[result] || result;
  }

  // 获取审核结果样式类
  getAuditResultClass(result: string): string {
    return result === "APPROVED" ? "audit-approved" : "audit-rejected";
  }

  // 获取时间轴步骤
  get timelineSteps(): TimelineStep[] {
    if (!this.refundDetail) return [];

    const steps: TimelineStep[] = [
      {
        title: "提交退款申请",
        time: this.formatTime(this.refundDetail.applyTime),
        active: true,
        completed: true,
      },
    ];

    if (this.refundDetail.auditTime) {
      steps.push({
        title: `商家审核${this.getAuditResultText(this.refundDetail.auditResult || "")}`,
        time: this.formatTime(this.refundDetail.auditTime),
        active: true,
        completed: true,
      });
    }

    if (this.refundDetail.refundStatus === "PROCESSING") {
      steps.push({
        title: "退款处理中",
        active: true,
        completed: false,
      });
    }

    if (this.refundDetail.wxSuccessTime) {
      steps.push({
        title: "退款成功",
        time: this.formatTime(this.refundDetail.wxSuccessTime),
        active: true,
        completed: true,
      });
    }

    return steps;
  }
}
</script>

<style lang="scss" scoped>
.refund-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
}

.detail-content {
  height: calc(100vh - 88px);
  padding: 15px;
}

.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  color: #fff;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;

  &.icon-pending {
    background-color: rgba(250, 140, 22, 0.8);
  }

  &.icon-approved {
    background-color: rgba(82, 196, 26, 0.8);
  }

  &.icon-rejected {
    background-color: rgba(255, 77, 79, 0.8);
  }

  &.icon-processing {
    background-color: rgba(24, 144, 255, 0.8);
  }

  &.icon-success {
    background-color: rgba(82, 196, 26, 0.8);
  }

  &.icon-failed {
    background-color: rgba(255, 77, 79, 0.8);
  }

  &.icon-default {
    background-color: rgba(153, 153, 153, 0.8);
  }
}

.status-info {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.status-desc {
  font-size: 14px;
  opacity: 0.9;
}

.refund-amount {
  text-align: right;
}

.amount-label {
  display: block;
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 5px;
}

.amount-value {
  font-size: 24px;
  font-weight: bold;
}

.info-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}

.info-list {
  .info-item {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.info-label {
  color: #666;
  font-size: 14px;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  font-size: 14px;
  flex: 1;
}

.reason-content {
  .reason-text {
    display: block;
    color: #333;
    font-size: 14px;
    margin-bottom: 10px;
  }

  .description-text {
    display: block;
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #409eff;
  }
}

.images-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  width: 80px;
  height: 80px;
  cursor: pointer;
}

.evidence-image {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.audit-content {
  .audit-result {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }

  .audit-label {
    color: #666;
    font-size: 14px;
  }

  .audit-value {
    font-size: 14px;
    font-weight: bold;
    margin-left: 5px;

    &.audit-approved {
      color: #52c41a;
    }

    &.audit-rejected {
      color: #ff4d4f;
    }
  }
}

.refund-success-info {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background-color: #f6ffed;
  border-radius: 6px;
  border: 1px solid #b7eb8f;
}

.success-icon {
  margin-right: 15px;
  margin-top: 2px;
}

.success-content {
  flex: 1;
}

.success-title {
  display: block;
  color: #52c41a;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.success-desc {
  display: block;
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.success-time {
  display: block;
  color: #999;
  font-size: 12px;
}

.timeline {
  .timeline-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      .timeline-dot {
        background-color: #1890ff;
        border-color: #1890ff;
      }

      .timeline-title {
        color: #333;
        font-weight: bold;
      }
    }

    &.completed {
      .timeline-dot {
        background-color: #52c41a;
        border-color: #52c41a;
      }
    }
  }
}

.timeline-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f0f0f0;
  border: 2px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  margin-top: 2px;
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  display: block;
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.timeline-time {
  display: block;
  color: #999;
  font-size: 12px;
}

.loading-state,
.error-state {
  padding: 50px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}
</style>
