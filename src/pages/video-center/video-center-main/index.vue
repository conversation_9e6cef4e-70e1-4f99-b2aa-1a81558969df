<template>
  <div class="video-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="video" />
          视频管理
        </div>
        <div class="page-desc">上传视频，在课程管理中进行关联</div>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="addModel" size="large">
          <t-icon name="upload" />
          上传视频
        </t-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧分组列表 -->
      <div class="sidebar">
        <action-list @select="onSelected" ref="actionListRef"></action-list>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索筛选区域 -->
        <div class="filter-section">
          <div class="filter-content">
            <t-form :data="form" ref="formSearch" layout="inline" class="filter-form">
              <div class="filter-row">
                <t-form-item label="视频名称" name="videoName" class="filter-item">
                  <t-input
                    v-model="form.videoName"
                    :clearable="true"
                    placeholder="请输入视频名称"
                    class="filter-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="search" />
                    </template>
                  </t-input>
                </t-form-item>

                <t-form-item label="上传日期" name="uploadDate" class="filter-item">
                  <t-date-range-picker
                    v-model="searchDate"
                    :clearable="true"
                    allow-input
                    placeholder="请选择上传日期"
                    class="filter-date"
                  />
                </t-form-item>

                <!-- 操作按钮 -->
                <div class="filter-buttons">
                  <t-button theme="primary" @click="onSubmit" class="search-btn">
                    <t-icon name="search" />
                    查询
                  </t-button>
                  <t-button variant="outline" @click="onReset" class="reset-btn">
                    <t-icon name="refresh" />
                    重置
                  </t-button>
                </div>
              </div>
            </t-form>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-section">
          <common-table
            ref="commonTable"
            :columns="columns"
            :url="url"
            :isRequest="!!activeId"
            :params="tableParams"
            :operations="operations"
            class="video-table"
          >
          </common-table>
        </div>
      </div>
    </div>

    <video-upload :visible="uploadVideoModalVisible" @close="uploadVideoModalVisible = false"></video-upload>
    <preview-resource style="z-index: 3000" :visible="previewVisible" :src="previewResource" @close="previewVisible = false"></preview-resource>
  </div>
</template>

<script>
import { SearchIcon } from 'tdesign-icons-vue';
import ActionList from './components/action-list.vue';
import CommonTable from '@/components/common-table/index.vue';
import {getMenuChildrenList, getOperationTypeList, randomString} from '@/utils/utils.js';
import videoUpload from "@/pages/video-center/video-center-main/components/videoUpload.vue";
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import PreviewResource from "@/components/preview-resource/index.vue";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";

export default {
  name: 'task-library',
  components: {PreviewResource, ActionList, CommonTable, videoUpload },
  data() {
    return {
      url: sysVideoUploadApi.queryVideoListByGroupId.url,
      searchDate: [],
      params: {
        videoName: '',
        createDate: '',
        endDate: '',
      },
      form: {
        videoName: '',
        activeId: '',
        createDate: '',
        endDate: '',
      },
      activeOptions: [],
      columns: [
        {
          colKey: 'videoName',
          title: '视频名称',
          width: 150,
        },
        {
          colKey: 'videoGroupid',
          title: '视频分组',
          width: 150,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getVideoGroup(val);
          },
        },
        // {
        //   colKey: 'videoDuration',
        //   title: '视频时长',
        //   width: 80,
        // },
        {
          colKey: 'videoSize',
          title: '视频大小',
          width: 80,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val? `${val.toFixed(1)}MB` : '';
          },
        },
        {
          colKey: 'op',
          title: '操作',
          width: 100,
          cell: 'op',
        },
      ],
      uploadVideoModalVisible: false,
      previewVisible: false,
      previewResource: '',
      activeId: 'ALL',
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      },
      userOperationTab: [], // 用户可操作按钮
    };
  },
  computed: {
    tableParams() {
      return {
        id: this.activeId === 'ALL' ? 'ALL' : this.activeId,
        videoName: this.params.videoName,
        startDate: this.params.startDate,
        endDate: this.params.endDate,
      };
    },
    operations() {
      return [
        {
          type: 'preview',
          onClick: this.onPreview,
        },
        {
          type: 'download',
          onClick: this.onDownload,
        },
        {
          type: 'delete',
          onClick: this.onDel,
          visible: this.userOperation.hasDelete,
        },
      ]
    }
  },
  watch: {
    '$store.state.upload.successTasks': {
      handler(newVal, oldVal) {
        this.$refs.commonTable.getList();
      },
      deep: true,
    }
  },
  mounted() {
    // 获取用户操作类型按钮列表
    this.getOperationTypeList();
  },
  methods: {
    /**
     * @description: 获取操作类型按钮列表
     * <AUTHOR>
     * @date 2025/6/2 11:29
     */
    getOperationTypeList() {
      // 登录后用户具有的菜单已经存入了storage中，从中获取
      this.childrenButton = getMenuChildrenList();
      // console.log("获取到具有按钮菜单：", this.childrenButton);
      if(this.childrenButton && this.childrenButton.length > 0) {
        this.childrenButton.map((item) => {
          if (item.menuType && item.menuType == SYSTEM_MENU_CONST.O.value && item.menuUrl) {
            // 截取操作按钮，区分类型
            const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
            this.userOperationTab.push(operationType);
          }
        })
      }
      this.userOperation = getOperationTypeList(this.userOperationTab)
      // console.log("获取到可操作按钮类型：", this.userOperation);
      // console.log("操作栏：", this.operations);
    },
    addModel() {
      this.uploadVideoModalVisible = true;
    },
    onSelected(val) {
      this.activeId = val;
    },
    getVideoGroup(val) {
      const data = this.$refs.actionListRef.actionTypeList;
      return data.find((item) => item.id === val)?.groupName || '';
    },
    async onDel(row) {
      const { code } = await this.$request.post(sysVideoUploadApi.deleteVideoById.url, { id: row.id });
      if (code === 0) {
        this.$message.success({ content: '删除成功' });
        this.$refs.commonTable.getList();
      } else {
        this.$message.error({ content: '操作失败' });
      }
    },
    onPreview(data) {
      this.previewVisible = true;
      this.previewResource = data.videoUrl;
    },
    onDownload(data) {
      const { videoUrl } = data;
      const a = document.createElement('a');
      a.href = videoUrl;
      a.setAttribute('download', data.videoName);
      a.click();
    },
    onSubmit() {
      if (this.searchDate) {
        if (this.searchDate.length > 0) {
          this.form.startDate = this.searchDate[0];
        }
        if (this.searchDate.length > 1) {
          this.form.startDate = this.searchDate[0];
          this.form.endDate = this.searchDate[1];
        }
      }
      this.params = {
        videoName: this.form.videoName,
        startDate: this.form.startDate,
        endDate: this.form.endDate,
      };
    },
    onReset() {
      this.searchDate = [];
      this.form = {
        videoName: '',
        startDate: '',
        endDate: '',
      };
      this.onSubmit();
    },

    // 刷新数据
    refreshData() {
      this.$refs.commonTable.getList();
    },
  },
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面主体样式 ====================
.video-management-page {
  padding: 12px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      .t-button {
        height: 44px;
        padding: 0 24px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 24px;
    min-height: calc(100vh - 300px);
  }

  // 左侧边栏
  .sidebar {
    width: 250px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
  }

  // 右侧内容区域
  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 0;
  }

  // 筛选区域
  .filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .filter-content {
      padding: 16px 20px;

      .filter-form {
        .filter-row {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          align-items: flex-end;

          .filter-item {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            margin-bottom: 16px;

            .filter-input,
            .filter-date {
              width: 100%;
              border-radius: 8px;

              transition: all 0.2s ease;

              &:hover {
                border-color: #9ca3af;
              }

              &:focus,
              &.t-is-focused {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-left: auto;
            flex-shrink: 0;

            .t-button {
              padding: 0 16px;
              border-radius: 8px;
              font-weight: 500;
              transition: all 0.3s ease;

              .t-icon {
                margin-right: 4px;
              }

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }

            .search-btn {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              border: none;
              color: #fff;

              &:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
              }
            }

            .reset-btn {

              background: #fff;
              color: #374151;

              &:hover {
                background: #f9fafb;
                border-color: #9ca3af;
              }
            }
          }
        }
      }
    }
  }

  // 表格区域
  .table-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    flex: 1;
    min-height: 0;
    overflow: hidden;

    .video-table {
      height: 100%;

      /deep/ .t-table {
        height: 100%;

        .t-table__header {
          th {
            background: #f8f9fa;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
          }
        }

        .t-table__body {
          tr {
            transition: all 0.2s ease;

            &:hover {
              background: #f8f9fa;
            }

            td {
              border-bottom: 1px solid #f3f4f6;
              color: #374151;
            }
          }
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .video-management-page {
    .main-content {
      flex-direction: column;

      .sidebar {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .video-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            flex-direction: column;

            .filter-item {
              min-width: 100%;
              max-width: 100%;
            }

            .filter-buttons {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 12px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
