<template>
  <t-dialog
    :visible.sync='modalVisible'
    width="600px"
    @close='closeModal'
    header="视频上传"
    class='video-upload-dialog'
    @confirm='uploadVideo'
    :confirm-btn="{ content: '开始上传', theme: 'primary' }"
    :cancel-btn="{ content: '取消', variant: 'outline' }"
  >
    <div class="upload-container">

      <!-- 表单内容 -->
      <t-form ref='form' :data='form' :rules='rules' @submit='onSubmit' class="upload-form">
        <t-form-item label='视频分组' name='className'>
          <t-select
            v-model='form.className'
            :options='classList'
            placeholder="请选择视频分组"
            class="group-select"
          >
            <template #prefixIcon>
              <t-icon name="folder" />
            </template>
          </t-select>
        </t-form-item>

        <t-form-item label='选择视频' name='video'>
          <t-upload
            :key="uploadKey"
            ref="uploadRef"
            theme="file-flow"
            placeholder="支持批量上传文件"
            :multiple="true"
            :autoUpload="false"
            @change="onSubmit"
          >
            <t-button theme="primary" size="small" @click.prevent.stop="uploadVideo">选择视频文件</t-button>
          </t-upload>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script>

import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import vodUploadManager from "@/utils/vodUploadManager";

export default {
  name: 'VideoUpload',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      classList: [],
      rules: {
        className: [{ required: true, message: '视频分组必选' }],
        video: [{ required: true, message: '视频必上传' }],
      },
      form: {},
      modalVisible: false,
      uploadKey: Date.now(),
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.form = {};
      } else {
        this.$refs.form.reset();
        this.form = {};
      }
      this.modalVisible = val;
      this.modalVisible && this.getClassList();
    },
  },
  methods: {
    closeModal() {
      this.uploadKey = Date.now();
      this.$emit('close');
    },
    async getClassList() {
      const res = await this.$request.get(sysVideoUploadApi.queryVideoGroupList.url);
      if (res.code === 0) {
        this.classList = res.data.map(item => ({ label: item.groupName, value: item.id }));
      } else {
        this.$message.error({ content: '获取视频分组列表失败' });
      }
    },
    uploadVideo() {
      // this.$store.commit('SET_SHOW_UPLOAD_LIST', true);
      if (!this.form.className) {
        return this.$message.error({ content: '请选择视频分组' });
      }
      this.$refs.uploadRef.triggerUpload();
    },
    async onSubmit(data) {
      if (!this.form.className) {
        this.uploadKey = Date.now();
        return this.$message.error({ content: '请选择视频分组' });
      }
      vodUploadManager.addFiles({files: data, groupId: this.form.className}, this.$store);
      this.$message.success({content: '操作成功'});
      this.closeModal();
    },
  },
};
</script>

<style lang='less' scoped>
.video-upload-dialog {
  /deep/ .t-dialog__body {
    padding: 24px;
  }

  .upload-container {

    .upload-form {
      .t-form-item {
        margin-bottom: 20px;

        .t-form__label {
          font-weight: 500;
          color: #374151;
          margin-bottom: 8px;
        }
      }
    }
  }
}

// 全局组件优化
/deep/ .t-form__controls {
  margin-left: 0 !important;
}

/deep/ .t-upload__flow-bottom {
  display: none !important;
}
</style>

