<template>
  <t-dialog :visible.sync='modalVisible' width="35%" @close='closeModal' :header="form.id ? '编辑视频分组' : '新建视频分组'"
            class='action-type-edit-dialog' @confirm='onValid'>

    <t-form ref='form' :data='form' :rules='rules' @submit='onSubmit'>
      <t-form-item label='视频分组名称' name='className'>
        <t-input v-model='form.groupName' placeholder='请输入视频分组名称' :maxlength='15'></t-input>
      </t-form-item>
    </t-form>

  </t-dialog>
</template>

<script>
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";

export default {
  name: 'video-group-modal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    initForm: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      rules: {
        groupName: [{ required: true, message: '视频分组名称必填' },{message: '请输入有效信息', whitespace: true}],
      },
      form: { ...this.initForm },
      modalVisible: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.form = { ...this.initForm };
      } else {
        this.$refs.form.reset();
        this.form = {};
      }
      this.modalVisible = val;
    },
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },
    async onValid() {
      const data = await this.$refs.form.validate();
      if (typeof data === 'boolean') {
        this.$refs.form.submit();
      }
    },
    async onSubmit() {
      const { id = '', groupName } = this.form;
      if (id) {
        await this.$request.post(`${sysVideoUploadApi.updateVideoGroup.url}`, { id, groupName });
      } else {
        await this.$request.post(sysVideoUploadApi.addVideoGroup.url, { groupName });
      }

      this.$message.success({ content: '操作成功' });
      this.$emit('success');
    },


  },
};
</script>

<style lang='less' scoped>
.action-type-edit-dialog {
  /deep/ .t-dialog__body {
    padding-top: 30px;
  }
}
/deep/ .t-form__controls {
  margin-left: 125px !important;
}
</style>

