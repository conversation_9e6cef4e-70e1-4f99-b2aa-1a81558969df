<template>
  <div class="video-group-container">
    <!-- 头部标题区域 -->
    <div class="group-header">
      <h3 class="group-title">
        <t-icon name="folder" />
        视频分组
      </h3>
      <t-button theme="default" shape="square" variant="text" @click="handleAdd" class="add-group-btn">
        <add-circle-icon />
      </t-button>
    </div>

    <!-- 分组列表区域 -->
    <div class="group-list">
      <div class="group-item" :class="{ active: activeId === 'ALL' }" @click="activeId = 'ALL'">
        <span class="group-name">全部</span>
      </div>
      <template v-for="i in actionTypeList">
        <div class="group-item" :key="i.id" :class="{ active: activeId === i.id }" @click="activeId = i.id">
          <span class="group-name">{{ i.groupName }}</span>
          <t-dropdown class="group-actions">
            <more-icon class="action-icon" />
            <t-dropdown-menu slot="dropdown">
              <t-dropdown-item @click="onUpdate">
                <t-icon name="edit" />
                编辑
              </t-dropdown-item>
              <t-dropdown-item @click="onDel">
                <t-icon name="delete" />
                删除
              </t-dropdown-item>
            </t-dropdown-menu>
          </t-dropdown>
        </div>
      </template>
    </div>

    <edit-type-modal
      :initForm="initForm"
      :visible="addModalVisible"
      @close="onClose"
      @success="onSuccess"
    ></edit-type-modal>
  </div>
</template>

<script>
import { AddCircleIcon, MoreIcon } from 'tdesign-icons-vue';
import EditTypeModal from './edit-task-type.vue';
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";

export default {
  name: 'type-list',
  components: { AddCircleIcon, MoreIcon, EditTypeModal },
  data() {
    return {
      addModalVisible: false,
      actionTypeList: [],
      activeId: 'ALL',
      initForm: {},
    };
  },
  watch: {
    activeId(val) {
      this.$emit('select', val);
    },
  },
  created() {
    this.getList();
  },

  methods: {
    handleAdd() {
      this.addModalVisible = true;
    },
    onClose() {
      this.addModalVisible = false;
      this.initForm = {};
    },
    onSuccess() {
      this.onClose();
      this.getList();
    },
    async getList() {
      const { data } = await this.$request.get(sysVideoUploadApi.queryVideoGroupList.url);
      this.actionTypeList = data;
    },
    async onDel() {
      const res = await this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          const { code } = await this.$request.post(sysVideoUploadApi.deleteVideoGroup.url, {id: this.activeId});
          if (code !== 0) {
            this.$message.error({ content: '操作失败' });
            return;
          }
          this.$message.success({ content: '操作成功' });
          this.getList();
          this.activeId = 'ALL';
          res.destroy();
        },
      });
    },
    onUpdate() {
      const item = this.actionTypeList.find((v) => v.id === this.activeId);
      this.initForm = item;
      this.addModalVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
.video-group-container {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;

  // 头部标题区域
  .group-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .group-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      .t-icon {
        font-size: 18px;
      }
    }

    .add-group-btn {
      width: 28px;
      height: 28px;
      border-radius: 6px;
      color: #fff;
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: scale(1.05);
      }

      .t-icon {
        font-size: 14px;
      }
    }
  }

  // 分组列表区域
  .group-list {
    flex: 1;
    overflow-y: auto;

    .group-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 14px 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: #f8f9fa;

        .group-actions {
          opacity: 1;
        }
      }

      // 选中状态样式 - 与用户管理保持一致
      &.active {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
        color: #1565c0 !important;
        border: 1px solid #90caf9 !important;
        box-shadow: 0 2px 8px rgba(25, 101, 192, 0.15) !important;
        padding-left: 20px; // 因为有左边框，所以增加左内边距
        border-left: 4px solid #1565c0 !important;

        .group-name {
          font-weight: 600;
          color: #1565c0;
        }

        .action-icon {
          color: #1565c0;
          display: block;
        }
      }

      .group-name {
        font-size: 14px;
        color: #374151;
        transition: all 0.2s ease;
      }

      .group-actions {
        opacity: 0;
        transition: opacity 0.2s ease;

        .action-icon {
          font-size: 16px;
          color: #6b7280;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #374151;
          }
        }
      }
    }
  }
}

// 下拉菜单样式优化
/deep/ .t-dropdown__menu {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  .t-dropdown__item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;

    &:hover {
      background: #f8f9fa;
      color: #3b82f6;

      .t-icon {
        color: #3b82f6;
      }
    }

    .t-icon {
      font-size: 14px;
      color: #6b7280;
    }
  }
}
</style>
