<template>
  <div class="category-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <t-icon name="layers" />
          商品分类管理
        </h1>
        <p class="page-desc">管理商品分类，支持多级分类结构</p>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="handleAdd">
          <t-icon name="add" />
          新增分类
        </t-button>
        <t-button
          theme="danger"
          variant="outline"
          @click="handleBatchDelete"
          :disabled="selectedRowKeys.length === 0"
        >
          <t-icon name="delete" />
          批量删除
        </t-button>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <t-card class="search-card">
      <t-form :data="searchForm" layout="inline" @submit="handleSearch" @reset="handleReset">
        <t-form-item label="分类名称" name="name">
          <t-input
            v-model="searchForm.name"
            placeholder="请输入分类名称"
            clearable
            style="width: 200px;"
          />
        </t-form-item>
        <t-form-item label="父分类" name="parentId">
          <t-select
            v-model="searchForm.parentId"
            :options="parentCategoryOptions"
            placeholder="请选择父分类"
            clearable
            style="width: 200px;"
          />
        </t-form-item>
        <div class="filter-buttons">
          <t-button theme="primary" @click="handleSearch" class="search-btn">
            <t-icon name="search" />
            查询
          </t-button>
          <t-button variant="outline" @click="handleReset" class="reset-btn">
            <t-icon name="refresh" />
            重置
          </t-button>
        </div>
      </t-form>
    </t-card>

    <!-- 分类树形表格 -->
    <t-card class="table-card">
      <t-table
        :columns="columns"
        :data="categoryTree"
        row-key="id"
        :selected-row-keys="selectedRowKeys"
        :loading="dataLoading"
        @select-change="handleSelectChange"
        stripe
        hover
      >
        <template #name="{ row, rowIndex }">
          <div class="category-name" :style="{ paddingLeft: (getNodeLevel(row) * 24) + 'px' }">
            <!-- 展开/折叠图标 -->
            <span
              v-if="row.children && row.children.length > 0"
              class="tree-expand-icon"
              @click="toggleExpand(row)"
            >
              <t-icon :name="isExpanded(row.id) ? 'chevron-down' : 'chevron-right'" />
            </span>
            <span v-else class="tree-expand-placeholder"></span>

            <span class="name-text">{{ row.name }}</span>
            <t-tag v-if="row.parentId === '0'" theme="primary" variant="light" size="small">
              顶级
            </t-tag>
          </div>
        </template>

        <template #parentName="{ row }">
          <span v-if="row.parentName">{{ row.parentName }}</span>
          <t-tag v-else theme="primary" variant="light">顶级分类</t-tag>
        </template>

        <template #productCount="{ row }">
          <t-tag theme="success" variant="light">{{ row.productCount || 0 }}</t-tag>
        </template>

        <template #createdAt="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>

        <template #op="{ row }">
          <t-space>
            <t-button variant="text" theme="primary" @click="handleEdit(row)">
              <t-icon name="edit" />
              编辑
            </t-button>
            <t-button variant="text" theme="success" @click="handleAddChild(row)">
              <t-icon name="add" />
              添加子分类
            </t-button>
            <t-button variant="text" theme="danger" @click="handleDelete(row)">
              <t-icon name="delete" />
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 新增/编辑分类对话框 -->
    <t-dialog
      :visible="formDialog.visible"
      :header="formDialog.title"
      width="600px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
      @close="handleCancel"
    >
      <t-form
        ref="formRef"
        :data="formDialog.formData"
        :rules="formRules"
        label-align="right"
        :label-width="120"
      >
        <t-form-item label="分类名称" name="name">
          <t-input
            v-model="formDialog.formData.name"
            placeholder="请输入分类名称"
            :maxlength="50"
          />
        </t-form-item>

        <t-form-item label="父分类" name="parentId">
          <t-select
            v-model="formDialog.formData.parentId"
            :options="parentCategoryOptions"
            placeholder="请选择父分类，不选则为顶级分类"
            clearable
          />
        </t-form-item>

        <t-form-item label="分类描述" name="remark">
          <t-textarea
            v-model="formDialog.formData.remark"
            placeholder="请输入分类描述"
            :maxlength="200"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>

        <t-form-item label="排序值" name="sortOrder">
          <t-input-number
            v-model="formDialog.formData.sortOrder"
            :min="0"
            :max="9999"
            placeholder="数值越小排序越靠前"
          />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 删除确认对话框 -->
    <t-dialog
      :visible="deleteDialog.visible"
      header="删除确认"
      @confirm="handleConfirmDelete"
      @cancel="deleteDialog.visible = false"
      @close="deleteDialog.visible = false"
    >
      <div class="delete-content">
        <t-icon name="error-circle" class="warning-icon" />
        <div class="delete-text">
          <p class="delete-title">确定要删除分类"{{ deleteDialog.categoryName }}"吗？</p>
          <p class="delete-desc" v-if="deleteDialog.hasChildren">
            该分类下还有子分类，删除后子分类也将被删除
          </p>
          <p class="delete-desc" v-if="deleteDialog.hasProducts">
            该分类下还有 {{ deleteDialog.productCount }} 个商品，删除后这些商品将失去分类
          </p>
        </div>
      </div>
    </t-dialog>

    <!-- 批量删除确认对话框 -->
    <t-dialog
      :visible="batchDeleteDialog.visible"
      header="批量删除确认"
      @confirm="handleConfirmBatchDelete"
      @cancel="batchDeleteDialog.visible = false"
      @close="batchDeleteDialog.visible = false"
    >
      <div class="delete-content">
        <t-icon name="error-circle" class="warning-icon" />
        <div class="delete-text">
          <p class="delete-title">确定要删除选中的 {{ selectedRowKeys.length }} 个分类吗？</p>
          <p class="delete-desc">删除后无法恢复，请谨慎操作</p>
        </div>
      </div>
    </t-dialog>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import productCategoryApi from '@/constants/api/back/product-category.api';

export default Vue.extend({
  name: 'CategoryManagement',

  data() {
    return {
      // 搜索表单
      searchForm: {
        name: '',
        parentId: null,
      },

      // 分类树形数据
      categoryTree: [],
      originalTreeData: [], // 原始树形数据
      parentCategoryOptions: [],

      // 表格配置
      rowKey: 'id',
      selectedRowKeys: [],
      expandedRowKeys: [], // 展开的行keys
      dataLoading: false,



      // 表单对话框
      formDialog: {
        visible: false,
        title: '新增分类',
        formData: {
          id: null,
          name: '',
          parentId: null,
          remark: '',
          sortOrder: 0,
        }
      },

      // 删除对话框
      deleteDialog: {
        visible: false,
        categoryId: null,
        categoryName: '',
        hasChildren: false,
        hasProducts: false,
        productCount: 0,
      },

      // 批量删除对话框
      batchDeleteDialog: {
        visible: false,
      },
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' },
        ],
        sortOrder: [
          { required: true, message: '请输入排序值', trigger: 'blur' },
          { type: 'number', min: 0, max: 9999, message: '排序值范围为 0-9999', trigger: 'blur' },
        ],
      },

      // 表格列配置
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 50,
          fixed: 'left',
        },
        {
          title: '分类名称',
          colKey: 'name',
          width: 250,
          ellipsis: true,
        },
        {
          title: '父分类',
          colKey: 'parentName',
          width: 150,
          ellipsis: true,
        },
        {
          title: '商品数量',
          colKey: 'productCount',
          width: 100,
          align: 'center',
        },
        {
          title: '排序值',
          colKey: 'sortOrder',
          width: 100,
          align: 'center',
        },
        {
          title: '描述',
          colKey: 'remark',
          width: 200,
          ellipsis: true,
        },
        {
          title: '创建时间',
          colKey: 'createdAt',
          width: 160,
          align: 'center',
        },
        {
          title: '操作',
          colKey: 'op',
          width: 350,
          align: 'center',
          fixed: 'right',
        },
      ],

    };
  },

  computed: {
    // 计算属性
  },

  mounted() {
    this.loadCategoryTree();
    this.loadParentCategoryOptions();
    // 延迟设置展开状态
    this.$nextTick(() => {
      console.log('组件挂载完成，树形配置:', this.treeConfig);
    });
  },

  methods: {
    // 加载分类树形数据
    async loadCategoryTree() {
      try {
        this.dataLoading = true;
        const response = await this.$request.get(productCategoryApi.getCategoryTree.url);

        if (response.code === 0) {
          // 保存原始树形数据
          this.originalTreeData = response.data || [];
          // 设置默认展开所有父级节点
          this.expandedRowKeys = this.getAllParentKeys(response.data || []);
          // 扁平化数据用于表格显示
          this.categoryTree = this.flattenTreeData(response.data || []);

          console.log('=== 树形表格调试信息 ===');
          console.log('原始数据:', response.data);
          console.log('扁平化数据:', this.categoryTree);
          console.log('展开的keys:', this.expandedRowKeys);
        } else {
          MessagePlugin.error(response.message || '获取分类数据失败');
        }
      } catch (error) {
        console.error('获取分类数据失败:', error);
        MessagePlugin.error('获取分类数据失败');
      } finally {
        this.dataLoading = false;
      }
    },

    // 加载父分类选项
    async loadParentCategoryOptions() {
      try {
        const response = await this.$request.get(productCategoryApi.getAllCategories.url);

        if (response.code === 0) {
          this.parentCategoryOptions = [
            { label: '顶级分类', value: null },
            ...(response.data || []).map(item => ({
              label: item.name,
              value: item.id
            }))
          ];
        }
      } catch (error) {
        console.error('获取父分类选项失败:', error);
      }
    },

    // 处理树形数据
    processTreeData(data) {
      return data.map(item => {
        const processedItem = {
          ...item,
        };

        // 递归处理子节点
        if (item.children && item.children.length > 0) {
          processedItem.children = this.processTreeData(item.children);
        }

        return processedItem;
      });
    },

    // 获取所有父级节点的keys
    getAllParentKeys(data) {
      const keys = [];
      data.forEach(item => {
        if (item.children && item.children.length > 0) {
          keys.push(item.id);
          // 递归获取子级的父级keys
          keys.push(...this.getAllParentKeys(item.children));
        }
      });
      return keys;
    },

    // 扁平化树形数据
    flattenTreeData(data, level = 0, parentExpanded = true) {
      let result = [];

      data.forEach(item => {
        // 添加当前节点
        const flatItem = {
          ...item,
          level,
          hasChildren: item.children && item.children.length > 0,
        };

        // 只有当父级展开时才显示当前节点
        if (parentExpanded) {
          result.push(flatItem);
        }

        // 如果有子节点且当前节点展开，则递归处理子节点
        if (item.children && item.children.length > 0) {
          const isCurrentExpanded = this.isExpanded(item.id);
          const childrenFlat = this.flattenTreeData(
            item.children,
            level + 1,
            parentExpanded && isCurrentExpanded
          );
          result = result.concat(childrenFlat);
        }
      });

      return result;
    },

    // 获取节点层级
    getNodeLevel(row) {
      return row.level || 0;
    },

    // 检查节点是否展开
    isExpanded(nodeId) {
      return this.expandedRowKeys.includes(nodeId);
    },

    // 切换展开/折叠状态
    toggleExpand(row) {
      const index = this.expandedRowKeys.indexOf(row.id);
      if (index > -1) {
        // 折叠
        this.expandedRowKeys.splice(index, 1);
      } else {
        // 展开
        this.expandedRowKeys.push(row.id);
      }

      // 重新扁平化数据
      this.categoryTree = this.flattenTreeData(this.originalTreeData);
    },

    // 搜索
    handleSearch() {
      this.loadCategoryTree();
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        name: '',
        parentId: null,
      };
      this.loadCategoryTree();
    },

    // 表格选择变化
    handleSelectChange(selectedRowKeys: number[]) {
      this.selectedRowKeys = selectedRowKeys;
    },


    // 新增分类
    handleAdd() {
      this.formDialog.title = '新增分类';
      this.formDialog.formData = {
        id: null,
        name: '',
        parentId: null,
        remark: '',
        sortOrder: 0,
      };
      this.formDialog.visible = true;
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },

    // 编辑分类
    handleEdit(row) {
      this.formDialog.title = '编辑分类';
      this.formDialog.formData = { ...row };
      this.formDialog.visible = true;
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },

    // 添加子分类
    handleAddChild(row) {
      this.formDialog.title = '新增子分类';
      this.formDialog.formData = {
        id: null,
        name: '',
        parentId: row.id,
        remark: '',
        sortOrder: 0,
      };
      this.formDialog.visible = true;
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },
    // 取消表单
    handleCancel() {
      this.formDialog.visible = false;
    },

    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.formRef.validate();
        if (!valid) return;

        const response = await this.$request.post(
          productCategoryApi.saveCategory.url,
          this.formDialog.formData
        );

        if (response.code === 0) {
          MessagePlugin.success(this.formDialog.formData.id ? '更新成功' : '新增成功');
          this.formDialog.visible = false;
          this.loadCategoryTree();
          this.loadParentCategoryOptions();
        } else {
          MessagePlugin.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('提交失败:', error);
        MessagePlugin.error('操作失败');
      }
    },

    // 删除分类
    handleDelete(row) {
      this.deleteDialog.visible = true;
      this.deleteDialog.categoryId = row.id;
      this.deleteDialog.categoryName = row.name;
      this.deleteDialog.hasChildren = row.children && row.children.length > 0;
      this.deleteDialog.hasProducts = row.productCount > 0;
      this.deleteDialog.productCount = row.productCount || 0;
    },

    // 确认删除
    async handleConfirmDelete() {
      try {
        const response = await this.$request.delete(
          `${productCategoryApi.deleteCategory.url}/${this.deleteDialog.categoryId}`
        );

        if (response.code === 0) {
          MessagePlugin.success('删除成功');
          this.loadCategoryTree();
          this.loadParentCategoryOptions();
        } else {
          MessagePlugin.error(response.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      } finally {
        this.deleteDialog.visible = false;
      }
    },
    // 批量删除
    handleBatchDelete() {
      if (this.selectedRowKeys.length === 0) {
        MessagePlugin.warning('请选择要删除的分类');
        return;
      }
      this.batchDeleteDialog.visible = true;
    },

    // 确认批量删除
    async handleConfirmBatchDelete() {
      try {
        const response = await this.$request.post(
          `${productCategoryApi.deleteCategory.url}/batch`,
          { ids: this.selectedRowKeys }
        );

        if (response.code === 0) {
          MessagePlugin.success('批量删除成功');
          this.selectedRowKeys = [];
          this.loadCategoryTree();
          this.loadParentCategoryOptions();
        } else {
          MessagePlugin.error(response.message || '批量删除失败');
        }
      } catch (error) {
        console.error('批量删除失败:', error);
        MessagePlugin.error('批量删除失败');
      } finally {
        this.batchDeleteDialog.visible = false;
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.category-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px 32px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .t-icon {
          font-size: 28px;
          color: #6366f1;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .t-button {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 搜索卡片
  .search-card {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.t-card__body) {
      padding: 20px 24px;
    }

    .t-form {
      .t-form-item {
        margin-bottom: 0;
      }
    }

    .filter-buttons {
      display: flex;
      gap: 12px;
      margin-left: auto;

      .t-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 表格卡片
  .table-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.t-card__body) {
      padding: 0;
    }

    :deep(.t-table) {
      border-radius: 12px;
      overflow: hidden;

      .t-table__header {
        background: #f8fafc;
      }

      .t-button--variant-text {
        padding: 4px 8px;
        margin: 0 2px;
      }
    }
  }

  // 删除确认对话框
  .delete-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 0;

    .warning-icon {
      font-size: 24px;
      color: #f59e0b;
      flex-shrink: 0;
      margin-top: 2px;
    }

    .delete-text {
      flex: 1;

      .delete-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 8px 0;
      }

      .delete-desc {
        font-size: 14px;
        color: #6b7280;
        margin: 4px 0;
        line-height: 1.5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 20px;

      .header-left {
        text-align: center;
      }

      .header-actions {
        width: 100%;
        justify-content: center;

        .t-button {
          flex: 1;
          max-width: 150px;
        }
      }
    }

    .search-card {
      :deep(.t-form) {
        .t-form-item {
          margin-bottom: 16px;
        }
      }

      .filter-buttons {
        margin-left: 0;
        margin-top: 16px;
        width: 100%;
        justify-content: center;
      }
    }
  }
}

// 分类名称样式
.category-name {
  display: flex;
  align-items: center;
  gap: 8px;

  .tree-expand-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 2px;
    transition: all 0.2s ease;
    color: #667eea;

    &:hover {
      background: #f0f4ff;
      color: #5a67d8;
    }

    .t-icon {
      font-size: 12px;
    }
  }

  .tree-expand-placeholder {
    width: 16px;
    height: 16px;
  }

  .name-text {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
  }
}

// 树形表格样式
:deep(.t-table) {
  .t-table__tree-col {
    .t-table__tree-op-icon {
      color: #667eea;

      &:hover {
        background: #f0f4ff;
      }
    }
  }

  .t-table__tree-content {
    .category-name {
      .name-text {
        font-weight: 500;
      }
    }
  }

  // 子分类行样式
  .t-table__row--level-1 {
    background: #fafbfc;

    .category-name {
      .name-text {
        color: #6b7280;
        font-size: 13px;
      }
    }
  }

  // 顶级分类行样式
  .t-table__row--level-0 {
    .category-name {
      .name-text {
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}
</style>
