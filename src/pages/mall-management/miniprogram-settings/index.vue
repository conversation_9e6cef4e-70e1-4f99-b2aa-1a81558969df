<template>
  <div class="banner-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="image" />
          横幅管理
        </div>
        <div class="page-desc">管理小程序首页轮播、消息通知和个人中心横幅</div>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="showCreateDialog">
          <t-icon name="add" />
          新增横幅
        </t-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-content">
        <t-form :data="filterForm" layout="inline" class="filter-form">
          <div class="filter-row">
            <t-form-item label="横幅标题" class="filter-item">
              <t-input
                v-model="filterForm.title"
                placeholder="请输入横幅标题"
                clearable
                class="filter-input"
              />
            </t-form-item>

            <t-form-item label="横幅类型" class="filter-item">
              <t-select
                v-model="filterForm.bannerType"
                placeholder="请选择横幅类型"
                :options="bannerTypeOptions"
                clearable
                class="filter-select"
              />
            </t-form-item>

            <t-form-item label="启用状态" class="filter-item">
              <t-select
                v-model="filterForm.isActive"
                placeholder="请选择状态"
                :options="statusOptions"
                clearable
                class="filter-select"
              />
            </t-form-item>

            <t-form-item label="创建时间" class="filter-item filter-item-wide">
              <t-date-range-picker
                v-model="filterForm.timeRange"
                :clearable="true"
                allow-input
                class="filter-date"
              />
            </t-form-item>

            <div class="filter-buttons">
              <t-button theme="primary" @click="handleSearch" class="search-btn">
                <t-icon name="search" />
                查询
              </t-button>
              <t-button variant="outline" @click="handleReset" class="reset-btn">
                <t-icon name="refresh" />
                重置
              </t-button>
            </div>
          </div>
        </t-form>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="table-info">
          <t-icon name="image" />
          <span>横幅列表</span>
          <span class="count-info">（共 {{ pagination.total }} 条）</span>
        </div>
        <div v-if="selectedRowKeys.length > 0" class="batch-actions">
          <span class="selected-count">已选择 {{ selectedRowKeys.length }} 项</span>
          <t-button variant="outline" theme="danger" @click="handleBatchDelete">
            <t-icon name="delete" />
            批量删除
          </t-button>
        </div>
      </div>

      <div class="toolbar-right">
        <t-button variant="outline" @click="refreshData" class="refresh-btn">
          <t-icon name="refresh" />
          刷新
        </t-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <t-card class="table-card">
        <t-table
          :columns="columns"
          :data="tableData"
          :rowKey="'id'"
          :pagination="pagination"
          :hover="true"
          :selected-row-keys="selectedRowKeys"
          :loading="dataLoading"
          @change="handleTableChange"
          @select-change="handleSelectChange"
          stripe
        >
          <!-- 横幅类型 -->
          <template #bannerType="{ row }">
            <t-tag :theme="getBannerTypeTheme(row.bannerType)" variant="light-outline">
              {{ getBannerTypeName(row.bannerType) }}
            </t-tag>
          </template>

          <!-- 横幅预览 -->
          <template #preview="{ row }">
            <div class="banner-preview-cell">
              <img v-if="row.imageUrl" :src="row.imageUrl" alt="横幅预览" class="preview-image" />
              <div v-else-if="row.bannerType === 2" class="message-preview">
                <t-icon :name="row.messageIcon || 'notification'" />
                <span>{{ row.messageText }}</span>
              </div>
              <div v-else-if="row.bannerType === 3" class="personal-preview">
                <span>{{ row.contentText }}</span>
                <small>{{ row.subText }}</small>
              </div>
              <span v-else class="no-preview">暂无预览</span>
            </div>
          </template>

          <!-- 关联商品 -->
          <template #productInfo="{ row }">
            <span v-if="row.productId" class="product-link">
              商品ID: {{ row.productId }}
            </span>
            <span v-else class="no-product">未关联</span>
          </template>

          <!-- 启用状态 -->
          <template #isActive="{ row }">
            <t-switch
              :value="row.isActive === 1"
              @change="(value) => handleStatusChange(row, value)"
            />
          </template>
          <template #createdAt="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>

          <!-- 操作列 -->
          <template #actions="{ row }">
            <div class="action-buttons">
              <t-button variant="text" theme="primary" @click="handleEdit(row)">
                <t-icon name="edit" />
                编辑
              </t-button>
              <t-button variant="text" theme="danger" @click="handleDelete(row)">
                <t-icon name="delete" />
                删除
              </t-button>
            </div>
          </template>
        </t-table>
      </t-card>
    </div>

    <!-- 横幅创建/编辑对话框 -->
    <t-dialog
      :visible="dialogVisible"
      :header="dialogTitle"
      width="800px"
      :footer="false"
      @close="handleDialogCancel"
      class="banner-dialog"
    >
      <div class="banner-form">
        <t-form :data="formData" :rules="dynamicFormRules" ref="bannerForm" layout="vertical">
          <!-- 基础信息 -->
          <div class="form-section">
            <h4 class="section-title">基础信息</h4>

            <t-form-item label="横幅标题" name="title">
              <t-input
                v-model="formData.title"
                placeholder="请输入横幅标题"
                :maxlength="50"
                show-word-limit
              />
            </t-form-item>

            <div class="form-row">
              <t-form-item label="横幅类型" name="bannerType" class="form-item">
                <t-select
                  v-model="formData.bannerType"
                  :options="bannerTypeOptions"
                  placeholder="请选择横幅类型"
                  @change="handleBannerTypeChange"
                />
              </t-form-item>

              <t-form-item label="排序权重" name="sortOrder" class="form-item">
                <t-input-number
                  v-model="formData.sortOrder"
                  :min="1"
                  :max="999"
                  placeholder="数值越小越靠前"
                />
              </t-form-item>
            </div>

            <t-form-item label="启用状态">
              <t-switch v-model="formData.isActive" />
            </t-form-item>
          </div>

          <!-- 首页轮播横幅配置 -->
          <div v-if="formData.bannerType === 1" class="form-section">
            <h4 class="section-title">轮播横幅配置</h4>

            <t-form-item label="横幅图片" name="imageUrl">
              <div class="image-upload-area">
                <div v-if="formData.imageUrl" class="image-preview">
                  <img :src="formData.imageUrl" alt="横幅预览" />
                  <div class="image-actions">
                    <t-button variant="outline" size="small" @click="uploadImage">重新上传</t-button>
                    <t-button variant="outline" theme="danger" size="small" @click="removeImage">删除</t-button>
                  </div>
                </div>
                <div v-else class="upload-placeholder" @click="uploadImage">
                  <t-icon name="add" />
                  <span>点击上传图片</span>
                  <small>建议尺寸：750 x 300 像素</small>
                </div>
              </div>
            </t-form-item>

            <div class="form-row">
              <t-form-item label="关联商品" class="form-item">
                <t-switch v-model="formData.linkProduct" />
              </t-form-item>
              <t-form-item v-if="formData.linkProduct" label="选择商品" name="productId" class="form-item">
                <div class="product-selector">
                  <div v-if="selectedProduct" class="selected-product">
                    <div class="product-info">
                      <img v-if="selectedProduct.image" :src="selectedProduct.image" alt="商品图片" class="product-image" />
                      <div class="product-details">
                        <div class="product-name">{{ selectedProduct.name }}</div>
                        <div class="product-price">¥{{ selectedProduct.price }}</div>
                      </div>
                    </div>
                    <t-button variant="outline" size="small" @click="clearSelectedProduct">
                      <t-icon name="close" />
                    </t-button>
                  </div>
                  <t-button v-else variant="outline" @click="showProductSelector">
                    <t-icon name="add" />
                    选择商品
                  </t-button>
                </div>
              </t-form-item>
            </div>

            <t-form-item label="跳转链接" name="linkUrl">
              <t-input
                v-model="formData.linkUrl"
                placeholder="请输入跳转链接（可选）"
              />
            </t-form-item>

            <div class="form-row">
              <t-form-item label="开始时间" name="startTime" class="form-item">
                <t-date-picker
                  v-model="formData.startTime"
                  enable-time-picker
                  placeholder="请选择开始时间"
                />
              </t-form-item>
              <t-form-item label="结束时间" name="endTime" class="form-item">
                <t-date-picker
                  v-model="formData.endTime"
                  enable-time-picker
                  placeholder="请选择结束时间"
                />
              </t-form-item>
            </div>
          </div>

          <!-- 消息通知横幅配置 -->
          <div v-if="formData.bannerType === 2" class="form-section">
            <h4 class="section-title">消息通知配置</h4>

            <t-form-item label="消息文本" name="messageText">
              <t-textarea
                v-model="formData.messageText"
                placeholder="请输入消息文本"
                :maxlength="100"
                show-word-limit
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </t-form-item>

            <div class="form-row">
              <t-form-item label="消息图标" name="messageIcon" class="form-item">
                <t-select
                  v-model="formData.messageIcon"
                  :options="iconOptions"
                  placeholder="请选择图标"
                />
              </t-form-item>
              <t-form-item label="图标颜色" name="iconColor" class="form-item">
                <t-color-picker v-model="formData.iconColor" />
              </t-form-item>
            </div>
          </div>

          <!-- 个人中心横幅配置 -->
          <div v-if="formData.bannerType === 3" class="form-section">
            <h4 class="section-title">个人中心配置</h4>

            <div class="form-row">
              <t-form-item label="主文本" name="contentText" class="form-item">
                <t-input
                  v-model="formData.contentText"
                  placeholder="请输入主文本"
                  :maxlength="20"
                  show-word-limit
                />
              </t-form-item>
              <t-form-item label="副文本" name="subText" class="form-item">
                <t-input
                  v-model="formData.subText"
                  placeholder="请输入副文本"
                  :maxlength="30"
                  show-word-limit
                />
              </t-form-item>
            </div>

            <div class="form-row">
              <t-form-item label="背景颜色" name="backgroundColor" class="form-item">
                <t-color-picker v-model="formData.backgroundColor" />
              </t-form-item>

              <t-form-item label="装饰图片" name="smallImageUrl" class="form-item">
                <div class="small-image-upload">
                  <div v-if="formData.smallImageUrl" class="small-image-preview">
                    <img :src="formData.smallImageUrl" alt="装饰图片预览" />
                    <div class="small-image-actions">
                      <t-button size="small" variant="outline" @click="uploadSmallImage">重新上传</t-button>
                      <t-button size="small" variant="outline" theme="danger" @click="removeSmallImage">删除</t-button>
                    </div>
                  </div>
                  <div v-else class="small-upload-placeholder" @click="uploadSmallImage">
                    <t-icon name="add" />
                    <span>上传图片</span>
                  </div>
                </div>
              </t-form-item>
            </div>

            <t-form-item label="跳转链接" name="linkUrl">
              <t-input
                v-model="formData.linkUrl"
                placeholder="请输入跳转链接（可选）"
              />
            </t-form-item>
          </div>

          <!-- 备注信息 -->
          <div class="form-section">
            <h4 class="section-title">备注信息</h4>
            <t-form-item label="备注" name="remark">
              <t-textarea
                v-model="formData.remark"
                placeholder="请输入备注信息（可选）"
                :maxlength="200"
                show-word-limit
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </t-form-item>
          </div>
        </t-form>

        <!-- 底部按钮 -->
        <div class="dialog-footer">
          <t-button variant="outline" @click="handleDialogCancel">
            取消
          </t-button>
          <t-button variant="outline" @click="handleFormReset">
            重置
          </t-button>
          <t-button theme="primary" @click="handleDialogConfirm" :loading="dialogLoading">
            {{ isEdit ? '更新横幅' : '创建横幅' }}
          </t-button>
        </div>
      </div>
    </t-dialog>

    <!-- 商品选择对话框 -->
    <t-dialog
      :visible="productSelectorVisible"
      header="选择商品"
      width="1000px"
      :footer="false"
      @close="productSelectorVisible = false"
      class="product-selector-dialog"
    >
      <div class="product-selector-content">
        <!-- 搜索区域 -->
        <div class="search-section">
          <t-form layout="inline" class="search-form">
            <t-form-item label="商品名称">
              <t-input
                v-model="productSearchForm.name"
                placeholder="请输入商品名称"
                clearable
                @enter="searchProducts"
              />
            </t-form-item>
            <t-form-item label="商品分类">
              <t-select
                v-model="productSearchForm.categoryId"
                placeholder="请选择分类"
                :options="categoryOptions"
                clearable
              />
            </t-form-item>
            <t-form-item label="商品状态">
              <t-select
                v-model="productSearchForm.status"
                placeholder="请选择状态"
                :options="productStatusOptions"
                clearable
              />
            </t-form-item>
            <t-form-item>
              <t-button theme="primary" @click="searchProducts">
                <t-icon name="search" />
                搜索
              </t-button>
              <t-button variant="outline" @click="resetProductSearch">
                <t-icon name="refresh" />
                重置
              </t-button>
            </t-form-item>
          </t-form>
        </div>

        <t-table
          :columns="columnsProduct"
          :data="productList"
          rowKey="id"
          :pagination="productPagination"
          :selected-row-keys="selectedProductKeys"
          :loading="productListLoading"
          @page-change="handleProductPageChange"
          @select-change="handleSelectProductChange"
          stripe
          hover
        >
          <template #image="{ row }">
            <div class="product-image">
              <img :src="row.image" :alt="row.name" />
            </div>
          </template>

          <template #price="{ row }">
            <div class="price-info">
              <div class="current-price">¥{{ row.price }}</div>
              <div class="original-price" v-if="row.originalPrice && row.originalPrice > row.price">
                ¥{{ row.originalPrice }}
              </div>
            </div>
          </template>

          <template #stock="{ row }">
            <div class="stock-info">
            <span :class="{ 'low-stock': true }">
              {{ row.stock }}
            </span>
            </div>
          </template>

          <template #isActive="{ row }">
            <t-tag v-if="row.isActive === 1" theme="success">已上架</t-tag>
            <t-tag v-else-if="row.isActive === 0" theme="danger">已下架</t-tag>
            <t-tag v-else theme="warning">草稿</t-tag>
          </template>
        </t-table>

        <!-- 底部操作 -->
        <div class="selector-footer">
          <div class="selected-info">
            <span v-if="selectedProduct">
              已选择: {{ selectedProduct.name }}
            </span>
            <span v-else class="no-selection">
              请选择一个商品
            </span>
          </div>
          <div class="footer-actions">
            <t-button variant="outline" @click="productSelectorVisible = false">
              取消
            </t-button>
            <t-button theme="primary" @click="confirmProductSelection" :disabled="!selectedProduct">
              确认选择
            </t-button>
          </div>
        </div>
      </div>
    </t-dialog>

    <!-- 图片上传对话框 -->
    <t-dialog
      :visible="uploadDialogVisible"
      header="上传图片"
      width="600px"
      @confirm="handleUploadConfirm"
      @cancel="uploadDialogVisible = false"
      @close="uploadDialogVisible = false"
    >
      <div class="upload-dialog">
        <div class="preview-container" v-if="uploadFiles.length">
          <div v-for="(file, index) in uploadFiles" :key="index" class="preview-item">
            <img :src="file" class="preview-image" />
            <div class="preview-mask">
              <t-icon name="delete" style="color: #ed0909;" @click="handleImageRemove(file, index)"/>
            </div>
          </div>
        </div>
        <t-upload
          v-if="!uploadFiles.length"
          theme="image"
          key="main-image-upload"
          accept="image/*"
          :show-image-file-name="false"
          :max="1"
          :size-limit="{ size: 5, unit: 'MB' }"
          :autoUpload="false"
          @change="onSubmitImage"
        />
        <div class="upload-tips">
          <p>建议尺寸：750 x 300 像素（轮播横幅）</p>
          <p>支持格式：JPG、PNG、GIF</p>
          <p>文件大小：不超过 5MB</p>
        </div>
      </div>
    </t-dialog>


  </div>
</template>

<script>
import bannerApi from '@/constants/api/back/miniprogram-settings.api';
import productManagementApi from '@/constants/api/back/product-management.api';
import productCategoryApi from '@/constants/api/back/product-category.api';
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import dayjs from "dayjs";

const testDomain = import.meta.env.VITE_TEST_IMAGE_DOMAIN;

export default {
  name: 'BannerManagement',
  data() {
    return {
      // 表格数据
      tableData: [],
      dataLoading: false,
      selectedRowKeys: [],

      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showJumper: true,
        showSizer: true,
        pageSizeOptions: [10, 20, 50, 100]
      },

      // 筛选表单
      filterForm: {
        title: '',
        bannerType: undefined,
        isActive: undefined,
        timeRange: []
      },

      // 横幅类型选项
      bannerTypeOptions: [
        { label: '首页轮播', value: 1 },
        { label: '消息通知', value: 2 },
        { label: '个人中心', value: 3 }
      ],

      // 状态选项
      statusOptions: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],

      // 对话框相关
      dialogVisible: false,
      dialogTitle: '',
      dialogLoading: false,
      isEdit: false,
      currentEditId: null,

      // 表单数据
      formData: {
        title: '',
        bannerType: 1,
        imageUrl: '',
        productId: null,
        linkProduct: false,
        linkUrl: '',
        startTime: '',
        endTime: '',
        sortOrder: 1,
        isActive: true,
        remark: '',
        // 消息通知相关
        messageText: '',
        messageIcon: 'notification',
        iconColor: '#ff6b6b',
        // 个人中心相关
        contentText: '',
        subText: '',
        backgroundColor: '#4ecdc4',
        smallImageUrl: ''
      },

      // 表单验证规则
      formRules: {
        title: [{ required: true, message: '请输入横幅标题', trigger: 'blur' }],
        bannerType: [{ required: true, message: '请选择横幅类型', trigger: 'change' }],
        sortOrder: [{ required: true, message: '请输入排序权重', trigger: 'blur' }]
      },

      // 商品选择相关
      productSelectorVisible: false,
      productList: [],
      productListLoading: false,
      selectedProduct: null,
      selectedProductKeys: [],
      productSearchForm: {
        name: '',
        categoryId: undefined,
        status: undefined
      },
      productPagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      categoryOptions: [
        { label: '数码产品', value: 1 },
        { label: '服装鞋帽', value: 2 },
        { label: '家居用品', value: 3 },
        { label: '食品饮料', value: 4 }
      ],
      productStatusOptions: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 }
      ],

      // 图标选项
      iconOptions: [
        { label: '通知', value: 'notification' },
        { label: '卡车', value: 'truck' },
        { label: '礼品', value: 'gift' },
        { label: '火焰', value: 'fire' },
        { label: '优惠券', value: 'discount' },
        { label: '心形', value: 'heart' }
      ],

      // 上传相关
      uploadDialogVisible: false,
      uploadFiles: [],
      uploadAction: bannerApi.uploadBannerImage.url,
      currentUploadType: 'main', // 'main' | 'small'

      columnsProduct: [
        {
          colKey: 'row-select',
          type: 'single',
          width: 50,
          fixed: 'left',
        },
        {
          title: '图片',
          colKey: 'image',
          width: 100,
          align: 'center',
        },
        {
          title: '名称',
          colKey: 'name',
          width: 200,
          align: 'center',
        },
        {
          title: '价格',
          colKey: 'price',
          width: 120,
          align: 'center',
        },
        {
          title: '库存',
          colKey:'stock',
          width: 100,
          align: 'center',
        },
        {
          title: '状态',
          colKey: 'isActive',
          width: 100,
          align: 'center',
        }
      ],

      // 表格列配置
      columns: [
        { colKey: 'row-select', type: 'multiple', width: 50 },
        {
          title: 'ID',
          colKey: 'id',
          width: 80,
          align: 'center'
        },
        {
          title: '横幅标题',
          colKey: 'title',
          width: 200,
          ellipsis: true
        },
        {
          title: '横幅类型',
          colKey: 'bannerType',
          width: 120,
          cell: 'bannerType'
        },
        {
          title: '预览',
          colKey: 'preview',
          width: 200,
          cell: 'preview'
        },
        {
          title: '关联商品',
          colKey: 'productInfo',
          width: 120,
          cell: 'productInfo'
        },
        {
          title: '点击次数',
          colKey: 'clickCount',
          width: 100,
          align: 'center'
        },
        {
          title: '排序',
          colKey: 'sortOrder',
          width: 80,
          align: 'center'
        },
        {
          title: '状态',
          colKey: 'isActive',
          width: 100,
          cell: 'isActive'
        },
        {
          title: '创建时间',
          colKey: 'createdAt',
          width: 160,
          ellipsis: true
        },
        {
          title: '操作',
          colKey: 'actions',
          width: 150,
          cell: 'actions',
          fixed: 'right'
        }
      ]
    };
  },

  computed: {
    // 动态表单验证规则
    dynamicFormRules() {
      const rules = { ...this.formRules };

      // 根据横幅类型添加动态验证
      if (this.formData.bannerType === 1) {
        // 首页轮播需要图片
        rules.imageUrl = [{ required: true, message: '请上传横幅图片', trigger: 'change' }];
      } else if (this.formData.bannerType === 2) {
        // 消息通知需要文本
        rules.messageText = [{ required: true, message: '请输入消息文本', trigger: 'blur' }];
      } else if (this.formData.bannerType === 3) {
        // 个人中心需要主文本
        rules.contentText = [{ required: true, message: '请输入主文本', trigger: 'blur' }];
      }

      return rules;
    }
  },

  mounted() {
    this.loadData();
    this.loadCategoryOptions();
  },

  methods: {
    // 加载数据
    async loadData() {
      try {
        this.dataLoading = true;
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          title: this.filterForm.title || undefined,
          bannerType: this.filterForm.bannerType || undefined,
          isActive: this.filterForm.isActive !== undefined ? this.filterForm.isActive : undefined,
          createdAtStart: this.filterForm.timeRange?.[0] || undefined,
          createdAtEnd: this.filterForm.timeRange?.[1] || undefined
        };

        const response = await this.$request.get(bannerApi.getBannerList.url, { params });
        if (response.code === 0) {
          this.tableData = (response.data && response.data.records) || [];
          this.pagination.total = +response.data.total || 0;
          this.pagination.current = +response.data.current || 1;
        } else {
          this.$message.error(response.message || '获取数据失败');
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('数据加载失败');
      } finally {
        this.dataLoading = false;
      }
    },
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
    },

    // 加载商品列表
    async loadProductList() {
      try {
        this.productListLoading = true;
        const params = {
          pageNum: this.productPagination.current,
          pageSize: this.productPagination.pageSize,
          name: this.productSearchForm.name || undefined,
          categoryId: this.productSearchForm.categoryId || undefined,
          isActive: this.productSearchForm.status !== undefined ? this.productSearchForm.status : undefined
        };

        // 使用正确的商品管理API
        const response = await this.$request.post(productManagementApi.queryProductPage.url, params);
        if (response.code === 0) {
          this.productList = response.data.records || [];
          this.productPagination.total = +response.data.total || 0;
        } else {
          this.$message.error(response.message || '获取商品列表失败');
        }
      } catch (error) {
        console.error('加载商品列表失败:', error);
        this.$message.error('获取商品列表失败');
      } finally {
        this.productListLoading = false;
      }
    },

    // 加载商品分类选项
    async loadCategoryOptions() {
      try {
        const response = await this.$request.get(productCategoryApi.getAllCategories.url);
        if (response.code === 0) {
          this.categoryOptions = (response.data || []).map(item => ({
            label: item.name,
            value: item.id
          }));
        }
      } catch (error) {
        console.error('获取分类选项失败:', error);
        // 保持默认的模拟分类选项
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1;
      this.loadData();
    },

    // 重置筛选
    handleReset() {
      this.filterForm = {
        title: '',
        bannerType: undefined,
        isActive: undefined,
        timeRange: []
      };
      this.pagination.current = 1;
      this.loadData();
    },

    // 刷新数据
    refreshData() {
      this.loadData();
    },

    // 表格变化处理
    handleTableChange(changeParams) {
      if (changeParams.pagination) {
        this.pagination.current = changeParams.pagination.current;
        this.pagination.pageSize = changeParams.pagination.pageSize;
        this.loadData();
      }
    },

    // 选择变化处理
    handleSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },

    // 显示创建对话框
    showCreateDialog() {
      this.isEdit = false;
      this.currentEditId = null;
      this.dialogTitle = '新增横幅';
      this.resetFormData();
      this.dialogVisible = true;
    },

    // 编辑横幅
    handleEdit(row) {
      this.isEdit = true;
      this.currentEditId = row.id;
      this.dialogTitle = '编辑横幅';
      this.fillFormData(row);
      this.dialogVisible = true;
    },

    // 删除横幅
    async handleDelete(row) {
      try {
        const res = this.$dialog.confirm({
          theme: 'danger',
          header: '确认删除',
          body: `确定要删除横幅${row.title}吗？`,
          onConfirm: async () => {
            const url = bannerApi.deleteBanner.url.replace('{id}', row.id);
            const response = await this.$request.delete(url);
            if (response.code === 0) {
              this.$message.success('删除成功');
              this.loadData();
            } else {
              this.$message.error(response.message || '删除失败');
            }
            res.destroy();
          }
        });
      } catch (error) {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
      }
    },
    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要删除的横幅');
        return;
      }

      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          try {
            const response = await this.$request.post(bannerApi.batchDeleteBanner.url, this.selectedRowKeys);
            if (response.code === 0) {
              this.$message.success('批量删除成功');
              this.selectedRowKeys = [];
              this.loadData();
            } else {
              this.$message.error(response.message || '批量删除失败');
            }
          } catch (error) {
            console.error('批量删除失败:', error);
            this.$message.error('批量删除失败');
          }
          res.destroy();
        }
      });
    },

    // 状态变化处理
    async handleStatusChange(row, value) {
      try {
        const url = bannerApi.updateBannerStatus.url.replace('{id}', row.id);
        const response = await this.$request.post(`${url}?isActive=${value ? 1 : 0}`);
        if (response.code === 0) {
          row.isActive = value ? 1 : 0;
          this.$message.success('状态更新成功');
        } else {
          this.$message.error(response.message || '状态更新失败');
        }
      } catch (error) {
        console.error('状态更新失败:', error);
        this.$message.error('状态更新失败');
      }
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        title: '',
        bannerType: 1,
        imageUrl: '',
        productId: null,
        linkProduct: false,
        linkUrl: '',
        startTime: '',
        endTime: '',
        sortOrder: 1,
        isActive: true,
        remark: '',
        messageText: '',
        messageIcon: 'notification',
        iconColor: '#ff6b6b',
        contentText: '',
        subText: '',
        backgroundColor: '#4ecdc4',
        smallImageUrl: ''
      };
    },

    // 填充表单数据
    fillFormData(row) {
      this.formData = {
        title: row.title || '',
        bannerType: row.bannerType || 1,
        imageUrl: row.imageUrl || '',
        productId: row.productId || null,
        linkProduct: !!row.productId,
        linkUrl: row.linkUrl || '',
        startTime: row.startTime || '',
        endTime: row.endTime || '',
        sortOrder: row.sortOrder || 1,
        isActive: row.isActive === 1,
        remark: row.remark || '',
        messageText: row.messageText || '',
        messageIcon: row.messageIcon || 'notification',
        iconColor: row.iconColor || '#ff6b6b',
        contentText: row.contentText || '',
        subText: row.subText || '',
        backgroundColor: row.backgroundColor || '#4ecdc4',
        smallImageUrl: row.smallImageUrl || ''
      };

      // 如果有关联商品，设置选中的商品信息
      if (row.productId && row.product) {
        const { id, name, price, image } = row.product;
        this.selectedProduct = {
          id,
          name,
          price: price || 0,
          image: image || ''
        };
      } else {
        this.selectedProduct = null;
      }
    },

    // 横幅类型变化处理
    handleBannerTypeChange(value) {
      // 清空相关字段
      if (value === 1) {
        // 首页轮播
        this.formData.messageText = '';
        this.formData.contentText = '';
        this.formData.subText = '';
      } else if (value === 2) {
        // 消息通知
        this.formData.imageUrl = '';
        this.formData.productId = null;
        this.formData.linkProduct = false;
        this.formData.contentText = '';
        this.formData.subText = '';
      } else if (value === 3) {
        // 个人中心
        this.formData.imageUrl = '';
        this.formData.productId = null;
        this.formData.linkProduct = false;
        this.formData.messageText = '';
      }
    },

    // 对话框确认
    async handleDialogConfirm() {
      try {
        // 表单验证
        const valid = await this.$refs.bannerForm.validate();
        if (!valid) return;

        // 自定义验证
        if (!this.validateFormData()) return;

        this.dialogLoading = true;

        // 构建请求数据
        const requestData = {
          title: this.formData.title,
          bannerType: this.formData.bannerType,
          sortOrder: this.formData.sortOrder,
          isActive: this.formData.isActive ? 1 : 0,
          remark: this.formData.remark
        };

        // 根据横幅类型添加特定字段
        if (this.formData.bannerType === 1) {
          // 首页轮播
          requestData.imageUrl = this.formData.imageUrl;
          requestData.linkUrl = this.formData.linkUrl;
          requestData.startTime = this.formatDateTime(this.formData.startTime);
          requestData.endTime = this.formatDateTime(this.formData.endTime);
          if (this.formData.linkProduct && this.formData.productId) {
            requestData.productId = this.formData.productId;
          }
        } else if (this.formData.bannerType === 2) {
          // 消息通知
          requestData.messageText = this.formData.messageText;
          requestData.messageIcon = this.formData.messageIcon;
          requestData.iconColor = this.formData.iconColor;
        } else if (this.formData.bannerType === 3) {
          // 个人中心
          requestData.contentText = this.formData.contentText;
          requestData.subText = this.formData.subText;
          requestData.backgroundColor = this.formData.backgroundColor;
          requestData.smallImageUrl = this.formData.smallImageUrl;
          requestData.linkUrl = this.formData.linkUrl;
        }

        let response;
        if (this.isEdit) {
          // 更新
          const url = bannerApi.updateBanner.url.replace('{id}', this.currentEditId);
          response = await this.$request.put(url, requestData);
        } else {
          // 新增
          response = await this.$request.post(bannerApi.createBanner.url, requestData);
        }

        if (response.code === 0) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功');
          this.dialogVisible = false;
          this.loadData();
        } else {
          this.$message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('操作失败:', error);
        this.$message.error('操作失败');
      } finally {
        this.dialogLoading = false;
      }
    },

    // 对话框取消
    handleDialogCancel() {
      this.dialogVisible = false;
    },

    // 上传图片
    uploadImage() {
      this.currentUploadType = 'main';
      this.uploadDialogVisible = true;
    },

    // 上传小图片
    uploadSmallImage() {
      this.currentUploadType = 'small';
      this.uploadDialogVisible = true;
    },

    // 删除图片
    removeImage() {
      this.formData.imageUrl = '';
    },

    // 删除小图片
    removeSmallImage() {
      this.formData.smallImageUrl = '';
    },

    // 确认上传
    handleUploadConfirm() {
      this.uploadDialogVisible = false;
      this.uploadFiles = [];
    },
    handleImageRemove(data, index) {
      this.uploadFiles.splice(index, 1);
      if (this.currentUploadType === 'main') {
        this.formData.imageUrl = '';
      } else if (this.currentUploadType === 'small') {
        this.formData.smallImageUrl = '';
      }
    },
    async onSubmitImage(data) {
      const imagePath = import.meta.env.VITE_SHOP_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      Promise.all(promises).then((res) => {
        if (res.length) {
          this.uploadFiles = [res[0].data.startsWith('https://huaxiacomp.cn')
            ? res[0].data.replace(
              'https://huaxiacomp.cn',
              testDomain
            )
            : res[0].data];
        }
        if (this.currentUploadType === 'main') {
          this.formData.imageUrl = this.uploadFiles[0] || '';
        } else if (this.currentUploadType === 'small') {
          this.formData.smallImageUrl = this.uploadFiles[0] || '';
        }
        this.$message.success({ content: '上传成功' });
      }).catch(() => { // 上传失败 关闭弹窗
        this.$message.error({ content: '操作失败' });
      });
    },

    // 验证表单数据
    validateFormData() {
      // 根据横幅类型进行特定验证
      if (this.formData.bannerType === 1) {
        // 首页轮播验证
        if (!this.formData.imageUrl) {
          this.$message.warning('请上传横幅图片');
          return false;
        }
        if (this.formData.linkProduct && !this.formData.productId) {
          this.$message.warning('已开启商品关联，请选择关联商品');
          return false;
        }
      } else if (this.formData.bannerType === 2) {
        // 消息通知验证
        if (!this.formData.messageText || !this.formData.messageText.trim()) {
          this.$message.warning('请输入消息文本');
          return false;
        }
      } else if (this.formData.bannerType === 3) {
        // 个人中心验证
        if (!this.formData.contentText || !this.formData.contentText.trim()) {
          this.$message.warning('请输入主文本');
          return false;
        }
      }

      return true;
    },

    // 获取横幅类型名称
    getBannerTypeName(type) {
      const typeMap = {
        1: '首页轮播',
        2: '消息通知',
        3: '个人中心'
      };
      return typeMap[type] || '未知';
    },

    // 获取横幅类型主题
    getBannerTypeTheme(type) {
      const themeMap = {
        1: 'primary',
        2: 'warning',
        3: 'success'
      };
      return themeMap[type] || 'default';
    },

    // 获取类型描述
    getTypeDescription(type) {
      const descMap = {
        1: '图片轮播，支持商品关联',
        2: '文字消息，带图标展示',
        3: '文字内容，带背景色和小图片'
      };
      return descMap[type] || '';
    },

    // 表单重置
    handleFormReset() {
      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '确认重置',
        body: '确定要重置表单吗？所有已填写的内容将被清空。',
        onConfirm: async () => {
          this.resetFormData();
          this.$message.success('表单已重置');
          res.destroy();
        },
      });
    },

    // ==================== 商品选择相关方法 ====================
    // 显示商品选择器
    showProductSelector() {
      this.productSelectorVisible = true;
      this.productPagination.current = 1;
      this.loadProductList();
    },

    // 搜索商品
    searchProducts() {
      this.productPagination.current = 1;
      this.loadProductList();
    },

    // 重置商品搜索
    resetProductSearch() {
      this.productSearchForm = {
        name: '',
        categoryId: undefined,
        status: undefined
      };
      this.productPagination.current = 1;
      this.loadProductList();
    },

    // 商品分页变化
    handleProductPageChange(pageInfo) {
      this.productPagination.current = pageInfo.current;
      this.productPagination.pageSize = pageInfo.pageSize;
      this.loadProductList();
    },

    // 选择商品
    handleSelectProductChange(selectedRowKeys, data) {
      this.selectedProductKeys = selectedRowKeys;
      this.selectedProduct = { ...data.currentRowData };
    },

    // 确认选择商品
    confirmProductSelection() {
      if (this.selectedProduct) {
        this.formData.productId = this.selectedProduct.id;
        this.productSelectorVisible = false;
        this.$message.success(`已选择商品: ${this.selectedProduct.name}`);
      }
    },

    // 清除选择的商品
    clearSelectedProduct() {
      this.selectedProduct = null;
      this.formData.productId = null;
    }
  }
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面主体样式 ====================
.banner-management-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      .t-button {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 筛选区域
  .filter-section {
    margin-bottom: 24px;

    .filter-content {
      background: #fff;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .filter-form {
        .filter-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          align-items: flex-end;

          .filter-item {
            min-width: 200px;
            margin-bottom: 16px;

            .filter-input,
            .filter-select {
              width: 200px;
            }

            .filter-date {
              width: 280px;
            }

            &.filter-item-wide {
              min-width: 280px;
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;

            .t-button {
              height: 32px;
              padding: 0 16px;
              border-radius: 6px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  // 工具栏
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 24px;

      .table-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;

        .t-icon {
          font-size: 18px;
          color: #3b82f6;
        }

        .count-info {
          font-size: 14px;
          font-weight: 400;
          color: #6b7280;
        }
      }

      .batch-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .selected-count {
          font-size: 14px;
          color: #6b7280;
        }

        .t-button {
          height: 32px;
          padding: 0 16px;
          border-radius: 6px;
          font-size: 12px;
        }
      }
    }

    .toolbar-right {
      .refresh-btn {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-size: 12px;
      }
    }
  }

  // 表格区域
  .table-section {
    .table-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      border: none;
    }
  }

  // ==================== 表格样式 ====================
  .banner-preview-cell {
    .preview-image {
      width: 80px;
      height: 40px;
      object-fit: cover;
      border-radius: 4px;
      border: 1px solid #e2e8f0;
    }

    .message-preview {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #6b7280;

      .t-icon {
        font-size: 14px;
      }
    }

    .personal-preview {
      display: flex;
      flex-direction: column;
      gap: 2px;
      font-size: 12px;

      span {
        color: #1f2937;
        font-weight: 500;
      }

      small {
        color: #6b7280;
      }
    }

    .no-preview {
      font-size: 12px;
      color: #9ca3af;
    }
  }

  .product-link {
    font-size: 12px;
    color: #3b82f6;
  }

  .no-product {
    font-size: 12px;
    color: #9ca3af;
  }

  .action-buttons {
    display: flex;
    gap: 8px;

    .t-button {
      padding: 4px 8px;
      font-size: 12px;
    }
  }

  // ==================== 对话框表单样式 ====================
  .banner-form {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;

    .form-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
      }

      .form-row {
        display: flex;
        gap: 16px;

        .form-item {
          flex: 1;
        }
      }

      .switch-text {
        margin-left: 8px;
        font-size: 14px;
        color: #6b7280;
      }

      // 商品选择器样式
      .product-selector {
        .selected-product {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          background: #f8fafc;

          .product-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .product-image {
              width: 40px;
              height: 40px;
              border-radius: 4px;
              object-fit: cover;
              border: 1px solid #e2e8f0;
            }

            .product-details {
              .product-name {
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                margin-bottom: 2px;
              }

              .product-price {
                font-size: 12px;
                color: #ef4444;
                font-weight: 600;
              }
            }
          }
        }
      }
    }

    // 对话框底部
    .dialog-footer {
      padding: 16px 24px;
      border-top: 1px solid #e5e7eb;
      display: flex;
      justify-content: flex-end;
      gap: 12px;

      .t-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
      }
    }
  }

  // ==================== 商品选择对话框样式 ====================
  .product-selector-dialog {
    .product-selector-content {
      .search-section {
        padding: 20px;
        border-bottom: 1px solid #e5e7eb;

        .search-form {
          .t-form-item {
            margin-bottom: 0;
          }
        }
      }

      .product-list-section {
        padding: 20px;
        min-height: 400px;

        .list-header {
          margin-bottom: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .total-count {
            font-size: 14px;
            color: #6b7280;
          }
        }

        .product-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 16px;
          margin-bottom: 24px;

          .product-card {
            position: relative;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fff;

            &:hover {
              border-color: #3b82f6;
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            }

            &.selected {
              border-color: #3b82f6;
              background: #eff6ff;
            }

            .product-image-container {
              width: 100%;
              height: 60px;
              margin-bottom: 8px;
              border-radius: 6px;
              overflow: hidden;
              background: #f8fafc;
              display: flex;
              align-items: center;
              justify-content: center;

              .product-image {
                max-width: 100%;
                max-height: 100%;
                width: auto;
                height: auto;
                object-fit: contain;
                border-radius: 4px;
              }

              .no-image {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                color: #9ca3af;

                .t-icon {
                  font-size: 24px;
                  margin-bottom: 4px;
                }

                span {
                  font-size: 12px;
                }
              }
            }

            .product-info {
              .product-name {
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                margin-bottom: 4px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .product-price {
                font-size: 16px;
                font-weight: 600;
                color: #ef4444;
                margin-bottom: 4px;
              }

              .product-stock {
                font-size: 12px;
                color: #6b7280;
                margin-bottom: 4px;
              }

              .product-status {
                display: flex;
                justify-content: flex-start;
              }
            }

            .selected-indicator {
              position: absolute;
              top: 8px;
              right: 8px;
              color: #3b82f6;
              font-size: 20px;
            }
          }
        }

        .pagination-section {
          display: flex;
          justify-content: center;
        }
      }

      .selector-footer {
        padding: 16px 20px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8fafc;

        .selected-info {
          font-size: 14px;
          color: #374151;

          .no-selection {
            color: #9ca3af;
          }
        }

        .footer-actions {
          display: flex;
          gap: 12px;
          text-align: right;

          .t-button {
            height: 32px;
            padding: 0 16px;
            border-radius: 6px;
          }
        }
      }
    }
  }

  // ==================== 上传对话框样式 ====================
  .upload-dialog {
    .upload-tips {
      margin-top: 16px;
      padding: 16px;
      background: #f0f9ff;
      border-radius: 8px;
      border-left: 4px solid #0ea5e9;

      p {
        margin: 0 0 4px 0;
        font-size: 12px;
        color: #0369a1;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .banner-management-page {
    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            .filter-item {
              min-width: 180px;

              .filter-input,
              .filter-select {
                width: 180px;
              }

              .filter-date {
                width: 240px;
              }

              &.filter-item-wide {
                min-width: 240px;
              }
            }
          }
        }
      }
    }

    // 商品选择对话框响应式
    .product-selector-dialog {
      .product-selector-content {
        .product-list-section {
          .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));

            .product-card {
              .product-image-container {
                height: 70px;

                .product-image {
                  max-width: 100%;
                  max-height: 100%;
                  width: auto;
                  height: auto;
                  object-fit: contain;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .banner-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }

    .filter-section {
      .filter-content {
        padding: 16px;

        .filter-form {
          .filter-row {
            flex-direction: column;
            gap: 12px;

            .filter-item {
              min-width: auto;

              .filter-input,
              .filter-select,
              .filter-date {
                width: 100%;
              }

              &.filter-item-wide {
                min-width: auto;
              }
            }

            .filter-buttons {
              width: 100%;
              justify-content: center;
            }
          }
        }
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .batch-actions {
          justify-content: center;
        }
      }

      .toolbar-right {
        display: flex;
        justify-content: center;
      }
    }

    .banner-dialog {
      .t-dialog {
        width: 95vw !important;
        max-width: 500px;
      }
    }

    // 商品选择对话框移动端样式
    .product-selector-dialog {
      .t-dialog {
        width: 95vw !important;
        max-width: none;
      }

      .product-selector-content {
        .search-section {
          padding: 16px;

          .search-form {
            .t-form-item {
              margin-bottom: 12px;
            }
          }
        }

        .product-list-section {
          padding: 16px;

          .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;

            .product-card {
              padding: 8px;

              .product-image-container {
                height: 60px;

                .product-image {
                  width: 60px;
                  height: 60px;
                  border-radius: 8px;
                  overflow: hidden;

                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }
                }
              }

              .product-info {
                .product-name {
                  font-size: 12px;
                }

                .product-price {
                  font-size: 14px;
                }

                .product-stock {
                  font-size: 11px;
                }
              }
            }
          }
        }

        .selector-footer {
          padding: 12px 16px;
          flex-direction: column;
          gap: 12px;

          .footer-actions {
            text-align: right;
            width: 100%;
            justify-content: space-between;
          }
        }
      }
    }

    .banner-form {
      padding: 16px;

      .form-section {
        .form-row {
          flex-direction: column;
          gap: 12px;
        }

        .section-title {
          font-size: 14px;
        }
      }

      .image-upload-area {
        .image-preview img,
        .upload-placeholder {
          width: 100%;
          max-width: 300px;
          height: 120px;
        }
      }

      .dialog-footer {
        padding: 12px 16px;
        justify-content: center;
      }
    }

    .product-selector-dialog {
      .t-dialog {
        width: 95vw !important;
        max-width: 600px;
      }

      .product-selector-content {
        .search-section {
          padding: 16px;

          .search-form {
            .t-form-item {
              margin-bottom: 12px;
            }
          }
        }

        .product-list-section {
          padding: 16px;

          .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 12px;

            .product-card {
              .product-image-container {
                height: 100px;
              }

              .product-info {
                .product-name {
                  font-size: 12px;
                }

                .product-price {
                  font-size: 14px;
                }
              }
            }
          }
        }

        .selector-footer {
          flex-direction: column;
          gap: 12px;
          align-items: stretch;

          .footer-actions {
            text-align: right;
            justify-content: center;
          }
        }
      }
    }
  }
}
.image-upload-area {
  .image-preview {
    position: relative;
    display: inline-block;

    img {
      width: 250px;
      height: 125px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
    }

    .image-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      gap: 8px;

      .t-button {
        height: 28px;
        padding: 0 12px;
        font-size: 12px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(4px);
      }
    }
  }

  .upload-placeholder {
    width: 250px;
    height: 125px;
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8fafc;

    &:hover {
      border-color: #3b82f6;
      background: #eff6ff;
    }

    .t-icon {
      font-size: 24px;
      color: #6b7280;
    }

    span {
      font-size: 14px;
      color: #6b7280;
    }

    small {
      font-size: 12px;
      color: #9ca3af;
    }
  }
}
.small-image-upload {
  .small-image-preview {
    position: relative;
    display: inline-block;

    img {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
    }

    .small-image-actions {
      position: absolute;
      top: 4px;
      right: 4px;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .t-button {
        height: 24px;
        padding: 0 8px;
        font-size: 10px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(4px);
      }
    }
  }

  .small-upload-placeholder {
    width: 100px;
    height: 100px;
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8fafc;

    &:hover {
      border-color: #3b82f6;
      background: #eff6ff;
    }

    .t-icon {
      font-size: 16px;
      color: #6b7280;
    }

    span {
      font-size: 10px;
      color: #6b7280;
    }
  }
}
// 商品图片样式
.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.product-name {
  .name-text {
    font-size: 14px;
    color: #374151;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .product-code {
    font-size: 12px;
    color: #9ca3af;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
}

// 价格信息样式
.price-info {
  text-align: right;

  .current-price {
    font-size: 16px;
    font-weight: 600;
    color: #dc2626;
    margin-bottom: 2px;
  }

  .original-price {
    font-size: 12px;
    color: #9ca3af;
    text-decoration: line-through;
  }
}

// 库存信息样式
.stock-info {
  .low-stock {
    color: #dc2626;
    font-weight: 600;
  }

  .stock-status {
    font-size: 11px;
    color: #dc2626;
    margin-top: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;

    .t-icon {
      font-size: 10px;
    }
  }
}
.dialog-footer {
  text-align: right;
}
.footer-actions {
  text-align: right;
}
.search-section {
  /deep/ .t-form-inline {
    flex-wrap: nowrap !important;
  }
}
.preview-container {
  display: flex;
  gap: 10px;
}
.preview-item {
  position: relative;
  width: 110px;
  height: 110px;
}
.preview-mask {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.preview-item:hover .preview-mask {
  display: flex;
}
.preview-image {
  width: 110px;
  height: 110px;
  object-fit: contain;
}
.preview-detail {
  width: 160px;
  height: 160px;
  object-fit: contain;
}
</style>
