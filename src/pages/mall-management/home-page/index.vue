<template>
  <div class="dashboard-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="chart-line" />
          订单数据概览
        </div>
        <div class="page-desc">实时监控订单数据和业务指标</div>
      </div>
      <div class="header-actions">
        <t-button theme="default" variant="outline" @click="refreshData">
          <t-icon name="refresh" />
          刷新数据
        </t-button>
<!--        <t-button theme="primary" @click="exportData">-->
<!--          <t-icon name="download" />-->
<!--          导出报表-->
<!--        </t-button>-->
      </div>
    </div>

    <!-- 统计指标卡片区域 -->
    <div class="metrics-container">
      <div class="metric-card order-count">
        <div class="metric-icon">
          <t-icon name="shop" />
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatNumber(dashboardData.todayOrders) }}</div>
          <div class="metric-title">今日订单数</div>
          <div class="metric-trend">
            <t-icon :name="getTrendIcon(dashboardData.todayOrdersGrowth)" :class="getTrendClass(dashboardData.todayOrdersGrowth)" />
            <span class="trend-text">{{ dashboardData.todayOrdersGrowth }}</span>
          </div>
        </div>
      </div>

      <div class="metric-card order-amount">
        <div class="metric-icon">
          <t-icon name="money-filled" />
        </div>
        <div class="metric-content">
          <div class="metric-value">¥{{ formatCurrency(dashboardData.todayRevenue) }}</div>
          <div class="metric-title">今日营收</div>
          <div class="metric-trend">
            <t-icon :name="getTrendIcon(dashboardData.todayRevenueGrowth)" :class="getTrendClass(dashboardData.todayRevenueGrowth)" />
            <span class="trend-text">{{ dashboardData.todayRevenueGrowth }}</span>
          </div>
        </div>
      </div>

      <div class="metric-card user-count">
        <div class="metric-icon">
          <t-icon name="user" />
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatNumber(dashboardData.totalUsers) }}</div>
          <div class="metric-title">用户总数</div>
          <div class="metric-trend">
            <t-icon :name="getTrendIcon(dashboardData.totalUsersGrowth)" :class="getTrendClass(dashboardData.totalUsersGrowth)" />
            <span class="trend-text">{{ dashboardData.totalUsersGrowth }}</span>
          </div>
        </div>
      </div>

      <div class="metric-card avg-order">
        <div class="metric-icon">
          <t-icon name="chart-bubble" />
        </div>
        <div class="metric-content">
          <div class="metric-value">¥{{ formatCurrency(dashboardData.avgOrderAmount) }}</div>
          <div class="metric-title">平均订单金额</div>
          <div class="metric-trend">
            <t-icon :name="getTrendIcon(dashboardData.avgOrderAmountGrowth)" :class="getTrendClass(dashboardData.avgOrderAmountGrowth)" />
            <span class="trend-text">{{ dashboardData.avgOrderAmountGrowth }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <div class="chart-card main-chart">
        <div class="chart-header">
          <h3 class="chart-title">
            <t-icon name="chart-line" />
            订单趋势分析
          </h3>
          <div class="chart-controls">
            <t-radio-group v-model="chartPeriod" variant="default-filled" @change="onPeriodChange">
              <t-radio-button value="7d">近7天</t-radio-button>
              <t-radio-button value="30d">近30天</t-radio-button>
              <t-radio-button value="90d">近3个月</t-radio-button>
            </t-radio-group>
          </div>
        </div>
        <div ref="orderChart" class="chart-container"></div>
      </div>

      <div class="chart-card side-chart">
        <div class="chart-header">
          <h3 class="chart-title">
            <t-icon name="chart-pie" />
            订单状态分布
          </h3>
        </div>
        <div ref="statusChart" class="chart-container small"></div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <div class="action-card" @click="goToMiniprogramSettings">
        <div class="action-icon miniprogram-icon">
          <t-icon name="mobile" />
        </div>
        <div class="action-content">
          <div class="action-title">横幅管理</div>
          <div class="action-desc">管理首页轮播、消息通知和个人中心横幅</div>
        </div>
        <t-button theme="primary" variant="text">
          <t-icon name="chevron-right" />
        </t-button>
      </div>

      <div class="action-card" @click="goToOrderManagement">
        <div class="action-icon order-icon">
          <t-icon name="order-ascending" />
        </div>
        <div class="action-content">
          <div class="action-title">订单管理</div>
          <div class="action-desc">查看和管理所有订单</div>
        </div>
        <t-button theme="primary" variant="text">
          <t-icon name="chevron-right" />
        </t-button>
      </div>

      <div class="action-card" @click="goToStoreSettings">
        <div class="action-icon store-icon">
          <t-icon name="shop" />
        </div>
        <div class="action-content">
          <div class="action-title">店铺设置</div>
          <div class="action-desc">管理店铺信息和配置</div>
        </div>
        <t-button theme="primary" variant="text">
          <t-icon name="chevron-right" />
        </t-button>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import mallDashboardService from '@/services/mall-dashboard.service';

export default {
  name: 'HomePage',
  data() {
    return {
      loading: false,
      dashboardData: {
        todayOrders: 0,
        todayOrdersGrowth: '+0%',
        todayRevenue: 0,
        todayRevenueGrowth: '+0%',
        totalUsers: 0,
        totalUsersGrowth: '+0%',
        avgOrderAmount: 0,
        avgOrderAmountGrowth: '+0%',
        totalProducts: 0,
        totalProductsGrowth: '+0%',
        totalStores: 0,
        totalStoresGrowth: '+0%',
        pendingOrders: 0,
        lowStockProducts: 0,
        monthlyOrders: 0,
        monthlyRevenue: 0,
        yesterdayOrders: 0,
        yesterdayRevenue: 0,
        lastMonthOrders: 0,
        lastMonthRevenue: 0
      },
      chartPeriod: '30d',
      orderChart: null,
      statusChart: null,
      orderTrendData: [],
      statusDistributionData: []
    };
  },
  created() {
    this.fetchDashboardData();
  },
  mounted() {
    this.initCharts();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.orderChart) {
      this.orderChart.dispose();
      this.orderChart = null;
    }
    if (this.statusChart) {
      this.statusChart.dispose();
      this.statusChart = null;
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    async fetchDashboardData() {
      try {
        this.loading = true;

        // 获取所有驾驶舱数据
        const result = await mallDashboardService.getAllDashboardData();

        // 更新概览数据
        this.dashboardData = result.overview;

        // 更新图表数据
        this.orderTrendData = result.orderTrend;
        this.statusDistributionData = result.statusDistribution;

        // 更新图表
        this.updateChart();
        this.updateStatusChart();

      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('数据加载失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    async fetchOrderTrend(period = 30) {
      try {
        const periodMap = {
          '7d': 7,
          '30d': 30,
          '90d': 90
        };

        const response = await mallDashboardService.getOrderTrend({
          period: periodMap[this.chartPeriod] || period
        });

        this.orderTrendData = response.data;
        this.updateChart();

      } catch (error) {
        console.error('获取订单趋势数据失败:', error);
        this.$message.error('订单趋势数据加载失败');
      }
    },

    initCharts() {
      this.initOrderChart();
      this.initStatusChart();
    },

    initOrderChart() {
      if (this.orderChart) {
        this.orderChart.dispose();
      }

      const chartDom = this.$refs.orderChart;
      this.orderChart = echarts.init(chartDom);
      this.updateChart();
    },

    initStatusChart() {
      if (this.statusChart) {
        this.statusChart.dispose();
      }

      const chartDom = this.$refs.statusChart;
      this.statusChart = echarts.init(chartDom);
      this.updateStatusChart();
    },

    updateStatusChart() {
      if (!this.statusChart || !this.statusDistributionData.length) return;

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '订单状态',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.statusDistributionData.map(item => ({
              value: item.count,
              name: item.statusName,
              itemStyle: {
                color: item.color
              }
            }))
          }
        ]
      };

      this.statusChart.setOption(option);
    },

    updateChart() {
      if (!this.orderChart || !this.orderTrendData.length) return;

      const dates = this.orderTrendData.map(item => item.dateLabel);
      const orders = this.orderTrendData.map(item => item.orderCount);
      const amounts = this.orderTrendData.map(item => item.orderAmount);

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['订单数', '订单金额'],
          top: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: dates,
            axisLabel: {
              color: '#666'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '订单数',
            position: 'left',
            axisLabel: {
              color: '#666'
            }
          },
          {
            type: 'value',
            name: '金额(元)',
            position: 'right',
            axisLabel: {
              formatter: '¥{value}',
              color: '#666'
            }
          }
        ],
        series: [
          {
            name: '订单数',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#1890ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                  { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
                ]
              }
            },
            data: orders
          },
          {
            name: '订单金额',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#52c41a'
            },
            data: amounts
          }
        ]
      };

      this.orderChart.setOption(option);
    },

    handleResize() {
      if (this.orderChart) {
        this.orderChart.resize();
      }
      if (this.statusChart) {
        this.statusChart.resize();
      }
    },

    async onPeriodChange() {
      await this.fetchOrderTrend();
    },

    async refreshData() {
      await this.fetchDashboardData();
      this.$message.success('数据刷新成功');
    },

    exportData() {
      this.$message.success('报表导出成功');
    },

    getTrendIcon(growth) {
      if (growth.startsWith('+')) {
        return 'chevron-up';
      } if (growth.startsWith('-')) {
        return 'chevron-down';
      }
      return 'minus';
    },

    getTrendClass(growth) {
      if (growth.startsWith('+')) {
        return 'trend-up';
      } if (growth.startsWith('-')) {
        return 'trend-down';
      }
      return 'trend-neutral';
    },

    formatNumber(num) {
      if (!num) return '0';
      if (num >= 10000) {
        return `${(num / 10000).toFixed(1)}w`;
      } if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}k`;
      }
      return num.toString();
    },

    formatCurrency(amount) {
      if (!amount) return '0.00';
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    },

    // 跳转到小程序首页设置
    goToMiniprogramSettings() {
      this.$router.push({
        name: 'miniprogram-settings'
      });
    },

    // 跳转到订单管理
    goToOrderManagement() {
      this.$router.push({
        name: 'mall-management-order-list'
      });
    },

    // 跳转到店铺设置
    goToStoreSettings() {
      this.$router.push({
        name: 'mall-management-store-setting'
      });
    }
  }
};
</script>
<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面主体样式 ====================
.dashboard-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .t-button {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 指标卡片容器
  .metrics-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;

    .metric-card {
      background: #fff;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        transition: all 0.3s ease;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);

        &::before {
          height: 6px;
        }
      }

      &.order-count::before {
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
      }

      &.order-amount::before {
        background: linear-gradient(90deg, #10b981, #059669);
      }

      &.user-count::before {
        background: linear-gradient(90deg, #f59e0b, #d97706);
      }

      &.avg-order::before {
        background: linear-gradient(90deg, #8b5cf6, #7c3aed);
      }

      display: flex;
      align-items: center;
      gap: 16px;

      .metric-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
      }

      &.order-count .metric-icon {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      }

      &.order-amount .metric-icon {
        background: linear-gradient(135deg, #10b981, #059669);
      }

      &.user-count .metric-icon {
        background: linear-gradient(135deg, #f59e0b, #d97706);
      }

      &.avg-order .metric-icon {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
      }

      .metric-content {
        flex: 1;

        .metric-value {
          font-size: 28px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .metric-title {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        .metric-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;

          .trend-up {
            color: #10b981;
          }

          .trend-down {
            color: #ef4444;
          }

          .trend-neutral {
            color: #6b7280;
          }

          .trend-text {
            font-weight: 600;
          }
        }
      }
    }
  }

  // 图表容器
  .charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 24px;

    .chart-card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      overflow: hidden;

      .chart-header {
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chart-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 8px;

          .t-icon {
            font-size: 18px;
            color: #3b82f6;
          }
        }

        .chart-controls {
          .t-radio-group {
            .t-radio-button {
              border-radius: 6px;
              font-size: 12px;
              padding: 4px 12px;
            }
          }
        }
      }

      .chart-container {
        padding: 20px;
        min-height: 400px;

        &.small {
          min-height: 300px;
        }
      }
    }
  }

  // 快速操作区域
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    .action-card {
      background: #fff;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 16px;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }

      .action-icon {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #3b82f6;

        &.miniprogram-icon {
          background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
          color: #7c3aed;
        }

        &.order-icon {
          background: linear-gradient(135deg, #fef3c7, #fde68a);
          color: #d97706;
        }

        &.store-icon {
          background: linear-gradient(135deg, #ecfdf5, #d1fae5);
          color: #059669;
        }
      }

      .action-content {
        flex: 1;

        .action-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .action-desc {
          font-size: 14px;
          color: #6b7280;
        }
      }

      .t-button {
        color: #3b82f6;

        &:hover {
          background: rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .dashboard-page {
    .charts-container {
      grid-template-columns: 1fr;
    }

    .metrics-container {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .dashboard-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }

    .metrics-container {
      grid-template-columns: 1fr;
    }

    .quick-actions {
      grid-template-columns: 1fr;
    }

    .metric-card {
      .metric-content {
        .metric-value {
          font-size: 24px;
        }
      }
    }
  }
}
</style>
