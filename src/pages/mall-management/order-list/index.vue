<template>
  <div class="order-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <t-icon name="order" />
          订单管理
        </h1>
        <p class="page-desc">管理商城订单，处理订单状态和物流信息</p>
      </div>
      <div class="header-actions"></div>
    </div>

    <!-- 订单统计卡片 -->
    <div class="stats-cards">
      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon pending">
            <t-icon name="time" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ orderStats.pendingCount || 0 }}</div>
            <div class="stat-label">待付款</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon processing">
            <t-icon name="loading" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ orderStats.processingCount || 0 }}</div>
            <div class="stat-label">待发货</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon shipping">
            <t-icon name="cart" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ orderStats.shippingCount || 0 }}</div>
            <div class="stat-label">待收货</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon completed">
            <t-icon name="check-circle" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ orderStats.completedCount || 0 }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </t-card>
    </div>

    <!-- 搜索筛选 -->
    <t-card class="search-card">
      <t-form :data="searchForm" layout="inline" @submit="handleSearch" @reset="handleReset">
        <t-form-item label="订单号" name="orderNo">
          <t-input
            v-model="searchForm.orderNumber"
            placeholder="请输入订单号"
            clearable
            style="width: 200px;"
          />
        </t-form-item>

        <t-form-item label="订单状态" name="status">
          <t-select
            v-model="searchForm.orderStatus"
            :options="statusOptions"
            placeholder="请选择订单状态"
            clearable
            style="width: 150px;"
          />
        </t-form-item>

        <div class="filter-buttons">
          <t-button theme="primary" @click="handleSearch" class="search-btn">
            <t-icon name="search" />
            查询
          </t-button>
          <t-button variant="outline" @click="handleReset" class="reset-btn">
            <t-icon name="refresh" />
            重置
          </t-button>
        </div>
      </t-form>
    </t-card>

    <!-- 订单列表 -->
    <t-card class="table-card">
      <t-table
        :columns="columns"
        :data="orderList"
        :rowKey="rowKey"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        :loading="dataLoading"
        @page-change="handlePageChange"
        @select-change="handleSelectChange"
        stripe
        hover
      >
        <template #orderNumber="{ row }">
          <div class="order-no">
            <span class="order-text">{{ row.orderNumber }}</span>
            <t-button
              variant="text"
              theme="primary"
              size="small"
              @click="handleCopyOrderNo(row.orderNumber)"
            >
              <t-icon name="copy" />
            </t-button>
          </div>
        </template>

        <template #items="{ row }">
          <div class="product-list">
            <div
              v-for="(product, index) in row.items"
              :key="product.id + index"
              class="product-item"
            >
              <img :src="product.image" :alt="product.name" class="product-image" />
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-spec">{{ product.spec }}</div>
                <div class="product-quantity">x{{ product.quantity }}</div>
              </div>
            </div>
          </div>
        </template>

        <template #totalAmount="{ row }">
          <div class="amount-info">
            <div class="total-amount">¥{{ row.totalAmount }}</div>
            <div class="amount-detail">
              <span>商品：¥{{ calculateProductTotal(row.items) }}</span>
              <span v-if="row.deliveryFee > 0">运费：¥{{ row.deliveryFee }}</span>
            </div>
          </div>
        </template>

        <template #createTime="{ row }">
          {{ formatDate(row.createTime) }}
        </template>

        <template #op="{ row }">
          <t-space>
            <t-button variant="text" theme="primary" @click="handleDetail(row)">
              <t-icon name="view" />
              详情
            </t-button>

            <t-button
              v-if="row.statusText === '已付款'"
              variant="text"
              theme="success"
              @click="handleShip(row)"
            >
              <t-icon name="delivery" />
              发货
            </t-button>

            <t-button
              v-if="row.orderStatus === 'pending'"
              variant="text"
              theme="warning"
              @click="handleCancel(row)"
            >
              <t-icon name="close" />
              取消
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 订单详情对话框 -->
    <t-dialog
      :visible="detailDialog.visible"
      header="订单详情"
      width="800px"
      @close="detailDialog.visible = false"
      :close-on-overlay-click="false"
      :cancelBtn="null"
      @confirm="detailDialog.visible = false"
    >
      <div class="order-detail" v-if="detailDialog.orderData">
        <!-- 订单基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">订单信息</h3>
          <t-row :gutter="16">
            <t-col :span="12">
              <div class="info-item">
                <span class="label">订单号：</span>
                <span class="value">{{ detailDialog.orderData.orderNumber }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <span class="label">订单状态：</span>
                <t-tag :theme="getStatusTheme(detailDialog.orderData.orderStatus)">
                  {{ detailDialog.orderData.statusText }}
                </t-tag>
              </div>
            </t-col>
          </t-row>
        </div>

        <!-- 收货信息 -->
        <div class="detail-section">
          <h3 class="section-title">收货信息</h3>
          <t-row :gutter="16">
            <t-col :span="12">
              <div class="info-item">
                <span class="label">收货人：</span>
                <span class="value">{{ detailDialog.orderData.receiverName }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ detailDialog.orderData.receiverPhone }}</span>
              </div>
            </t-col>
          </t-row>
        </div>
      </div>
    </t-dialog>
    <!-- 订单发货对话框 -->
    <t-dialog
      :visible="cancelDialog.visible"
      header="订单取消"
      width="400px"
      :close-on-overlay-click="false"
      @confirm="handleConfirmCancel"
      @cancel="cancelDialog.visible = false"
      @close="cancelDialog.visible = false"
    >
      <div class="cancel-confirm">
        <p>
          确定要取消订单
          <span class="order-no">{{ cancelDialog.orderData.orderId }}</span>
          吗？
        </p>
        <p>取消后，该订单将无法恢复。</p>
      </div>
    </t-dialog>

    <!-- 发货对话框 -->
    <t-dialog
      :visible="shipDialog.visible"
      header="订单发货"
      width="500px"
      :close-on-overlay-click="false"
      @confirm="handleConfirmShip"
      @cancel="shipDialog.visible = false"
      @close="shipDialog.visible = false"
    >
      <t-form ref="shipFormRef" :data="shipDialog.formData" :rules="shipRules">
        <t-form-item label="物流公司" name="logisticsCompany">
          <t-select
            v-model="shipDialog.formData.logisticsCompany"
            :options="logisticsOptions"
            placeholder="请选择物流公司"
          />
        </t-form-item>

        <t-form-item label="运单号" name="trackingNo">
          <t-input
            v-model="shipDialog.formData.trackingNo"
            placeholder="请输入运单号"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import orderManagementApi from '@/constants/api/back/order-management.api';

export default Vue.extend({
  name: 'OrderManagement',

  data() {
    return {
      // 搜索表单
      searchForm: {
        orderNumber: '',
        orderStatus: null,
        customerId: '',
      },

      // 订单列表数据
      orderList: [],
      orderStats: {},

      // 表格配置
      rowKey: 'id',
      selectedRowKeys: [],
      dataLoading: false,

      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },

      // 订单状态选项
      statusOptions: [
        { label: '已付款', value: "paid" },
        { label: '待付款', value: "pending" },
        { label: '待收货', value: "shipped" },
        { label: '已完成', value: "completed" },
        { label: '已取消', value: "cancelled" },
      ],

      // 物流公司选项
      logisticsOptions: [
        { label: '顺丰速运', value: 'SF' },
        { label: '圆通速递', value: 'YTO' },
        { label: '中通快递', value: 'ZTO' },
        { label: '申通快递', value: 'STO' },
        { label: '韵达速递', value: 'YD' },
        { label: '百世汇通', value: 'HTKY' },
        { label: '德邦快递', value: 'DBL' },
      ],

      // 订单详情对话框
      detailDialog: {
        visible: false,
        orderData: null,
      },
      // 订单取消对话框
      cancelDialog: {
        visible: false,
        orderData: {
          customerId: '',
          orderId: null,
        },
      },

      // 发货对话框
      shipDialog: {
        visible: false,
        orderId: null,
        formData: {
          logisticsCompany: '',
          trackingNo: '',
        },
      },

      // 发货表单验证规则
      shipRules: {
        logisticsCompany: [
          { required: true, message: '请选择物流公司', trigger: 'change' },
        ],
        trackingNo: [
          { required: true, message: '请输入运单号', trigger: 'blur' },
        ],
      },
      // 表格列配置
      columns: [
        // {
        //   colKey: 'row-select',
        //   type: 'multiple',
        //   width: 50,
        //   fixed: 'left',
        // },
        {
          title: '订单号',
          colKey: 'orderNumber',
          width: 180,
          ellipsis: true,
        },
        {
          title: '订单状态',
          colKey: 'statusText',
          width: 100,
          align: 'center',
        },
        {
          title: '商品信息',
          colKey: 'items',
          width: 200,
        },
        {
          title: '收货人',
          colKey: 'receiverName',
          width: 100,
          ellipsis: true,
        },
        {
          title: '收货地址',
          colKey: 'receiverAddress',
          width: 100,
          ellipsis: true,
        },
        {
          title: '联系电话',
          colKey: 'receiverPhone',
          width: 120,
        },
        {
          title: '支付方式',
          colKey: 'paymentMethod',
          width: 100,
        },
        {
          title: '订单金额',
          colKey: 'totalAmount',
          width: 120,
          align: 'right',
        },
        {
          title: '下单时间',
          colKey: 'createTime',
          width: 160,
          align: 'center',
        },
        {
          title: '操作',
          colKey: 'op',
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
    };
  },

  computed: {
    // 计算属性
  },

  mounted() {
    this.loadOrderList();
    this.loadOrderStats();
  },

  methods: {
    calculateProductTotal(items: any[]) {
      return items.reduce((total, product) => total + (product.price * product.quantity), 0).toFixed(2) // 保留两位小数
    },
    // 加载订单列表
    async loadOrderList() {
      try {
        this.dataLoading = true;
        const params = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.searchForm,
        };

        const response = await this.$request.post(orderManagementApi.queryOrderPage.url, params);

        if (response.code === 0) {
          this.orderList = response.data.records || [];
          this.pagination.total = +response.data.total || 0;
        } else {
          MessagePlugin.error(response.message || '获取订单列表失败');
        }
      } catch (error) {
        console.error('获取订单列表失败:', error);
        MessagePlugin.error('获取订单列表失败');
      } finally {
        this.dataLoading = false;
      }
    },

    // 加载订单统计
    async loadOrderStats() {
      try {
        const response = await this.$request.get(orderManagementApi.getOrderStats.url);

        if (response.code === 0) {
          this.orderStats = response.data || {};
        }
      } catch (error) {
        console.error('获取订单统计失败:', error);
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1;
      this.loadOrderList();
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        orderNumber: '',
        orderStatus: null,
        customerId: '',
      };
      this.pagination.current = 1;
      this.loadOrderList();
    },

    // 分页变化
    handlePageChange(pageInfo: any) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
      this.loadOrderList();
    },

    // 表格选择变化
    handleSelectChange(selectedRowKeys: number[]) {
      this.selectedRowKeys = selectedRowKeys;
    },

    // 查看订单详情
    async handleDetail(row: any) {
      try {
        const response = await this.$request.get(
          `${orderManagementApi.getOrderDetail.url}/${row.id}?customerId=${row.customerId}`
        );

        if (response.code === 0) {
          this.detailDialog.orderData = response.data;
          this.detailDialog.visible = true;
        } else {
          MessagePlugin.error(response.message || '获取订单详情失败');
        }
      } catch (error) {
        console.error('获取订单详情失败:', error);
        MessagePlugin.error('获取订单详情失败');
      }
    },

    // 发货
    handleShip(row: any) {
      this.shipDialog.orderId = row.id;
      this.shipDialog.formData = {
        logisticsCompany: '',
        trackingNo: '',
      };
      this.shipDialog.visible = true;
    },

    // 确认发货
    async handleConfirmShip() {
      try {
        const valid = await this.$refs.shipFormRef.validate();
        if (!valid) return;

        const response = await this.$request.post(orderManagementApi.shipOrder.url, {
          orderId: this.shipDialog.orderId,
          ...this.shipDialog.formData,
        });

        if (response.code === 0) {
          MessagePlugin.success('发货成功');
          this.shipDialog.visible = false;
          this.loadOrderList();
          this.loadOrderStats();
        } else {
          MessagePlugin.error(response.message || '发货失败');
        }
      } catch (error) {
        console.error('发货失败:', error);
        MessagePlugin.error('发货失败');
      }
    },

    // 取消订单
    handleCancel(row: any) {
      this.cancelDialog.orderData.orderId = row.id;
      this.cancelDialog.orderData.customerId = row.customerId;
      this.cancelDialog.visible = true;
    },

    // 确认取消订单
    async handleConfirmCancel() {
      try {
        const response = await this.$request.post(orderManagementApi.cancelOrder.url, {
          orderId: this.cancelDialog.orderData.orderId,
          customerId: this.cancelDialog.orderData.customerId,
        });

        if (response.code === 0) {
          MessagePlugin.success('订单取消成功');
          this.cancelDialog.visible = false;
          this.loadOrderList();
          this.loadOrderStats();
        } else {
          MessagePlugin.error(response.message || '取消订单失败');
        }
      } catch (error) {
        console.error('取消订单失败:', error);
        MessagePlugin.error('取消订单失败');
      }
    },

    // 复制订单号
    handleCopyOrderNo(orderNo: string) {
      navigator.clipboard.writeText(orderNo).then(() => {
        MessagePlugin.success('订单号已复制到剪贴板');
      }).catch(() => {
        MessagePlugin.error('复制失败');
      });
    },

    // 获取状态主题色
    getStatusTheme(status: any) {
      const themes: any = {
        "pending": 'warning',
        "shipped": 'primary',
        "paid": 'info',
        "completed": 'success',
        "cancelled": 'danger',
      };
      return themes[status] || 'default';
    },

    // 格式化日期
    formatDate(dateString: string) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.order-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px 32px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .t-icon {
          font-size: 28px;
          color: #6366f1;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .t-button {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 统计卡片
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      :deep(.t-card__body) {
        padding: 20px;
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: #fff;

          &.pending {
            background: linear-gradient(135deg, #f59e0b, #d97706);
          }

          &.processing {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
          }

          &.shipping {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
          }

          &.completed {
            background: linear-gradient(135deg, #10b981, #059669);
          }
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #6b7280;
          }
        }
      }
    }
  }

  // 搜索卡片
  .search-card {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.t-card__body) {
      padding: 20px 24px;
    }

    .t-form {
      .t-form-item {
        margin-bottom: 0;
      }
    }

    .filter-buttons {
      display: flex;
      gap: 12px;
      margin-left: auto;

      .t-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 表格卡片
  .table-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.t-card__body) {
      padding: 0;
    }

    :deep(.t-table) {
      border-radius: 12px;
      overflow: hidden;

      .t-table__header {
        background: #f8fafc;
      }

      .t-button--variant-text {
        padding: 4px 8px;
        margin: 0 2px;
      }
    }
  }

  // 订单号样式
  .order-no {
    display: flex;
    align-items: center;
    gap: 8px;

    .order-text {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: #374151;
    }
  }

  // 商品列表样式
  .product-list {
    .product-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .product-image {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        object-fit: cover;
      }

      .product-info {
        flex: 1;

        .product-name {
          font-size: 12px;
          color: #374151;
          margin-bottom: 2px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .product-spec {
          font-size: 11px;
          color: #9ca3af;
          margin-bottom: 2px;
        }

        .product-quantity {
          font-size: 11px;
          color: #6b7280;
        }
      }
    }
  }

  // 金额信息样式
  .amount-info {
    text-align: right;

    .total-amount {
      font-size: 16px;
      font-weight: 600;
      color: #dc2626;
      margin-bottom: 4px;
    }

    .amount-detail {
      font-size: 11px;
      color: #6b7280;
      line-height: 1.4;

      span {
        display: block;
      }
    }
  }
}

// 对话框样式
.order-detail {
  .detail-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e7eb;
    }

    .info-item {
      margin-bottom: 12px;

      .label {
        font-size: 14px;
        color: #6b7280;
        margin-right: 8px;
      }

      .value {
        font-size: 14px;
        color: #374151;
        font-weight: 500;
      }
    }

    .amount-summary {
      .amount-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f3f4f6;

        &.total {
          border-bottom: none;
          font-weight: 600;
          font-size: 16px;
          color: #dc2626;
        }

        .label {
          color: #6b7280;
        }

        .value {
          color: #374151;
        }
      }
    }
  }
}

// 删除确认对话框
.delete-content,
.cancel-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;

  .warning-icon {
    font-size: 24px;
    color: #f59e0b;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .delete-text,
  .cancel-text {
    flex: 1;

    .delete-title,
    .cancel-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 8px 0;
    }

    .delete-desc,
    .cancel-desc {
      font-size: 14px;
      color: #6b7280;
      margin: 4px 0;
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .order-management {
    .stats-cards {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 768px) {
  .order-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 20px;

      .header-left {
        text-align: center;
      }

      .header-actions {
        width: 100%;
        justify-content: center;

        .t-button {
          flex: 1;
          max-width: 150px;
        }
      }
    }

    .stats-cards {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .search-card {
      :deep(.t-form) {
        .t-form-item {
          margin-bottom: 16px;
        }
      }

      .filter-buttons {
        margin-left: 0;
        margin-top: 16px;
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
