<template>
  <div class="product-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <t-icon name="shop" />
          商品管理
        </h1>
        <p class="page-desc">管理商城商品，包括商品信息、分类、库存等</p>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="handleAdd">
          <t-icon name="add" />
          新增商品
        </t-button>
        <t-button
          theme="success"
          variant="outline"
          @click="handleBatchPublish"
          :disabled="selectedRowKeys.length === 0"
        >
          <t-icon name="check" />
          批量上架
        </t-button>
        <t-button
          theme="warning"
          variant="outline"
          @click="handleBatchUnpublish"
          :disabled="selectedRowKeys.length === 0"
        >
          <t-icon name="close" />
          批量下架
        </t-button>
        <t-button
          theme="danger"
          variant="outline"
          @click="handleBatchDelete"
          :disabled="selectedRowKeys.length === 0"
        >
          <t-icon name="delete" />
          批量删除
        </t-button>
      </div>
    </div>

    <!-- 商品统计卡片 -->
    <div class="stats-cards">
      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon total">
            <t-icon name="shop" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ productStats.totalProducts || 0 }}</div>
            <div class="stat-label">商品总数</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon published">
            <t-icon name="check-circle" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ productStats.onlineProducts || 0 }}</div>
            <div class="stat-label">已上架</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon unpublished">
            <t-icon name="close-circle" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ productStats.offlineProducts || 0 }}</div>
            <div class="stat-label">已下架</div>
          </div>
        </div>
      </t-card>

      <t-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon low-stock">
            <t-icon name="error-circle" />
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ productStats.lowStockProducts || 0 }}</div>
            <div class="stat-label">库存不足</div>
          </div>
        </div>
      </t-card>
    </div>

    <!-- 搜索筛选 -->
    <t-card class="search-card">
      <t-form :data="searchForm" layout="inline" @submit="handleSearch" @reset="handleReset">
        <t-form-item label="商品名称" name="name">
          <t-input
            v-model="searchForm.name"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px;"
          />
        </t-form-item>

        <t-form-item label="商品分类" name="categoryId">
          <t-select
            v-model="searchForm.categoryId"
            :options="categoryOptions"
            placeholder="请选择商品分类"
            clearable
            style="width: 200px;"
          />
        </t-form-item>

        <t-form-item label="商品状态" name="status">
          <t-select
            v-model="searchForm.isActive"
            :options="statusOptions"
            placeholder="请选择商品状态"
            clearable
            style="width: 150px;"
          />
        </t-form-item>

<!--        <t-form-item label="库存预警" name="lowStock">-->
<!--          <t-checkbox v-model="searchForm.lowStock">仅显示库存不足</t-checkbox>-->
<!--        </t-form-item>-->

        <t-form-item label="价格范围" name="priceRange">
          <t-input-number
            v-model="searchForm.minPrice"
            placeholder="最低价"
            :min="0"
            style="min-width: 150px;"
          />
          <span style="margin: 0 8px;">-</span>
          <t-input-number
            v-model="searchForm.maxPrice"
            placeholder="最高价"
            :min="0"
            style="min-width: 150px;"
          />
        </t-form-item>

        <div class="filter-buttons">
          <t-button theme="primary" @click="handleSearch" class="search-btn">
            <t-icon name="search" />
            查询
          </t-button>
          <t-button variant="outline" @click="handleReset" class="reset-btn">
            <t-icon name="refresh" />
            重置
          </t-button>
        </div>
      </t-form>
    </t-card>

    <!-- 商品列表 -->
    <t-card class="table-card">
      <t-table
        :columns="columns"
        :data="productList"
        :rowKey="rowKey"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        :loading="dataLoading"
        @page-change="handlePageChange"
        @select-change="handleSelectChange"
        stripe
        hover
      >
        <template #image="{ row }">
          <div class="product-image">
            <img :src="row.image" :alt="row.name" />
          </div>
        </template>

        <template #name="{ row }">
          <div class="product-name">
            <div class="name-text">{{ row.name }}</div>
            <div class="product-code">编码：{{ row.code }}</div>
          </div>
        </template>

        <template #price="{ row }">
          <div class="price-info">
            <div class="current-price">¥{{ row.price }}</div>
            <div class="original-price" v-if="row.originalPrice && row.originalPrice > row.price">
              ¥{{ row.originalPrice }}
            </div>
          </div>
        </template>

        <template #category="{ row }">
          <t-tag theme="default" variant="light">{{ row.categoryName }}</t-tag>
        </template>

        <template #stock="{ row }">
          <div class="stock-info">
            <span :class="{ 'low-stock': true }">
              {{ row.stock }}
            </span>
<!--            <div class="stock-status" v-if="row.stock <= row.lowStockThreshold">-->
<!--              <t-icon name="error-circle" />-->
<!--              库存不足-->
<!--            </div>-->
          </div>
        </template>

        <template #isActive="{ row }">
          <t-tag v-if="row.isActive === 1" theme="success">已上架</t-tag>
          <t-tag v-else-if="row.isActive === 0" theme="danger">已下架</t-tag>
          <t-tag v-else theme="warning">草稿</t-tag>
        </template>

        <template #createdAt="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>

        <template #op="{ row }">
          <t-space>
            <t-button variant="text" theme="primary" @click="handleDetail(row)">
              <t-icon name="view" />
              详情
            </t-button>

            <t-button variant="text" theme="primary" @click="handleEdit(row)">
              <t-icon name="edit" />
              编辑
            </t-button>

            <t-button variant="text" theme="primary" @click="handleCopy(row)">
              <t-icon name="copy" />
              复制
            </t-button>

            <t-button
              v-if="row.isActive === 0"
              variant="text"
              theme="success"
              @click="handlePublish(row)"
            >
              <t-icon name="check" />
              上架
            </t-button>

            <t-button
              v-if="row.isActive === 1"
              variant="text"
              theme="warning"
              @click="handleUnpublish(row)"
            >
              <t-icon name="close" />
              下架
            </t-button>

            <t-button variant="text" theme="danger" @click="handleDelete(row)">
              <t-icon name="delete" />
              删除
            </t-button>
          </t-space>
        </template>
      </t-table>
    </t-card>



    <!-- 删除确认对话框 -->
    <t-dialog
      :visible="deleteDialog.visible"
      header="删除确认"
      @confirm="handleConfirmDelete"
      @cancel="deleteDialog.visible = false"
      @close="deleteDialog.visible = false"
    >
      <div class="delete-content">
        <t-icon name="error-circle" class="warning-icon" />
        <div class="delete-text">
          <p class="delete-title">确定要删除商品"{{ deleteDialog.productName }}"吗？</p>
          <p class="delete-desc">删除后无法恢复，请谨慎操作</p>
        </div>
      </div>
    </t-dialog>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import productManagementApi from '@/constants/api/back/product-management.api';
import productCategoryApi from '@/constants/api/back/product-category.api';

export default Vue.extend({
  name: 'ProductManagement',

  data() {
    return {
      // 搜索表单
      searchForm: {
        name: '',
        categoryId: null,
        minPrice: null,
        maxPrice: null,
        isActive: 1,
      },

      // 商品列表数据
      productList: [],
      productStats: {},
      categoryOptions: [],

      // 表格配置
      rowKey: 'id',
      selectedRowKeys: [],
      dataLoading: false,

      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },

      // 商品状态选项
      statusOptions: [
        { label: '已上架', value: 1 },
        { label: '已下架', value: 0 },
        { label: '草稿', value: -1 },
      ],

      // 上传配置
      uploadAction: '/api/upload/product-image',
      // 商品详情对话框
      detailDialog: {
        visible: false,
        productData: null,
      },



      // 删除对话框
      deleteDialog: {
        visible: false,
        productId: null,
        productName: '',
      },

      // 批量删除对话框
      batchDeleteDialog: {
        visible: false,
      },
      // 表格列配置
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 50,
          fixed: 'left',
        },
        {
          title: '商品图片',
          colKey: 'image',
          width: 80,
          align: 'center',
        },
        {
          title: '商品名称',
          colKey: 'name',
          width: 200,
          ellipsis: true,
        },
        {
          title: '价格',
          colKey: 'price',
          width: 120,
          align: 'right',
        },
        {
          title: '分类',
          colKey: 'category',
          width: 120,
          align: 'center',
        },
        {
          title: '库存',
          colKey: 'stock',
          width: 100,
          align: 'center',
        },
        {
          title: '状态',
          colKey: 'isActive',
          width: 100,
          align: 'center',
        },
        {
          title: '创建时间',
          colKey: 'createdAt',
          width: 160,
          align: 'center',
        },
        {
          title: '操作',
          colKey: 'op',
          width: 500,
          align: 'center',
          fixed: 'right',
        },
      ],
    };
  },

  computed: {
    // 计算属性
  },

  mounted() {
    this.loadProductList();
    this.loadProductStats();
    this.loadCategoryOptions();
  },

  methods: {
    // 加载商品列表
    async loadProductList() {
      try {
        this.dataLoading = true;
        const params = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.searchForm,
        };

        const response = await this.$request.post(productManagementApi.queryProductPage.url, params);

        if (response.code === 0) {
          this.productList = response.data.records || [];
          this.pagination.total = +response.data.total || 0;
        } else {
          MessagePlugin.error(response.message || '获取商品列表失败');
        }
      } catch (error) {
        console.error('获取商品列表失败:', error);
        MessagePlugin.error('获取商品列表失败');
      } finally {
        this.dataLoading = false;
      }
    },

    // 加载商品统计
    async loadProductStats() {
      try {
        const response = await this.$request.get(productManagementApi.getProductStats.url);

        if (response.code === 0) {
          this.productStats = response.data || {};
        }
      } catch (error) {
        console.error('获取商品统计失败:', error);
      }
    },

    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await this.$request.get(productCategoryApi.getAllCategories.url);

        if (response.code === 0) {
          this.categoryOptions = (response.data || []).map(item => ({
            label: item.name,
            value: item.id
          }));
        }
      } catch (error) {
        console.error('获取分类选项失败:', error);
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1;
      this.loadProductList();
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        name: '',
        categoryId: null,
        isActive: 1,
        minPrice: null,
        maxPrice: null,
      };
      this.pagination.current = 1;
      this.loadProductList();
    },

    // 分页变化
    handlePageChange(pageInfo: any) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
      this.loadProductList();
    },

    // 表格选择变化
    handleSelectChange(selectedRowKeys: number[]) {
      this.selectedRowKeys = selectedRowKeys;
    },
    // 新增商品
    handleAdd() {
      this.$router.push('/mall-management/product-form/add');
    },

    // 编辑商品
    handleEdit(row: any) {
      this.$router.push(`/mall-management/product-form/edit/${row.id}`);
    },

    // 查看商品详情
    async handleDetail(row: any) {
      try {
        const response = await this.$request.get(
          `${productManagementApi.getProductDetail.url}${row.id}`
        );

        if (response.code === 0) {
          this.detailDialog.productData = response.data;
          this.detailDialog.visible = true;
        } else {
          MessagePlugin.error(response.message || '获取商品详情失败');
        }
      } catch (error) {
        console.error('获取商品详情失败:', error);
        MessagePlugin.error('获取商品详情失败');
      }
    },

    // 复制商品
    async handleCopy(row: any) {
      this.$router.push({ name: 'product-form-edit', params: { id: row.id, type: 'copy' } });
    },

    // 上架商品
    async handlePublish(row: any) {
      try {
        const response = await this.$request.post(`${productManagementApi.unpublishProduct.url}?id=${row.id}&isActive=1`);

        if (response.code === 0) {
          MessagePlugin.success('上架成功');
          this.loadProductList();
          this.loadProductStats();
        } else {
          MessagePlugin.error(response.message || '上架失败');
        }
      } catch (error) {
        console.error('上架失败:', error);
        MessagePlugin.error('上架失败');
      }
    },

    // 下架商品
    async handleUnpublish(row: any) {
      try {
        const response = await this.$request.post(`${productManagementApi.unpublishProduct.url}?id=${row.id}&isActive=0`);

        if (response.code === 0) {
          MessagePlugin.success('下架成功');
          this.loadProductList();
          this.loadProductStats();
        } else {
          MessagePlugin.error(response.message || '下架失败');
        }
      } catch (error) {
        console.error('下架失败:', error);
        MessagePlugin.error('下架失败');
      }
    },

    // 删除商品
    handleDelete(row: any) {
      this.deleteDialog.productId = row.id;
      this.deleteDialog.productName = row.name;
      this.deleteDialog.visible = true;
    },

    // 确认删除商品
    async handleConfirmDelete() {
      try {
        const response = await this.$request.post(productManagementApi.batchDeleteProduct.url, [this.deleteDialog.productId]);

        if (response.code === 0) {
          MessagePlugin.success('删除成功');
          this.deleteDialog.visible = false;
          this.loadProductList();
          this.loadProductStats();
        } else {
          MessagePlugin.error(response.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    },

    // 批量上架
    async handleBatchPublish() {
      try {
        const response = await this.$request.post(`${productManagementApi.batchUpdateStatus.url}?isActive=1`, this.selectedRowKeys);

        if (response.code === 0) {
          MessagePlugin.success('批量上架成功');
          this.selectedRowKeys = [];
          this.loadProductList();
          this.loadProductStats();
        } else {
          MessagePlugin.error(response.message || '批量上架失败');
        }
      } catch (error) {
        console.error('批量上架失败:', error);
        MessagePlugin.error('批量上架失败');
      }
    },

    // 批量下架
    async handleBatchUnpublish() {
      try {
        const response = await this.$request.post(`${productManagementApi.batchUpdateStatus.url}?isActive=0`, this.selectedRowKeys);

        if (response.code === 0) {
          MessagePlugin.success('批量下架成功');
          this.selectedRowKeys = [];
          this.loadProductList();
          this.loadProductStats();
        } else {
          MessagePlugin.error(response.message || '批量下架失败');
        }
      } catch (error) {
        console.error('批量下架失败:', error);
        MessagePlugin.error('批量下架失败');
      }
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRowKeys.length === 0) {
        MessagePlugin.warning('请选择要删除的商品');
        return;
      }
      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          const { code } = await this.$request.post(productManagementApi.batchDeleteProduct.url, this.selectedRowKeys);
          if (code !== 0) {
            this.$message.error({ content: '操作失败' });
            return;
          }
          this.$message.success({ content: '操作成功' });
          this.loadProductList();
          this.loadProductStats();
          this.selectedRowKeys = [];
          res.destroy();
        },
      });
    },

    // 图片加载错误
    handleImageError(event) {
      event.target.src = '/default-product.png'; // 设置默认图片
    },

    // 获取状态主题色
    getStatusTheme(status: any) {
      const themes: any = {
        1: 'success',
        0: 'danger',
        '-1': 'warning',
      };
      return themes[status] || 'default';
    },

    // 获取状态文本
    getStatusText(status: any) {
      const texts: any = {
        1: '已上架',
        0: '已下架',
        '-1': '草稿',
      };
      return texts[status] || '未知';
    },

    // 格式化日期
    formatDate(dateString: string) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.product-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px 32px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .t-icon {
          font-size: 28px;
          color: #6366f1;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      .t-button {
        height: 40px;
        padding: 0 16px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 统计卡片
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 24px;

    .stat-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      :deep(.t-card__body) {
        padding: 20px;
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: #fff;

          &.total {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
          }

          &.published {
            background: linear-gradient(135deg, #10b981, #059669);
          }

          &.unpublished {
            background: linear-gradient(135deg, #ef4444, #dc2626);
          }

          &.low-stock {
            background: linear-gradient(135deg, #f59e0b, #d97706);
          }
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #6b7280;
          }
        }
      }
    }
  }

  // 搜索卡片
  .search-card {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.t-card__body) {
      padding: 20px 24px;
    }

    .t-form {
      .t-form-item {
        margin-bottom: 0;
      }
    }

    .filter-buttons {
      display: flex;
      gap: 12px;
      margin-left: auto;

      .t-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 表格卡片
  .table-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.t-card__body) {
      padding: 0;
    }

    :deep(.t-table) {
      border-radius: 12px;
      overflow: hidden;

      .t-table__header {
        background: #f8fafc;
      }

      .t-button--variant-text {
        padding: 4px 8px;
        margin: 0 2px;
      }
    }
  }

  // 商品图片样式
  .product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  // 商品名称样式
  .product-name {
    .name-text {
      font-size: 14px;
      color: #374151;
      margin-bottom: 4px;
      font-weight: 500;
    }

    .product-code {
      font-size: 12px;
      color: #9ca3af;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }

  // 价格信息样式
  .price-info {
    text-align: right;

    .current-price {
      font-size: 16px;
      font-weight: 600;
      color: #dc2626;
      margin-bottom: 2px;
    }

    .original-price {
      font-size: 12px;
      color: #9ca3af;
      text-decoration: line-through;
    }
  }

  // 库存信息样式
  .stock-info {
    text-align: center;

    .low-stock {
      color: #dc2626;
      font-weight: 600;
    }

    .stock-status {
      font-size: 11px;
      color: #dc2626;
      margin-top: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2px;

      .t-icon {
        font-size: 10px;
      }
    }
  }
}

// 对话框样式
.product-detail {
  .detail-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #e5e7eb;
    }

    .product-main-image {
      width: 100%;
      height: 200px;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #e5e7eb;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .product-info {
      .product-title {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 16px 0;
      }

      .info-item {
        margin-bottom: 12px;

        .label {
          font-size: 14px;
          color: #6b7280;
          margin-right: 8px;
        }

        .value {
          font-size: 14px;
          color: #374151;
          font-weight: 500;

          &.price {
            color: #dc2626;
            font-size: 18px;
            font-weight: 600;
          }

          &.low-stock {
            color: #dc2626;
          }
        }
      }
    }

    .product-description {
      font-size: 14px;
      color: #374151;
      line-height: 1.6;
      padding: 16px;
      background: #f9fafb;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }
  }
}



// 删除确认对话框
.delete-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;

  .warning-icon {
    font-size: 24px;
    color: #f59e0b;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .delete-text {
    flex: 1;

    .delete-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 8px 0;
    }

    .delete-desc {
      font-size: 14px;
      color: #6b7280;
      margin: 4px 0;
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .product-management {
    .stats-cards {
      grid-template-columns: repeat(2, 1fr);
    }

    .header-actions {
      .t-button {
        padding: 0 12px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 768px) {
  .product-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 20px;

      .header-left {
        text-align: center;
      }

      .header-actions {
        width: 100%;
        justify-content: center;

        .t-button {
          flex: 1;
          max-width: 120px;
          padding: 0 8px;
          font-size: 12px;
        }
      }
    }

    .stats-cards {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .search-card {
      :deep(.t-form) {
        .t-form-item {
          margin-bottom: 16px;
        }
      }

      .filter-buttons {
        margin-left: 0;
        margin-top: 16px;
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
