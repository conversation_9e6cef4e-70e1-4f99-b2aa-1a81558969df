<template>
  <div class="product-form-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">{{ isEdit ? copyType === 'copy' ? '复制商品' : '编辑商品' : '新增商品' }}</h1>
      </div>
      <div class="header-actions">
        <t-button variant="outline" @click="handleBack">取消</t-button>
        <t-button theme="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? copyType === 'copy' ? '复制' : '编辑' : '新增' }}
        </t-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <product-form
        ref="productFormRef"
        :product-info="formData"
        :category-options="categoryOptions"
        @update:product-info="handleFormDataUpdate"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { MessagePlugin } from 'tdesign-vue';
import ProductForm from './components/ProductForm.vue';
import productManagementApi from '@/constants/api/back/product-management.api';
import productCategoryApi from '@/constants/api/back/product-category.api';
import BeanUtilsService from "@/service/bean-utils.service";

export default {
  name: 'ProductFormPage',

  components: {
    ProductForm,
  },

  data() {
    return {
      submitLoading: false,
      categoryOptions: [],
      formData: {
        // 基本信息
        id: null,
        name: '',
        subtitle: '',
        categoryId: null,
        description: '',

        // 图片信息
        image: '',
        images: [],
        detailImages: [],

        // 价格库存
        price: 0,
        originalPrice: null,
        stock: 0,

        // 规格信息
        specs: [],

        // 标签促销
        tags: [],
        promotions: [],

        // 商品属性
        isActive: 1,
      },
    };
  },

  computed: {
    isEdit() {
      return !!this.$route.params.id;
    },

    productId() {
      return this.$route.params.id;
    },
    copyType() {
      return this.$route.params.type;
    },
  },

  async mounted() {
    await this.loadCategoryOptions();

    if (this.isEdit) {
      await this.loadProductDetail();
    }
  },

  methods: {
    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await this.$request.get(productCategoryApi.getAllCategories.url);

        if (response.code === 0) {
          this.categoryOptions = (response.data || []).map(item => ({
            label: item.name,
            value: item.id
          }));
        }
      } catch (error) {
        console.error('获取分类选项失败:', error);
        MessagePlugin.error('获取分类选项失败');
      }
    },

    // 加载商品详情
    async loadProductDetail() {
      try {
        const { code, data, message } = await this.$request.get(
          `${productManagementApi.getProductDetail.url}${this.productId}`
        );

        if (code === 0) {
          this.formData = { ...data, imageFiles: data.image ? [data.image] : [],
            images: data.images && JSON.parse(data.images).length ? BeanUtilsService.copy(JSON.parse(data.images)) : [],
            imagesFiles: data.images && JSON.parse(data.images).length ? BeanUtilsService.copy(JSON.parse(data.images)) : [],
            detailImages: data.detailImages && JSON.parse(data.detailImages).length ? BeanUtilsService.copy(JSON.parse(data.detailImages)) : [],
            detailImagesFiles: data.detailImages && JSON.parse(data.detailImages).length ? BeanUtilsService.copy(JSON.parse(data.detailImages)) : [],
            promotions: data.promotions && JSON.parse(data.promotions).length ? BeanUtilsService.copy(JSON.parse(data.promotions)) : [],
            tags: data.tags && JSON.parse(data.tags).length ? BeanUtilsService.copy(JSON.parse(data.tags)) : [],
            specs: data.specs && JSON.parse(data.specs).length ? BeanUtilsService.copy(JSON.parse(data.specs)) : [],
          };
        } else {
          MessagePlugin.error(message || '获取商品详情失败');
          this.handleBack();
        }
      } catch (error) {
        console.error('获取商品详情失败:', error);
        MessagePlugin.error('获取商品详情失败');
        this.handleBack();
      }
    },

    // 表单数据更新
    handleFormDataUpdate(newData: any) {
      this.formData = { ...newData };
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 验证表单
        const valid = await this.$refs.productFormRef.validate();
        if (!valid) return;

        const formDataProduct = this.$refs.productFormRef.formData;

        this.submitLoading = true;
        const formData = JSON.parse(JSON.stringify(formDataProduct));
        // 删除saveProduct接口中不需要的字段
        delete formData.imageFiles;
        delete formData.detailImagesFiles;
        delete formData.imagesFiles;
        delete formData.categoryName;
        delete formData.salesCount;
        delete formData.status;
        delete formData.createdAt;
        delete formData.updatedAt;

        // 复制商品
        if (this.isEdit && this.copyType === 'copy') {
          delete formData.id;
        }

        const response = await this.$request.post(
          productManagementApi.saveProduct.url,
          formData
        );

        if (response.code === 0) {
          let msg = '商品创建成功'; // 默认创建文案
          if (this.isEdit) {
            msg = this.copyType === 'copy' ? '复制商品成功' : '商品更新成功';
          }
          MessagePlugin.success(msg);
          this.handleBack();
        } else {
          MessagePlugin.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('提交失败:', error);
        MessagePlugin.error('操作失败');
      } finally {
        this.submitLoading = false;
      }
    },

    // 返回列表页
    handleBack() {
      this.$router.push('/mall-management/mall-management-product-list');
    },
  },
};
</script>

<style lang="less" scoped>
@import '@/style/variables';

.product-form-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;

  .page-header {
    background: #fff;
    border-radius: 20px;
    padding: 20px 24px;
    margin-bottom: 28px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;

      .back-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        border-radius: 12px;
        color: #64748b;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        border: 1px solid #e2e8f0;

        &:hover {
          color: #667eea;
          background: #f8fafc;
          border-color: #cbd5e0;
          transform: translateX(-2px);
        }

        .t-icon {
          font-size: 18px;
        }
      }

      .page-title {
        font-size: 20px;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        letter-spacing: -0.025em;
      }
    }

    .header-actions {
      display: flex;
      gap: 16px;

      .t-button {
        height: 38px;
        padding: 0 24px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover::before {
          left: 100%;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        &[theme="primary"] {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #fff;

          &:hover {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
          }
        }

        &[variant="outline"] {
          background: #fff;
          border: 2px solid #e2e8f0;
          color: #64748b;

          &:hover {
            border-color: #cbd5e0;
            background: #f8fafc;
          }
        }
      }
    }
  }

  .form-container {
    position: relative;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .product-form-page {
    padding: 20px;

    .page-header {
      padding: 28px 32px;
      margin-bottom: 28px;

      .header-left {
        .page-title {
          font-size: 24px;
        }
      }

      .header-actions {
        gap: 12px;

        .t-button {
          height: 44px;
          padding: 0 20px;
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .product-form-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 24px;
      padding: 24px 20px;
      margin-bottom: 24px;
      text-align: center;

      .header-left {
        flex-direction: column;
        gap: 16px;
        width: 100%;

        .back-btn {
          align-self: flex-start;
        }

        .page-title {
          font-size: 22px;
        }
      }

      .header-actions {
        width: 100%;
        justify-content: center;

        .t-button {
          flex: 1;
          max-width: 140px;
          height: 42px;
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .product-form-page {
    padding: 12px;

    .page-header {
      padding: 20px 16px;
      margin-bottom: 20px;

      .header-left {
        .page-title {
          font-size: 20px;
        }
      }

      .header-actions {
        flex-direction: column;
        gap: 12px;

        .t-button {
          max-width: none;
          width: 100%;
        }
      }
    }
  }
}
</style>
