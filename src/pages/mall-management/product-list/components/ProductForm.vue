<template>
  <div class="product-form">
    <div class="form-container">
      <t-form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        label-width="120px"
        @submit="handleSubmit"
        class="modern-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="shop" />
            </div>
            <div class="section-info">
              <h3 class="section-title">基本信息</h3>
              <p class="section-desc">设置商品的基础信息和分类</p>
            </div>
          </div>

          <div class="section-content">
            <div class="form-grid">
              <div class="form-item-wrapper">
                <t-form-item label="商品名称" name="name" class="form-item">
                  <t-input
                    v-model="formData.name"
                    placeholder="请输入商品名称，如：时尚T恤"
                    :maxlength="100"
                    class="modern-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="shop" />
                    </template>
                  </t-input>
                </t-form-item>
                <div class="field-tip">商品名称将显示在商品列表和详情页</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="商品副标题" name="subtitle" class="form-item">
                  <t-input
                    v-model="formData.subtitle"
                    placeholder="请输入商品副标题，如：舒适透气"
                    :maxlength="50"
                    class="modern-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="edit" />
                    </template>
                  </t-input>
                </t-form-item>
                <div class="field-tip">简短的商品特色描述</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="商品分类" name="categoryId" class="form-item">
                  <t-select
                    v-model="formData.categoryId"
                    :options="categoryOptions"
                    placeholder="请选择商品所属分类"
                    :clearable="true"
                    class="modern-select"
                  >
                    <template #prefixIcon>
                      <t-icon name="layers" />
                    </template>
                  </t-select>
                </t-form-item>
                <div class="field-tip">选择最符合的商品分类</div>
              </div>

              <div class="form-item-wrapper full-width">
                <t-form-item label="商品描述" name="description" class="form-item">
                  <t-textarea
                    v-model="formData.description"
                    placeholder="请输入商品详细描述，包括材质、特点、使用方法等"
                    :maxlength="1000"
                    :autosize="{ minRows: 4, maxRows: 8 }"
                    class="modern-textarea"
                  />
                </t-form-item>
                <div class="field-tip">详细的商品介绍，有助于提升转化率</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品图片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="image" />
            </div>
            <div class="section-info">
              <h3 class="section-title">商品图片</h3>
              <p class="section-desc">上传商品主图、轮播图和详情图</p>
            </div>
          </div>

          <div class="section-content">
            <div class="upload-grid">
              <!-- 商品主图 -->
              <div class="upload-item-wrapper">
                <div class="upload-label">商品主图</div>
                <div class="upload-row">
                  <div class="preview-container" v-if="formData.imageFiles.length">
                    <div v-for="(file, index) in formData.imageFiles" :key="index" class="preview-item">
                      <img :src="file" class="preview-image" />
                      <div class="preview-mask">
                        <t-icon name="delete" style="color: #ed0909;" @click="handleMainImageRemove(file, index)"/>
                      </div>
                    </div>
                  </div>
                  <div class="upload-container" v-if="formData.imageFiles.length < 1">
                    <t-upload
                      :show-image-file-name="false"
                      theme="image"
                      key="main-image-upload"
                      :max="1"
                      :size-limit="{ size: 5, unit: 'MB' }"
                      accept="image/*"
                      :autoUpload="false"
                      @change="onSubmitMainImage"
                      @remove="handleMainImageRemove"
                      class="main-image-upload"
                    >
                      <template #trigger>
                        <div class="upload-trigger main-trigger">
                          <t-icon name="add" />
                          <div class="upload-text">上传主图</div>
                        </div>
                      </template>
                    </t-upload>
                  </div>
                  <div class="upload-tips">
                    <div class="tip-text">商品主图（必填）</div>
                    <div class="tip-list">
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>建议尺寸：800x800px</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>支持格式：JPG/PNG</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>文件大小：不超过5MB</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 商品轮播图 -->
              <div class="upload-item-wrapper">
                <div class="upload-label">商品轮播图</div>
                <div class="upload-row">
                  <div class="preview-container" v-if="formData.imagesFiles.length">
                    <div v-for="(file, index) in formData.imagesFiles" :key="index" class="preview-item">
                      <img :src="file" class="preview-image" />
                      <div class="preview-mask">
                        <t-icon name="delete" style="color: #ed0909;" @click="handleImagesRemove(file, index)"/>
                      </div>
                    </div>
                  </div>
                  <div class="upload-container" v-if="formData.imagesFiles.length < 5">
                    <t-upload
                      :show-image-file-name="false"
                      theme="custom"
                      key="images-upload"
                      multiple
                      :max="5"
                      :size-limit="{ size: 5, unit: 'MB' }"
                      accept="image/*"
                      :autoUpload="false"
                      @change="onSubmitImages"
                      @remove="handleImagesRemove"
                      class="images-upload"
                    >
                      <template #trigger>
                        <div class="upload-trigger images-trigger">
                          <t-icon name="add" />
                          <div class="upload-text">上传轮播图</div>
                        </div>
                      </template>
                    </t-upload>
                  </div>
                  <div class="upload-tips">
                    <div class="tip-text">商品轮播图（可选，最多5张）</div>
                    <div class="tip-list">
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>用于商品详情页轮播展示</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>建议尺寸：800x800px</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 商品详情图 -->
              <div class="upload-item-wrapper">
                <div class="upload-label">商品详情图</div>
                <div class="upload-row">
                  <div class="preview-container" v-if="formData.detailImagesFiles.length">
                    <div v-for="(file, index) in formData.detailImagesFiles" :key="index" class="preview-item">
                      <img :src="file" class="preview-image" />
                      <div class="preview-mask">
                        <t-icon name="delete" style="color: #ed0909;" @click="handleDetailImagesRemove(file, index)"/>
                      </div>
                    </div>
                  </div>
                  <div class="upload-container" v-if="formData.detailImagesFiles.length < 10">
                    <t-upload
                      :show-image-file-name="false"
                      theme="custom"
                      key="main-image-upload"
                      multiple
                      :max="10"
                      :size-limit="{ size: 5, unit: 'MB' }"
                      accept="image/*"
                      :autoUpload="false"
                      @change="onSubmitDetailImages"
                      @remove="handleDetailImagesRemove"
                      class="detail-images-upload"
                    >
                      <template #trigger>
                        <div class="upload-trigger detail-trigger">
                          <t-icon name="add" />
                          <div class="upload-text">上传详情图</div>
                        </div>
                      </template>
                    </t-upload>
                  </div>
                  <div class="upload-tips">
                    <div class="tip-text">商品详情图（可选，最多10张）</div>
                    <div class="tip-list">
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>用于详细展示商品细节</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>建议尺寸：750px宽度</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格库存 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="logo-codepen" />
            </div>
            <div class="section-info">
              <h3 class="section-title">价格库存</h3>
              <p class="section-desc">设置商品的价格和库存信息</p>
            </div>
          </div>

          <div class="section-content">
            <div class="form-grid">
              <div class="form-item-wrapper">
                <t-form-item label="销售价格" name="price" class="form-item">
                  <t-input-number
                    v-model="formData.price"
                    :min="0"
                    :decimal-places="2"
                    placeholder="请输入销售价格"
                    class="modern-input-number"
                    style="width: 100%;"
                  >
                    <template #prefixIcon>
                      <t-icon name="money-circle" />
                    </template>
                  </t-input-number>
                </t-form-item>
                <div class="field-tip">商品的实际销售价格</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="原价" name="originalPrice" class="form-item">
                  <t-input-number
                    v-model="formData.originalPrice"
                    :min="0"
                    :decimal-places="2"
                    placeholder="请输入原价（可选）"
                    class="modern-input-number"
                    style="width: 100%;"
                  >
                    <template #prefixIcon>
                      <t-icon name="money-circle" />
                    </template>
                  </t-input-number>
                </t-form-item>
                <div class="field-tip">用于显示折扣效果，可不填</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="库存数量" name="stock" class="form-item">
                  <t-input-number
                    v-model="formData.stock"
                    :min="0"
                    placeholder="请输入库存数量"
                    class="modern-input-number"
                    style="width: 100%;"
                  >
                    <template #prefixIcon>
                      <t-icon name="layers" />
                    </template>
                  </t-input-number>
                </t-form-item>
                <div class="field-tip">商品的可售库存数量</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品规格 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="setting" />
            </div>
            <div class="section-info">
              <h3 class="section-title">商品规格</h3>
              <p class="section-desc">设置商品的不同规格和对应价格库存</p>
            </div>
          </div>

          <div class="section-content">
            <div class="specs-container">
              <div class="specs-header">
                <t-button theme="default" variant="outline" @click="addSpec">
                  <t-icon name="add" />
                  添加规格
                </t-button>
              </div>

              <div class="specs-list" v-if="formData.specs && formData.specs.length > 0">
                <div
                  v-for="(spec, index) in formData.specs"
                  :key="index"
                  class="spec-item"
                >
                  <div class="spec-header">
                    <span class="spec-title">规格 {{ index + 1 }}</span>
                    <t-button
                      variant="text"
                      theme="danger"
                      @click="removeSpec(index)"
                      class="remove-btn"
                    >
                      <t-icon name="delete" />
                      删除
                    </t-button>
                  </div>

                  <div class="spec-form">
                    <div class="spec-row">
                      <div class="spec-field">
                        <label class="spec-label">规格名称</label>
                        <t-input
                          v-model="spec.name"
                          placeholder="如：红色-M"
                          class="spec-input"
                        />
                      </div>
                      <div class="spec-field">
                        <label class="spec-label">价格</label>
                        <t-input-number
                          v-model="spec.price"
                          :min="0"
                          :decimal-places="2"
                          placeholder="规格价格"
                          class="spec-input"
                        />
                      </div>
                      <div class="spec-field">
                        <label class="spec-label">库存</label>
                        <t-input-number
                          v-model="spec.stock"
                          :min="0"
                          placeholder="规格库存"
                          class="spec-input"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="specs-empty" v-else>
                <t-icon name="inbox" />
                <p>暂无规格，点击上方按钮添加</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品标签和促销 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="tag" />
            </div>
            <div class="section-info">
              <h3 class="section-title">标签促销</h3>
              <p class="section-desc">设置商品标签和促销信息</p>
            </div>
          </div>

          <div class="section-content">
            <div class="form-grid">
              <div class="form-item-wrapper full-width">
                <t-form-item label="商品标签" name="tags" class="form-item">
                  <t-tag-input
                    v-model="formData.tags"
                    placeholder="请输入标签，按回车添加"
                    :max="10"
                    class="modern-tag-input"
                  />
                </t-form-item>
                <div class="field-tip">用于商品搜索和分类，最多10个标签</div>
              </div>

              <div class="form-item-wrapper full-width">
                <t-form-item label="促销信息" name="promotions" class="form-item">
                  <div class="promotions-container">
                    <div class="promotions-header">
                      <t-button theme="default" variant="outline" @click="addPromotion">
                        <t-icon name="add" />
                        添加促销
                      </t-button>
                    </div>

                    <div class="promotions-list" v-if="formData.promotions && formData.promotions.length > 0">
                      <div
                        v-for="(promotion, index) in formData.promotions"
                        :key="index"
                        class="promotion-item"
                      >
                        <t-input
                          v-model="promotion.text"
                          placeholder="如：限时8折"
                          class="promotion-input"
                        />
                        <t-button
                          variant="text"
                          theme="danger"
                          @click="removePromotion(index)"
                        >
                          <t-icon name="delete" />
                        </t-button>
                      </div>
                    </div>
                  </div>
                </t-form-item>
                <div class="field-tip">促销标签将显示在商品卡片上</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="商品状态" name="isActive" class="form-item">
                  <t-radio-group v-model="formData.isActive" class="status-radio">
                    <t-radio :value="1">上架</t-radio>
                    <t-radio :value="0">下架</t-radio>
                  </t-radio-group>
                </t-form-item>
                <div class="field-tip">设置商品的上架状态</div>
              </div>
            </div>
          </div>
        </div>
      </t-form>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import BeanUtilsService from "@/service/bean-utils.service";

const testDomain = import.meta.env.VITE_TEST_IMAGE_DOMAIN;

export default Vue.extend({
  name: 'ProductForm',

  props: {
    productInfo: {
      type: Object,
      default: () => ({}),
    },
    categoryOptions: {
      type: Array,
      default: () => [],
    },
    showActions: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {

      // 表单数据
      formData: {
        id: null,
        name: '',
        subtitle: '',
        categoryId: null,
        description: '',
        image: '',
        images: [],
        detailImages: [],
        imageFiles: [],
        imagesFiles: [],
        detailImagesFiles: [],
        price: 0,
        originalPrice: null,
        stock: 0,
        specs: [],
        tags: [],
        promotions: [],
        isActive: 1,
      },

      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入商品名称', trigger: 'blur' },
          { min: 1, max: 100, message: '商品名称长度在 1 到 100 个字符', trigger: 'blur' },
        ],
        categoryId: [
          { required: true, message: '请选择商品分类', trigger: 'change' },
        ],
        price: [
          { required: true, message: '请输入销售价格', trigger: 'blur' },
          { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' },
        ],
        stock: [
          { required: true, message: '请输入库存数量', trigger: 'blur' },
          { type: 'number', min: 0, message: '库存不能小于0', trigger: 'blur' },
        ],
        image: [
          { required: true, message: '请上传商品主图', trigger: 'change' },
        ],
      },
    };
  },

  watch: {
    productInfo: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          Object.keys(newVal).forEach(key => {
            this.$set(this.formData, key, newVal[key])
          });
        }
      },
      deep: true,
    },
  },

  methods: {
    // 主图移除
    handleMainImageRemove(url: string, index: number) {
      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          this.formData.imageFiles.splice(index, 1)
          this.formData.imageFiles = [...this.formData.imageFiles]
          this.formData.image = '';
          res.destroy();
        },
      });
    },
    async onSubmitMainImage(data: any) {
      const imagePath = import.meta.env.VITE_SHOP_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item: any) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      Promise.all(promises).then((res) => {
        if (res.length) {
          this.formData.imageFiles = [res[0].data.startsWith('https://huaxiacomp.cn')
            ? res[0].data.replace(
              'https://huaxiacomp.cn',
              testDomain
            )
            : res[0].data];
          this.formData.image = this.formData.imageFiles[0].url;
        }
        this.$message.success({ content: '操作成功' });
      }).catch(() => { // 上传失败 关闭弹窗
        this.$message.error({ content: '操作失败' });
      });
    },

    async onSubmitImages(data: any) {
      const imagePath = import.meta.env.VITE_SHOP_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item: any) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      await Promise.all(promises).then((res) => {
        console.error(res);
        this.formData.imagesFiles = res.map(item => item.data.startsWith('https://huaxiacomp.cn')
          ? item.data.replace(
            'https://huaxiacomp.cn',
            testDomain
          )
          : item.data);
        this.formData.images = BeanUtilsService.copy(this.formData.imagesFiles);
        this.$message.success({ content: '操作成功' });
      }).catch(() => { // 上传失败 关闭弹窗
        this.$message.error({ content: '操作失败' });
      });
    },
    // 轮播图移除
    handleImagesRemove(file: any, index: number) {
      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          this.formData.images.splice(index, 1);
          this.formData.imageFiles.splice(index, 1);
          res.destroy();
        },
      });
    },

    // 详情图移除
    handleDetailImagesRemove(file: any, index: number) {
      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          this.formData.detailImages.splice(index, 1);
          this.formData.detailImagesFiles.splice(index, 1);
          res.destroy();
        },
      });
    },
    onSubmitDetailImages(data: any) {
      const imagePath = import.meta.env.VITE_SHOP_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item: any) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      Promise.all(promises).then((res) => {
        this.formData.detailImagesFiles = res.map(item => item.data.startsWith('https://huaxiacomp.cn')
          ? item.data.replace(
            'https://huaxiacomp.cn',
            testDomain
          )
          : item.data);
        this.formData.detailImages = BeanUtilsService.copy(this.formData.detailImagesFiles);
        this.$message.success({ content: '操作成功' });
      }).catch(() => { // 上传失败 关闭弹窗
        this.$message.error({ content: '操作失败' });
      });
    },

    // 添加规格
    addSpec() {
      if (!this.formData.specs) {
        this.formData.specs = [];
      }
      this.formData.specs.push({
        name: '',
        price: 0,
        stock: 0,
      });
    },

    // 删除规格
    removeSpec(index: any) {
      this.formData.specs.splice(index, 1);
    },

    // 添加促销
    addPromotion() {
      if (!this.formData.promotions) {
        this.formData.promotions = [];
      }
      this.formData.promotions.push({
        text: '',
      });
    },

    // 删除促销
    removePromotion(index) {
      this.formData.promotions.splice(index, 1);
    },

    // 发送表单数据
    emitFormData() {
      this.$emit('update:product-info', this.formData);
    },

    // 表单提交
    handleSubmit() {
      this.$emit('submit', this.formData);
    },

    // 表单验证
    async validate() {
      try {
        const valid = await this.$refs.formRef.validate();
        return valid;
      } catch (error) {
        return false;
      }
    },

    // 重置表单
    reset() {
      this.$refs.formRef.reset();
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.product-form {
  .form-container {
    .modern-form {
      // 表单区块样式
      .form-section {
        background: #fff;
        border-radius: 16px;
        margin-bottom: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
          border-color: #e0e0e0;
        }

        .section-header {
          display: flex;
          align-items: center;
          padding: 24px 32px 20px;
          border-bottom: 1px solid #f5f5f5;
          background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

          .section-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

            .t-icon {
              font-size: 24px;
              color: #fff;
            }
          }

          .section-info {
            flex: 1;

            .section-title {
              font-size: 20px;
              font-weight: 700;
              color: #1a202c;
              margin: 0 0 4px 0;
              letter-spacing: -0.025em;
            }

            .section-desc {
              font-size: 14px;
              color: #718096;
              margin: 0;
              line-height: 1.5;
            }
          }
        }

        .section-content {
          padding: 32px;

          .form-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;

            .form-item-wrapper {
              &.full-width {
                grid-column: 1 / -1;
              }

              .form-item {
                margin-bottom: 0;

                :deep(.t-form__label) {
                  font-weight: 600;
                  color: #2d3748;
                  font-size: 14px;
                  margin-bottom: 8px;
                }

                .modern-input,
                .modern-select,
                .modern-textarea,
                .modern-input-number {
                  border-radius: 12px;
                  transition: all 0.3s ease;
                  font-size: 14px;

                  &:hover {
                    border-color: #cbd5e0;
                  }

                  &:focus,
                  &.t-is-focused {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                  }

                  :deep(.t-input__prefix) {
                    .t-icon {
                      color: #667eea;
                      font-size: 16px;
                    }
                  }
                }

                .modern-tag-input {
                  border-radius: 12px;

                  &:hover {
                    border-color: #cbd5e0;
                  }

                  &:focus-within {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                  }
                }
              }

              .field-tip {
                margin-top: 8px;
                font-size: 12px;
                color: #718096;
                line-height: 1.4;
                text-align: right;
                justify-content: flex-end;
                display: flex;
                align-items: center;

                &::before {
                  content: '';
                  width: 4px;
                  height: 4px;
                  border-radius: 50%;
                  background: #cbd5e0;
                  margin-right: 8px;
                  flex-shrink: 0;
                }
              }
            }
          }

          // 上传网格布局
          .upload-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 32px;

            .upload-item-wrapper {
              .upload-label {
                font-size: 16px;
                font-weight: 600;
                color: #2d3748;
                margin-bottom: 16px;
                display: flex;
                align-items: center;

                &::before {
                  content: '';
                  width: 4px;
                  height: 16px;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  border-radius: 2px;
                  margin-right: 12px;
                }
              }

              .upload-row {
                display: flex;
                gap: 24px;
                align-items: flex-start;

                .upload-container {
                  flex-shrink: 0;

                  .upload-trigger {
                    width: 160px;
                    height: 160px;
                    border: 2px dashed #cbd5e0;
                    border-radius: 16px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background: #f7fafc;

                    &:hover {
                      border-color: #667eea;
                      background: #edf2f7;
                      transform: translateY(-2px);
                      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
                    }

                    .t-icon {
                      font-size: 32px;
                      color: #a0aec0;
                      margin-bottom: 8px;
                      transition: all 0.3s ease;
                    }

                    .upload-text {
                      font-size: 14px;
                      color: #718096;
                      font-weight: 500;
                    }

                    &.main-trigger {
                      border-color: #667eea;
                      background: linear-gradient(135deg, #f8faff 0%, #edf2f7 100%);

                      .t-icon {
                        color: #667eea;
                      }

                      .upload-text {
                        color: #667eea;
                      }
                    }
                  }
                }

                .upload-tips {
                  flex: 1;
                  padding-top: 8px;

                  .tip-text {
                    font-size: 14px;
                    font-weight: 600;
                    color: #2d3748;
                    margin-bottom: 12px;
                  }

                  .tip-list {
                    .tip-item {
                      display: flex;
                      align-items: center;
                      margin-bottom: 8px;
                      font-size: 13px;
                      color: #718096;

                      .t-icon {
                        font-size: 14px;
                        color: #48bb78;
                        margin-right: 8px;
                        flex-shrink: 0;
                      }
                    }
                  }
                }
              }
            }
          }

          // 规格容器
          .specs-container {
            .specs-header {
              margin-bottom: 24px;
            }

            .specs-list {
              .spec-item {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 16px;

                .spec-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 16px;

                  .spec-title {
                    font-weight: 600;
                    color: #2d3748;
                  }

                  .remove-btn {
                    color: #e53e3e;
                  }
                }

                .spec-form {
                  .spec-row {
                    display: grid;
                    grid-template-columns: 2fr 1fr 1fr;
                    gap: 16px;

                    .spec-field {
                      .spec-label {
                        display: block;
                        font-size: 12px;
                        font-weight: 600;
                        color: #4a5568;
                        margin-bottom: 8px;
                      }

                      .spec-input {
                        width: 100%;
                        border-radius: 8px;
                      }
                    }
                  }
                }
              }
            }

            .specs-empty {
              text-align: center;
              padding: 40px;
              color: #a0aec0;

              .t-icon {
                font-size: 48px;
                margin-bottom: 16px;
              }

              p {
                margin: 0;
                font-size: 14px;
              }
            }
          }

          // 促销容器
          .promotions-container {
            .promotions-header {
              margin-bottom: 16px;
            }

            .promotions-list {
              .promotion-item {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 12px;

                .promotion-input {
                  flex: 1;
                  border-radius: 8px;
                }
              }
            }
          }

          // 状态单选组
          .status-radio {
            :deep(.t-radio) {
              margin-right: 24px;

              .t-radio__label {
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .product-form {
    .form-container {
      .modern-form {
        .form-section {
          .section-content {
            .form-grid {
              grid-template-columns: 1fr;
            }

            .upload-grid {
              .upload-item-wrapper {
                .upload-row {
                  flex-direction: column;
                  gap: 16px;

                  .upload-container {
                    align-self: center;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .product-form {
    .form-container {
      .modern-form {
        .form-section {
          margin-bottom: 16px;
          border-radius: 12px;

          .section-header {
            padding: 20px 24px 16px;

            .section-icon {
              width: 40px;
              height: 40px;
              margin-right: 12px;

              .t-icon {
                font-size: 20px;
              }
            }

            .section-info {
              .section-title {
                font-size: 18px;
              }

              .section-desc {
                font-size: 13px;
              }
            }
          }

          .section-content {
            padding: 24px 20px;

            .upload-grid {
              .upload-item-wrapper {
                .upload-row {
                  .upload-container {
                    .upload-trigger {
                      width: 120px;
                      height: 120px;

                      .t-icon {
                        font-size: 24px;
                      }

                      .upload-text {
                        font-size: 12px;
                      }
                    }
                  }
                }
              }
            }

            .specs-container {
              .specs-list {
                .spec-item {
                  .spec-form {
                    .spec-row {
                      grid-template-columns: 1fr;
                      gap: 12px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.preview-container {
  display: flex;
  gap: 10px;
}
.preview-item {
  position: relative;
  width: 110px;
  height: 110px;
}
.preview-mask {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.preview-item:hover .preview-mask {
  display: flex;
}
.preview-image {
  width: 110px;
  height: 110px;
  object-fit: contain;
}
.preview-detail {
  width: 160px;
  height: 160px;
  object-fit: contain;
}
</style>
