<template>
  <div class="category-management">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <t-button theme="primary" @click="handleAdd">
        <t-icon name="add" />
        新增分类
      </t-button>
      <t-button theme="default" @click="refreshData">
        <t-icon name="refresh" />
        刷新
      </t-button>
    </div>

    <!-- 分类表格 -->
    <t-table
      :columns="columns"
      :data="categoryList"
      :loading="loading"
      row-key="id"
      :pagination="false"
      stripe
      hover
    >
      <template #icon="{ row }">
        <t-image
          v-if="row.icon"
          :src="row.icon"
          :style="{ width: '32px', height: '32px', borderRadius: '4px' }"
          fit="cover"
          :error="'加载失败'"
        />
        <div v-else class="no-icon">无图标</div>
      </template>

      <template #status="{ row }">
        <t-tag v-if="row.status === 1" theme="success">启用</t-tag>
        <t-tag v-else theme="danger">禁用</t-tag>
      </template>

      <template #operation="{ row }">
        <t-space>
          <t-button variant="text" theme="primary" @click="handleEdit(row)">
            编辑
          </t-button>
          <t-button
            variant="text"
            :theme="row.status === 1 ? 'warning' : 'success'"
            @click="handleToggleStatus(row)"
          >
            {{ row.status === 1 ? '禁用' : '启用' }}
          </t-button>
          <t-button variant="text" theme="danger" @click="handleDelete(row)">
            删除
          </t-button>
        </t-space>
      </template>
    </t-table>

    <!-- 新增/编辑分类对话框 -->
    <t-dialog
      :visible="dialogVisible"
      :header="dialogTitle"
      width="500px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
      @close="handleCancel"
    >
      <t-form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        label-width="80px"
        @submit="handleSubmit"
      >
        <t-form-item label="分类名称" name="name">
          <t-input
            v-model="formData.name"
            placeholder="请输入分类名称"
            :maxlength="50"
          />
        </t-form-item>

        <t-form-item label="分类描述" name="description">
          <t-textarea
            v-model="formData.description"
            placeholder="请输入分类描述"
            :maxlength="200"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>

        <t-form-item label="分类图标" name="icon">
          <div class="preview-container" v-if="formData.iconFiles.length">
            <div v-for="(file, index) in formData.iconFiles" :key="index" class="preview-item">
              <img :src="file" class="preview-image" />
              <div class="preview-mask">
                <t-icon name="delete" style="color: #ed0909;" @click="handleImagesRemove(file, index)"/>
              </div>
            </div>
          </div>
          <t-upload
            v-if="!formData.iconFiles.length"
            theme="image"
            :size-limit="{ size: 2, unit: 'MB' }"
            accept="image/*"
            :autoUpload="false"
            @change="handleIconUploadSuccess"
            @remove="handleIconRemove"
          >
            <template #trigger>
              <t-button variant="outline">
                <t-icon name="upload" />
                上传图标
              </t-button>
            </template>
            <template #tips>
              <div class="upload-tips">
                建议尺寸：64x64px，支持格式：PNG/JPG，大小不超过2MB
              </div>
            </template>
          </t-upload>
        </t-form-item>

        <t-form-item label="排序" name="sortOrder">
          <t-input-number
            v-model="formData.sortOrder"
            :min="0"
            :max="9999"
            placeholder="数字越小排序越靠前"
          />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 删除确认对话框 -->
    <t-dialog
      :visible="deleteDialogVisible"
      header="删除确认"
      @confirm="handleConfirmDelete"
      @cancel="handleCancel"
      :closeOnOverlayClick="false"
      @close="handleCancel"
    >
      <p>确定要删除分类"{{ selectedCategory && selectedCategory.name }}"吗？</p>
      <p class="warning-text">删除后不可恢复，请谨慎操作！</p>
    </t-dialog>
  </div>
</template>

<script>
import storeManagementApi from '@/constants/api/back/store-management.api';
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";

const testDomain = import.meta.env.VITE_TEST_IMAGE_DOMAIN;

export default {
  name: 'CategoryManagement',
  data() {
    return {
      loading: false,
      categoryList: [],
      dialogVisible: false,
      dialogTitle: '新增分类',
      deleteDialogVisible: false,
      selectedCategory: null,

      formData: {
        id: null,
        name: '',
        description: '',
        icon: '',
        iconFiles: [],
        sortOrder: 0
      },

      formRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
        ],
        sortOrder: [
          { required: true, message: '请输入排序值', trigger: 'blur' }
        ]
      },

      columns: [
        {
          title: '分类名称',
          colKey: 'name',
          width: 150,
          align: 'left'
        },
        {
          title: '分类图标',
          colKey: 'icon',
          width: 100,
          align: 'center'
        },
        {
          title: '描述',
          colKey: 'description',
          ellipsis: true,
          align: 'left'
        },
        {
          title: '排序',
          colKey: 'sortOrder',
          width: 80,
          align: 'center'
        },
        {
          title: '状态',
          colKey: 'status',
          width: 80,
          align: 'center'
        },
        {
          title: '创建时间',
          colKey: 'createdAt',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          colKey: 'operation',
          width: 180,
          align: 'center',
          fixed: 'right'
        }
      ]
    };
  },

  mounted() {
    this.loadCategoryList();
  },

  methods: {
    // 加载分类列表
    async loadCategoryList() {
      try {
        this.loading = true;
        const response = await this.$request.get(storeManagementApi.queryCategoryList.url);

        if (response.code === 0) {
          this.categoryList = response.data || [];
        } else {
          this.$message.error(response.message || '获取分类列表失败');
        }
      } catch (error) {
        console.error('获取分类列表失败:', error);
        this.$message.error('获取分类列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 刷新数据
    refreshData() {
      this.loadCategoryList();
    },

    // 新增分类
    handleAdd() {
      this.dialogTitle = '新增分类';
      this.resetFormData();
      this.dialogVisible = true;
    },

    // 编辑分类
    handleEdit(row) {
      this.dialogTitle = '编辑分类';
      this.formData = {
        id: row.id,
        name: row.name,
        description: row.description || '',
        icon: row.icon || '',
        iconFiles: row.icon ? [row.icon] : [],
        sortOrder: row.sortOrder || 0
      };
      this.dialogVisible = true;
    },

    // 切换状态
    async handleToggleStatus(row) {
      try {
        const newStatus = row.status === 1 ? 0 : 1;
        const response = await this.$request.post(
          `${storeManagementApi.category.updateCategoryStatus.url}?id=${row.id}&status=${newStatus}`
        );

        if (response.code === 0) {
          this.$message.success(`${newStatus === 1 ? '启用' : '禁用'}成功`);
          this.loadCategoryList();
        } else {
          this.$message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('更新状态失败:', error);
        this.$message.error('操作失败');
      }
    },

    // 删除分类
    handleDelete(row) {
      this.selectedCategory = row;
      this.deleteDialogVisible = true;
    },

    // 确认删除
    async handleConfirmDelete() {
      try {
        const response = await this.$request.delete(
          `${storeManagementApi.category.deleteCategory.url}/${this.selectedCategory.id}`
        );

        if (response.code === 0) {
          this.$message.success('删除成功');
          this.loadCategoryList();
        } else {
          this.$message.error(response.message || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
      } finally {
        this.deleteDialogVisible = false;
        this.selectedCategory = null;
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.formRef.validate();
        if (!valid) return;

        const submitData = {
          ...this.formData,
          iconFiles: undefined // 移除文件对象
        };

        const response = await this.$request.post(
          storeManagementApi.saveCategory.url,
          submitData
        );

        if (response.code === 0) {
          this.$message.success(this.formData.id ? '更新成功' : '新增成功');
          this.dialogVisible = false;
          this.loadCategoryList();
        } else {
          this.$message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('提交失败:', error);
        this.$message.error('操作失败');
      }
    },

    // 取消操作
    handleCancel() {
      this.deleteDialogVisible = false;
      this.dialogVisible = false;
      this.resetFormData();
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        id: null,
        name: '',
        description: '',
        icon: '',
        iconFiles: [],
        sortOrder: 0
      };
    },
    handleImagesRemove(file, index) {
      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          this.formData.iconFiles.splice(index, 1);
          res.destroy();
        },
      });
    },

    // 图标上传成功
    async handleIconUploadSuccess(data) {
      const imagePath = import.meta.env.VITE_SHOP_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      await Promise.all(promises).then((res) => {
        this.formData.iconFiles = res.map(item => item.data.startsWith('https://huaxiacomp.cn')
          ? item.data.replace(
            'https://huaxiacomp.cn',
            testDomain
          )
          : item.data
        );// 上传成功 关闭弹窗
        this.formData.icon = res[0].data;
        this.$message.success({ content: '操作成功' });
      }).catch(() => { // 上传失败 关闭弹窗
        this.$message.error({ content: '操作失败' });
      });
    },

    // 移除图标
    handleIconRemove() {
      this.formData.iconFiles = [];
      this.formData.icon = '';
    }
  }
};
</script>

<style lang="less" scoped>
.category-management {
  .operation-bar {
    margin-bottom: 16px;
    display: flex;
    gap: 12px;
  }

  .no-icon {
    color: #999;
    font-size: 12px;
    text-align: center;
  }

  .upload-tips {
    color: #999;
    font-size: 12px;
    margin-top: 4px;
  }

  .warning-text {
    color: #e34d59;
    font-size: 12px;
    margin-top: 8px;
  }
}
.preview-container {
  display: flex;
  gap: 10px;
}
.preview-item {
  position: relative;
  width: 110px;
  height: 110px;
}
.preview-mask {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.preview-item:hover .preview-mask {
  display: flex;
}
.preview-image {
  width: 110px;
  height: 110px;
  object-fit: contain;
}
.preview-detail {
  width: 160px;
  height: 160px;
  object-fit: contain;
}
</style>
