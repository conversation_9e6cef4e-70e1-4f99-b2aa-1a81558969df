<template>
  <div class="store-form">
    <div class="form-container">
      <t-form
        ref="formRef"
        :data="formData"
        :rules="formRules"
        label-width="120px"
        @submit="handleSubmit"
        class="modern-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="shop" />
            </div>
            <div class="section-info">
              <h3 class="section-title">基本信息</h3>
              <p class="section-desc">设置店铺的基础信息和联系方式</p>
            </div>
          </div>

          <div class="section-content">
            <div class="form-grid">
              <div class="form-item-wrapper">
                <t-form-item label="店铺名称" name="storeName" class="form-item">
                  <t-input
                    v-model="formData.storeName"
                    placeholder="请输入店铺名称，如：张三"
                    :maxlength="50"
                    class="modern-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="shop" />
                    </template>
                  </t-input>
                </t-form-item>
                <div class="field-tip">店铺名称将显示在店铺首页</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="店铺编码" name="storeCode" class="form-item">
                  <t-input
                    v-model="formData.storeCode"
                    placeholder="请输入或生成店铺编码"
                    :maxlength="20"
                    class="modern-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="barcode" />
                    </template>
                    <template #suffix>
                      <t-button
                        variant="text"
                        theme="primary"
                        @click="generateCode"
                        class="generate-btn"
                      >
                        <t-icon name="refresh" />
                        生成
                      </t-button>
                    </template>
                  </t-input>
                </t-form-item>
                <div class="field-tip">唯一标识，用于系统内部识别</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="店铺分类" name="categoryId" class="form-item">
                  <t-select
                    v-model="formData.categoryId"
                    :options="categoryOptions"
                    placeholder="请选择店铺所属分类"
                    :clearable="true"
                    class="modern-select"
                  >
                    <template #prefixIcon>
                      <t-icon name="layers" />
                    </template>
                  </t-select>
                </t-form-item>
                <div class="field-tip">选择最符合的店铺类型</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="店主姓名" name="ownerName" class="form-item">
                  <t-input
                    v-model="formData.ownerName"
                    placeholder="请输入店主真实姓名"
                    :maxlength="20"
                    class="modern-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="user" />
                    </template>
                  </t-input>
                </t-form-item>
                <div class="field-tip">店铺负责人姓名</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="店主手机" name="ownerPhone" class="form-item">
                  <t-input
                    v-model="formData.ownerPhone"
                    placeholder="请输入11位手机号码"
                    :maxlength="11"
                    class="modern-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="mobile" />
                    </template>
                  </t-input>
                </t-form-item>
                <div class="field-tip">用于接收订单通知和客户联系</div>
              </div>

              <div class="form-item-wrapper">
                <t-form-item label="店主邮箱" name="ownerEmail" class="form-item">
                  <t-input
                    v-model="formData.ownerEmail"
                    placeholder="请输入邮箱地址（可选）"
                    class="modern-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="mail" />
                    </template>
                  </t-input>
                </t-form-item>
                <div class="field-tip">用于接收重要通知邮件</div>
              </div>

<!--              <div class="form-item-wrapper">-->
<!--                <t-form-item label="店铺状态" name="status" class="form-item">-->
<!--                  <t-radio-group v-model="formData.status" class="status-radio">-->
<!--                    <t-radio :value="1">营业中</t-radio>-->
<!--                    <t-radio :value="0">已关闭</t-radio>-->
<!--                  </t-radio-group>-->
<!--                </t-form-item>-->
<!--                <div class="field-tip">设置店铺的营业状态</div>-->
<!--              </div>-->
            </div>
          </div>
        </div>

        <!-- 店铺图片 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="image" />
            </div>
            <div class="section-info">
              <h3 class="section-title">店铺图片</h3>
              <p class="section-desc">上传店铺Logo和头图，提升店铺形象</p>
            </div>
          </div>

          <div class="section-content">
            <div class="upload-grid">
              <div class="upload-item-wrapper">
                <div class="upload-label">店铺Logo</div>
                <div class="upload-row">
                  <div class="preview-container" v-if="storeLogo.length">
                    <div v-for="(file, index) in storeLogo" :key="index" class="preview-item">
                      <img :src="file" class="preview-image" />
                      <div class="preview-mask">
                        <t-icon name="delete" style="color: #ed0909;" @click="handleImagesRemove(file, index)"/>
                      </div>
                    </div>
                  </div>
                  <div class="upload-container" v-if="!storeLogo.length">
                    <t-upload
                      theme="image"
                      :size-limit="{ size: 2, unit: 'MB' }"
                      accept="image/*"
                      :autoUpload="false"
                      :max="1"
                      @remove="handleLogoRemove"
                      @change="onSubmit"
                      class="modern-upload"
                    >
                      <template #trigger>
                        <div class="upload-trigger logo-trigger">
                          <div class="upload-icon">
                            <t-icon name="add" />
                          </div>
                          <div class="upload-text">点击上传图片</div>
                        </div>
                      </template>
                    </t-upload>
                  </div>
                  <div class="upload-tips">
                    <div class="tip-text">Logo将显示在店铺页面和商品列表中</div>
                    <div class="tip-list">
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>建议尺寸：200x200px</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>支持格式：PNG/JPG</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>文件大小：不超过2MB</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="upload-item-wrapper">
                <div class="upload-label">店铺头图</div>
                <div class="upload-row">
                  <div class="preview-container" v-if="formData.storeImages.length">
                    <div v-for="(file, index) in formData.storeImages" :key="index" class="preview-item">
                      <img :src="file" class="preview-image" />
                      <div class="preview-mask">
                        <t-icon name="delete" style="color: #ed0909;" @click="handleStoreImagesRemove(file, index)"/>
                      </div>
                    </div>
                  </div>
                  <div class="upload-container" v-if="!formData.storeImages.length">
                    <t-upload
                      v-model="formData.storeImages"
                      theme="image"
                      :max="1"
                      :size-limit="{ size: 5, unit: 'MB' }"
                      accept="image/*"
                      :autoUpload="false"
                      @change="onSubmitStoreImages"
                      @remove="handleCoverRemove"
                      class="modern-upload"
                    >
                      <template #trigger>
                        <div class="upload-trigger cover-trigger">
                          <div class="upload-icon">
                            <t-icon name="add" />
                          </div>
                          <div class="upload-text">点击上传图片</div>
                        </div>
                      </template>
                    </t-upload>
                  </div>
                  <div class="upload-tips">
                    <div class="tip-text">头图将作为店铺首页的背景展示</div>
                    <div class="tip-list">
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>建议尺寸：800x400px</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>支持格式：PNG/JPG</span>
                      </div>
                      <div class="tip-item">
                        <t-icon name="check-circle" />
                        <span>文件大小：不超过5MB</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 营业信息 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <t-icon name="time" />
            </div>
            <div class="section-info">
              <h3 class="section-title">营业信息</h3>
              <p class="section-desc">设置店铺营业时间和地址信息</p>
            </div>
          </div>

          <div class="section-content">
            <!-- 营业时间 -->
            <div class="time-section">
              <h4 class="sub-title">
                <t-icon name="time" />
                营业时间
              </h4>
              <div class="form-grid">
                <div class="form-item-wrapper">
                  <t-form-item label="开始时间" name="startTime" class="form-item">
                    <t-time-picker
                      v-model="formData.startTime"
                      placeholder="请选择营业开始时间"
                      format="HH:mm"
                      class="modern-time-picker"
                    >
                      <template #prefixIcon>
                        <t-icon name="time" />
                      </template>
                    </t-time-picker>
                  </t-form-item>
                  <div class="field-tip">店铺每日营业开始时间</div>
                </div>

                <div class="form-item-wrapper">
                  <t-form-item label="结束时间" name="endTime" class="form-item">
                    <t-time-picker
                      v-model="formData.endTime"
                      placeholder="请选择营业结束时间"
                      format="HH:mm"
                      class="modern-time-picker"
                    >
                      <template #prefixIcon>
                        <t-icon name="time" />
                      </template>
                    </t-time-picker>
                  </t-form-item>
                  <div class="field-tip">店铺每日营业结束时间</div>
                </div>
              </div>
            </div>

            <!-- 地址信息 -->
            <div class="address-section">
              <h4 class="sub-title">
                <t-icon name="location" />
                地址信息
              </h4>
              <div class="form-grid address-grid">
                <div class="form-item-wrapper">
                  <t-form-item label="省份" name="province" class="form-item">
                    <t-input
                      v-model="formData.province"
                      placeholder="如：广东省"
                      :maxlength="20"
                      class="modern-input"
                    >
                      <template #prefixIcon>
                        <t-icon name="location" />
                      </template>
                    </t-input>
                  </t-form-item>
                  <div class="field-tip">请输入省份名称</div>
                </div>

                <div class="form-item-wrapper">
                  <t-form-item label="城市" name="city" class="form-item">
                    <t-input
                      v-model="formData.city"
                      placeholder="如：深圳市"
                      :maxlength="20"
                      class="modern-input"
                    >
                      <template #prefixIcon>
                        <t-icon name="location" />
                      </template>
                    </t-input>
                  </t-form-item>
                  <div class="field-tip">请输入城市名称</div>
                </div>

                <div class="form-item-wrapper">
                  <t-form-item label="区县" name="district" class="form-item">
                    <t-input
                      v-model="formData.district"
                      placeholder="如：南山区"
                      :maxlength="20"
                      class="modern-input"
                    >
                      <template #prefixIcon>
                        <t-icon name="location" />
                      </template>
                    </t-input>
                  </t-form-item>
                  <div class="field-tip">请输入区县名称</div>
                </div>

                <div class="form-item-wrapper full-width">
                  <t-form-item label="详细地址" name="address" class="form-item">
                    <t-input
                      v-model="formData.address"
                      placeholder="请输入详细地址，如：科技园南区深圳湾科技生态园"
                      :maxlength="200"
                      class="modern-input"
                    >
                      <template #prefixIcon>
                        <t-icon name="location" />
                      </template>
                    </t-input>
                  </t-form-item>
                  <div class="field-tip">详细的街道地址，便于客户找到店铺</div>
                </div>
              </div>
            </div>

            <!-- 店铺描述 -->
            <div class="description-section">
              <h4 class="sub-title">
                <t-icon name="edit" />
                店铺描述
              </h4>
              <div class="form-item-wrapper full-width">
                <t-form-item label="店铺介绍" name="description" class="form-item">
                  <t-textarea
                    v-model="formData.description"
                    placeholder="请介绍您的店铺特色、主营商品、服务理念等，让客户更好地了解您的店铺..."
                    :maxlength="500"
                    :autosize="{ minRows: 3, maxRows: 5 }"
                    class="modern-textarea"
                  />
                </t-form-item>
                <div class="field-tip">
                  <span>店铺介绍将显示在店铺首页，建议详细描述店铺特色</span>
                  <span class="char-count">{{ formData.description.length }}/500</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions" v-if="showActions">
          <div class="actions-container">
            <t-button
              theme="primary"
              type="submit"
              :loading="submitting"
              class="submit-btn"
              size="large"
            >
              <t-icon name="check" />
              {{ isEdit ? '更新店铺' : '创建店铺' }}
            </t-button>
            <t-button
              theme="default"
              @click="handleCancel"
              class="cancel-btn"
              size="large"
            >
              <t-icon name="close" />
              取消
            </t-button>
          </div>
          <div class="actions-tip">
            <t-icon name="info-circle" />
            <span>{{ isEdit ? '更新后的信息将立即生效' : '创建后店铺将进入审核状态' }}</span>
          </div>
        </div>
      </t-form>
    </div>
  </div>
</template>

<script>
import storeManagementApi from '@/constants/api/back/store-management.api';
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import BeanUtilsService from "@/service/bean-utils.service";

const testDomain = import.meta.env.VITE_TEST_IMAGE_DOMAIN;

export default {
  name: 'StoreForm',
  props: {
    storeInfo: {
      type: Object,
      default: () => ({})
    },
    categoryOptions: {
      type: Array,
      default: () => []
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      submitting: false,
      storeLogo: [],

      formData: {
        // 基本信息
        id: null,
        storeName: '',
        storeCode: '',
        categoryId: null,

        // 店主信息
        ownerId: '**********',
        ownerName: '',
        ownerPhone: '',
        ownerEmail: '',

        // 证照信息
        businessLicense: '',
        licenseImage: '',

        // 店铺图片
        storeLogo: '',
        storeBanner: '',
        storeImages: [],

        // 店铺描述
        description: '',

        // 地址信息
        address: '',
        province: '',
        city: '',
        district: '',
        longitude: null,
        latitude: null,

        // 联系信息
        businessHours: '',
        contactPhone: '',
        servicePhone: '',
        qq: '',
        wechat: '',
        website: '',

        // 店铺类型和经营范围
        storeType: 1,
        businessScope: '',
        tags: [],

        // 佣金和保证金
        commissionRate: null,
        depositAmount: null,

      },

      formRules: {
        storeName: [
          { required: true, message: '请输入店铺名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        storeCode: [
          { required: true, message: '请输入店铺编码', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择店铺分类', trigger: 'change' }
        ],
        ownerName: [
          { required: true, message: '请输入店主姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        ownerPhone: [
          { required: true, message: '请输入店主手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        ownerEmail: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        businessLicense: [
          { required: true, message: '请输入营业执照号', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' }
        ],
        contactPhone: [
          { pattern: /^(\d{3,4}-?)?\d{7,8}$|^1[3-9]\d{9}$/, message: '请输入正确的联系电话', trigger: 'blur' }
        ],
        commissionRate: [
          { type: 'number', min: 0, max: 100, message: '佣金比例应在0-100之间', trigger: 'blur' }
        ],
        depositAmount: [
          { type: 'number', min: 0, message: '保证金不能小于0', trigger: 'blur' }
        ]
      },

      statusOptions: [
        { label: '营业中', value: 1 },
        { label: '已关闭', value: 0 },
        { label: '审核中', value: 2 }
      ]
    };
  },

  computed: {
    isEdit() {
      return !!(this.storeInfo && this.storeInfo.id);
    }
  },

  watch: {
    storeInfo: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.initFormData(newVal);
        } else {
          this.resetFormData();
        }
      },
      deep: true
    }
  },

  methods: {
    // 初始化表单数据
    initFormData(storeInfo) {
      this.formData = {
        // 基本信息
        id: storeInfo.id,
        storeName: storeInfo.storeName || '',
        storeCode: storeInfo.storeCode || '',
        categoryId: storeInfo.categoryId || null,

        // 店主信息
        ownerId: storeInfo.ownerId || null,
        ownerName: storeInfo.ownerName || '',
        ownerPhone: storeInfo.ownerPhone || '',
        ownerEmail: storeInfo.ownerEmail || '',

        // 证照信息
        businessLicense: storeInfo.businessLicense || '',
        licenseImage: storeInfo.licenseImage || '',

        // 店铺图片
        storeLogo: storeInfo.storeLogo || '',
        storeBanner: storeInfo.storeBanner || '',
        storeImages: storeInfo.storeImages && JSON.parse(storeInfo.storeImages).length > 0 ? BeanUtilsService.copy(JSON.parse(storeInfo.storeImages)) : [],

        // 店铺描述
        description: storeInfo.description || '',

        // 地址信息
        address: storeInfo.address || '',
        province: storeInfo.province || '',
        city: storeInfo.city || '',
        district: storeInfo.district || '',
        longitude: storeInfo.longitude || null,
        latitude: storeInfo.latitude || null,

        // 联系信息
        businessHours: storeInfo.businessHours || '',
        contactPhone: storeInfo.contactPhone || '',
        servicePhone: storeInfo.servicePhone || '',
        qq: storeInfo.qq || '',
        wechat: storeInfo.wechat || '',
        website: storeInfo.website || '',

        // 店铺类型和经营范围
        storeType: storeInfo.storeType || 1,
        businessScope: storeInfo.businessScope || '',
        tags: storeInfo.tags || [],

        // 佣金和保证金
        commissionRate: storeInfo.commissionRate || null,
        depositAmount: storeInfo.depositAmount || null,

      };
      this.storeLogo = storeInfo.storeLogo ? [storeInfo.storeLogo] : [];
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        // 基本信息
        id: null,
        storeName: '',
        storeCode: '',
        categoryId: null,

        // 店主信息
        ownerId: null,
        ownerName: '',
        ownerPhone: '',
        ownerEmail: '',

        // 证照信息
        businessLicense: '',
        licenseImage: '',

        // 店铺图片
        storeLogo: '',
        storeBanner: '',
        storeImages: [],

        // 店铺描述
        description: '',

        // 地址信息
        address: '',
        province: '',
        city: '',
        district: '',
        longitude: null,
        latitude: null,

        // 联系信息
        businessHours: '',
        contactPhone: '',
        servicePhone: '',
        qq: '',
        wechat: '',
        website: '',

        // 店铺类型和经营范围
        storeType: 1,
        businessScope: '',
        tags: [],

        // 佣金和保证金
        commissionRate: null,
        depositAmount: null,

      };
    },

    // 生成店铺编码
    async generateCode() {
      try {
        const response = await this.$request.get(storeManagementApi.generateStoreCode.url);
        if (response.code === 0) {
          this.formData.storeCode = response.data;
        }
      } catch (error) {
        console.error('生成编码失败:', error);
        // 生成随机编码作为备选
        this.formData.storeCode = `STORE${  Date.now().toString().slice(-6)}`;
      }
    },

    // Logo上传成功
    handleLogoUploadSuccess(context) {
      if (context.response && context.response.data) {
        this.formData.logo = context.response.data.url;
      }
    },

    // 移除Logo
    handleLogoRemove() {
      this.storeLogo = [];
      this.formData.storeLogo = '';
    },
    handleStoreImagesRemove(file, index) {
      const res = this.$dialog.confirm({
        theme: 'danger',
        body: '确定要删除吗？',
        onConfirm: async () => {
          this.formData.storeImages.splice(index, 1);
          res.destroy();
        },
      });
    },
    handleImagesRemove(file, index) {
      const res = this.$dialog.confirm({
        theme: 'danger',
        header: '温馨提示',
        body: '确定要删除吗？',
        onConfirm: async () => {
          this.storeLogo.splice(index, 1);
          res.destroy();
        },
      });
    },
    async onSubmit(data) {
      const imagePath = import.meta.env.VITE_SHOP_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      await Promise.all(promises).then((res) => {
        this.storeLogo = res.map(item => item.data.startsWith('https://huaxiacomp.cn')
          ? item.data.replace(
            'https://huaxiacomp.cn',
            testDomain
          )
          : item.data
        );// 上传成功 关闭弹窗
        this.$message.success({ content: '操作成功' });
      }).catch(() => { // 上传失败 关闭弹窗
        this.$message.error({ content: '操作失败' });
      });
    },
    async onSubmitStoreImages(data) {
      const imagePath = import.meta.env.VITE_SHOP_IMAGE_ADDRESS;
      // 使用Promise.all遍历并等待上传完成
      const promises = data.map((item) => {
        const formData = new FormData();
        formData.append('file', item.raw);
        formData.append('path', imagePath);
        return this.$request.post(sysVideoUploadApi.coursewareUpload.url, formData);
      });
      await Promise.all(promises).then((res) => {
        this.formData.storeImages = res.map(item =>
          item.data.startsWith('https://huaxiacomp.cn')
            ? item.data.replace(
              'https://huaxiacomp.cn',
              testDomain
            )
            : item.data
        );// 上传成功 关闭弹窗
        this.$message.success({ content: '操作成功' });
      }).catch(() => { // 上传失败 关闭弹窗
        this.$message.error({ content: '操作失败' });
      });
    },

    // 头图上传成功
    handleCoverUploadSuccess(context) {
      if (context.response && context.response.data) {
        this.formData.cover = context.response.data.url;
      }
    },

    // 移除头图
    handleCoverRemove() {
      this.formData.storeImages = [];
    },

    // 提交表单
    async handleSubmit() {
      try {
        const valid = await this.$refs.formRef.validate();
        if (!valid) return;

        this.submitting = true;
        if (this.storeLogo.length) {
          this.formData.storeLogo = this.storeLogo[0] || '';
        }

        this.formData.ownerId = '**********'
        const submitData = {
          ...this.formData,
        };

        const response = await this.$request.post(
          storeManagementApi.saveStore.url,
          submitData
        );

        if (response.code === 0) {
          this.$message.success(this.isEdit ? '更新成功' : '新增成功');
          this.$emit('submit', response.data);
        } else {
          this.$message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('提交失败:', error);
        this.$message.error('操作失败');
      } finally {
        this.submitting = false;
      }
    },

    // 取消操作
    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 表单容器样式 ====================
.store-form {
  background: #f5f7fa;
  min-height: auto;
  padding: 0;

  .form-container {
    padding: 20px;
  }

  .modern-form {
    background: transparent;
  }

  // 表单区块样式
  .form-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;

    // 区块头部
    .section-header {
      background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
      padding: 16px 20px;
      display: flex;
      align-items: center;
      gap: 12px;

      .section-icon {
        width: 24px;
        height: 24px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #fff;
      }

      .section-info {
        flex: 1;

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #fff;
          margin: 0 0 2px 0;
        }

        .section-desc {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          margin: 0;
        }
      }
    }

    // 区块内容
    .section-content {
      padding: 20px;
    }
  }

  // 子标题样式
  .sub-title {
    font-size: 15px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 18px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e7eb;

    .t-icon {
      color: #6366f1;
      font-size: 16px;
    }
  }

  // 时间、地址、描述区块
  .time-section,
  .address-section,
  .description-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 表单网格布局
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;

    &.address-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .form-item-wrapper {
    display: flex;
    flex-direction: column;

    &.full-width {
      grid-column: 1 / -1;
    }
  }

  // 上传网格布局
  .upload-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .upload-item-wrapper {
    display: flex;
    flex-direction: column;
  }

  .upload-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 12px;
  }

  // 上传行布局（图片和提示在一行）
  .upload-row {
    display: flex;
    gap: 16px;
    align-items: flex-start;
  }

  // 表单项样式
  .form-item {
    margin-bottom: 0;

    :deep(.t-form-item__label) {
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;

      &::before {
        color: #ef4444;
        margin-right: 4px;
      }
    }
  }

  // 字段提示样式
  .field-tip {
    font-size: 12px;
    color: #6b7280;
    margin-top: 8px;
    line-height: 1.4;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .char-count {
      color: #9ca3af;
      font-weight: 500;
      margin-left: 8px;
    }
  }

  // 状态单选按钮样式
  .status-radio {
    :deep(.t-radio) {
      margin-right: 24px;
    }
  }

  // 现代化输入框样式
  .modern-input,
  .modern-select,
  .modern-time-picker,
  .modern-textarea {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      border-color: #9ca3af;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    &:focus-within {
      border-color: #6366f1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    :deep(.t-input__inner) {
      font-size: 14px;
    }

    :deep(.t-select__single-display) {
      font-size: 14px;
    }
  }

  // 生成按钮样式
  .generate-btn {
    color: #6366f1;
    font-weight: 500;

    &:hover {
      background: rgba(99, 102, 241, 0.1);
    }
  }

  // 上传组件样式
  .upload-container {
    flex-shrink: 0;
    width: 160px;

    .modern-upload {
      .upload-trigger {
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        padding: 24px 16px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: #fafafa;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        &:hover {
          border-color: #7c3aed;
          background: #f8faff;
        }

        .upload-icon {
          width: 32px;
          height: 32px;
          background: #e5e7eb;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          color: #9ca3af;
          margin-bottom: 8px;
        }

        .upload-text {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }
  }

  .upload-tips {
    flex: 1;
    padding-left: 0;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .tip-text {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 8px;
      text-align: left;
    }

    .tip-list {
      text-align: left;

      .tip-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 6px;
        margin-bottom: 4px;
        font-size: 12px;
        color: #10b981;

        &:last-child {
          margin-bottom: 0;
        }

        .t-icon {
          color: #10b981;
          font-size: 12px;
          order: 0;
        }

        span {
          color: #6b7280;
          order: 1;
        }
      }
    }
  }

  // 操作按钮样式
  .form-actions {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding: 20px;
    text-align: center;

    .actions-container {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-bottom: 12px;

      .submit-btn,
      .cancel-btn {
        min-width: 100px;
        height: 36px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .actions-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      font-size: 12px;
      color: #6b7280;

      .t-icon {
        color: #6366f1;
        font-size: 14px;
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 768px) {
  .store-form {
    .form-container {
      padding: 16px 20px;
    }

    .form-section {
      margin-bottom: 16px;

      .section-header {
        padding: 12px 16px;
        gap: 8px;

        .section-icon {
          width: 20px;
          height: 20px;
          font-size: 12px;
        }

        .section-info {
          .section-title {
            font-size: 14px;
          }

          .section-desc {
            font-size: 11px;
          }
        }
      }

      .section-content {
        padding: 16px;
      }
    }

    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      &.address-grid {
        grid-template-columns: 1fr;
      }
    }

    .upload-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .upload-row {
      flex-direction: column;
      gap: 12px;
    }

    .sub-title {
      font-size: 14px;
      margin-bottom: 12px;
      padding: 8px 0;

      .t-icon {
        font-size: 14px;
      }
    }

    .time-section,
    .address-section,
    .description-section {
      margin-bottom: 16px;
    }

    .form-item {
      :deep(.t-form-item__label) {
        font-size: 13px;
      }
    }

    .form-actions {
      padding: 16px;

      .actions-container {
        flex-direction: column;
        gap: 8px;

        .submit-btn,
        .cancel-btn {
          width: 100%;
          max-width: 200px;
        }
      }
    }

    .upload-container {
      width: 100%;

      .modern-upload {
        .upload-trigger {
          padding: 20px 16px;
          height: 100px;

          .upload-icon {
            width: 28px;
            height: 28px;
            font-size: 14px;
            margin-bottom: 6px;
          }

          .upload-text {
            font-size: 11px;
          }
        }
      }
    }

    .upload-tips {
      .tip-text {
        text-align: left;
        font-size: 11px;
      }

      .tip-list {
        text-align: left;
      }

      .tip-item {
        font-size: 10px;
        gap: 4px;

        .t-icon {
          font-size: 9px;
        }
      }
    }
  }
}
.preview-container {
  display: flex;
  gap: 10px;
}
.preview-item {
  position: relative;
  width: 110px;
  height: 110px;
}
.preview-mask {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.5);
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.preview-item:hover .preview-mask {
  display: flex;
}
.preview-image {
  width: 110px;
  height: 110px;
  object-fit: contain;
}
.preview-detail {
  width: 160px;
  height: 160px;
  object-fit: contain;
}
</style>
