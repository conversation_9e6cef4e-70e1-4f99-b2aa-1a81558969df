<template>
  <div class="store-detail">
    <t-card title="店铺详情" :bordered="false">
      <template #actions>
        <t-button theme="primary" @click="handleEdit">
          <t-icon name="edit" />
          编辑
        </t-button>
        <t-button theme="default" @click="$emit('close')">
          关闭
        </t-button>
      </template>

      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <t-row :gutter="24">
            <t-col :span="12">
              <div class="info-item">
                <label>店铺名称：</label>
                <span>{{ storeInfo.name || '-' }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>店铺编码：</label>
                <span>{{ storeInfo.code || '-' }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>店铺分类：</label>
                <span>{{ storeInfo.categoryName || '-' }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>店主姓名：</label>
                <span>{{ storeInfo.ownerName || '-' }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>联系电话：</label>
                <span>{{ storeInfo.phone || '-' }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>店铺状态：</label>
                <t-tag v-if="storeInfo.status === 1" theme="success">营业中</t-tag>
                <t-tag v-else-if="storeInfo.status === 0" theme="danger">已关闭</t-tag>
                <t-tag v-else-if="storeInfo.status === 2" theme="warning">审核中</t-tag>
                <t-tag v-else theme="default">未知</t-tag>
              </div>
            </t-col>
          </t-row>
        </div>

        <!-- 店铺图片 -->
        <div class="info-section">
          <h3 class="section-title">店铺图片</h3>
          <t-row :gutter="16">
            <t-col :span="8">
              <div class="image-item">
                <label>店铺Logo：</label>
                <div class="image-container">
                  <t-image
                    v-if="storeInfo.logo"
                    :src="storeInfo.logo"
                    :style="{ width: '80px', height: '80px' }"
                    fit="cover"
                    :error="'加载失败'"
                  />
                  <div v-else class="no-image">暂无图片</div>
                </div>
              </div>
            </t-col>
            <t-col :span="16">
              <div class="image-item">
                <label>店铺头图：</label>
                <div class="image-container">
                  <t-image
                    v-if="storeInfo.cover"
                    :src="storeInfo.cover"
                    :style="{ width: '200px', height: '80px' }"
                    fit="cover"
                    :error="'加载失败'"
                  />
                  <div v-else class="no-image">暂无图片</div>
                </div>
              </div>
            </t-col>
          </t-row>
        </div>

        <!-- 营业信息 -->
        <div class="info-section">
          <h3 class="section-title">营业信息</h3>
          <t-row :gutter="24">
            <t-col :span="12">
              <div class="info-item">
                <label>营业时间：</label>
                <span>{{ formatBusinessHours(storeInfo.startTime, storeInfo.endTime) }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>营业状态：</label>
                <t-tag :theme="isBusinessOpen ? 'success' : 'warning'">
                  {{ isBusinessOpen ? '营业中' : '休息中' }}
                </t-tag>
              </div>
            </t-col>
            <t-col :span="24">
              <div class="info-item">
                <label>店铺地址：</label>
                <span>{{ formatAddress(storeInfo.province, storeInfo.city, storeInfo.district, storeInfo.address) }}</span>
              </div>
            </t-col>
            <t-col :span="24">
              <div class="info-item">
                <label>店铺描述：</label>
                <div class="description">{{ storeInfo.description || '暂无描述' }}</div>
              </div>
            </t-col>
          </t-row>
        </div>

        <!-- 统计信息 -->
        <div class="info-section">
          <h3 class="section-title">统计信息</h3>
          <t-row :gutter="24">
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ storeInfo.productCount || 0 }}</div>
                <div class="stat-label">商品数量</div>
              </div>
            </t-col>
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ storeInfo.orderCount || 0 }}</div>
                <div class="stat-label">订单数量</div>
              </div>
            </t-col>
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ storeInfo.viewCount || 0 }}</div>
                <div class="stat-label">浏览次数</div>
              </div>
            </t-col>
            <t-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ storeInfo.rating || '0.0' }}</div>
                <div class="stat-label">店铺评分</div>
              </div>
            </t-col>
          </t-row>
        </div>

        <!-- 时间信息 -->
        <div class="info-section">
          <h3 class="section-title">时间信息</h3>
          <t-row :gutter="24">
            <t-col :span="12">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDateTime(storeInfo.createdAt) }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>更新时间：</label>
                <span>{{ formatDateTime(storeInfo.updatedAt) }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>创建人：</label>
                <span>{{ storeInfo.createdBy || '-' }}</span>
              </div>
            </t-col>
            <t-col :span="12">
              <div class="info-item">
                <label>更新人：</label>
                <span>{{ storeInfo.updatedBy || '-' }}</span>
              </div>
            </t-col>
          </t-row>
        </div>
      </div>
    </t-card>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'StoreDetail',
  props: {
    storeInfo: {
      type: Object,
      default: () => ({})
    }
  },
  
  computed: {
    // 判断是否在营业时间内
    isBusinessOpen() {
      if (!this.storeInfo.startTime || !this.storeInfo.endTime) {
        return false;
      }
      
      const now = dayjs();
      const start = dayjs(this.storeInfo.startTime, 'HH:mm');
      const end = dayjs(this.storeInfo.endTime, 'HH:mm');
      const current = dayjs(now.format('HH:mm'), 'HH:mm');
      
      return current.isAfter(start) && current.isBefore(end);
    }
  },
  
  methods: {
    // 编辑店铺
    handleEdit() {
      this.$emit('edit', this.storeInfo);
    },
    
    // 格式化营业时间
    formatBusinessHours(startTime, endTime) {
      if (!startTime || !endTime) {
        return '未设置';
      }
      return `${startTime} - ${endTime}`;
    },
    
    // 格式化地址
    formatAddress(province, city, district, address) {
      const parts = [province, city, district, address].filter(Boolean);
      return parts.length > 0 ? parts.join('') : '未设置';
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style lang="less" scoped>
.store-detail {
  .detail-content {
    .info-section {
      margin-bottom: 32px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
      }
      
      .info-item {
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;
        
        label {
          font-weight: 500;
          color: #6b7280;
          min-width: 100px;
          margin-right: 8px;
        }
        
        span {
          color: #1f2937;
          flex: 1;
        }
        
        .description {
          color: #1f2937;
          line-height: 1.6;
          white-space: pre-wrap;
        }
      }
      
      .image-item {
        margin-bottom: 16px;
        
        label {
          display: block;
          font-weight: 500;
          color: #6b7280;
          margin-bottom: 8px;
        }
        
        .image-container {
          .no-image {
            width: 80px;
            height: 80px;
            background: #f3f4f6;
            border: 1px dashed #d1d5db;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-size: 12px;
          }
        }
      }
      
      .stat-item {
        text-align: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        
        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }
  }
}
</style>
