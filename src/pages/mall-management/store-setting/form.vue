<template>
  <div class="store-form-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <div class="page-info">
          <h1 class="page-title">
            <t-icon name="shop" />
            {{ isEdit ? '编辑店铺' : '新增店铺' }}
          </h1>
          <p class="page-desc">
            {{ isEdit ? '修改店铺信息，更新后立即生效' : '创建新店铺，提交后将进入审核流程' }}
          </p>
        </div>
      </div>
      <div class="header-actions">
        <t-button variant="outline" @click="handleReset" v-if="!isEdit">
          <t-icon name="refresh" />
          重置表单
        </t-button>
        <t-button theme="primary" @click="handleSubmit" :loading="submitting">
          <t-icon name="check" />
          {{ isEdit ? '保存修改' : '创建店铺' }}
        </t-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <store-form
        ref="storeFormRef"
        :store-info="storeInfo"
        :category-options="categoryOptions"
        @submit="handleFormSubmit"
        @cancel="handleBack"
        :show-actions="false"
      />
    </div>
  </div>
</template>

<script>
import { MessagePlugin } from 'tdesign-vue';
import storeManagementApi from '@/constants/api/back/store-management.api';
import StoreForm from './components/StoreForm.vue';

export default {
  name: 'StoreFormPage',
  components: {
    StoreForm
  },

  data() {
    return {
      submitting: false,
      storeInfo: {},
      categoryOptions: [],
      isEdit: false
    };
  },

  created() {
    this.initPage();
  },

  methods: {
    // 初始化页面
    async initPage() {
      // 判断是编辑还是新增
      const storeId = this.$route.params.id;
      this.isEdit = !!storeId;

      // 加载分类选项
      await this.loadCategoryOptions();

      // 如果是编辑，加载店铺信息
      if (this.isEdit) {
        await this.loadStoreInfo(storeId);
      }
    },

    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await this.$request.get(storeManagementApi.getEnabledCategories.url);

        if (response.code === 0) {
          this.categoryOptions = (response.data || []).map(item => ({
            label: item.name,
            value: item.id
          }));
        }
      } catch (error) {
        console.error('获取分类选项失败:', error);
      }
    },

    // 加载店铺信息
    async loadStoreInfo(storeId) {
      try {
        const response = await this.$request.get(
          `${storeManagementApi.getStoreDetail.url}/${storeId}`
        );

        if (response.code === 0) {
          this.storeInfo = response.data;
        } else {
          this.$message.error(response.message || '获取店铺信息失败');
          this.handleBack();
        }
      } catch (error) {
        console.error('获取店铺信息失败:', error);
        this.$message.error('获取店铺信息失败');
        this.handleBack();
      }
    },

    // 返回列表
    handleBack() {
      this.$router.push('mall-management-store-setting');
    },

    // 重置表单
    handleReset() {
      if (this.$refs.storeFormRef) {
        this.$refs.storeFormRef.resetFormData();
        this.$message.success('表单已重置');
      }
    },

    // 提交表单
    async handleSubmit() {
      if (this.$refs.storeFormRef) {
        this.$refs.storeFormRef.handleSubmit();
      }
    },

    // 表单提交成功
    handleFormSubmit(data) {
      this.$message.success(this.isEdit ? '店铺更新成功' : '店铺创建成功');

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.handleBack();
      }, 1500);
    }
  }
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面布局 ====================
.store-form-page {
  min-height: 100vh;
  background: #f5f7fa;

  // 页面头部
  .page-header {
    background: #fff;
    border-bottom: 1px solid #e5e7eb;
    padding: 20px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;

      .back-btn {
        height: 36px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateX(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }

      .page-info {
        .page-title {
          font-size: 20px;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 4px 0;
          display: flex;
          align-items: center;
          gap: 8px;

          .t-icon {
            font-size: 20px;
            color: #6366f1;
          }
        }

        .page-desc {
          font-size: 14px;
          color: #6b7280;
          margin: 0;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .t-button {
        height: 36px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 表单内容区域
  .form-content {
    padding: 0;
    max-width: none;
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 768px) {
  .store-form-page {
    .page-header {
      padding: 16px 20px;
      flex-direction: column;
      gap: 12px;
      position: static;

      .header-left {
        width: 100%;
        gap: 12px;

        .back-btn {
          height: 32px;
          padding: 0 12px;
          font-size: 13px;
        }

        .page-info {
          .page-title {
            font-size: 18px;
            gap: 6px;

            .t-icon {
              font-size: 18px;
            }
          }

          .page-desc {
            font-size: 13px;
          }
        }
      }

      .header-actions {
        width: 100%;
        justify-content: center;

        .t-button {
          height: 32px;
          padding: 0 12px;
          font-size: 13px;
          flex: 1;
          max-width: 120px;
        }
      }
    }
  }
}
</style>
