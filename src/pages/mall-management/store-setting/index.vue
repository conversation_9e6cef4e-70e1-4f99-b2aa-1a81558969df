<template>
  <div class="store-setting-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="shop" />
          店铺管理
        </div>
        <div class="page-desc">管理平台内所有店铺信息，包括店铺审核、分类管理等</div>
      </div>
      <div class="header-actions">
        <t-button theme="default" variant="outline" @click="handleCategoryManagement">
          <t-icon name="layers" />
          分类管理
        </t-button>
        <t-button theme="primary" @click="handleAdd">
          <t-icon name="add" />
          新增店铺
        </t-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <div class="filter-content">
        <t-form :data="searchForm" ref="searchFormRef" layout="inline" class="filter-form">
          <div class="filter-row">
            <t-form-item label="店铺名称" name="storeName" class="filter-item">
              <t-input
                v-model="searchForm.storeName"
                placeholder="请输入店铺名称"
                :clearable="true"
                class="filter-input"
              >
                <template #prefixIcon>
                  <t-icon name="search" />
                </template>
              </t-input>
            </t-form-item>

            <t-form-item label="店铺分类" name="categoryId" class="filter-item">
              <t-select
                v-model="searchForm.categoryId"
                :options="categoryOptions"
                placeholder="请选择店铺分类"
                :clearable="true"
                class="filter-select"
              >
                <template #prefixIcon>
                  <t-icon name="layers" />
                </template>
              </t-select>
            </t-form-item>

            <t-form-item label="店铺状态" name="status" class="filter-item">
              <t-select
                v-model="searchForm.status"
                :options="statusOptions"
                placeholder="请选择店铺状态"
                :clearable="true"
                class="filter-select"
              >
                <template #prefixIcon>
                  <t-icon name="check-circle" />
                </template>
              </t-select>
            </t-form-item>

            <t-form-item label="所在城市" name="city" class="filter-item">
              <t-input
                v-model="searchForm.city"
                placeholder="请输入城市名称"
                :clearable="true"
                class="filter-input"
              >
                <template #prefixIcon>
                  <t-icon name="location" />
                </template>
              </t-input>
            </t-form-item>

            <!-- 操作按钮 -->
            <div class="filter-buttons">
              <t-button theme="primary" @click="handleSearch" class="search-btn">
                <t-icon name="search" />
                查询
              </t-button>
              <t-button variant="outline" @click="handleReset" class="reset-btn">
                <t-icon name="refresh" />
                重置
              </t-button>
            </div>
          </div>
        </t-form>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="table-info">
          <t-icon name="view-list" />
          <span>店铺列表</span>
          <span class="count-info">（共 {{ pagination.total }} 条）</span>
        </div>
        <div class="batch-operations" v-if="selectedRowKeys.length > 0">
          <span class="selected-count">已选择 {{ selectedRowKeys.length }} 项</span>
          <t-button variant="outline" theme="danger" @click="handleBatchDelete">
            <t-icon name="delete" />
            批量删除
          </t-button>
        </div>
      </div>

      <div class="toolbar-right">
        <t-button variant="outline" @click="refreshData" class="refresh-btn">
          <t-icon name="refresh" />
          刷新
        </t-button>
        <t-button variant="outline" @click="handleExport" class="export-btn">
          <t-icon name="download" />
          导出
        </t-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <t-card class="table-card">
        <t-table
          :columns="columns"
          :data="tableData"
          :loading="loading"
          :pagination="pagination"
          :selected-row-keys="selectedRowKeys"
          row-key="id"
          select-on-row-click
          stripe
          hover
          @select-change="handleSelectChange"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <!-- 店铺Logo -->
          <template #logo="{ row }">
            <t-image
              v-if="row.logo"
              :src="row.logo"
              :style="{ width: '40px', height: '40px', borderRadius: '4px' }"
              fit="cover"
              :error="'加载失败'"
            />
            <div v-else class="no-logo">无Logo</div>
          </template>

          <!-- 店铺信息 -->
          <template #storeInfo="{ row }">
            <div class="store-info">
              <div class="store-name">{{ row.storeName }}</div>
              <div class="store-code">编码：{{ row.storeCode }}</div>
            </div>
          </template>

          <!-- 店主信息 -->
          <template #ownerInfo="{ row }">
            <div class="owner-info">
              <div class="owner-name">{{ row.ownerName }}</div>
              <div class="owner-phone">{{ row.ownerPhone }}</div>
            </div>
          </template>

          <!-- 店铺状态 -->
          <template #status="{ row }">
            <t-tag v-if="row.status === 1" theme="success">营业中</t-tag>
            <t-tag v-else-if="row.status === 0" theme="danger">已关闭</t-tag>
            <t-tag v-else-if="row.status === 2" theme="warning">审核中</t-tag>
            <t-tag v-else theme="default">未知</t-tag>
          </template>

          <!-- 统计信息 -->
          <template #stats="{ row }">
            <div class="stats-info">
              <div class="stat-item">商品：{{ row.productCount || 0 }}</div>
              <div class="stat-item">订单：{{ row.orderCount || 0 }}</div>
            </div>
          </template>

          <!-- 操作列 -->
          <template #operation="{ row }">
            <t-space>
              <t-button variant="text" theme="primary" @click="handleView(row)">
                查看
              </t-button>
              <t-button variant="text" theme="primary" @click="handleEdit(row)">
                编辑
              </t-button>
              <t-dropdown :options="getDropdownOptions(row)" @click="handleDropdownClick($event, row)">
                <t-button variant="text" theme="primary">
                  更多
                  <t-icon name="chevron-down" />
                </t-button>
              </t-dropdown>
            </t-space>
          </template>
        </t-table>
      </t-card>
    </div>

    <!-- 店铺详情抽屉 -->
    <t-drawer
      :visible="detailDrawerVisible"
      title="店铺详情"
      size="large"
      :footer="false"
      @close="detailDrawerVisible = false"
    >
      <store-detail
        v-if="detailDrawerVisible"
        :store-info="selectedStore"
        @edit="handleEditFromDetail"
        @close="detailDrawerVisible = false"
      />
    </t-drawer>



    <!-- 分类管理对话框 -->
    <t-dialog
      :visible="categoryDialogVisible"
      header="分类管理"
      width="1000px"
      :confirm-btn="null"
      :cancel-btn="null"
      @close="categoryDialogVisible = false"
    >
      <category-management v-if="categoryDialogVisible" />
    </t-dialog>

    <!-- 批量删除确认对话框 -->
    <t-dialog
      :visible="batchDeleteDialogVisible"
      header="批量删除确认"
      @confirm="handleConfirmBatchDelete"
      @cancel="batchDeleteDialogVisible = false"
    >
      <p>确定要删除选中的 {{ selectedRowKeys.length }} 个店铺吗？</p>
      <p class="warning-text">删除后不可恢复，请谨慎操作！</p>
    </t-dialog>
  </div>
</template>

<script>
import { MessagePlugin } from 'tdesign-vue';
import storeManagementApi from '@/constants/api/back/store-management.api';
import CategoryManagement from './components/CategoryManagement.vue';
import StoreDetail from './components/StoreDetail.vue';

export default {
  name: 'StoreSetting',
  components: {
    CategoryManagement,
    StoreDetail
  },
  data() {
    return {
      loading: false,
      tableData: [],
      selectedRowKeys: [],
      detailDrawerVisible: false,
      categoryDialogVisible: false,
      batchDeleteDialogVisible: false,
      selectedStore: {},

      // 搜索表单
      searchForm: {
        storeName: '',
        categoryId: '',
        status: '',
        city: ''
      },

      // 分页配置
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0,
        showJumper: true,
        showSizeChanger: true,
        pageSizeOptions: [10, 20, 50, 100]
      },

      // 分类选项
      categoryOptions: [],

      // 状态选项
      statusOptions: [
        { label: '营业中', value: 1 },
        { label: '已关闭', value: 0 },
        { label: '审核中', value: 2 }
      ],

      // 表格列配置
      columns: [
        {
          title: '店铺Logo',
          colKey: 'logo',
          width: 80,
          align: 'center'
        },
        {
          title: '店铺信息',
          colKey: 'storeInfo',
          width: 200,
          align: 'left'
        },
        {
          title: '店铺分类',
          colKey: 'categoryName',
          width: 120,
          align: 'center'
        },
        {
          title: '店主信息',
          colKey: 'ownerInfo',
          width: 150,
          align: 'left'
        },
        {
          title: '所在地区',
          colKey: 'city',
          width: 120,
          align: 'center'
        },
        {
          title: '店铺状态',
          colKey: 'status',
          width: 100,
          align: 'center'
        },
        {
          title: '统计信息',
          colKey: 'stats',
          width: 120,
          align: 'center'
        },
        {
          title: '创建时间',
          colKey: 'createdAt',
          width: 160,
          align: 'center'
        },
        {
          title: '操作',
          colKey: 'operation',
          width: 350,
          align: 'center',
          fixed: 'right'
        }
      ]
    };
  },

  mounted() {
    this.loadStoreList();
    this.loadCategoryOptions();
  },

  methods: {
    // 加载店铺列表
    async loadStoreList() {
      try {
        this.loading = true;
        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          ...this.searchForm
        };

        const response = await this.$request.post(storeManagementApi.queryStorePage.url, params);

        if (response.code === 0) {
          this.tableData = response.data.records || [];
          this.pagination.total = +response.data.total || 0;
        } else {
          this.$message.error(response.message || '获取店铺列表失败');
        }
      } catch (error) {
        console.error('获取店铺列表失败:', error);
        this.$message.error('获取店铺列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await this.$request.get(storeManagementApi.getEnabledCategories.url);

        if (response.code === 0) {
          this.categoryOptions = (response.data || []).map(item => ({
            label: item.name,
            value: item.id
          }));
        }
      } catch (error) {
        console.error('获取分类选项失败:', error);
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1;
      this.loadStoreList();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        storeName: '',
        categoryId: '',
        status: '',
        city: ''
      };
      this.pagination.current = 1;
      this.loadStoreList();
    },

    // 刷新数据
    refreshData() {
      this.loadStoreList();
    },

    // 新增店铺
    handleAdd() {
      this.$router.push('/mall-management/store-form/add');
    },

    // 查看店铺详情
    handleView(row) {
      this.selectedStore = row;
      this.detailDrawerVisible = true;
    },

    // 编辑店铺
    handleEdit(row) {
      this.$router.push(`/mall-management/store-form/edit/${row.id}`);
    },

    // 从详情页编辑
    handleEditFromDetail(storeInfo) {
      this.detailDrawerVisible = false;
      this.$router.push(`/mall-management/store-form/edit/${storeInfo.id}`);
    },

    // 分类管理
    handleCategoryManagement() {
      this.categoryDialogVisible = true;
    },

    // 获取下拉菜单选项
    getDropdownOptions(row) {
      const options = [
        {
          content: row.status === 1 ? '关闭店铺' : '开启店铺',
          value: 'toggleStatus'
        },
        {
          content: '审核店铺',
          value: 'audit'
        },
        {
          content: '删除店铺',
          value: 'delete'
        }
      ];

      return options;
    },

    // 下拉菜单点击
    async handleDropdownClick(data, row) {
      const { value } = data;

      switch (value) {
      case 'toggleStatus':
        await this.handleToggleStatus(row);
        break;
      case 'audit':
        await this.handleAudit(row);
        break;
      case 'delete':
        await this.handleDelete(row);
        break;
      }
    },

    // 切换状态
    async handleToggleStatus(row) {
      try {
        const newStatus = row.status === 1 ? 0 : 1;
        const response = await this.$request.post(
          `${storeManagementApi.updStoreStatus.url}?id=${row.id}&status=${newStatus}`
        );

        if (response.code === 0) {
          this.$message.success(`${newStatus === 1 ? '开启' : '关闭'}成功`);
          this.loadStoreList();
        } else {
          this.$message.error(response.message || '操作失败');
        }
      } catch (error) {
        console.error('更新状态失败:', error);
        this.$message.error('操作失败');
      }
    },

    // 审核店铺
    async handleAudit(row) {
      try {
        const response = await this.$request.post(
          `${storeManagementApi.auditStore.url}?id=${row.id}&status=1`
        );

        if (response.code === 0) {
          this.$message.success('审核通过');
          this.loadStoreList();
        } else {
          this.$message.error(response.message || '审核失败');
        }
      } catch (error) {
        console.error('审核失败:', error);
        this.$message.error('审核失败');
      }
    },

    // 删除店铺
    async handleDelete(row) {
      const confirm = this.$dialog.confirm({
        header: '删除确认',
        body: `确定要删除店铺"${row.storeName}"吗？删除后不可恢复！`,
        theme: 'warning',
        onConfirm: async () => {
          try {
            const response = await this.$request.delete(
              `${storeManagementApi.deleteStore.url}/${row.id}`
            );

            if (response.code === 0) {
              this.$message.success('删除成功');
              this.loadStoreList();
            } else {
              this.$message.error(response.message || '删除失败');
            }
          } catch (error) {
            console.error('删除失败:', error);
            this.$message.error('删除失败');
          }
          confirm.destroy();
        }
      })
    },

    // 批量删除
    handleBatchDelete() {
      this.batchDeleteDialogVisible = true;
    },

    // 确认批量删除
    async handleConfirmBatchDelete() {
      try {
        const response = await this.$request.post(
          storeManagementApi.batchDeleteStore.url,
          { ids: this.selectedRowKeys }
        );

        if (response.code === 0) {
          this.$message.success('批量删除成功');
          this.selectedRowKeys = [];
          this.loadStoreList();
        } else {
          this.$message.error(response.message || '批量删除失败');
        }
      } catch (error) {
        console.error('批量删除失败:', error);
        this.$message.error('批量删除失败');
      } finally {
        this.batchDeleteDialogVisible = false;
      }
    },

    // 导出
    handleExport() {
      this.$message.success('导出功能开发中');
    },

    // 表格选择变化
    handleSelectChange(value) {
      this.selectedRowKeys = value;
    },

    // 分页变化
    handlePageChange(pageInfo) {
      this.pagination.current = pageInfo.current;
      this.loadStoreList();
    },

    // 页面大小变化
    handlePageSizeChange(pageInfo) {
      this.pagination.pageSize = pageInfo.pageSize;
      this.pagination.current = 1;
      this.loadStoreList();
    }
  }
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面主体样式 ====================
.store-setting-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .t-button {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 筛选区域
  .filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 16px;
    overflow: hidden;

    .filter-content {
      padding: 20px 24px;

      .filter-form {
        .filter-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          align-items: flex-end;

          .filter-item {
            min-width: 200px;
            margin-bottom: 0;

            .filter-input,
            .filter-select {
              width: 200px;
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;
            margin-left: auto;

            .t-button {
              height: 32px;
              padding: 0 16px;
              border-radius: 6px;
              font-weight: 500;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }
          }
        }
      }
    }
  }

  // 工具栏
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 24px;

      .table-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #374151;

        .t-icon {
          color: #6b7280;
        }

        .count-info {
          color: #6b7280;
        }
      }

      .batch-operations {
        display: flex;
        align-items: center;
        gap: 12px;

        .selected-count {
          font-size: 14px;
          color: #6b7280;
        }
      }
    }

    .toolbar-right {
      display: flex;
      gap: 12px;

      .t-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 表格区域
  .table-section {
    .table-card {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      overflow: hidden;

      .no-logo {
        width: 40px;
        height: 40px;
        background: #f3f4f6;
        border: 1px dashed #d1d5db;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
        font-size: 12px;
      }

      .store-info {
        .store-name {
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .store-code {
          font-size: 12px;
          color: #6b7280;
        }
      }

      .owner-info {
        .owner-name {
          font-weight: 500;
          color: #1f2937;
          margin-bottom: 4px;
        }

        .owner-phone {
          font-size: 12px;
          color: #6b7280;
        }
      }

      .stats-info {
        .stat-item {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 2px;
        }
      }
    }
  }

  .warning-text {
    color: #e34d59;
    font-size: 12px;
    margin-top: 8px;
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .store-setting-page {
    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            .filter-buttons {
              margin-left: 0;
              margin-top: 16px;
              width: 100%;
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .store-setting-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }

    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            flex-direction: column;
            align-items: stretch;

            .filter-item {
              min-width: auto;

              .filter-input,
              .filter-select {
                width: 100%;
              }
            }

            .filter-buttons {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}
</style>
