<template>
  <div>
    <t-row :gutter="16">
      <t-col :span="2">
        <t-tree  class="tree-area" :data="companyTreeData" :expandLevel="1" activable hover transition @active="companyTreeChange">
          <template #icon="{ node }">
            <caret-right-small-icon v-if="node.getChildren() && !node.expanded" />
            <caret-down-small-icon v-else-if="node.getChildren() && node.expanded" />
            <template v-else>
              <span class="dept-icon" v-if="node.data.deptId" title="公司"><usergroup-icon></usergroup-icon></span>
              <!-- <span><caret-down-small-icon /></span> -->
            </template>
          </template>
        </t-tree>
      </t-col>
      <t-col :span="10">
        <t-form
          ref="form"
          :data="formData"
          :label-width="80"
          colon
          @reset="onReset"
          @submit="onSubmit"
        >
          <t-row class="search-container">
                <t-col>
                  <t-form-item label="公司名称" name="companyName">
                    <t-input
                      v-model="formData.name"
                      class="form-item-content"
                      type="search"
                      placeholder="请输入公司名称"
                      :style="{ minWidth: '134px' }"
                    />
                  </t-form-item>
                </t-col>

                <t-col>
                  <t-form-item label="公司状态" name="status">
                    <t-select
                      v-model="formData.status"
                      class="form-item-content`"
                      :options="STATUS_OPTIONS"
                      placeholder="请选择公司状态"
                    />
                  </t-form-item>
                </t-col>

            <t-col class="operation-container">
              <t-button theme="primary" class="search-button" type="submit" :style="{ marginLeft: '8px' }"> 查询 </t-button>
              <t-button type="reset" class="reset-button" variant="base" theme="default"> 重置 </t-button>
            </t-col>
          </t-row>
          <t-row justify="space-between" class="left-operation-container">
            <div>
              <p v-if="!!selectedRowKeys.length" class="selected-count">已选{{ selectedRowKeys.length }}项</p>
            </div>
            <div>
              <t-dropdown
                trigger="click"
                :options="changeStatusOption"
                @click="clickHandlerChangeStatus"
                :minColumnWidth="88"
              >
                <t-button theme="default" :disabled="selectedRowKeys.length == 0" variant="outline">改变状态</t-button>
              </t-dropdown>
              <t-button @click="handleAdd" v-if="userOperation.hasAdd"> 新增<add-circle-icon slot="icon"/> </t-button>
            </div>
          </t-row>
        </t-form>
        <div class="table-container">
          <t-table
            stripe
            :columns="columns"
            :data="data"
            :rowKey="rowKey"
            :verticalAlign="verticalAlign"
            :hover="true"
            :pagination="pagination"
            :selected-row-keys="selectedRowKeys"
            :loading="dataLoading"
            @page-change="rehandlePageChange"
            @select-change="rehandleSelectChange"
            :headerAffixedTop="true"
          >
            <template #columnName="{ row }">
              <span>
                 {{ getColumnName(row.columnId) }}
               </span>
            </template>
            <template #status="{ row }">
              <t-tag theme="success" v-if="row.status == 1"> 有效 </t-tag>
              <t-tag v-else-if="row.status == 0" theme="danger"> 禁用 </t-tag>
            </template>
            <template #op="slotProps">
              <a class="t-button-link" @click="handleClickDetail(slotProps)" v-if="userOperation.hasEdit">修改</a>
              <t-divider layout="vertical" style="background: #e4e4e4"/>
              <a class="t-button-link t-button-link--theme-danger" @click="handleClickDelete(slotProps.row)" v-if="userOperation.hasDelete">删除</a>
              <t-divider layout="vertical" style="background: #e4e4e4"/>
              <a class="t-button-link" @click="getSellUrl(slotProps.row)">邀请码</a>
              <t-divider layout="vertical" style="background: #e4e4e4"/>
              <a class="t-button-link" @click="handleOpen(slotProps.row)">商户信息</a>
              <t-divider layout="vertical" style="background: #e4e4e4"/>
              <a class="t-button-link" v-if="isAdmin" @click="handleBindQyWx(slotProps.row)">绑定企微</a>
            </template>
          </t-table>
        </div>
      </t-col>
    </t-row>
    <edit-company-modal
      :data="editModalVar.data"
      :allSysCompanyInfos="allSysCompanyInfos"
      :visible="editModalVar.visible"
      @cancel="handelCloseEditModal"
      @success="handelEditModalSuccess"
    ></edit-company-modal>
    <bind-wx-pay
      :wxData="bindWxPayVar.data"
      :visible="bindWxPayVar.visible"
      @cancel="handelCloseBindWxPay"
      @success="handelBindWxPaySuccess"
    ></bind-wx-pay>
    <preview-resource :visible="imageVisible" :rowData="rowData" @close="previewClose"></preview-resource>
    <binding-qw-modal :visible="bindingQwModal.visible" :rowData="bindingQwModal.rowData" @close="handleCloseBindingQwModal"></binding-qw-modal>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { AddCircleIcon, CaretRightSmallIcon, CaretDownSmallIcon, UsergroupIcon } from 'tdesign-icons-vue';
import { treeNodeConvertService } from '@/service/tree-node-convert.service';
import EditCompanyModal from '../components/edit-company-modal.vue';
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import {STATUS_OPTIONS} from '@/constants';
import {Code} from "@/constants/enum/general/code.enum";
import PreviewResource from "@/components/preview-resource/index.vue";
import BindWxPay from "@/pages/organization-management/components/bind-wx-pay.vue";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";
import {getOperationTypeList} from "@/utils/utils";
import {SYSTEM_ROLE_CONST} from "@/constants/enum/system/system-role.enum";
import BindingQwModal from "@/pages/organization-management/components/binding-qw-modal.vue";

export default Vue.extend({
  name: 'TenantManagementMain',
  components: {
    BindWxPay,
    PreviewResource,
    EditCompanyModal,
    AddCircleIcon,
    CaretRightSmallIcon,
    CaretDownSmallIcon,
    UsergroupIcon,
    BindingQwModal,
  },
  props: {
    childrenOperation: {
      type: Array,
      default: [],
      // required: false,
    },
  },
  data() {
    return {
      STATUS_OPTIONS,
      companyTreeData: [],
      allSysCompanyInfos: [],

      nodeContrast: {
        id: 'id',
        parentId: '',
        children: 'columns',
        cascadeField: 'columns',
        subNodeContrast: {
          id: 'id',
          parentId: '',
          children: 'companies',  // 注意这里必须与实际结构一致
          cascadeField: 'companies',
        }
      },
      nzNodeContrast: {
        key: 'id',
        title: 'name',
        label: 'name',
        children: 'columns',
        headquartersId: 'id',
        cascadeField: 'columns',
        subNzNodeContrast: {
          key: 'id',
          title: 'name',
          label: 'name',
          children: 'companies',
          columnId: 'id',
          cascadeField: 'companies',
        }
      },
      formData: {
        name:'',
        status: '',
      },
      changeStatusOption: [
        {
          content: '有效',
          value: 1,
        },
        {
          content: '禁用',
          value: 0,
        },
      ],

      moreButtons: [
        {
          content: '分配角色',
          value: 1,
        },
        // {
        //   content: '分配岗位',
        //   value: 2,
        // },
        {
          content: '重置密码',
          value: 3,
        },
        {
          content: '删除',
          value: 4,
        },
      ],

      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
        },
        {
          title: '公司名称',
          align: 'left',
          width: 160,
          colKey: 'companyName',
          ellipsis: true,
          // fixed: 'left',
        },
        {
          title: '所属栏目',
          align: 'left',
          width: 150,
          colKey: 'columnId',
          cell: 'columnName',  // 自定义渲染
        },
        {
          title: '公司负责人',
          align: 'left',
          width: 100,
          colKey: 'relPerson',
          ellipsis: true,
        },
        {
          title: '负责人手机号',
          align: 'left',
          width: 100,
          colKey: 'phone',
          ellipsis: true,
        },

        {
          title: '公司状态',
          align: 'center',
          width: 100,
          colKey: 'status',
          cell: 'status',
        },
        {
          title: '操作',
          align: 'center',
          width: 220,
          colKey: 'op',
          fixed: 'right',
          cell: 'op',
        },
      ],
      rowKey: 'id',
      active: {
        columnId: '',
      },
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',

      editModalVar: {
        visible: false,
        data: {},
        subData:{}
      },
      bindWxPayVar: {
        visible: false,
        data: {},
        subData:{}
      },
      bindingQwModal: {
        visible: false,
        rowData: {},
      },
      imageUrl: '',
      imageVisible: false,
      title: '',
      rowData: {},
      pagination: {
        // defaultPageSize: 10,
        total: 0,
        // defaultCurrent: 1,
        current: 1,
        pageSize: 10,
      },
      userOperationTab: [], // 用户可操作按钮
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      }
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
    userInfo: {
      get() {
        let user = {};
        try {
          user = JSON.parse(window.localStorage.getItem('core:user') || '{}');
        } catch (e) {
          user = {};
        }
        return user;
      },
    },
    isAdmin() {
      const user = this.userInfo;
      const roleType = user ? String(user.roleType) : '';
      // 设置是否为超级管理员 及 普通管理员
      return roleType === SYSTEM_ROLE_CONST.COMMON_ADMIN.value || roleType === SYSTEM_ROLE_CONST.ADMIN.value;
    }
  },
  async mounted() {
    this.getCompanyAuth();
    this.getList();
    // 获取当前页面可操作按钮，过滤具体类型
    // console.log("全部可操作按钮：", this.childrenOperation);
    if(this.childrenOperation && this.childrenOperation.length > 0) {
      this.childrenOperation.map((item: any) => {
        if (item.menuType && item.menuType == SYSTEM_MENU_CONST.O.value && item.menuUrl) {
          // 截取操作按钮，区分类型
          const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
          this.userOperationTab.push(operationType);
        }
      })
    }
    // console.log("获取到可操作按钮：", this.userOperationTab);
    this.userOperation = getOperationTypeList(this.userOperationTab)
    // console.log("获取到可操作按钮类型：", this.userOperation);
  },

  methods: {
    /**
     * 获取组织架构列表
     */
    async getCompanyAuth() {
      try {
        this.dataLoading = true;
        const params = {
          id: 31000, // 总公司ID
          level: 1, // 公司层级
        };
        const { code, data } = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });
        if (code === 0 && data) {
          const companyTreeData = [
            {
              id: data.id, // 31000
              name: data.name || '总部',
              columns: data.columns.map((column: any) => ({
                ...column,
              })),
            },
          ];
          this.allSysCompanyInfos = data.columns;
          const treeData = treeNodeConvertService.arrayConvertToNzTreeNode(
            companyTreeData,
            this.nodeContrast,
            this.nzNodeContrast,
          );
          this.companyTreeData = treeData;
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },
    getColumnName(columnId: string) {
      const column = this.allSysCompanyInfos.find(item => item.id === columnId);
      return column ? column.name : '未知栏目';
    },
    handleClickDetail(slotProps: any) {
      this.editModalVar.data = {
        ...slotProps.row,
        columnName: this.getColumnName(slotProps.row.columnId)
      };
      this.editModalVar.visible = true;
    },
    rehandlePageChange(curr: any) {
      this.pagination.current = curr.current;
      this.pagination.pageSize = curr.pageSize;
      this.getList();
    },
    /**
     * 设置状态
     */
    clickHandlerChangeStatus(status: any) {
      const confirm = this.$dialog.confirm({
        theme: 'warning',
        header: '温馨提示',
        body: `是否状态变更为：${status.value === 1 ? '有效' : '禁用'}?`,
        onConfirm: async () => {
          (confirm as any).destroy();
          const res = await this.changeStatusIds((this as any).selectedRowKeys, status.value);
          if (res === 'success') {
            this.selectedRowKeys = [];
            this.getList();
          }
        },
      });
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      this.editModalVar.visible = true;
      this.editModalVar.data = {};
    },
    handleOpen(rowData: any) {
      this.bindWxPayVar.visible = true;
      this.bindWxPayVar.data = {companyId: rowData.id};
    },
    /**
     * 搜索提交
     */
    onSubmit(result: any) {
      this.pagination.current = 1;
      if (result.validateResult === true) {
        this.getList();
      }
    },
    onReset() {
      this.pagination.current = 1;
      this.formData = {};
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    handleClickDelete(rowData: any) {
      const confirm = this.$dialog.confirm({
        theme: 'warning',
        header: '温馨提示',
        body: '确认删除该项？',
        onConfirm: async () => {
          (confirm as any).destroy();
          this.updateUserState(rowData);
        },
      });
    },
    handleBindQyWx(rowData: any) {
      const { companyName, id } = rowData;
      this.bindingQwModal.rowData = { companyName, id };
      this.bindingQwModal.visible = true;
    },

    async getSellUrl(rowData: any) {
      this.rowData = {
        ...rowData,
        headquartersId: this.userInfo.headquartersId,
      }
      this.imageVisible = true;
    },
    companyTreeChange(value: any, context: any) {
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      console.log(context.node.data);
      const { columnId } = context.node.data;
      this.active.columnId = columnId;
      this.getList(); // 查询拉取列表
    },
    /**
     * 关闭对话框
     */
    handelCloseEditModal() {
      this.editModalVar.visible = false;
      setTimeout(() => {
        this.editModalVar.data = {};
      }, 500);
    },

    handelCloseBindWxPay() {
      this.bindWxPayVar.visible = false;
      setTimeout(() => {
        this.bindWxPayVar.data = {};
      }, 500);
    },

    handelEditModalSuccess() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
      this.getList();
      this.getCompanyAuth();
    },

    handelBindWxPaySuccess() {
      this.bindWxPayVar.visible = false;
      this.bindWxPayVar.data = {};
    },
    previewClose() {
      this.imageVisible = false;
      this.rowData = {};
    },
    handleCloseBindingQwModal() {
      this.bindingQwModal.visible = false;
      this.bindingQwModal.rowData = {};
    },

    async getList() {
      // console.log(this.formData.status);
      const params = {
        current: this.pagination.current,
        size: this.pagination.pageSize,
      };
      const query = {
        columnId: this.active.columnId || '',
        name: this.formData.name || '',
        status: this.formData.status || '',
      };
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.post(
          sysCompanyGroupApi.queryCompanyByPage.url, query,
          {
            params,
          },
        );
        const { code, data, msg } = res;
        if (code === Code.OK.code) {
          this.data = data.records || [];
          this.pagination.total = +data.total || 0;
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },
    /**
     * 批量改变状态
     */
    async changeStatusIds(ids: [], status: string) {
      try {
        const res = await (this as any).$request.post(sysCompanyGroupApi.updateCompanyStatus.url, {
          ids,
          status,
        });
        console.log(this.selectedRowKeys);
        const { code, msg } = res;
        if (code === Code.OK.code) {
          this.$message.success(msg || '状态改变成功');
          return 'success';
        }
        this.$message.error(msg || '状态改变失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    /**
     * 批量改变状态
     */
    async resetRasswordBatch(ids: any) {
      try {
        const res = await (this as any).$request.put('/sys-user-login/reset_password_batch', ids);
        const { code, msg } = res;
        if (code === 1) {
          this.$message.success(msg || '重置成功');
          return 'success';
        }
        this.$message.error(msg || '重置失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    /**
     * 批量改变状态
     */
    async updateUserState(rowData: any) {
      try {
        const res = await (this as any).$request.post(
          sysCompanyGroupApi.deleteCompany.url,
          {id: rowData.id},
        );
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success(msg || '删除成功');
          this.selectedRowKeys = [];
          this.getList();
          this.getCompanyAuth();
          return 'success';
        }
        this.$message.error(msg || '删除失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

.left-operation-container {
  padding: 0 0 10px 0;

  .selected-count {
    display: inline-block;
    color: @text-color-secondary;
  }
}

.search-input {
  width: 360px;
}
.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.more-search-row {
  margin: 10px 0 0 0;
}
.dept-icon {
  .t-icon {
    color: var(--td-brand-color);
  }
}
.tdesign-demo-dropdown__text {
  color: @brand-color;
}
.tree-area {
  overflow: auto;
  height: 700px;
}
</style>
