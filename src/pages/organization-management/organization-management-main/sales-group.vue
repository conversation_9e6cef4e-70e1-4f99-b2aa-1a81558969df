<template>
  <div>
    <t-row :gutter="16">
      <t-col :span="2">
        <t-tree class="tree-area" :data="companyTreeData" :expandLevel="1" activable hover transition @active="companyTreeChange">
          <template #icon="{ node }">
            <caret-right-small-icon v-if="node.getChildren() && !node.expanded" />
            <caret-down-small-icon v-else-if="node.getChildren() && node.expanded" />
            <template v-else>
              <span class="dept-icon" v-if="node.data.deptId" title="销售组"><usergroup-icon></usergroup-icon></span>
            </template>
          </template>
        </t-tree>
      </t-col>
      <t-col :span="10">
        <t-form
          ref="form"
          :data="formData"
          :label-width="80"
          colon
          @reset="onReset"
          @submit="onSubmit"
        >
          <t-row class="search-container">
            <t-col>
              <t-form-item label="销售组名称" name="deptName">
                <t-input
                  v-model="formData.name"
                  class="form-item-content"
                  type="search"
                  placeholder="请输入销售组名称"
                  :style="{ minWidth: '134px' }"
                />
              </t-form-item>
            </t-col>

            <t-col>
              <t-form-item label="销售组状态" name="status">
                <t-select
                  v-model="formData.status"
                  class="form-item-content`"
                  :options="STATUS_OPTIONS"
                  placeholder="请选择销售组状态"
                />
              </t-form-item>
            </t-col>

            <t-col class="operation-container">
              <t-button theme="primary" class="search-button" type="submit" :style="{ marginLeft: '8px' }"> 查询 </t-button>
              <t-button type="reset" class="reset-button" variant="base" theme="default"> 重置 </t-button>
            </t-col>
          </t-row>
          <t-row justify="space-between" class="left-operation-container">
            <div>
              <p v-if="!!selectedRowKeys.length" class="selected-count">已选{{ selectedRowKeys.length }}项</p>
            </div>
            <div>
              <t-dropdown
                trigger="click"
                :options="changeStatusOption"
                @click="clickHandlerChangeStatus"
                :minColumnWidth="88"
              >
                <t-button theme="default" :disabled="selectedRowKeys.length == 0" variant="outline">改变状态</t-button>
              </t-dropdown>
              <t-button @click="handleAdd" v-if="userOperation.hasAdd"> 新增<add-circle-icon slot="icon"/> </t-button>
            </div>
          </t-row>
        </t-form>
        <div class="table-container">
          <t-table
            stripe
            :columns="columns"
            :data="data"
            :rowKey="rowKey"
            :verticalAlign="verticalAlign"
            :hover="true"
            :pagination="pagination"
            :selected-row-keys="selectedRowKeys"
            :loading="dataLoading"
            @page-change="rehandlePageChange"
            @select-change="rehandleSelectChange"
            :headerAffixedTop="true"
          >
            <template #companyName="{ row }">
              <span>{{ getColumnName(row.companyId) }}</span>
            </template>
            <template #status="{ row }">
              <t-tag theme="success" v-if="row.status == 1"> 有效 </t-tag>
              <t-tag v-else-if="row.status == 0" theme="danger"> 禁用 </t-tag>
            </template>
            <template #op="slotProps">
              <a class="t-button-link" @click="handleClickDetail(slotProps)" v-if="userOperation.hasEdit">修改</a>
              <t-divider layout="vertical" style="background: #e4e4e4"/>
              <a class="t-button-link t-button-link--theme-danger" @click="handleClickDelete(slotProps.row)" v-if="userOperation.hasDelete">删除</a>
            </template>
          </t-table>
        </div>
      </t-col>
    </t-row>
    <edit-dept-modal
      :data="editModalVar.data"
      :subData="editModalVar.subData"
      :allSysCompanyInfos="companyTreeData"
      :visible="editModalVar.visible"
      @cancel="handelCloseEditModal"
      @success="handelEditModalSuccess"
    ></edit-dept-modal>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { AddCircleIcon, CaretRightSmallIcon, CaretDownSmallIcon, UsergroupIcon } from 'tdesign-icons-vue';
import { treeNodeConvertService } from '@/service/tree-node-convert.service';
import EditDeptModal from '../components/edit-group-modal.vue';
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import {STATUS_OPTIONS} from '@/constants';
import {Code} from "@/constants/enum/general/code.enum";
import salesGroupApi from "@/constants/api/back/sales-group.api";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";
import {getOperationTypeList} from "@/utils/utils";

export default Vue.extend({
  name: 'TenantManagementMain',
  components: {
    EditDeptModal,
    AddCircleIcon,
    CaretRightSmallIcon,
    CaretDownSmallIcon,
    UsergroupIcon,
  },
  props: {
    childrenOperation: {
      type: Array,
      default: [],
      // required: false,
    },
  },
  data() {
    return {
      STATUS_OPTIONS,
      companyTreeData: [],
      allSysCompanyInfos: [],

      nodeContrast: {
        id: 'id',
        parentId: '',
        children: 'columns',
        cascadeField: 'columns',
        subNodeContrast: {
          id: 'id',
          parentId: '',
          children: 'companies',  // 注意这里必须与实际结构一致
          cascadeField: 'companies',
          subNodeContrast: {
            id: 'id',
            parentId: '',
            children: 'salesGroups', // 最后一层没有 children
            cascadeField: 'salesGroups',
          }
        }
      },
      nzNodeContrast: {
        key: 'id',
        title: 'name',
        label: 'name',
        children: 'columns',
        headquartersId: 'id',
        cascadeField: 'columns',
        subNzNodeContrast: {
          key: 'id',
          title: 'name',
          label: 'name',
          children: 'companies',
          columnId: 'id',
          cascadeField: 'companies',
          subNzNodeContrast: {
            key: 'id',
            label: 'name',
            children: 'salesGroups',
            companyId: 'id',
            cascadeField: 'salesGroups',
          }
        }
      },
      formData: {
        name:'',
        status: '',
      },
      changeStatusOption: [
        {
          content: '有效',
          value: 1,
        },
        {
          content: '禁用',
          value: 0,
        },
      ],

      moreButtons: [
        {
          content: '分配角色',
          value: 1,
        },
        // {
        //   content: '分配岗位',
        //   value: 2,
        // },
        {
          content: '重置密码',
          value: 3,
        },
        {
          content: '删除',
          value: 4,
        },
      ],

      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
        },
        {
          title: '销售组名称',
          align: 'left',
          width: 200,
          colKey: 'salesGroupName',
          ellipsis: true,
        },
        {
          title: '所属公司',
          align: 'left',
          width: 200,
          colKey: 'companyId',
          cell: 'companyName', // 关键点：使用自定义渲染
        },
        {
          title: '销售组负责人',
          align: 'left',
          width: 150,
          colKey: 'relPerson',
          ellipsis: true,
        },
        {
          title: '负责人手机号',
          align: 'left',
          width: 150,
          colKey: 'phone',
          ellipsis: true,
        },

        {
          title: '销售组状态',
          align: 'center',
          width: 150,
          colKey: 'status',
          cell: 'status',
        },
        {
          title: '操作',
          align: 'center',
          width: 220,
          colKey: 'op',
          fixed: 'right',
          cell: 'op',
        },
      ],
      rowKey: 'id',
      active: {
        companyId: '',
      },
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',

      editModalVar: {
        visible: false,
        data: {},
        subData:{}
      },

      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      userOperationTab: [], // 用户可操作按钮
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      }
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
  },
  async mounted() {
    this.getCompanyAuth();
    this.getList();
    // 获取当前页面可操作按钮，过滤具体类型
    // console.log("全部可操作按钮：", this.childrenOperation);
    if(this.childrenOperation && this.childrenOperation.length > 0) {
      this.childrenOperation.map((item: any) => {
        if (item.menuType && item.menuType == SYSTEM_MENU_CONST.O.value && item.menuUrl) {
          // 截取操作按钮，区分类型
          const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
          this.userOperationTab.push(operationType);
        }
      })
    }
    // console.log("获取到可操作按钮：", this.userOperationTab);
    this.userOperation = getOperationTypeList(this.userOperationTab)
    // console.log("获取到可操作按钮类型：", this.userOperation);
  },

  methods: {
    /**
     * 获取组织架构列表
     */
    async getCompanyAuth() {
      try {
        this.dataLoading = true;
        const params = {
          id: 31000, // 总销售组ID
          level: 2, // 销售组层级
        };
        const { code, data } = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });
        // console.error(data);
        if (code === 0 && data) {
          const companyTreeData = [
            {
              id: data.id, // 31000
              name: data.name || '总部',
              columns: data.columns.map((column: any) => ({
                ...column,
              })),
            },
          ];
          this.allSysCompanyInfos = data.columns;
          const treeData = treeNodeConvertService.arrayConvertToNzTreeNode(
            companyTreeData,
            this.nodeContrast,
            this.nzNodeContrast,
          );
          // console.log(treeData);
          this.companyTreeData = treeData;
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },
    rehandlePageChange(curr: any) {
      this.pagination.current = curr.current;
      this.pagination.pageSize = curr.size;
      this.getList();
    },
    /**
     * 设置状态
     */
    clickHandlerChangeStatus(status: any) {
      const confirm = this.$dialog.confirm({
        theme: 'warning',
        header: '温馨提示',
        body: `是否状态变更为：${status.value === 1 ? '有效' : '禁用'}?`,
        onConfirm: async () => {
          (confirm as any).destroy();
          const res = await this.changeStatusIds((this as any).selectedRowKeys, status.value);
          if (res === 'success') {
            this.selectedRowKeys = [];
            this.getList()
          }
        },
      });
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      this.editModalVar.visible = true;
      this.editModalVar.data = {};
    },
    handleClickDetail(slotProps: any) {
      this.editModalVar.data = { ...slotProps.row };
      this.editModalVar.visible = true;
    },    /**
     * 搜索提交
     */
    onSubmit(result: any) {
      this.pagination.current = 1;
      if (result.validateResult === true) {
        // alert(111);
        this.getList();
      }
    },
    onReset() {
      // console.log(222);
      this.pagination.current = 1;
      this.formData = {};
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    handleClickDelete(rowData: any) {
      const confirm = this.$dialog.confirm({
        theme: 'warning',
        header: '温馨提示',
        body: '确认删除该项？',
        onConfirm: async () => {
          (confirm as any).destroy();
          this.updateUserState(rowData);
        },
      });
    },

    companyTreeChange(value: any, context: any) {
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      // console.log(context.node.data);
      const { companyId } = context.node.data;
      this.active.companyId = companyId;
      this.getList(); // 查询拉取列表
    },
    /**
     * 关闭对话框
     */
    handelCloseEditModal() {
      this.editModalVar.visible = false;
      setTimeout(() => {
        this.editModalVar.data = {};
      }, 500);
    },

    handelEditModalSuccess() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
      this.getList();
      this.getCompanyAuth();
    },
    getColumnName(companyId: string) {
      for (const column of this.allSysCompanyInfos) {
        if (Array.isArray(column.companies)) {
          const company = column.companies.find(c => c.id === companyId);
          if (company) return company.name;
        }
      }
      return '未知公司';
    },
    async getList() {
      const params = {
        current: this.pagination.current,
        size: this.pagination.pageSize,
      };
      const query = {
        companyId: this.active.companyId || '',
        name: this.formData.name || '',
        status: this.formData.status || '',
      };

      try {
        this.dataLoading = true;
        const res = await (this as any).$request.post(
          salesGroupApi.querySalesGroupByPage.url, query,{params},
        );
        const { code, data, msg } = res;
        if (code === Code.OK.code) {
          this.data = data.records || [];
          this.pagination.total = +data.total || 0;
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },
    /**
     * 批量改变状态
     */
    async changeStatusIds(ids: [], status: string) {
      const res = await (this as any).$request.post(salesGroupApi.updateSalesGroupStatus.url, {
        ids,
        status
      });
      const { code, msg } = res;
      if (code === Code.OK.code) {
        this.$message.success(msg || '状态改变成功');
        return 'success';
      }
      this.$message.error(msg || '状态改变失败');
    },
    /**
     * 批量改变状态
     */
    async resetRasswordBatch(ids: any) {
      try {
        const res = await (this as any).$request.put('/sys-user-login/reset_password_batch', ids);
        const { code, msg } = res;
        if (code === 1) {
          this.$message.success(msg || '重置成功');
          return 'success';
        }
        this.$message.error(msg || '重置失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    /**
     * 批量删除
     */
    async updateUserState(rowData: any) {
      try {
        const res = await (this as any).$request.post(
          salesGroupApi.deleteSalesGroup.url,
          {id: rowData.id},
        );
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success('删除成功');
          this.selectedRowKeys = [];
          this.getList();
          this.getCompanyAuth();
          return 'success';
        }
        this.$message.error(msg || '删除失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

.left-operation-container {
  padding: 0 0 10px 0;

  .selected-count {
    display: inline-block;
    color: @text-color-secondary;
  }
}

.search-input {
  width: 360px;
}
.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.more-search-row {
  margin: 10px 0 0 0;
}
.dept-icon {
  .t-icon {
    color: var(--td-brand-color);
  }
}
.tdesign-demo-dropdown__text {
  color: @brand-color;
}
.tree-area {
  overflow: auto;
  height: 700px;
}
</style>
