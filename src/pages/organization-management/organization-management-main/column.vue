<template>
  <div>
    <t-row justify="space-between" class="left-operation-container">
      <div style="display: flex;align-items: center">
        <p v-if="!!selectedRowKeys.length" class="selected-count">已选{{ selectedRowKeys.length }}项</p>
      </div>
      <div>
        <t-dropdown
          trigger="click"
          :options="changeStatusOption"
          @click="clickHandlerChangeStatus"
          :minColumnWidth="88"
        >
          <t-button theme="default" :disabled="selectedRowKeys.length == 0" variant="outline">改变状态</t-button>
        </t-dropdown>
        <t-button @click="handleAdd" class="primary-button" v-if="userOperation.hasAdd"> 新增栏目<add-circle-icon slot="icon" /></t-button>
      </div>
    </t-row>

    <t-table
      stripe
      ref="table"
      row-key="id"
      drag-sort="row-handler"
      :columns="columns"
      :data="ColumnInfos"
      :pagination="pagination"
      :tree="{
        childrenKey: 'columns',
        defaultExpandAll: true,
        indent:10
      }"
      :selected-row-keys="selectedRowKeys"
      @select-change="rehandleSelectChange"
      @page-change="rehandlePageChange"
    >
      <template #status="{ row }">
        <t-tag v-if="row.status == 1" theme="success">有效</t-tag>
        <t-tag v-else-if="row.status == 0" theme="danger">禁用</t-tag>
      </template>
      <template #op="{ row }">
        <a class="t-button-link" @click="handleEditRow(row)"  v-if="userOperation.hasEdit">编辑</a>
        <t-divider layout="vertical" style="background: #e4e4e4"/>
        <a class="t-button-link t-button-link--theme-danger" @click="handleClickDelete(row)" v-if="userOperation.hasDelete">删除</a>
      </template>
    </t-table>
    <edit-modal
      :data="editModalVar.data"
      :subData="editModalVar.subData"
      :allColumnInfos="allColumnInfos"
      :visible="editModalVar.visible"
      @cancel="handelCloseEditModal"
      @success="handelEditModalSuccess"
    ></edit-modal>
    <t-dialog
      theme="warning"
      header="确认要删除该项？"
      :visible.sync="confirmVisible"
      @confirm="onConfirmDelete"
      :onCancel="onCancel"
    >
    </t-dialog>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { AddCircleIcon } from 'tdesign-icons-vue';
import EditModal from '../components/edit-column-modal.vue';
import columnApi from "@/constants/api/back/column";
import {Code, CODE} from "@/constants/enum/general/code.enum";
import {SYSTEM_MENU_CONST, SYSTEM_OPERATION_CONST} from "@/constants/enum/system/system-menu.enum";
import {getOperationTypeList} from "@/utils/utils";

export default Vue.extend({
  name: 'TenantManagementMain',
  components: {
    AddCircleIcon,
    EditModal,
  },
  props: {
    childrenOperation: {
      type: Array,
      default: [],
      // required: false,
    },
  },
  data() {
    return {
      tabValue: '1',
      pagination: {
        // defaultPageSize: 10,
        total: 0,
        // defaultCurrent: 1,
        current: 1,
        pageSize: 10,
      },
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
        },
        {
          colKey: 'columnName',
          title: '栏目名称',
          width: 300,
        },
        {
          colKey: 'relPerson',
          width: 200,
          title: '联系人',
        },
        {
          colKey: 'phone',
          width: 200,
          title: '联系电话',
        },
        {
          colKey: 'status',
          align: 'center',
          title: '使用状态',
          width: 200,
          cell: 'status',
        },
        {
          colKey: 'op',
          title: '操作',
          cell: 'op',
          width: 260,
          align: 'center',
          fixed:'right'
        },
      ],
      changeStatusOption: [
        {
          content: '有效',
          value: 1,
        },
        {
          content: '禁用',
          value: 0,
        },
      ],
      editModalVar: {
        visible: false,
        data: {},
        subData: {},
      },
      selectedRowKeys: [],
      allColumnInfos: [],
      ColumnInfos: [],
      deleteIdx: '',
      confirmVisible: false,
      nodeContrast: {
        id: 'id',
        parentId: '',
        children: 'columns',
        cascadeField: 'columns',
      },
      nzNodeContrast: {
        key: 'id',
        title: 'name',
        label: 'name',
        children: 'columns',
        headquartersId: 'id',
        cascadeField: 'columns',
      },
      userOperationTab: [], // 用户可操作按钮
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      }
    };
  },
  computed: {

  },
  async mounted() {
    this.getCompanyAuth();
    // 获取当前页面可操作按钮，过滤具体类型
    // console.log("全部可操作按钮：", this.childrenOperation);
    if(this.childrenOperation && this.childrenOperation.length > 0) {
      this.childrenOperation.map((item: any) => {
        if (item.menuType && item.menuType == SYSTEM_MENU_CONST.O.value && item.menuUrl) {
          // 截取操作按钮，区分类型
          const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
          this.userOperationTab.push(operationType);
        }
      })
    }
    // console.log("获取到可操作按钮：", this.userOperationTab);
    this.userOperation = getOperationTypeList(this.userOperationTab)
    // console.log("获取到可操作按钮类型：", this.userOperation);
  },
  methods: {
    handleAdd() {
      this.editModalVar.visible = true;
      this.editModalVar.data = {};
      this.editModalVar.subData = {addChild:'1'};
    },
    handleAddChild(row: any) {
      this.editModalVar.visible = true;
      this.editModalVar.subData = {...row,sub:this.allColumnInfos.filter((item)=>item.comId===row.supComId)[0]?.comName,addChild:'1'};
    },
    handelCloseEditModal() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
      this.editModalVar.subData = {};
    },
    handelEditModalSuccess() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
      this.editModalVar.subData = {};
      this.getCompanyAuth();
    },
    handleEditRow(row: any) {
      this.editModalVar.visible = true;
      this.editModalVar.data = {...row,sub:this.allColumnInfos.filter((item)=>item.comId===row.supComId)[0]?.comName}
    },
    handleClickDelete(row: any) {
      console.log(row);
      this.deleteIdx = row.id;
      this.confirmVisible = true;
    },
    async onConfirmDelete() {
      const res = await this.changeStatusId(this.deleteIdx);
      if (res === 'success') {
        this.deleteIdx = '';
        this.confirmVisible = false;
        this.selectedRowKeys = [];
      }
    },
    async changeStatusId(deleteIdx: any) {
      try {
        const res = await (this as any).$request.post(
          columnApi.deleteColumn.url,
          {id: deleteIdx},
        );
        const { code, msg } = res;
        if (code === Code.OK.code) {
          this.$message.success(msg || '删除成功');
          return 'success';
        }
        this.$message.error(msg || '删除失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    async changeStatusIds(ids: [], state: string) {
      try {
        const res = await (this as any).$request.post(columnApi.updateColumnStatus.url, {
          ids,
          status: state,
        });
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success(msg || '状态改变成功');
          return 'success';
        }
        this.$message.error(msg || '状态改变失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    onCancel() {
      this.deleteIdx = '';
    },
    rehandlePageChange(curr: any) {
      this.pagination.current = curr.current;
      this.pagination.pageSize = curr.pageSize;
      this.getCompanyAuth();
    },

    async getCompanyAuth() {
      try {
        const params = {
          headquartersId: 31000, // 总公司ID
          current: this.pagination.current,
          pageSize: this.pagination.pageSize,
        };
        const { code, data } = await this.$request.get(columnApi.queryColumnByPage.url, { params });
        // console.error(data);
        if (code === 0 && data) {
          this.ColumnInfos = data.records || [];
          this.pagination.total = +data.total || 0;
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
      }
    },
    /**
     * 设置状态
     */
    clickHandlerChangeStatus(status: any) {
      const confirm = this.$dialog.confirm({
        theme: 'warning',
        header: '温馨提示',
        body: `是否状态变更为：${status.value === 1 ? '有效' : '禁用'}?`,
        onConfirm: async () => {
          console.error(this.selectedRowKeys, status);
          (confirm as any).destroy();
          const res = await this.changeStatusIds((this as any).selectedRowKeys, status.value);
          if (res === 'success') {
            this.selectedRowKeys = [];
            this.getCompanyAuth();
          }
        },
      });
    },

    rehandleSelectChange(value: any) {
      this.selectedRowKeys = value;
      // console.log(value, selectedRowData);
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.left-operation-container {
  padding: 16px 0 10px 0;

  .selected-count {
    display: inline-block;
    color: @text-color-secondary;
  }
}
.t-button--theme-default:not(.t-is-disabled) {
  color: #4094F3;
  border-color: #4094F3;
}
</style>
