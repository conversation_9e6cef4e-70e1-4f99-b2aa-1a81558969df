<template>
  <div>
    <t-tabs class="list-card-container" v-model="tabValue">
      <t-tab-panel :value="1" label="栏目管理" v-if="userTab.includes('1')">
        <column-page :childrenOperation="buttonId[userTab.indexOf('1')]"/>
      </t-tab-panel>
      <t-tab-panel :value="2" label="公司管理" v-if="userTab.includes('2')">
        <company-page :childrenOperation="buttonId[userTab.indexOf('2')]"/>
      </t-tab-panel>
      <t-tab-panel :value="3" label="销售组管理" v-if="userTab.includes('3')">
        <sales-group-page :childrenOperation="buttonId[userTab.indexOf('3')]"/>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import ColumnPage from "./column.vue"
import CompanyPage from "./company.vue"
import SalesGroupPage from "./sales-group.vue"
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";
import breadcrumb from "@/layouts/components/Breadcrumb.vue";
import {getMenuChildrenList} from "@/utils/utils";

export default Vue.extend({
  name: 'TenantManagementMain',
  components: {
    ColumnPage,
    CompanyPage,
    SalesGroupPage
  },
  data() {
    return {
      tabValue: 1,
      childrenButton: [], // 子节点按钮
      userTab: [], // 按钮操作值
      buttonId: [] // 按钮操作id
    }
  },
  computed: {},
  mounted() {
    // 登录后用户具有的菜单已经存入了storage中，从中获取
    this.childrenButton = getMenuChildrenList();
    // console.log("获取到具有按钮菜单：", this.childrenButton);
    if (this.childrenButton && this.childrenButton.length > 0) {
      this.childrenButton.forEach((item: any) => {
        if (item.menuType && item.menuType == SYSTEM_MENU_CONST.F.value) {
          this.userTab.push(item.menuUrl);
          this.buttonId.push(item.children);
        }
      })
    }
    // console.log("获取到具有按钮菜单url：", this.userTab);
    // console.log("获取到具有按钮菜单下子按钮：", this.buttonId);
  },
  methods: {

  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
::v-deep .t-tabs__nav-scroll {
  height: 44px !important;
  background: white;
  border-bottom-color: #e4e4e4;
}
.left-operation-container {
  padding: 0 0 6px 0;
  margin-bottom: 16px;

  .selected-count {
    display: inline-block;
    margin-left: 8px;
    color: @text-color-secondary;
  }
}
</style>
