<template>
  <t-dialog
    :header="data.id ? '编辑' : '新增'"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <t-form :data="form" :rules="rules" :labelWidth="140" :statusIcon="true" ref="formRef" @submit="onSubmit">
        <t-form-item label="栏目名称" name="columnName">
          <t-input v-model="form.columnName" placeholder="请输入"></t-input>
        </t-form-item>
<!--        <t-form-item label="上级组织" name="id">-->
<!--          <t-tree-select-->
<!--            :disabled="!(!data.id && !subData.id)"-->
<!--            :data="ColumnTreeNodeOptions"-->
<!--            v-model="form.id"-->
<!--            filterable-->
<!--            :clearable="true"-->
<!--            placeholder="请选择"-->
<!--          />-->
<!--        </t-form-item>-->
        <t-form-item label="联络人" name="relPerson">
          <t-input v-model="form.relPerson" placeholder="请输入"></t-input>
        </t-form-item>
        <t-form-item label="联系方式" name="phone">
          <t-input v-model="form.phone" placeholder="请输入"></t-input>
        </t-form-item>

        <t-form-item label="使用状态" name="status">
          <t-select v-model="form.status" placeholder="请选择">
            <t-option
              v-for="item in STATUS_OPTIONS"
              :value="item.value"
              :label="item.label"
              :key="item.value"
            ></t-option>
          </t-select>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import { treeNodeConvertService } from '@/service/tree-node-convert.service';
import { STATUS_OPTIONS } from '@/constants';

import { prefix } from '@/config/global';
import columnApi from "@/constants/api/back/column";
import {Code} from "@/constants/enum/general/code.enum";

export default Vue.extend({
  name: 'EditModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    subData: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
  },

  data() {
    return {
      STATUS_OPTIONS,
      prefix,
      loading: false,
      form: {
        columnName: '',
        id: '',
        phone: '',
        relPerson: '',
        status: 1,
      },
      sysPostInfos: [],

      ColumnTreeNodeOptions: [],
      deptTreeNodeOptions: [],
      comNodeContrast: { id: 'id', parentId: 'supid', children: 'subSysColumnInfos' }, // 平级转树形结构映射
      comNzNodeContrast: {
        key: 'id',
        label: 'columnName',
        title: 'columnName',
        children: 'subSysColumnInfos',
        value: 'id',
      }, // 树形结构转插件树
      deptNodeContrast: { id: 'deptId', parentId: 'supDeptId', children: 'subSysDeptInfo' }, // 平级转树形结构映射
      deptNzNodeContrast: {
        key: 'deptId',
        label: 'deptName',
        title: 'deptName',
        children: 'subSysDeptInfo',
        value: 'deptId',
      }, // 树形结构转插件树
    };
  },
  computed: {
    modalVisible: {
      get() {
        return (this as any).visible;
      },
      set() {
        // this.countData = v
      },
    },
    userInfo: {
      get() {
        let user = {};
        try {
          user = JSON.parse(window.localStorage.getItem('core:user') || '{}');
        } catch (e) {
          user = {};
        }
        return user;
      },
    },
    rules: {
      get() {
        return {
          columnName: [{ required: true }, { min: 2 }, { max: 50, type: 'error' },{ pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change' }],
          relPerson: [ { min: 2 }, { max: 20, type: 'error' },{ pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change' }],
          id:
            (this as any).ColumnTreeNodeOptions && (this as any).ColumnTreeNodeOptions.length > 0
              ? [{ required: true, type: 'error' }]
              : undefined,
          phone: [{ pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: '请输入正确格式的手机号码' }],
          status: [{ required: true, type: 'error' }],
        };
      },
    },
  },

  watch: {
    data: {
      handler(newValue) {
        console.log(this.userInfo.headquartersId);
        (this as any).form = {
          columnName: newValue.columnName,
          phone: newValue.phone,
          id: this.userInfo.headquartersId,
          relPerson: newValue.relPerson,
          status: newValue.status || 1,
        };
        (this as any).$refs.formRef.reset();
      },

      deep: true,
    },
    subData: {
      handler(newValue) {
        // console.log(newValue);
        (this as any).form = {
          id: newValue.id,
          status: 1,
        };
        (this as any).$refs.formRef.reset();
      },

      deep: true,
    },
  },

  async mounted() {
  },

  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      // console.log('ok')
      (this.$refs.formRef as any).submit();
    },

    async saveData() {
      (this as any).loading = true;

      let params = {
        headquartersId: this.userInfo.headquartersId,
        columnName: (this as any).form.columnName,
        phone: (this as any).form.phone,
        relPerson: (this as any).form.relPerson || '',
        status: (this as any).form.status || 1,
      };
      let url;
      let method = 'post';
      if ((this as any).data.id) {
        // todo
        url = columnApi.updateColumn.url;
        params = {
          ...params,
          headquartersId: (this as any).data.headquartersId,
          id: (this as any).data.id,
        };
        method = 'put';
      } else {
        url = columnApi.saveColumn.url;
      }
      try {
        const res = await (this as any).$request[method](url, params);
        const { code, msg } = res;
        if (code === Code.OK.code) {
          this.$message.success('保存成功');
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
        (this as any).loading = false;
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
        (this as any).loading = false;
      }
    },
    onSubmit(result: any) {
      if (result.validateResult === true) {
        this.saveData();
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.t-input-number {
  width: 200px;
}
.tree-area {
  overflow: auto;
}
</style>
