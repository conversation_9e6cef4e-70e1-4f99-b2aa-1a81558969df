<template>
  <t-dialog
    header="商户信息"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <t-form :data="form" :rules="rules" :labelWidth="140" :statusIcon="true" ref="formRef" @submit="onSubmit">
        <!-- 新增微信支付相关表单项 -->
        <!--        <t-form-item label="微信转账版本" name="keyVersion">-->
        <!--          <t-radio-group v-model="form.keyVersion">-->
        <!--            <t-radio :value="'new'">新版</t-radio>-->
        <!--            <t-radio :value="'old'">旧版</t-radio>-->
        <!--          </t-radio-group>-->
        <!--        </t-form-item>-->
        <t-form-item label="微信商户号" name="merchantId">
          <t-input v-model="form.merchantId" placeholder="请输入微信商户号"/>
        </t-form-item>
        <t-form-item label="密钥版本" name="keyVersion">
          <t-radio-group v-model="form.keyVersion">
            <t-radio :value="'0'">旧版</t-radio>
            <t-radio :value="'1'">新版</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="商户密钥" name="privateKey">
          <t-input v-model="form.privateKey" placeholder="请输入商户密钥">
            <template #suffixIcon>
              <t-tooltip content="微信商户的APIv3密钥">
                <help-circle-icon/>
              </t-tooltip>
            </template>
          </t-input>
        </t-form-item>
        <t-form-item label="平台证书序列号" name="platformSerialNumber">
          <t-input v-model="form.platformSerialNumber" placeholder="请输入平台证书序列号"/>
        </t-form-item>
        <!--        <t-form-item label="商户支付密钥" name="paySecret">-->
        <!--          <t-input v-model="form.paySecret" placeholder="请输入商户支付密钥" />-->
        <!--        </t-form-item>-->
        <t-form-item label="证书及公钥配置" name="mode">
          <t-radio-group v-model="form.mode">
            <t-radio :value="'0'">证书模式</t-radio>
            <t-radio :value="'1'">公钥模式</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item label="公钥证书" name="publicKeyPath" v-if="form.mode == '1'">
          <t-upload
            v-model="form.publicKeyPath"
            accept=".pem"
            ref="publicKeyUpload"
            :autoUpload="true"
            :showUploadProgress="true"
            :requestMethod="(file) => handleFileUpload(file, { type: 'public' })"
            placeholder="支持上传pem格式"
          />
        </t-form-item>
        <t-form-item label="公钥ID" name="publicKeyId" v-if="form.mode == '1'">
          <t-input v-model="form.publicKeyId" placeholder="请输入商户API v3 公钥"/>
        </t-form-item>
        <t-form-item label="商户API证书" name="privateKeyPath">
          <t-upload
            v-model="form.privateKeyPath"
            accept=".pem"
            ref="privateKeyUpload"
            :autoUpload="true"
            :showUploadProgress="true"
            :requestMethod="(file) => handleFileUpload(file, { type: 'private' })"
            placeholder="支持上传pem格式"
          />
        </t-form-item>
        <!--        <t-form-item label="平台证书序列号" name="merchantSerialNumber">-->
        <!--          <t-input v-model="form.merchantSerialNumber" placeholder="请输入商户证书序列号" />-->
        <!--        </t-form-item>-->
        <t-form-item label="商户API证书序列号" name="merchantSerialNumber">
          <t-input v-model="form.merchantSerialNumber" placeholder="请输入商户API证书序列号"/>
        </t-form-item>
        <t-form-item label="转账测试" name="merchantSerialNumber" v-if="isAdmin">
          <t-button theme="default" @click="submitTest" >点击转账</t-button>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import {STATUS_OPTIONS} from '@/constants';

import {prefix} from '@/config/global';
import sysVideoUploadApi from "@/constants/api/back/sys-video-upload.api";
import {Code} from "@/constants/enum/general/code.enum";
import merchantKeyApi from "@/constants/api/back/merchant-key";
import {HelpCircleIcon} from 'tdesign-icons-vue';
import BeanUtilsService from "@/service/bean-utils.service";
import sysMiniProgramApi from "@/constants/api/back/sys-miniprogram.api";
import {getUserCache} from "@/utils/utils";
import {SYSTEM_ROLE_CONST} from "@/constants/enum/system/system-role.enum";

export default Vue.extend({
  name: 'BindWxPay',
  components: {HelpCircleIcon},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    wxData: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
    allSysCompanyInfos: {
      type: Array,
      default() {
        return [];
      },
      required: false,
    },
  },

  data() {
    return {
      STATUS_OPTIONS,
      prefix,
      loading: false,
      isAdmin: false, // 是否为管理员
      form: {
        id: '',
        keyVersion: "0",
        merchantId: '',
        privateKey: '',
        paySecret: '',
        mode: "0",
        privateKeyPath: [{
          name: '',
          status: 'waiting',
          url: '',
        }],
        merchantSerialNumber: '',
        platformSerialNumber: '',
        publicKeyPath: [{
          name: '',
          status: 'waiting',
          url: '',
        }],
        publicKeyId: '',
      },
      backFormData: {
        id: '',
        keyVersion: "0",
        merchantId: '',
        privateKey: '',
        paySecret: '',
        mode: "0",
        privateKeyPath: [{
          name: '',
          status: 'waiting',
          url: '',
        }],
        merchantSerialNumber: '',
        platformSerialNumber: '',
        publicKeyPath: [{
          name: '',
          status: 'waiting',
          url: '',
        }],
        publicKeyId: '',
      },
      saveOrUpdate: false,
      rules: {
        companyName: [{required: true}, {min: 2}, {max: 20, type: 'error'}, {
          pattern: /^[^\s]*$/,
          message: '禁止输入空格',
          trigger: 'change'
        }],
        columnId: [{required: true, type: 'error'}],
        status: [{required: true, type: 'error'}],
        deptManager: [{min: 2}, {max: 20, type: 'error'}, {
          pattern: /^[^\s]*$/,
          message: '禁止输入空格',
          trigger: 'change'
        }],
        publicKeyPath: [{required: true, message: '请上传公钥证书', trigger: 'blur'}],
        publicKeyId: [{required: true, message: '请输入公钥', trigger: 'blur'}],
      },
      roleId: '',
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
    sysCompanyInfos: {
      get() {
        return this.allSysCompanyInfos.length ? this.allSysCompanyInfos.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        })) : [];
      },
    }
  },
  watch: {
    visible(val) {
      if (val && this.wxData.companyId) {
        this.getMerchantKeyInfo()
      }
    }
  },
  mounted() {
    // 获取当前用户角色
    this.getUserRoleId();
  },
  methods: {
    getUserRoleId() {
      const user = getUserCache();
      this.roleId = user.roleId;
      const roleType = user ? String(user.roleType) : '';
      // 设置是否为超级管理员
      this.isAdmin = roleType === SYSTEM_ROLE_CONST.ADMIN.value ||
        roleType === SYSTEM_ROLE_CONST.COMMON_ADMIN.value;
    },
    async handleFileUpload(file: any, context: { type: string }) {
      console.log(file);
      const formData = new FormData();
      formData.append('file', file.raw);
      formData.append('path', `wxPay/${this.wxData.companyId}/${context.type}`);
      try {
        const res = await this.$request.post(sysVideoUploadApi.commonDocumentUpload.url, formData);
        console.log(res);
        if (res.code === Code.OK.code && res.data) {
          // 从完整URL中提取文件名
          const fileName = res.data.split('/').pop();
          const fileInfo = {
            name: fileName,
            url: res.data,
            status: "success"
          };

          // 根据上传类型更新对应的文件信息
          if (context.type === 'public') {
            this.form.publicKeyPath[0] = fileInfo;
          } else {
            this.form.privateKeyPath[0] = fileInfo;
          }
          this.$message.success('上传成功');
        } else {
          const failInfo = {status: "fail"};
          if (context.type === 'public') {
            this.form.publicKeyPath[0] = failInfo;
          } else {
            this.form.privateKeyPath[0] = failInfo;
          }
          this.$message.error('上传失败');
        }
      } catch (e) {
        this.$message.error('上传失败');
        return '';
      }
    },
    async getMerchantKeyInfo() {
      try {
        const {code, data} = await this.$request.get(
          `${merchantKeyApi.queryMerchantKey.url}/${this.wxData.companyId}`
        );
        if (code === Code.OK.code && data) {
          this.saveOrUpdate = true;
          this.form = {
            id: data.id,
            keyVersion: data.keyVersion,
            merchantId: data.merchantId,
            privateKey: data.privateKey,
            privateKeyPath: [{name: data.privateKeyPath.split('/').pop(), url: data.privateKeyPath}],
            merchantSerialNumber: data.merchantSerialNumber,
            platformSerialNumber: data.platformSerialNumber,
            publicKeyPath: [{name: data.publicKeyPath.split('/').pop(), url: data.publicKeyPath}],
            publicKeyId: data.publicKeyId,
            mode: data.mode || '0',
          };
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
      }
    },
    handleCancel() {
      this.$set(this, 'form', BeanUtilsService.copy(this.backFormData));
      this.$emit('cancel');
    },
    handleOk() {
      (this as any).$refs.formRef.submit();
    },

    async saveData() {
      this.loading = true;
      let params = {
        keyVersion: this.form.keyVersion,
        merchantId: this.form.merchantId,
        privateKey: this.form.privateKey,
        paySecret: this.form.paySecret,
        mode: this.form.mode,
        privateKeyPath: this.form.privateKeyPath[0].url,
        merchantSerialNumber: this.form.merchantSerialNumber,
        platformSerialNumber: this.form.platformSerialNumber,
        publicKeyPath: this.form.publicKeyPath[0].url,
        publicKeyId: this.form.publicKeyId,
        companyId: this.wxData.companyId,
      };
      console.log(params);
      let url;
      let method = 'post';
      if (this.saveOrUpdate) {
        url = merchantKeyApi.updateMerchantKey.url;
        params = {
          ...params,
        };
        method = 'put';
      } else {
        url = merchantKeyApi.saveMerchantKey.url;
      }
      try {
        const res = await this.$request[method](url, params);
        const {code, msg} = res;
        if (code === Code.OK.code) {
          this.$message.success('保存成功');
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.loading = false;
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
        this.loading = false;
      }
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.saveData();
      }
    },
    /**
     * @description: 发起转账测试
     * <AUTHOR>
     * @date 2025/6/7 21:50
     */
    async submitTest() {
      let params = {
        companyId: this.wxData.companyId,
      };
      let url;
      const method = 'post';
      url = sysMiniProgramApi.toPayTest.url;
      params = {
        ...params,
      };
      try {
        const res = await this.$request[method](url, params);
        const {code, msg} = res;
        if (code === Code.OK.code) {
          this.$message.success('转账成功');
        } else {
          this.$message.error(msg || '转账失败');
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.t-input-number {
  width: 200px;
}
</style>
