<template>
  <t-dialog
    header="绑定企微"
    :width="500"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <p class="permission-title">为"{{ bindingQyAccountDialog.companyName }}"设置可绑定的企微</p>
      <t-transfer
        v-model="bindingQyAccountDialog.selectedQyIds"
        :data="bindingQyAccountDialog.qyAccountList"
        :title="['可选企微', '已选企微']"
        :search="true"
        :empty="[ '暂无可选企微', '暂无已选企微' ]"
      ></t-transfer>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';

import { prefix } from '@/config/global';
import {Code} from "@/constants/enum/general/code.enum";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

export default Vue.extend({
  name: 'BindingQyAccountModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
      default: () => ({}),
    }
  },

  data() {
    return {
      url: orderAndUserApi.queryQyList.url,
      prefix,
      loading: false,
      selectedRow: null,
      // 权限设置对话框相关
      bindingQyAccountDialog: {
        companyId: '',
        companyName: '',
        qyAccountList: [],
        selectedQyIds: [],
      }
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
  },
  watch: {
    rowData: {
      handler(v) {
        this.bindingQyAccountDialog.companyId = v.id;
        this.bindingQyAccountDialog.companyName = v.companyName;
        // 获取该公司已关联的企微账号列表
        this.getBindingQyAccountList();
        this.getQyAccountList();
      },
      deep: true,
    },
  },

  methods: {
    handleCancel() {
      this.bindingQyAccountDialog.selectedQyIds = [];
      this.$emit('close');
    },
    async getQyAccountList() {
      const { code, data } = await this.$request.get(orderAndUserApi.queryQyList.url);
      if (code === 0) {
        this.bindingQyAccountDialog.qyAccountList = data.map((item: any) => ({
          label: item.corpName || "",
          value: item.corpId,
        }));
      }
    },
    async getBindingQyAccountList() {
      if (!this.bindingQyAccountDialog.companyId) return;
      const { code, data } = await this.$request.post(
        orderAndUserApi.queryCompanyUnionQy.url,
        {
          queryScene: "0",
          companyId: this.bindingQyAccountDialog.companyId,
        }
      );
      if (code === 0) {
        this.bindingQyAccountDialog.selectedQyIds = data.map((item: any) => item.corpId);
      }
    },
    handleOk() {
      this.saveData();
    },

    async saveData() {
      if (this.bindingQyAccountDialog.selectedQyIds.length === 0) return this.$message.error('请选择绑定的企微');
      this.loading = true;
      const params = {
        bindScene: "0",
        companyId: this.bindingQyAccountDialog.companyId,
        corpIds: this.bindingQyAccountDialog.selectedQyIds,
      };
      const {url} = orderAndUserApi.batchCompanyBindQy;
      try {
        const res = await this.$request.post(url, params);
        const { code, msg } = res;
        if (code === Code.OK.code) {
          this.$message.success('保存成功');
          this.$emit('close');
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.loading = false;
      } catch (e) {
        this.loading = false;
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.t-input-number {
  width: 200px;
}
.permission-title {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
</style>
