<!-- @format -->

<template>
  <t-dialog
    :header="data.id ? '编辑' : '新增'"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <t-form :data="form" :rules="rules" :labelWidth="140" :statusIcon="true" ref="formRef" @submit="onSubmit">
        <t-form-item label="公司名称" name="companyName">
          <t-input v-model="form.companyName" placeholder="请输入公司名称" />
        </t-form-item>
        <t-form-item label="所属栏目" name="columnId">
          <t-tree-select
            :data="sysCompanyInfos"
            v-model="form.columnId"
            filterable
            :clearable="true"
            placeholder="请选择"
          />
        </t-form-item>
        <t-form-item label="公司联系人" name="relPerson">
          <t-input v-model="form.relPerson" placeholder="请输入联系人" />
        </t-form-item>
        <t-form-item label="联系电话" name="phone">
          <t-input v-model="form.phone" placeholder="请输入联系电话" />
        </t-form-item>
        <t-form-item label="公司状态" name="status">
          <t-select v-model="form.status" placeholder="请选择">
            <t-option
              v-for="item in STATUS_OPTIONS"
              :value="item.value"
              :label="item.label"
              :key="item.value"
            ></t-option>
          </t-select>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import {STATUS_OPTIONS} from '@/constants';

import { prefix } from '@/config/global';
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import {Code} from "@/constants/enum/general/code.enum";

export default Vue.extend({
  name: 'EditModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
    allSysCompanyInfos: {
      type: Array,
      default() {
        return [];
      },
      required: false,
    },
  },

  data() {
    return {
      STATUS_OPTIONS,
      prefix,
      loading: false,
      form: {
        id:'',
        columnId: '',
        columnName: '',
        companyName: '',
        relPerson: '',
        phone: '',
        status: 1,
      },
      sysPostInfos: [],

      rules: {
        companyName: [{ required: true }, { min: 2 }, { max: 20, type: 'error' },{ pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change' }],
        columnId: [{ required: true, type: 'error' }],
        status: [{ required: true, type: 'error' }],
        deptManager: [{ min: 2 }, { max: 20, type: 'error' },{ pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change' }],
      },
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
    sysCompanyInfos: {
      get() {
        return this.allSysCompanyInfos.length ? this.allSysCompanyInfos.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        })) : [];
      },
    }
  },

  watch: {
    data: {
      handler(newValue) {
        this.form = {
          id: newValue.id,
          companyName: newValue.companyName,
          relPerson: newValue.relPerson,
          phone: newValue.phone,
          columnId: newValue.sub ? newValue.sub : newValue.columnId,
          status: newValue.status || 1,
        };
      },
      deep: true,
    },
    visible(val) {
      if (val && this.data) {
        this.form = {
          id: this.data.id,
          companyName: this.data.companyName,
          relPerson: this.data.relPerson,
          phone: this.data.phone,
          columnId: this.data.sub ?? this.data.columnId,
          status: this.data.status || 1,
        };

        const selectedColumn = this.allSysCompanyInfos.find(
          (item) => item.id === this.form.columnId
        );
        if (selectedColumn) {
          this.form.columnName = selectedColumn.name; // 显示栏目名称
        }
      }
    }
  },

  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      (this as any).$refs.formRef.submit();
    },

    async saveData() {
      console.log(this.form);
      this.loading = true;
      let params = {
        id: this.form.id,
        columnId: this.form.columnId,
        companyName: this.form.companyName,
        relPerson: this.form.relPerson,
        phone: this.form.phone,
        status: this.form.status,
      };
      let url;
      let method = 'post';
      if (this.data.id) {
        url = sysCompanyGroupApi.updateCompany.url;
        params = {
          ...params,
        };
        method = 'put';
      } else {
        url = sysCompanyGroupApi.saveCompany.url;
      }
      try {
        const res = await this.$request[method](url, params);
        const { code, msg } = res;
        if (code === Code.OK.code) {
          this.$message.success('保存成功');
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.loading = false;
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
        this.loading = false;
      }
    },
    onSubmit({ validateResult }) {
      if (validateResult === true) {
        this.saveData();
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.t-input-number {
  width: 200px;
}
</style>
