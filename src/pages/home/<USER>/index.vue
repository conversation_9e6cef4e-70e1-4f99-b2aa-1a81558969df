<template>
  <div class="card-wrapper">
    <t-card v-if="userInfo.roleId != 1">
      <div class="result-success">
        <img src="@/assets/png/customer-service-2-fill.png" class="result-success-icon" />
        <div class="result-success-title">欢迎您{{userInfo.username?`-${userInfo.username}`:''}}</div>
      </div>
    </t-card>
    <t-card v-else class="card-Style" :title="title" hover-shadow :style="{ width: '100%' }">
      <template #actions>
        <span>最后更新时间：{{dashboardStats.refreshDate}}</span>
      </template>
      <div class="statistic-container">
        <div style="position: absolute;right: 60px;top: 50px">
          <t-form layout="inline">
            <t-form-item label="栏目" name="campPeriodId">
              <t-select
                v-model="columnId"
                placeholder="请选择栏目"
                :options="columnOptions"
                clearable
                :filter="filterColumn"
                @change="handleColumnSelect"
              />
            </t-form-item>
            <t-form-item label="公司" name="companyId">
              <t-select
                :disabled="!columnId ||columnId === ''"
                :options="companyData"
                v-model="companyId"
                clearable
                placeholder="请选择公司"
                :filter="filterCompany"
                @change="handleCompanySelect"
              />
            </t-form-item>
          </t-form>
        </div>
        <t-card class="statistic-Style" :bordered="false">
          <t-statistic
            class="statistic-Items"
            title="会员数"
            suffix=""
            :value="dashboardStats.totalMembers"
            :decimal-places="0"
          />
          <t-statistic
            class="statistic-Items"
            title="今日新增会员"
            suffix=""
            :value="dashboardStats.todayNewMembers"
            :decimal-places="0"
          />
          <t-statistic
            class="statistic-Items"
            title="今日观看（次）"
            suffix=""
            :value="dashboardStats.todayViews"
            :decimal-places="0"
          />
          <t-statistic
            class="statistic-Items"
            title="今日发放红包（个）"
            suffix=""
            :value="dashboardStats.todayRedPackets"
            :decimal-places="0"
          />
        </t-card>
        <t-card class="statistic-Style" :bordered="false">
          <t-statistic
            class="statistic-Items"
            title="总公司数"
            suffix=""
            :value="dashboardStats.totalCompanies"
            :decimal-places="0"
          />
          <t-statistic
            class="statistic-Items"
            title="本月新增公司数"
            suffix=""
            :value="dashboardStats.monthNewCompanies"
            :decimal-places="0"
          />
          <t-statistic
            class="statistic-Items"
            title="总销售账号"
            suffix=""
            :value="dashboardStats.totalSalesAccounts"
            :decimal-places="0"
          />
          <t-statistic
            class="statistic-Items"
            title="本月新增销售账号"
            suffix=""
            :value="dashboardStats.monthNewSalesAccounts"
            :decimal-places="0"
          />
        </t-card>
      </div>
    </t-card>

    <t-card class="charts-Card">
      <div class="chart-header">
        <t-space>
          <t-date-range-picker
            v-model="dateRange"
            allow-input
            :presets="presets"
            clearable
            :on-change="handleChange"
          />
          <t-radio-group v-model="chartTheme" variant="default-filled" size="small">
            <t-radio-button value="light">
              <t-icon name="browse" />
              明亮主题
            </t-radio-button>
            <t-radio-button value="dark">
              <t-icon name="browse-off" />
              暗黑主题
            </t-radio-button>
          </t-radio-group>
        </t-space>
      </div>
      <t-tabs class="charts-Tab" :value="tabsValue" @change="tabsChange">
        <t-tab-panel class="charts-TabPanel" value="first" label="观看数据">
          <div class="chart-Container">
            <div ref="viewChart" style="height: 380px;width: 100%;"></div>
          </div>
        </t-tab-panel>
        <t-tab-panel class="charts-TabPanel" value="second" label="课题数据">
          <div class="chart-Container">
            <div ref="subjectChart" style="height: 380px;width: 100%;"></div>
          </div>
        </t-tab-panel>
      </t-tabs>
    </t-card>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import dayjs from "dayjs";
import Dashboard from "../../../constants/api/back/sys-dashboard.api";
import sysCompanyGroupApi from "../../../constants/api/back/sys-company.api";

export default {
  name: 'DashboardCharts',
  data() {
    return {
      title: '数据概览',
      tabsValue: 'first',
      chartTheme: 'light', // 默认使用明亮主题
      dateRange: [
        dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      presets: {
        '最近7天': [
          dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ],
        '最近3天': [
          dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ],
        '今天': [
          dayjs().format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ],
        '本月': [
          dayjs().startOf('month').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ],
        '上月': [
          dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')
        ]
      },
      viewChart: null,
      subjectChart: null,
      resizeTimer: null,
      dashboardStats:{
        totalMembers: 0,
        todayNewMembers: 0,
        todayViews: 0,
        todayRedPackets: 0,
        totalCompanies: 0,
        monthNewCompanies: 0,
        totalSalesAccounts: 0,
        monthNewSalesAccounts: 0,
        refreshDate:0
      },
      queryData:{
        startDate: 0,
        endDate: 0
      },
      chartOption: {
        xAxis: {
          data: [] // 初始化为空数组
        },
        series: [
          { data: [] }, // 观看数据系列
          { data: [] }, // 发红包数据系列
          { data: [] },  // 新增会员数据系列
          { data: [] }  // 完播数据系列
        ]
      },
      columnOptions:[],
      companyData:[],
      campPeriodId:'',
      columnId:'',
      companyId:'',
      // 主题配置
      themes: {
        light: {
          backgroundColor: '#ffffff',
          textColor: '#333333',
          axisLineColor: '#cccccc',
          splitLineColor: '#eeeeee',
          colors: ['#6ED190', '#36A76D', '#A18FFF', '#FFB980']
        },
        dark: {
          backgroundColor: '#1e1e1e',
          textColor: '#ffffff',
          axisLineColor: '#555555',
          splitLineColor: '#333333',
          colors: ['#5DB270', '#2D8A59', '#9370DB', '#E8A765']
        }
      }
    };
  },
  computed: {
    userInfo() {
      let user = {};
      try {
        user = JSON.parse(window.localStorage.getItem('core:user')||'{}');

        console.log("user:::",user);
      } catch (e) {
        user = {};
      }
      return user;
    },
    // 获取当前主题的配置
    currentTheme() {
      return this.themes[this.chartTheme];
    }
  },
  watch: {
    chartTheme: {
      handler(newTheme) {
        this.$nextTick(() => {
          // 当主题变化时，重新初始化图表
          if (this.tabsValue === 'first') {
            this.initViewChart();
          } else {
            this.initSubjectChart();
          }
        });
      }
    }
  },
  created() {
    this.getDashboardStats();
    this.getChartsData();
    this.getColumnList();
  },
  mounted() {
    // 初始化背景
    const target = document.querySelector('.tdesign-starter-content-layout');
    if (target) {
      target.style.background = 'transparent';
    }

    // 初始化图表
    this.$nextTick(() => {
      this.initViewChart();
    });

    // 添加窗口缩放监听器
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 恢复背景
    const target = document.querySelector('.tdesign-starter-content-layout');
    if (target) target.style.background = '';

    // 销毁图表实例
    if (this.viewChart) {
      this.viewChart.dispose();
      this.viewChart = null;
    }

    if (this.subjectChart) {
      this.subjectChart.dispose();
      this.subjectChart = null;
    }

    // 移除窗口缩放监听器
    window.removeEventListener('resize', this.handleResize);
    // 清除防抖计时器
    if (this.resizeTimer) {
      cancelAnimationFrame(this.resizeTimer);
    }
  },
  methods: {
    filterCompany(search, option) {
      return option.label.indexOf(search) !== -1;
    },
    filterColumn(search, option) {
      return option.label.indexOf(search) !== -1;
    },
    async handleColumnSelect(value, context) {
      console.log('handleColumnSelect:', value, context);
      this.companyData = [];
      this.campPeriodOptions = [];
      this.companyId = '';
      this.campPeriodId = '';
      this.companyData = context.selectedOptions[0].child.map((item) => ({label: item.name, value: item.id}));
    },
    async handleCompanySelect(){
      const {
        code,
        data
      } = await this.$request.post(Dashboard.queryCompanySalesData.url, {id: this.companyId});
      if (code === 0 && data) {
        console.log("data:::",data);
        this.dashboardStats = {
          totalSalesAccounts: Number(data.totalSalesAccounts),
          monthNewSalesAccounts: Number(data.monthNewSalesAccounts),
        };
      }
    },
    async getColumnList() {
      try {
        const params = {
          id: 31000, // 总公司ID
          level: 2, // 公司层级
        };
        const {
          code,
          data
        } = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, {params});
        if (code === 0 && data) {
          console.log("栏目数据:", data);
          if (data.columns && Array.isArray(data.columns)) {
            this.columnOptions = data.columns.map((item) => ({
              label: item.name,
              value: item.id,
              child: item.companies || []
            }));
          } else {
            console.error("栏目数据格式不正确:", data);
            this.columnOptions = [];
          }
        } else {
          console.error("获取栏目数据失败:", code);
          this.columnOptions = [];
        }
      } catch (error) {
        console.error("获取栏目数据出错:", error);
        this.columnOptions = [];
      }
    },
    handleChange(value){
      console.log('onChange value', value);
      this.getChartsData();
    },
    async getDashboardStats() {
      const params = {};
      const {
        code,
        data
      } = await this.$request.post(Dashboard.queryDashboardData.url, {params});
      if (code === 0 && data) {
        console.log("data",data);
        this.dashboardStats = {
          totalMembers: Number(data.totalMembers),
          todayNewMembers: Number(data.todayNewMembers),
          todayViews: Number(data.todayViews),
          todayRedPackets: Number(data.todayRedPackets),
          totalCompanies: Number(data.totalCompanies),
          monthNewCompanies: Number(data.monthNewCompanies),
          totalSalesAccounts: Number(data.totalSalesAccounts),
          monthNewSalesAccounts: Number(data.monthNewSalesAccounts),
          refreshDate:data.refreshDate
        };
      }
    },
    async getChartsData() {
      const params = {

      };
      const {code, data} = await this.$request.post(Dashboard.queryChartsData.url, {
        startDate: this.dateRange[0],
        endDate: this.dateRange[1]
      });
      if (code === 0 && data) {
        console.log("data",data);
        this.chartOption.series[0].data = data.map(d => d.todayViews);
        this.chartOption.series[1].data = data.map(d => d.todayRedPackets);
        this.chartOption.series[2].data = data.map(d => d.todayNewMembers);
        this.chartOption.series[3].data = data.map(d => d.todayComplete);
        this.chartOption.xAxis.data = data.map(d => d.recordDate);
      }

      this.$nextTick(() => {
        this.initViewChart();
      });

    },
    tabsChange(value) {
      this.tabsValue = value;
      this.$nextTick(() => {
        // 初始化图表后主动触发resize
        if (value === 'first') {
          this.initViewChart();
        } else {
          this.initSubjectChart();
        }
        this.handleResize(); // 主动触发
      });
      // if (value === 'first') {
      //   this.$nextTick(() => {
      //     this.initViewChart();
      //   });
      // }else {
      //   this.$nextTick(() => {
      //     this.initSubjectChart();
      //   });
      // }
    },
    handleResize() {
      console.log("handleResize:::::::::::")
      // 使用防抖+requestAnimationFrame确保执行时机
      if (this.resizeTimer) {
        cancelAnimationFrame(this.resizeTimer);
      }
      this.resizeTimer = requestAnimationFrame(() => {
        // 确保DOM更新完成
        this.$nextTick(() => {
          if (this.viewChart) {
            this.viewChart.resize();
          }
          if (this.subjectChart) {
            this.subjectChart.resize();
          }
        });
        this.resizeTimer = null;
      });
    },
    // handleResize() {
    //   console.log("handleResize:::::::::::")
    //   if (this.viewChart) {
    //     this.viewChart.resize();
    //   }
    //   if (this.subjectChart) {
    //     this.subjectChart.resize();
    //   }
    // },
    initViewChart() {
      if (this.viewChart) {
        this.viewChart.dispose();
      }

      const chartDom = this.$refs.viewChart;
      // 使用当前主题初始化图表
      this.viewChart = echarts.init(chartDom, null, {
        renderer: 'canvas',
        backgroundColor: this.currentTheme.backgroundColor
      });

      // 确保图表容器有足够的高度
      if (chartDom) {
        chartDom.style.minHeight = '400px'; // 增加图表容器的最小高度
      }

      // 计算最大值，用于设置图表刻度和显示效果
      const maxViewValue = Math.max(...this.chartOption.series[0].data, 1);
      const maxRedPacketValue = Math.max(...this.chartOption.series[1].data, 1);
      const maxMemberValue = Math.max(...this.chartOption.series[2].data, 1);

      // 计算合适的Y轴间隔
      const leftInterval = Math.ceil(Math.max(maxViewValue, maxRedPacketValue) / 5 / 1000) * 1000;
      const rightInterval = Math.ceil(maxMemberValue / 5 / 100) * 100;

      // 获取主题颜色
      const {colors} = this.currentTheme;

      // 图表配置
      const option = {
        backgroundColor: this.currentTheme.backgroundColor,
        title: {
          text: '观看数据趋势分析',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: this.currentTheme.textColor
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter(params) {
            let result = `${params[0].axisValueLabel  }<br/>`;
            params.forEach(item => {
              // 为不同类型的数据添加不同的单位
              const unit = item.seriesName === '新增会员' ? '人' :
                (item.seriesName === '观看' ? '次' : '个');
              result +=
                `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${item.color};"></span>` +
                `${item.seriesName}: ${item.value} ${unit}<br/>`;
            });
            return result;
          }
        },
        legend: {
          data: ['观看', '发红包', '新增会员'],
          top: 25,
          selected: {
            '观看': true,
            '发红包': true,
            '新增会员': true
          },
          textStyle: {
            fontSize: 12,
            color: this.currentTheme.textColor
          }
        },
        toolbox: {
          show: true,
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            dataView: { readOnly: false },
            magicType: { type: ['line', 'bar'] },
            restore: {},
            saveAsImage: {}
          },
          right: 20,
          top: 10,
          iconStyle: {
            borderColor: this.currentTheme.textColor
          },
          emphasis: {
            iconStyle: {
              borderColor: colors[0]
            }
          }
        },
        grid: {
          top: 90,
          right: 30,
          left: 30,
          bottom: 40, // 增加底部边距，为日期标签提供更多空间
          containLabel: true
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            show: true,
            type: 'slider',
            bottom: 0,
            start: 0,
            end: 100
          }
        ],
        xAxis: {
          type: 'category',
          data: this.chartOption.xAxis.data,
          axisLabel: {
            formatter: '{value}',
            rotate: this.chartOption.xAxis.data.length > 7 ? 45 : 0,
            interval: 0, // 强制显示所有标签
            margin: 15, // 增加标签与轴线的距离
            color: this.currentTheme.textColor
          },
          axisTick: {
            alignWithLabel: true,
            lineStyle: {
              color: this.currentTheme.axisLineColor
            }
          },
          axisLine: {
            lineStyle: {
              color: this.currentTheme.axisLineColor
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '观看/发红包',
            min: 0,
            interval: leftInterval,
            axisLabel: {
              formatter: '{value}',
              color: this.currentTheme.textColor
            },
            nameTextStyle: {
              color: this.currentTheme.textColor
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: this.currentTheme.splitLineColor
              }
            },
            axisLine: {
              lineStyle: {
                color: this.currentTheme.axisLineColor
              }
            }
          },
          {
            type: 'value',
            name: '新增会员',
            min: 0,
            interval: rightInterval,
            axisLabel: {
              formatter: '{value}',
              color: this.currentTheme.textColor
            },
            nameTextStyle: {
              color: this.currentTheme.textColor
            },
            splitLine: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: this.currentTheme.axisLineColor
              }
            }
          }
        ],
        series: [
          {
            name: '观看',
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: this.chartTheme === 'light' ? '#83bff6' : '#5a8bbd' },
                { offset: 0.5, color: colors[0] },
                { offset: 1, color: this.chartTheme === 'light' ? '#188df0' : '#0e5da2' }
              ]),
              borderRadius: [5, 5, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: this.chartTheme === 'light' ? '#2378f7' : '#1a5ab8' },
                  { offset: 0.7, color: this.chartTheme === 'light' ? '#83bff6' : '#5a8bbd' },
                  { offset: 1, color: this.chartTheme === 'light' ? '#188df0' : '#0e5da2' }
                ])
              }
            },
            data: this.chartOption.series[0].data,
            markPoint: {
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' }
              ],
              label: {
                color: this.currentTheme.textColor
              }
            }
          },
          {
            name: '发红包',
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00d386' },
                { offset: 0.5, color: '#36A76D' },
                { offset: 1, color: '#008d59' }
              ]),
              borderRadius: [5, 5, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#00b372' },
                  { offset: 0.7, color: '#36A76D' },
                  { offset: 1, color: '#008d59' }
                ])
              }
            },
            data: this.chartOption.series[1].data
          },
          {
            name: '新增会员',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              shadowColor: 'rgba(0,0,0,0.3)',
              shadowBlur: 10,
              shadowOffsetY: 8
            },
            itemStyle: {
              color: '#A18FFF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(161, 143, 255, 0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(161, 143, 255, 0.1)'
                }
              ])
            },
            data: this.chartOption.series[2].data,
            markLine: {
              data: [
                { type: 'average', name: '平均值' }
              ]
            }
          }
        ]
      };

      this.viewChart.setOption(option);

      // 添加点击事件
      this.viewChart.on('click', (params) => {
        console.log('图表点击事件:', params);
        // 这里可以添加点击后的交互逻辑，如下钻分析
      });
    },
    initSubjectChart() {
      if (this.subjectChart) {
        this.subjectChart.dispose();
      }

      const chartDom = this.$refs.subjectChart;
      // 使用当前主题初始化图表
      this.subjectChart = echarts.init(chartDom, null, {
        renderer: 'canvas',
        backgroundColor: this.currentTheme.backgroundColor
      });

      // 计算最大值，用于设置图表刻度和显示效果
      const maxViewValue = Math.max(...this.chartOption.series[0].data, 1);
      const maxCompleteValue = Math.max(...this.chartOption.series[3].data, 1);
      const maxRedPacketValue = Math.max(...this.chartOption.series[1].data, 1);

      // 计算合适的Y轴间隔
      const leftInterval = Math.ceil(Math.max(maxViewValue, maxCompleteValue) / 5 / 1000) * 1000;
      const rightInterval = Math.ceil(maxRedPacketValue / 5);

      // 获取主题颜色
      const {colors} = this.currentTheme;

      // 计算完播率数据
      const completeRateData = this.chartOption.series[0].data.map((viewCount, index) => {
        const completeCount = this.chartOption.series[3].data[index];
        return viewCount > 0 ? Math.round((completeCount / viewCount) * 100) : 0;
      });

      // 图表配置
      const option = {
        backgroundColor: this.currentTheme.backgroundColor,
        title: {
          text: '课题数据分析',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: this.currentTheme.textColor
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter(params) {
            let result = `${params[0].axisValueLabel  }<br/>`;

            // 找到观看和完播的数据，计算完播率
            const viewData = params.find(item => item.seriesName === '观看');
            const completeData = params.find(item => item.seriesName === '完播');
            let completeRate = 0;

            if (viewData && completeData && viewData.value > 0) {
              completeRate = Math.round((completeData.value / viewData.value) * 100);
            }

            params.forEach(item => {
              // 为不同类型的数据添加不同的单位
              const unit = item.seriesName === '发红包' ? '个' :
                (item.seriesName === '完播率' ? '%' : '次');
              result +=
                `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${item.color};"></span>` +
                `${item.seriesName}: ${item.value} ${unit}<br/>`;
            });

            return result;
          }
        },
        legend: {
          data: ['观看', '完播', '发红包', '完播率'],
          top: 25,
          selected: {
            '观看': true,
            '完播': true,
            '发红包': true,
            '完播率': true
          },
          textStyle: {
            fontSize: 12,
            color: this.currentTheme.textColor
          }
        },
        toolbox: {
          show: true,
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            dataView: { readOnly: false },
            magicType: { type: ['line', 'bar'] },
            restore: {},
            saveAsImage: {}
          },
          right: 20,
          top: 10,  // 从25调整为10，避免与Y轴标题重叠
          iconStyle: {
            borderColor: this.currentTheme.textColor
          },
          emphasis: {
            iconStyle: {
              borderColor: colors[0]
            }
          }
        },
        grid: {
          top: 90,  // 从70增加到90，为工具栏和Y轴标题提供更多空间
          right: 30,
          left: 30,
          bottom: 70, // 增加底部边距，为日期标签提供更多空间
          containLabel: true
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            show: true,
            type: 'slider',
            bottom: 10,
            start: 0,
            end: 100,
            textStyle: {
              color: this.currentTheme.textColor
            },
            borderColor: this.currentTheme.axisLineColor,
            handleStyle: {
              color: colors[0],
              borderColor: colors[0]
            },
            moveHandleStyle: {
              color: colors[0]
            },
            fillerColor: this.chartTheme === 'light' ? 'rgba(110, 209, 144, 0.2)' : 'rgba(93, 178, 112, 0.2)'
          }
        ],
        xAxis: {
          type: 'category',
          data: this.chartOption.xAxis.data,
          axisLabel: {
            formatter: '{value}',
            rotate: this.chartOption.xAxis.data.length > 7 ? 45 : 0,
            interval: 0, // 强制显示所有标签
            margin: 15, // 增加标签与轴线的距离
            color: this.currentTheme.textColor
          },
          axisTick: {
            alignWithLabel: true,
            lineStyle: {
              color: this.currentTheme.axisLineColor
            }
          },
          axisLine: {
            lineStyle: {
              color: this.currentTheme.axisLineColor
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '观看/完播',
            min: 0,
            interval: leftInterval,
            axisLabel: {
              formatter: '{value}',
              color: this.currentTheme.textColor
            },
            nameTextStyle: {
              color: this.currentTheme.textColor
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: this.currentTheme.splitLineColor
              }
            },
            axisLine: {
              lineStyle: {
                color: this.currentTheme.axisLineColor
              }
            }
          },
          {
            type: 'value',
            name: '发红包',
            min: 0,
            interval: rightInterval,
            position: 'right',
            offset: 0,
            axisLabel: {
              formatter: '{value}',
              color: this.currentTheme.textColor
            },
            nameTextStyle: {
              color: this.currentTheme.textColor
            },
            splitLine: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: this.currentTheme.axisLineColor
              }
            }
          },
          {
            type: 'value',
            name: '完播率',
            min: 0,
            max: 100,
            interval: 20,
            position: 'right',
            offset: 80, // 与第二个Y轴保持一定距离
            axisLabel: {
              formatter: '{value}%',
              color: this.currentTheme.textColor
            },
            nameTextStyle: {
              color: this.currentTheme.textColor
            },
            splitLine: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: this.currentTheme.axisLineColor
              }
            }
          }
        ],
        series: [
          {
            name: '观看',
            type: 'bar',
            barWidth: '20%',
            stack: 'total',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: this.chartTheme === 'light' ? '#83bff6' : '#5a8bbd' },
                { offset: 0.5, color: colors[0] },
                { offset: 1, color: this.chartTheme === 'light' ? '#188df0' : '#0e5da2' }
              ]),
              borderRadius: [5, 5, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: this.chartTheme === 'light' ? '#2378f7' : '#1a5ab8' },
                  { offset: 0.7, color: this.chartTheme === 'light' ? '#83bff6' : '#5a8bbd' },
                  { offset: 1, color: this.chartTheme === 'light' ? '#188df0' : '#0e5da2' }
                ])
              }
            },
            data: this.chartOption.series[0].data,
            markPoint: {
              label: {
                color: this.currentTheme.textColor
              }
            }
          },
          {
            name: '完播',
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: this.chartTheme === 'light' ? '#00d386' : '#00a86b' },
                { offset: 0.5, color: colors[1] },
                { offset: 1, color: this.chartTheme === 'light' ? '#008d59' : '#006d45' }
              ]),
              borderRadius: [5, 5, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: this.chartTheme === 'light' ? '#00b372' : '#00915c' },
                  { offset: 0.7, color: colors[1] },
                  { offset: 1, color: this.chartTheme === 'light' ? '#008d59' : '#006d45' }
                ])
              }
            },
            data: this.chartOption.series[3].data,
            markPoint: {
              label: {
                color: this.currentTheme.textColor
              }
            }
          },
          {
            name: '发红包',
            type: 'line',
            yAxisIndex: 1, // 指向第二个Y轴（发红包）
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              shadowColor: this.chartTheme === 'light' ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.5)',
              shadowBlur: 10,
              shadowOffsetY: 8
            },
            itemStyle: {
              color: colors[2]
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: this.chartTheme === 'light' ? 'rgba(161, 143, 255, 0.8)' : 'rgba(147, 112, 219, 0.8)'
                },
                {
                  offset: 1,
                  color: this.chartTheme === 'light' ? 'rgba(161, 143, 255, 0.1)' : 'rgba(147, 112, 219, 0.1)'
                }
              ])
            },
            data: this.chartOption.series[1].data,
            markLine: {
              label: {
                color: this.currentTheme.textColor
              },
              lineStyle: {
                color: colors[2]
              }
            }
          },
          {
            name: '完播率',
            type: 'line',
            yAxisIndex: 2, // 指向第三个Y轴（完播率）
            smooth: true,
            symbol: 'diamond',
            symbolSize: 8,
            lineStyle: {
              width: 3,
              type: 'dashed',
              color: colors[3]
            },
            itemStyle: {
              color: colors[3]
            },
            data: completeRateData
          }
        ]
      };

      this.subjectChart.setOption(option);

      // 添加点击事件
      this.subjectChart.on('click', (params) => {
        console.log('图表点击事件:', params);
        // 这里可以添加点击后的交互逻辑，如下钻分析
      });
    },
  }
};
</script>

<style lang="less" scoped>
.card-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.statistic-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}

.statistic-Style {
  margin-top: 20px;
  flex: 1;
  min-width: 300px;
}

::v-deep .t-card__body {
  margin-top: -5px;
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--td-comp-paddingTB-l) var(--td-comp-paddingLR-xl);
}

::v-deep .t-statistic-title {
  font-size: 14px;
  color: #666;
  text-align: left;
  margin-bottom: 8px;
}

::v-deep .t-statistic-content {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2EAD7F;
  text-align: left;
  white-space: nowrap;
}

//::v-deep .t-tabs__content {
//  //overflow: hidden;
//  position: relative;
//  width: 1200px;
//}
//
//.chart-Container {
//  position: relative;
//  width: 100%;
//  height: 100%;
//  min-height: 380px; /* 防止高度塌陷 */
//}
::v-deep .t-tabs__content {
  overflow: hidden;
  position: relative;
  width: 100%; /* 改为自适应宽度 */
}

.chart-Container {
  position: relative;
  height: 380px;
  width: 100%;
  min-width: 0; /* 修复 flex 收缩问题 */
}

.charts-Card {
  width: 100%; /* 确保卡片宽度自适应 */
  overflow: hidden; /* 防止内容溢出 */
}

.charts-Tab {
  position: relative;
  width: 100%; /* 确保标签页宽度填满 */
}

.charts-TabPanel {
  width: 100%; /* 确保面板宽度填满 */
}

</style>
