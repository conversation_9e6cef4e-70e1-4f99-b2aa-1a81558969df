<template>
  <div class="user-center-page">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="user-avatar">
          <div class="avatar-circle">
            <t-icon name="user" size="32px" />
          </div>
        </div>
        <div class="welcome-text">
          <h1 class="welcome-title">Welcome, {{ userInfo.username }}</h1>
          <p class="welcome-subtitle">欢迎您的到来，祝您工作愉快！</p>
        </div>
      </div>
    </div>

    <!-- 个人信息卡片 -->
    <div class="info-section">
      <div class="section-header">
        <h2 class="section-title">个人信息</h2>
        <div class="section-divider"></div>
      </div>

      <div class="info-grid">
        <!-- 基本信息 -->
        <div class="info-group">
          <h3 class="group-title">基本信息</h3>
          <div class="info-items">
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="layers" />
              </div>
              <div class="item-content">
                <div class="item-label">栏目</div>
                <div class="item-value">{{ userInfo.columnName || '暂无' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="business" />
              </div>
              <div class="item-content">
                <div class="item-label">公司</div>
                <div class="item-value">{{ userInfo.companyName || '暂无' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="usergroup" />
              </div>
              <div class="item-content">
                <div class="item-label">销售组</div>
                <div class="item-value">{{ userInfo.saleGroupName || '暂无' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="user-circle" />
              </div>
              <div class="item-content">
                <div class="item-label">角色</div>
                <div class="item-value">{{ userInfo.roleName || '暂无' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 联系信息 -->
        <div class="info-group">
          <h3 class="group-title">联系信息</h3>
          <div class="info-items">
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="mobile" />
              </div>
              <div class="item-content">
                <div class="item-label">手机号码</div>
                <div class="item-value">{{ userInfo.phone || '暂无' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="calendar" />
              </div>
              <div class="item-content">
                <div class="item-label">出生日期</div>
                <div class="item-value">{{ (userInfo.birthDate || '').substr(0, 10) || '暂无' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="check-circle" />
              </div>
              <div class="item-content">
                <div class="item-label">状态</div>
                <div class="item-value">
                  <span :class="['status-badge', userInfo.status === 1 ? 'active' : 'inactive']">
                    {{ userInfo.status === 1 ? '有效' : '无效' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="info-group full-width">
          <h3 class="group-title">系统信息</h3>
          <div class="info-items">
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="time" />
              </div>
              <div class="item-content">
                <div class="item-label">创建时间</div>
                <div class="item-value">{{ userInfo.createdAt || '暂无' }}</div>
              </div>
            </div>
            <div class="info-item">
              <div class="item-icon">
                <t-icon name="refresh" />
              </div>
              <div class="item-content">
                <div class="item-label">更新时间</div>
                <div class="item-value">{{ userInfo.updatedAt || '暂无' }}</div>
              </div>
            </div>
            <div class="info-item full-width">
              <div class="item-icon">
                <t-icon name="chat" />
              </div>
              <div class="item-content">
                <div class="item-label">备注</div>
                <div class="item-value">{{ userInfo.remark || '暂无' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import * as echarts from 'echarts/core';
import { EditIcon } from 'tdesign-icons-vue';
import { mapState } from 'vuex';
import { LAST_7_DAYS } from '@/utils/date';
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";


echarts.use([GridComponent, TooltipComponent, LineChart, CanvasRenderer, LegendComponent]);

export default {
  name: 'UserIndex',

  components: {
    EditIcon,
  },
  data() {
    return {
      dashboardBase: '',
      lineContainer: '',
      lineChart: '',
      LAST_7_DAYS,
    };
  },
  computed: {
    ...mapState('setting', ['brandTheme', 'mode']),
    userInfo() {
      let user = {};
      try {
        user = JSON.parse(window.localStorage.getItem('core:user') || '{}');
      } catch (e) {
        user = {};
      }
      return user;
    },
  },
  watch: {
  },
  mounted() {
    this.getCompanyAuth();
  },
  methods: {
    async getCompanyAuth() {
      try {
        this.dataLoading = true;
        const params = {
          id: 31000, // 总公司ID
          level: 4, // 公司层级ss
        };
        const { code, data } = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });
        if (code === 0 && data) {
          // Find and update columnName, companyName, saleGroupName in userInfo
          const user = this.userInfo;
          const column = data.columns.find(col => String(col.id) === String(user.columnId));
          if (column) {
            user.columnName = column.name;

            const company = column.companies.find(comp => String(comp.id) === String(user.companyId));
            if (company) {
              user.companyName = company.name;

              const group = company.salesGroups.find(g => String(g.id) === String(user.salesGroupId));
              if (group) {
                user.saleGroupName = group.name;
              }
            }
          }
          window.localStorage.setItem('core:user', JSON.stringify(user));
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },

  },
};
</script>
<style lang="less" scoped>
@import url('./index.less');
</style>
