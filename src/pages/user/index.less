@import '@/style/variables';

.user-center-page {
  background: #f8f9fa;
  min-height: 100vh;

  // 欢迎区域
  .welcome-section {
    background: #fff;
    padding: 32px;
    border-radius: 0;
    border: none;
    box-shadow: none;
    border-bottom: 3px solid #3b82f6;
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .welcome-content {
      display: flex;
      align-items: center;
      gap: 20px;

      .user-avatar {
        .avatar-circle {
          width: 64px;
          height: 64px;
          background: #3b82f6;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
        }
      }

      .welcome-text {
        .welcome-title {
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 300;
          color: #1f2937;
          letter-spacing: -0.5px;
        }

        .welcome-subtitle {
          margin: 0;
          font-size: 16px;
          color: #6b7280;
          font-weight: 400;
        }
      }
    }

    .welcome-actions {
      .t-button {
        border-radius: 0;
        border: 2px solid #3b82f6;
        color: #3b82f6;
        background: transparent;
        font-weight: 500;
        padding: 8px 20px;
        transition: all 0.2s ease;

        &:hover {
          background: #3b82f6;
          color: #fff;
        }

        .t-icon {
          margin-right: 6px;
        }
      }
    }
  }

  // 信息区域
  .info-section {
    background: #fff;
    border: none;
    box-shadow: none;

    .section-header {
      padding: 24px 32px 0;
      margin-bottom: 24px;

      .section-title {
        margin: 0 0 12px 0;
        font-size: 24px;
        font-weight: 300;
        color: #1f2937;
        letter-spacing: -0.5px;
      }

      .section-divider {
        width: 60px;
        height: 3px;
        background: #3b82f6;
        border: none;
      }
    }

    .info-grid {
      padding: 0 32px 32px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px;

      .info-group {
        &.full-width {
          grid-column: 1 / -1;
        }

        .group-title {
          margin: 0 0 20px 0;
          font-size: 18px;
          font-weight: 500;
          color: #374151;
          padding-bottom: 8px;
          border-bottom: 1px solid #e5e7eb;
        }

        .info-items {
          display: flex;
          flex-direction: column;
          gap: 16px;

          .info-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;

            &:last-child {
              border-bottom: none;
            }

            &.full-width {
              grid-column: 1 / -1;
            }

            .item-icon {
              width: 40px;
              height: 40px;
              background: #f3f4f6;
              border-radius: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;

              .t-icon {
                font-size: 18px;
                color: #6b7280;
              }
            }

            .item-content {
              flex: 1;
              min-width: 0;

              .item-label {
                font-size: 12px;
                color: #9ca3af;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 4px;
              }

              .item-value {
                font-size: 16px;
                color: #1f2937;
                font-weight: 400;
                line-height: 1.4;
                word-break: break-all;

                .status-badge {
                  display: inline-block;
                  padding: 4px 12px;
                  border-radius: 0;
                  font-size: 12px;
                  font-weight: 500;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;

                  &.active {
                    background: #dcfce7;
                    color: #166534;
                    border: 1px solid #bbf7d0;
                  }

                  &.inactive {
                    background: #fef2f2;
                    color: #991b1b;
                    border: 1px solid #fecaca;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .welcome-section {
      padding: 20px;
      flex-direction: column;
      gap: 20px;
      text-align: center;

      .welcome-content {
        flex-direction: column;
        gap: 16px;

        .welcome-text {
          .welcome-title {
            font-size: 24px;
          }

          .welcome-subtitle {
            font-size: 14px;
          }
        }
      }
    }

    .info-section {
      .section-header {
        padding: 20px 20px 0;

        .section-title {
          font-size: 20px;
        }
      }

      .info-grid {
        padding: 0 20px 20px;
        grid-template-columns: 1fr;
        gap: 24px;

        .info-group {
          .group-title {
            font-size: 16px;
          }

          .info-items {
            .info-item {
              padding: 12px 0;

              .item-icon {
                width: 36px;
                height: 36px;

                .t-icon {
                  font-size: 16px;
                }
              }

              .item-content {
                .item-label {
                  font-size: 11px;
                }

                .item-value {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    padding: 12px;

    .welcome-section {
      padding: 16px;

      .user-avatar .avatar-circle {
        width: 48px;
        height: 48px;

        .t-icon {
          font-size: 24px;
        }
      }

      .welcome-text {
        .welcome-title {
          font-size: 20px;
        }

        .welcome-subtitle {
          font-size: 13px;
        }
      }
    }

    .info-section {
      .section-header {
        padding: 16px 16px 0;
      }

      .info-grid {
        padding: 0 16px 16px;
      }
    }
  }
}

// 全局组件优化
/deep/ .t-button {
  border-radius: 0;
  font-weight: 500;
  transition: all 0.2s ease;

  &.t-button--theme-primary {
    background: #3b82f6;
    border: none;

    &:hover {
      background: #2563eb;
    }
  }

  &.t-button--variant-outline {
    background: transparent;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}
