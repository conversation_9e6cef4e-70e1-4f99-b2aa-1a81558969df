<template>
  <view class="redpacket-container" :style="{ height: contentHeight  + 'px', paddingTop: topHeight + 'px' }">
    <navigation-bar title="我的红包" showIcon path="user-center/user-center" />

    <!-- 下拉刷新提示 -->
    <!--    <view class="refresh-tip">-->
    <!--      <text>下拉可刷新红包列表</text>-->
    <!--    </view>-->

    <!-- 总金额展示区域 -->
    <view class="total-amount-section">
      <view class="amount-title">已领取红包总金额</view>
      <view class="amount-value">¥{{ totalReceivedAmount || "0.00" }}</view>
    </view>

    <!-- 未领取红包列表 -->
    <view class="unreceived-section">
      <view class="section-title">待领取红包</view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <van-loading size="24px" vertical>加载中...</van-loading>
      </view>

      <view v-else-if="unreceivedRedPackets && unreceivedRedPackets.length > 0" class="redpacket-list">
        <view v-for="(item, index) in unreceivedRedPackets" :key="index" class="redpacket-item" :class="{ expired: isRedPacketExpired(item.createTime) }" @click="receiveRedPacket(item)">
          <view class="redpacket-info">
            <view class="redpacket-name">{{ item.courseName || "课程红包" }}<text v-if="isRedPacketExpired(item.createTime)" class="expired-tag">(已过期)</text></view>
            <view class="redpacket-desc">{{ item.description || "来自课程的红包" }}</view>

            <view class="redpacket-time">
              <text>{{ formatDate(item.createTime) }}</text>
              <text v-if="!isRedPacketExpired(item.createTime)" class="countdown">
                {{ getCountdown(item.createTime) }}
              </text>
            </view>
          </view>
          <view class="redpacket-amount">
            <text>¥{{ item.transferAmount || "0.00" }}</text>
            <van-button size="small" type="danger" :disabled="isRedPacketExpired(item.createTime)" class="receive-btn"> 领取 </van-button>
          </view>
        </view>
      </view>

      <van-empty v-else description="暂无待领取的红包">
        <view class="empty-tip">暂时没有可领取的红包，请稍后再来查看</view>
      </van-empty>
    </view>

    <!-- 已领取红包列表 -->
    <view class="received-section">
      <view class="section-title">已领取红包</view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <van-loading size="24px" vertical>加载中...</van-loading>
      </view>

      <view v-else-if="receivedRedPackets && receivedRedPackets.length > 0" class="redpacket-list">
        <view v-for="(item, index) in receivedRedPackets" :key="index" class="redpacket-item received">
          <view class="redpacket-info">
            <view class="redpacket-name">{{ item.courseName || "课程红包" }}</view>
            <view class="redpacket-desc">{{ item.description || "来自课程的红包" }}</view>
            <view class="redpacket-time">
              <text>{{ formatDate(item.createTime) }}</text>
              <!--              <text class="status-tag received">已领取</text>-->
            </view>
          </view>
          <view class="redpacket-amount">
            <text>¥{{ item.transferAmount || "0.00" }}</text>
          </view>
        </view>
      </view>

      <van-empty v-else description="暂无已领取的红包"></van-empty>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "@/components/common/NavigationBar.vue";
import { getCustomerRedPacketSummary } from "@/api/red-packet.api";
import { Code } from "@/constants/enum/code.enum";
import { getMoney } from "@/utils/miniprogram";
import {getLayoutHeights} from "@/utils/get-page-height.util";

interface CourseRedPacket {
  id: string | number;
  courseName: string;
  description: string;
  amount: string | number;
  createTime: string;
  [key: string]: any;
}

interface RedPacketSummary {
  totalReceivedAmount: string;
  unreceivedRedPackets: CourseRedPacket[];
  receivedRedPackets: CourseRedPacket[];
}

/**
 * @Desc 红包页面
 */
@Component({
  name: "redpacket",
  components: { NavigationBar },
})
export default class RedPacket extends Vue {
  userInfo = uni.getStorageSync("userInfo");
  totalReceivedAmount = "0.00";
  unreceivedRedPackets: CourseRedPacket[] = [];
  receivedRedPackets: CourseRedPacket[] = [];
  loading = false;
  countdownTimer: number | null = null;
  topHeight = 88;
  contentHeight = 0;

  onLoad() {
    this.fetchRedPacketData();
    this.startCountdownTimer();
  }

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  onPullDownRefresh() {
    this.fetchRedPacketData().finally(() => {
      uni.stopPullDownRefresh();
    });
  }

  onUnload() {
    // 清除定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  }

  /**
   * 获取红包数据
   */
  async fetchRedPacketData() {
    if (!this.userInfo || !this.userInfo.id) {
      uni.showToast({
        title: "请先登录",
        icon: "none",
      });
      return;
    }

    this.loading = true;
    try {
      const response = await getCustomerRedPacketSummary({ customerId: this.userInfo.id });
      if (response.code === Code.OK.code) {
        const data: RedPacketSummary = response.data;
        this.totalReceivedAmount = data.totalReceivedAmount || "0.00";

        // 处理未领取的红包数据
        if (data.unreceivedRedPackets && data.unreceivedRedPackets.length > 0) {
          // 检查日期字段
          const firstItem = data.unreceivedRedPackets[0];
          // 确保使用正确的日期字段
          this.unreceivedRedPackets = data.unreceivedRedPackets.map((item) => {
            // 如果API返回的是createdAt而不是createTime，进行字段映射
            if (!item.createTime && item.createdAt) {
              return {
                ...item,
                createTime: item.createdAt,
              };
            }
            return item;
          });
        } else {
          this.unreceivedRedPackets = [];
        }

        // 处理已领取的红包数据
        if (data.receivedRedPackets && data.receivedRedPackets.length > 0) {
          this.receivedRedPackets = data.receivedRedPackets.map((item) => {
            // 如果API返回的是createdAt而不是createTime，进行字段映射
            if (!item.createTime && item.createdAt) {
              return {
                ...item,
                createTime: item.createdAt,
              };
            }
            return item;
          });
        } else {
          this.receivedRedPackets = [];
        }
      } else {
        uni.showToast({
          title: response?.msg || "获取红包数据失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("获取红包数据失败:", error);
      uni.showToast({
        title: "获取红包数据失败，请稍后重试",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  /**
   * 领取红包
   */
  receiveRedPacket(item: CourseRedPacket) {
    // 检查红包是否过期
    if (this.isRedPacketExpired(item.createTime)) {
      uni.showToast({
        title: "红包已过期，无法领取",
        icon: "none",
      });
      return;
    }

    getMoney({
      mchId: item.merchantId,
      appId: process.env.VUE_APP_COM_APPID,
      package: item.packageInfo,
      success: (res: any) => {
        console.log("用户确认收款成功", res);
        uni.showToast({
          title: "红包领取成功",
          icon: "success",
        });

        // 延迟一下再刷新，让用户看到成功提示
        setTimeout(() => {
          this.fetchRedPacketData();
        }, 1500);
      },
      fail: (res: any) => {
        console.error("用户确认收款失败", res);
        uni.showToast({
          title: "领取失败，请稍后重试",
          icon: "none",
        });
      },
      complete: (res: any) => {
        console.log("红包领取请求完成", res);
      },
    });
  }

  /**
   * 检查红包是否过期
   * @param createTime 红包创建时间
   * @returns 是否过期
   */
  isRedPacketExpired(createTime: string): boolean {
    if (!createTime) return true;

    try {
      const createDate = this.parseDate(createTime);
      const expiryDate = new Date(createDate.getTime() + 24 * 60 * 60 * 1000); // 24小时后过期
      const now = new Date();

      // 检查是否是未来日期（可能是系统时间设置问题）
      if (createDate > now) {
        console.warn("警告: 红包创建时间在当前时间之后!");
      }

      return now >= expiryDate;
    } catch (e) {
      console.error("检查红包是否过期出错:", e);
      return true; // 出错时默认为过期
    }
  }

  /**
   * 格式化日期
   */
  formatDate(dateStr: string): string {
    if (!dateStr) return "";

    try {
      const date = this.parseDate(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    } catch (e) {
      return dateStr;
    }
  }

  /**
   * 解析日期字符串为Date对象
   * @param dateStr 日期字符串
   * @returns Date对象
   */
  parseDate(dateStr: string): Date {
    if (!dateStr) {
      throw new Error("日期字符串不能为空");
    }

    // 处理 "YYYY-MM-DD HH:MM:SS" 格式
    if (dateStr.includes(" ")) {
      const [datePart, timePart] = dateStr.split(" ");
      const [year, month, day] = datePart.split("-").map(Number);
      const [hours, minutes, seconds] = timePart.split(":").map(Number);

      // 创建本地时间的Date对象
      return new Date(year, month - 1, day, hours, minutes, seconds);
    }

    // 尝试直接解析
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      throw new Error("无效的日期格式");
    }
    return date;
  }

  /**
   * 计算红包倒计时
   * @param createTime 红包创建时间
   * @returns 格式化的倒计时字符串
   */
  getCountdown(createTime: string): string {
    if (!createTime) return "";

    try {
      const createDate = this.parseDate(createTime);
      const expiryDate = new Date(createDate.getTime() + 24 * 60 * 60 * 1000); // 24小时后过期
      const now = new Date();

      // 如果已经过期，返回空字符串
      if (now >= expiryDate) {
        return "";
      }

      // 计算剩余时间（毫秒）
      const remainingTime = expiryDate.getTime() - now.getTime();

      // 转换为小时和分钟
      const hours = Math.floor(remainingTime / (1000 * 60 * 60));
      const minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));

      return `${hours}小时${minutes}分钟后过期`;
    } catch (e) {
      console.error("计算倒计时出错:", e);
      return "";
    }
  }

  /**
   * 启动倒计时定时器
   */
  startCountdownTimer() {
    // 每分钟更新一次倒计时
    this.countdownTimer = setInterval(() => {
      // 强制更新视图
      this.$forceUpdate();
    }, 60000); // 60秒更新一次
  }
}
</script>

<style lang="scss" scoped>
.redpacket-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

.refresh-tip {
  background-color: rgba(0, 0, 0, 0.03);
  padding: 10rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #999;
}

.total-amount-section {
  background: $standard-color;
  color: #fff;
  padding: 40rpx 0;
  text-align: center;
  margin-bottom: 30rpx;
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.amount-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.amount-value {
  font-size: 60rpx;
  font-weight: bold;
}

.unreceived-section,
.received-section {
  background: #fff;
  border-radius: 20rpx;
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.received-section {
  margin-top: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 20rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;

  ::v-deep .van-loading {
    color: $standard-color;
  }
}

.redpacket-list {
  margin-top: 20rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
  text-align: center;
}

::v-deep .van-empty {
  padding: 60rpx 0;

  .van-empty__image {
    width: 200rpx;
    height: 200rpx;
  }

  .van-empty__description {
    color: #666;
    font-size: 28rpx;
    margin-top: 20rpx;
  }
}

.redpacket-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f0f0f0;

  &.expired {
    background-color: #fafafa;

    .redpacket-name,
    .redpacket-desc,
    .redpacket-time {
      color: #999;
    }

    .redpacket-amount text {
      color: #999;
    }
  }
}

.redpacket-info {
  flex: 1;
}

.redpacket-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.redpacket-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.expired-tag {
  font-size: 24rpx;
  color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  display: inline-block;
  margin-bottom: 10rpx;
}

.redpacket-time {
  font-size: 24rpx;
  color: #999;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .countdown {
    color: #ff7700;
    font-weight: 500;
    background-color: rgba(255, 119, 0, 0.1);
    padding: 4rpx 10rpx;
    border-radius: 10rpx;
    margin-left: 10rpx;

    &.expired {
      color: #999;
      background-color: rgba(153, 153, 153, 0.1);
    }
  }
}

.redpacket-amount {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;

  text {
    font-size: 36rpx;
    font-weight: bold;
    color: #ff4d4f;
    margin-bottom: 10rpx;
  }
}

.redpacket-item.received {
  border-left: 6rpx solid #07c160;
  padding-left: 24rpx;

  .redpacket-amount {
    text {
      color: #07c160;
      margin-bottom: 0;
    }
  }
}

.status-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;

  &.received {
    color: #07c160;
    background-color: rgba(7, 193, 96, 0.1);
  }
}

.receive-btn {
  ::v-deep .van-button--danger {
    background: #ff4d4f;
    border: none;
  }
}
</style>
