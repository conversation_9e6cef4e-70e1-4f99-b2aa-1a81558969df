<template>
  <div class="enterprise-wechat-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="logo-wecom" />
          企业微信管理
        </div>
        <div class="page-desc">管理企业微信授权信息，进行客户信息同步、客户群信息同步等操作</div>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="handleAdd">
          <t-icon name="add" />
          新增企业微信授权
        </t-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-section">
        <div class="filter-content">
          <t-form :data="formData" layout="inline" class="filter-form">
            <div class="filter-row">
              <t-form-item label="企业名称" name="corpName" class="filter-item">
                <t-input
                  v-model="formData.corpName"
                  placeholder="请输入企业名称"
                  clearable
                  class="filter-input"
                >
                  <template #prefixIcon>
                    <t-icon name="search" />
                  </template>
                </t-input>
              </t-form-item>

              <div class="filter-buttons">
                <t-button theme="primary" @click="onSubmit" class="search-btn">
                  <t-icon name="search" />
                  查询
                </t-button>
                <t-button variant="outline" @click="onReset" class="reset-btn">
                  <t-icon name="refresh" />
                  重置
                </t-button>
              </div>
            </div>
          </t-form>
        </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="table-info">
          <t-icon name="wechat" />
          <span>企业微信授权列表</span>
          <span class="count-info">（共 {{ pagination.total }} 条）</span>
        </div>
      </div>

      <div class="toolbar-right">
        <t-button variant="outline" @click="refreshData" class="refresh-btn">
          <t-icon name="refresh" />
          刷新
        </t-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <t-card class="table-card">
      <t-table
        :columns="columns"
        :data="data"
        table-layout="fixed"
        :rowKey="rowKey"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        :loading="dataLoading"
        @change="rehandleChange"
        @select-change="rehandleSelectChange"
        :empty="emptyConfig"
        stripe
        hover
      >
        <!-- 状态列插槽 -->
        <template #status="{ row }">
          <span class="status-badge status-active">已授权</span>
        </template>

        <!-- 操作列插槽 -->
        <template #op="{ row }">
          <div class="table-actions">
            <t-button variant="text" theme="primary" @click="handleShowDetail(row)" class="action-btn info-btn">
              <t-icon name="logo-wecom" />

              授权信息
            </t-button>
            <t-button variant="text" theme="primary" @click="handleTagManagement(row)" class="action-btn tag-btn">
              <t-icon name="tag" />
              标签管理
            </t-button>
            <t-popconfirm content="确定删除该授权信息？" @confirm="handleDelete(row)">
              <t-button variant="text" theme="danger" class="action-btn delete-btn">
                <t-icon name="delete" />
                删除
              </t-button>
            </t-popconfirm>
          </div>
        </template>
      </t-table>
    </t-card>

    <!-- 授权二维码弹窗 -->
    <t-dialog
      :visible="formDialog.visible"
      :header="formDialog.title"
      width="500px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
      @close="handleCancel"
      class="auth-dialog"
    >
      <div class="qrcode-container">
        <div class="qrcode-title">
          <t-icon name="wechat" class="wechat-icon" />
          企业微信授权
        </div>
        <div class="qrcode-wrapper">
          <div class="qrcode-img">
            <img :src="formDialog.qrcodeUrl" alt="企业微信授权二维码"/>
          </div>
          <div class="qrcode-tips">
            <p>请使用<strong>企业微信管理员账号</strong>扫描上方二维码</p>
            <p>授权成功后，系统将自动获取企业信息并完成配置</p>
            <p class="tip-note">注意：只有企业微信的管理员才能进行授权操作</p>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import {ACTIVE_TYPE_OPTIONS} from '@/constants';
import dayjs from 'dayjs';
import sysMiniProgram from '@/constants/api/back/sys-miniprogram.api';
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

export default Vue.extend({
  name: 'EnterpriseWechatManagement',
  components: {},
  data() {
    return {
      ACTIVE_TYPE_OPTIONS,
      // 侧边栏
      drawerVar: {
        visible: false,
        data: {},
      },
      // 客户创建时间
      createTime: [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
      ],
      // 搜索条件
      formData: {
        corpName: '',
      },
      // 批量操作选项
      batchOperationOption: [
        {
          content: '暂未开放',
          value: '1',
        },
      ],
      // 新增弹窗相关
      formDialog: {
        visible: false,
        title: '',
        qrcodeUrl: 'https://hxsy-prod-noaz-**********.cos.ap-guangzhou.myqcloud.com/staticImage/qrcode_8cm.png',
      },
      typeOptions: [
        {label: '微信小程序', value: '1'},
        {label: '微信公众号', value: '2'}
      ],
      statusOptions: [
        {label: '禁用', value: '0'},
        {label: '启用', value: '1'}
      ],
      formRules: {
        name: [{required: true, message: '名称必填'}],
        type: [{required: true}],
        appid: [{required: true, message: 'APPID必填'}],
        secret: [{required: true, message: 'SECRET必填'}]
      },
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
        },
        {
          title: '企业名称',
          align: 'left',
          width: 180,
          colKey: 'corpName',
          fixed: 'left',
        },
        {
          title: '企业ID',
          align: 'left',
          width: 200,
          colKey: 'corpId',
        },
        {
          title: '授权时间',
          align: 'left',
          width: 160,
          colKey: 'createdAt',
        },
        {
          title: '状态',
          align: 'center',
          width: 100,
          colKey: 'status',
          cell: 'status',
        },
        {
          title: '操作',
          align: 'center',
          width: 280,
          colKey: 'op',
          fixed: 'right',
          cell: 'op',
        },
      ],
      rowKey: 'id',
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      selectedRow: null,
      emptyConfig: {
        image: 'https://tdesign.gtimg.com/pro-template/personal/empty.png',
        description: '暂无企业微信授权信息，请先进行企业微信授权',
        action: {
          text: '立即授权',
          theme: 'primary',
          onClick: () => this.handleAdd()
        }
      }
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
  },
  async mounted() {
    this.getList();
  },

  methods: {
    // 显示详情
    showDrawer(slotProps: any) {
      this.drawerVar.visible = true;
      this.drawerVar.data = slotProps?.row;
      console.log("slotProps", slotProps);
    },
    // 关闭详情
    drawerCancel() {
      this.drawerVar.visible = false;
    },
    rehandleChange(changeParams: any) {
      this.pagination.pageSize = changeParams.pagination.pageSize;
      this.pagination.current = changeParams.pagination.current;
      this.getList();
      // console.log('统一Change', changeParams);
    },
    onSubmit(result: any) {
      console.log("this.formData", this.formData);
      this.pagination.current = 1;
      this.getList();
    },
    onReset() {
      this.pagination.current = 1;
      this.formData = {
        corpName: '',
      };
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    handleAdd() {
      this.formDialog.title = '企业微信授权';
      this.formDialog.visible = true;
    },
    handleCancel() {
      this.formDialog.visible = false
    },
    handleDelete(row: any) {
      console.log('删除企业微信授权:', row);
      // TODO: 实现删除逻辑
    },

    refreshData() {
      this.getList();
    },
    handleShowDetail(row: any) {
      // 企业信息
      this.$router.push({
        name: 'enterprise-wechat-message',
        params: { data: row, type: 'new' },
        query: {
          corpId: row.corpId,
          corpName: row.corpName,
        },
      });
    },
    handleTagManagement(row: any) {
      this.$router.push({
        name: 'enterprise-wechat-tag',
        query: {
          corpId: row.corpId,
          corpName: row.name
        },
      });
    },
    handleBindCourseMiniProgram(row: any) {
      //
    },
    async handleSubmit() {
      try {
        this.formDialog.visible = false;
      } catch (e) {
        console.error(e)
      }
    },
    async getList() {
      const query = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
        ...this.formData // 合并查询条件
      };
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.post(
          orderAndUserApi.queryQyListAuth.url,
          query,
        );
        console.log('企微授权数据响应:', res);
        const {code, data, msg} = res;
        if (code === 0) {
          this.data = data.records || [];
          this.pagination.total = +data.total || 0;
          console.log('企微授权数据:', this.data);
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log('获取企微授权数据失败:', e);
        this.dataLoading = false;
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

// ==================== 页面主体样式 ====================
.enterprise-wechat-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #07c160; // 微信绿色
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .t-button {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 筛选区域
  .filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 16px;
    overflow: hidden;

    .filter-content {
      padding: 20px 24px;

      .filter-form {
        .filter-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          align-items: flex-end;

          .filter-item {
            min-width: 200px;
            margin-bottom: 0;

            .filter-input {
              width: 200px;
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;
            margin-left: auto;

            .t-button {
              height: 32px;
              padding: 0 16px;
              border-radius: 6px;
              font-weight: 500;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }
          }
        }
      }
    }
  }

  // 工具栏
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 24px;

      .table-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #374151;

        .t-icon {
          color: #07c160;
        }

        .count-info {
          color: #6b7280;
        }
      }
    }

    .toolbar-right {
      display: flex;
      gap: 12px;

      .t-button {
        height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  // 表格区域
  .table-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;

    ::v-deep .t-table {
      border-radius: 0;

      .t-table__header {
        background: #fafbfc;

        th {
          background: #fafbfc;
          border-bottom: 1px solid #e5e7eb;
          font-weight: 600;
          color: #374151;
          padding: 16px;
        }
      }

      .t-table__body {
        td {
          padding: 16px;
          border-bottom: 1px solid #f3f4f6;

          &:last-child {
            border-bottom: none;
          }
        }

        tr {
          transition: all 0.2s ease;

          &:hover {
            background: #f9fafb;
          }
        }
      }

      // 表格操作按钮
      .table-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;

        .action-btn {
          padding: 6px 12px !important;
          border-radius: 6px;
          font-weight: 500;
          font-size: 12px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 4px;
          min-width: auto;

          .t-icon {
            font-size: 14px;
          }

          &.info-btn {
            color: #3b82f6;

            &:hover {
              background: rgba(59, 130, 246, 0.1);
              transform: translateY(-1px);
            }
          }

          &.tag-btn {
            color: #8b5cf6;

            &:hover {
              background: rgba(139, 92, 246, 0.1);
              transform: translateY(-1px);
            }
          }

          &.delete-btn {
            color: #ef4444;

            &:hover {
              background: rgba(239, 68, 68, 0.1);
              transform: translateY(-1px);
            }
          }
        }
      }

      // 状态徽章
      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;

        &.status-active {
          background: rgba(7, 193, 96, 0.1);
          color: #07c160;
          border: 1px solid rgba(7, 193, 96, 0.2);
        }

        &.status-inactive {
          background: rgba(156, 163, 175, 0.1);
          color: #6b7280;
          border: 1px solid rgba(156, 163, 175, 0.2);
        }
      }
    }

    ::v-deep .t-table__pagination {
      background: #fafbfc;
      padding: 16px 20px;
      border-top: 1px solid #e5e7eb;
    }
  }
}

// ==================== 弹窗样式 ====================
::v-deep .auth-dialog {
  .t-dialog__header {
    background: linear-gradient(135deg, #07c160, #05a850);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    font-weight: 600;
  }

  .t-dialog__body {
    padding: 32px 24px;
  }

  .t-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
  }
}

.qrcode-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;

  .qrcode-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    .wechat-icon {
      font-size: 20px;
      color: #07c160;
    }
  }

  .qrcode-wrapper {
    .qrcode-img {
      margin-bottom: 20px;
      padding: 16px;
      background: #f9fafb;
      border-radius: 12px;
      border: 2px dashed #d1d5db;

      img {
        width: 180px;
        height: 180px;
        border-radius: 8px;
      }
    }

    .qrcode-tips {
      p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #6b7280;
        line-height: 1.5;

        strong {
          color: #07c160;
          font-weight: 600;
        }

        &.tip-note {
          font-size: 12px;
          color: #f59e0b;
          background: rgba(245, 158, 11, 0.1);
          padding: 8px 12px;
          border-radius: 6px;
          border-left: 3px solid #f59e0b;
          margin-top: 12px;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .enterprise-wechat-page {
    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            .filter-buttons {
              margin-left: 0;
              margin-top: 16px;
              width: 100%;
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .enterprise-wechat-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .header-actions {
        width: 100%;
        justify-content: center;
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }

    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            flex-direction: column;
            align-items: stretch;

            .filter-item {
              min-width: auto;

              .filter-input {
                width: 100%;
              }
            }

            .filter-buttons {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}
</style>
