<template>
  <div id="auth-message" class="auth-message-container">
    <t-loading attach="#auth-message" size="medium" text="加载中..." :loading="loading"></t-loading>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2 class="page-title">企业微信授权管理</h2>
          <p class="page-subtitle">管理企业微信授权信息，配置回调服务器，同步企业数据</p>
        </div>
        <div class="header-actions">
          <t-button theme="default" variant="outline" @click="handleRefresh">
            <template #icon><refresh-icon /></template>
            刷新数据
          </t-button>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="tabs-container">
      <t-tabs :value="currentTab" @change="(value) => currentTab = value" theme="normal">
        <t-tab-panel value="auth" label="授权信息">
          <div class="tab-content">
            <!-- 企业微信账号信息 -->
            <t-card class="info-card" :bordered="false">
              <template #header>
                <div class="card-header">
                  <div class="header-left">
                    <div class="card-icon">
                      <t-icon name="user-circle" size="20px" />
                    </div>
                    <div class="header-text">
                      <h3 class="card-title">企业微信账号信息</h3>
                      <p class="card-subtitle">查看当前企业微信的基本配置信息</p>
                    </div>
                  </div>
                  <t-tag theme="success" variant="light" v-if="accountInfo.corpId">
                    已配置
                  </t-tag>
                </div>
              </template>

              <div class="form-container">
                <t-form :data="accountInfo" layout="vertical" class="info-form">
                  <t-row :gutter="[24, 10]">
                    <t-col :span="6">
                      <t-form-item label="企业名称" class="info-form-item">
                        <div class="form-value">
                          <span class="value-text">{{ accountInfo.corpName || '未设置' }}</span>
                        </div>
                      </t-form-item>
                    </t-col>
                    <t-col :span="6">
                      <t-form-item label-width="120px" label="企业ID（corpId）" class="info-form-item">
                        <div class="form-value">
                          <span class="value-text">{{ accountInfo.corpId || '未设置' }}</span>
                          <t-button variant="text" size="small" @click="copyToClipboard(accountInfo.corpId)" v-if="accountInfo.corpId">
                            <template #icon><copy-icon /></template>
                          </t-button>
                        </div>
                      </t-form-item>
                    </t-col>
                    <t-col :span="6">
                      <t-form-item label="Secret" class="info-form-item">
                        <div class="form-value">
                          <span class="value-text secret-text">{{ maskSecret(accountInfo.secret) }}</span>
                          <t-button variant="text" size="small" @click="toggleSecretVisibility" v-if="accountInfo.secret">
                            <template #icon><view-icon v-if="!showSecret" /><view-off-icon v-else /></template>
                          </t-button>
                        </div>
                      </t-form-item>
                    </t-col>
                    <t-col :span="6">
                      <t-form-item label="AgentId" class="info-form-item">
                        <div class="form-value">
                          <span class="value-text">{{ accountInfo.agentId || '未设置' }}</span>
                        </div>
                      </t-form-item>
                    </t-col>
                  </t-row>
                </t-form>
              </div>
            </t-card>

            <!-- 接收事件服务器设置 -->
            <t-card class="info-card" :bordered="false">
              <template #header>
                <div class="card-header">
                  <div class="header-left">
                    <div class="card-icon">
                      <t-icon name="server" size="20px" />
                    </div>
                    <div class="header-text">
                      <h3 class="card-title">接收事件服务器设置</h3>
                      <p class="card-subtitle">配置企业微信回调服务器，接收用户事件通知</p>
                    </div>
                  </div>
                </div>
              </template>

              <div class="form-container">
                <t-alert theme="warning" class="config-alert">
                  <template #icon><info-circle-icon /></template>
                  请务必将以下字段填写至企业微信后台，否则加好友后续动作和被客户删除记录等功能将无法使用。
                </t-alert>

                <t-form :data="accountInfo" layout="vertical" class="info-form">
                  <t-row :gutter="[24, 10]">
                    <t-col :span="12">
                      <t-form-item label="回调地址" class="info-form-item">
                        <div class="form-value">
                          <span class="value-text">{{ qyData.corpId ? callbackUrl + qyData.corpId : '暂无' }}</span>
                          <t-button variant="text" size="small" @click="copyToClipboard(callbackUrl + qyData.corpId)" v-if="qyData.corpId">
                            <template #icon><copy-icon /></template>
                          </t-button>
                        </div>
                      </t-form-item>
                    </t-col>
                    <t-col :span="6">
                      <t-form-item label="Token" class="info-form-item">
                        <div class="form-value">
                          <span class="value-text">{{ accountInfo.token || '未设置' }}</span>
                          <t-button variant="text" size="small" @click="copyToClipboard(accountInfo.token)" v-if="accountInfo.token">
                            <template #icon><copy-icon /></template>
                          </t-button>
                        </div>
                      </t-form-item>
                    </t-col>
                    <t-col :span="6">
                      <t-form-item label-width="140px" label="EncodingAESKey" class="info-form-item">
                        <div class="form-value">
                          <span class="value-text">{{ accountInfo.encodingAESKey || '未设置' }}</span>
                          <t-button variant="text" size="small" @click="copyToClipboard(accountInfo.encodingAESKey)" v-if="accountInfo.encodingAESKey">
                            <template #icon><copy-icon /></template>
                          </t-button>
                        </div>
                      </t-form-item>
                    </t-col>
                  </t-row>
                </t-form>
              </div>
            </t-card>

            <!-- 更多操作 -->
            <t-card class="info-card" :bordered="false">
              <template #header>
                <div class="card-header">
                  <div class="header-left">
                    <div class="card-icon">
                      <t-icon name="setting" size="20px" />
                    </div>
                    <div class="header-text">
                      <h3 class="card-title">数据同步设置</h3>
                      <p class="card-subtitle">手动同步企业数据或开启自动同步功能</p>
                    </div>
                  </div>
                </div>
              </template>

              <div class="form-container">
                <t-form :data="accountInfo" layout="vertical" class="operation-form">
                  <t-row :gutter="[24, 10]">
                    <t-col :span="12">
                      <t-form-item label="手动同步" class="info-form-item">
                        <div class="sync-buttons">
                          <t-button theme="primary" @click="syncCustomerInfo" :loading="syncLoading.customer">
                            <template #icon><refresh-icon /></template>
                            同步客户信息
                          </t-button>
                          <t-button theme="default" variant="outline" @click="syncStaffNickname" :loading="syncLoading.staff">
                            <template #icon><user-icon /></template>
                            同步员工信息
                          </t-button>
                        </div>
                      </t-form-item>
                    </t-col>
                    <t-col :span="12">
                      <t-form-item label="自动同步" class="info-form-item">
                        <div class="auto-sync-container">
                          <t-switch
                            v-model="accountInfo.autoSync"
                            @change="changeAutoSync"
                            :loading="syncLoading.auto"
                          />
                          <span class="switch-label">开启后将定时自动同步数据</span>
                        </div>
                      </t-form-item>
                    </t-col>
                  </t-row>
                </t-form>
              </div>
            </t-card>
          </div>
        </t-tab-panel>

        <t-tab-panel value="member" label="成员列表">
          <div class="tab-content">
            <member-list :qyData="qyData"></member-list>
          </div>
        </t-tab-panel>

        <t-tab-panel value="activation" label="激活账号管理">
          <div class="tab-content">
            <activation-account-management :qyData="qyData"></activation-account-management>
          </div>
        </t-tab-panel>

        <t-tab-panel value="order" label="订单管理">
          <div class="tab-content">
            <order-management :qyData="qyData"></order-management>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import { RefreshIcon, CopyIcon, InfoCircleIcon, UserIcon } from 'tdesign-icons-vue';
import MemberList from "@/pages/enterprise-wechat-management/components/member-list.vue";
import ActivationAccountManagement from "@/pages/enterprise-wechat-management/components/activation-account-management.vue";
import OrderManagement from "@/pages/enterprise-wechat-management/components/order-management.vue";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

export default Vue.extend({
  name: 'AuthMessage',
  components: {
    MemberList,
    ActivationAccountManagement,
    OrderManagement,
    RefreshIcon,
    CopyIcon,
    InfoCircleIcon,
    UserIcon
  },
  data() {
    return {
      qyData: this.$route.params.data,
      queryQyData: this.$route.params.query,
      callbackUrl: import.meta.env.VITE_SERVER_CALLBACK_URL,
      loading: false,
      showSecret: false,
      syncLoading: {
        customer: false,
        staff: false,
        auto: false
      },
      accountInfo: {
        corpName: '',
        corpId: '',
        secret: '',
        agentId: '',
        token: '',
        encodingAESKey: '',
        autoSync: false,
      },
      currentTab: 'auth',
    }
  },
  watch: {
    'qyData': {
      handler(val) {
        this.accountInfo = {
          corpName: val.corpName,
          corpId: val.corpId,
          secret: val.secret,
          agentId: val.agentId,
          token: val.token,
          encodingAESKey: val.encodingAESKey,
          autoSync: val.autoSync,
        };
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 复制到剪贴板
    async copyToClipboard(text) {
      if (!text) return;
      try {
        await navigator.clipboard.writeText(text);
        this.$message.success('复制成功');
      } catch (err) {
        this.$message.error('复制失败');
      }
    },

    // 切换密钥显示状态
    toggleSecretVisibility() {
      this.showSecret = !this.showSecret;
    },

    // 掩码显示密钥
    maskSecret(secret) {
      if (!secret) return '未设置';
      if (this.showSecret) return secret;
      return `${secret.substring(0, 8)  }****${  secret.substring(secret.length - 4)}`;
    },

    // 刷新数据
    handleRefresh() {
      this.$message.success('数据已刷新');
      // 重新获取数据的逻辑
    },

    syncCustomerInfo() {
      this.syncLoading.customer = true;
      setTimeout(() => {
        this.syncLoading.customer = false;
        this.$message.success('客户信息同步成功');
      }, 2000);
    },

    async syncStaffNickname() {
      const corpId = this.qyData.corpId || this.queryQyData.corpId;
      if (!corpId) {
        this.$message.error('获取企业ID为空');
        return;
      }
      const params = {
        corpId,
      }
      this.syncLoading.staff = true;
      try {
        const res = await this.$request.post(orderAndUserApi.saveQyContact.url, params);
        if (res.code === 0) {
          if (res.data && typeof res.data === 'string') {
            this.$message.success(res.data);
            return;
          }
          this.$message.success('同步员工信息成功');
        } else {
          this.$message.error('同步员工信息失败');
        }
      } catch (error) {
        this.$message.error('同步员工信息失败');
      } finally {
        this.syncLoading.staff = false;
      }
    },

    changeAutoSync(val) {
      this.syncLoading.auto = true;
      setTimeout(() => {
        this.syncLoading.auto = false;
        this.$message.success(val ? '已开启自动同步' : '已关闭自动同步');
      }, 1000);
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.auth-message-container {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 24px;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-section {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2329;
        margin: 0 0 8px 0;
      }

      .page-subtitle {
        font-size: 14px;
        color: #86909c;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
}

.tabs-container {
  //max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;

  ::v-deep .t-tabs__nav {
    background: #fff;
    border-radius: 8px 8px 0 0;
    padding: 0 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  ::v-deep .t-tabs__content {
    background: transparent;
    padding: 0;
  }
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e4e7ed;
  overflow: hidden;

  ::v-deep .t-card__header {
    background: #fafbfc;
    border-bottom: 1px solid #e4e7ed;
    padding: 20px 24px;
  }

  ::v-deep .t-card__body {
    padding: 24px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .card-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }

    .header-text {
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2329;
        margin: 0 0 4px 0;
      }

      .card-subtitle {
        font-size: 13px;
        color: #86909c;
        margin: 0;
      }
    }
  }
}

.form-container {
  .config-alert {
    margin-bottom: 24px;
  }

  .info-form {
    .t-form-item__label {
      font-weight: 500;
      color: #1f2329;
      margin-bottom: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .form-value {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f7f8fa;
      border-radius: 6px;
      border: 1px solid #e4e7ed;
      min-height: 44px;
      gap: 8px;

      .value-text {
        font-size: 14px;
        color: #1f2329;
        flex: 1;
        word-break: break-all;
        overflow-wrap: break-word;
        min-width: 0;

        &.secret-text {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }

      .t-button {
        flex-shrink: 0;
        opacity: 0.7;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .operation-form {
    .sync-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .auto-sync-container {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      background: #f7f8fa;
      border-radius: 6px;
      border: 1px solid #e4e7ed;

      .switch-label {
        font-size: 14px;
        color: #86909c;
      }
    }
  }
}

// 响应式设计优化
@media (max-width: 1200px) {
  .form-container .info-form {
    .t-col {
      &:nth-child(n+3) {
        margin-top: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .page-header .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .tabs-container {
    padding: 0 12px;
  }

  .info-card ::v-deep .t-card__body {
    padding: 16px;
  }

  .form-container {
    .info-form {
      .t-row {
        margin: 0 -8px;
      }

      .t-col {
        padding: 0 8px;
        margin-bottom: 16px;
      }
    }

    .operation-form .sync-buttons {
      flex-direction: column;
    }
  }
}

// 深色主题适配
.t-theme--dark {
  .auth-message-container {
    background: #1a1a1a;
  }

  .page-header {
    background: #2d2d2d;
    border-bottom-color: #404040;
  }

  .info-card {
    background: #2d2d2d;
    border-color: #404040;

    ::v-deep .t-card__header {
      background: #363636;
      border-bottom-color: #404040;
    }
  }

  .form-container .info-form .form-value {
    background: #363636;
    border-color: #404040;
  }
}
.info-form-item {
  display: flex;
  align-items: center;
}
/deep/ .t-form__controls {
  margin-left: 0 !important;
}
</style>
