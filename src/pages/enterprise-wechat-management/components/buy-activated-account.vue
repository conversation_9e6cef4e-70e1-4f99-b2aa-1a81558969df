<template>
  <t-dialog
    id="edit-task-dialog"
    width="35%"
    header="互通激活账号下单"
    :closeOnOverlayClick="false"
    :visible.sync="modalVisible"
    @close="closeModal"
    class="edit-task-dialog"
    @confirm="confirmCampData"
    @cancel="closeModal"
  >
    <div class="copy-camp-content" id="copy-camp-content">
      <t-loading :loading="selectLoading" attach="#copy-camp-content" size="small" />
      <t-form :data="form" :rules="rules" ref="form" validate-trigger="blur">
        <t-form-item label="购买数量" name="buyCount">
          <div class="buy-count">
            <t-input-number v-model="form.buyCount" type="number" min="1" max="9999"></t-input-number>
            <span class="span-content">请输入1～9999的整数</span>
          </div>
        </t-form-item>
        <t-form-item label="购买期限(天)" name="buyPeriod">
          <div class="buy-count">
            <t-input-number v-model="form.buyPeriod" min="31" max="9999" type="number"></t-input-number>
            <span class="span-content">请输入31～9999的整数</span>
          </div>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>
<script>

import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

export default {
  name: 'BuyActivatedAccount',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    corpid: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      modalVisible: false,
      form: {
        buyCount: 1,
        buyPeriod: 31,
      },
      selectLoading: false,
      buyerUserid: 'TangBao', // 购买者userid，暂时写死
      rules: {
        buyCount: [
          { required: true, message: '请输入购买数量' },
        ],
        buyPeriod: [
          { required: true, message: '请输入购买期限' },
        ],
      },
    }
  },
  computed: {},
  watch: {
    visible(val) {
      this.modalVisible = val;
    },
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },
    confirmCampData() {
      Promise.all([
        this.$refs.form.validate()
      ]).then(async (res) => {
        console.error(res);
        // res为数组，两个值都为true才表示校验成功
        if (res.every((item) => item === true)) {
          // 校验成功，提交表单
          const params = {
            corpId: this.corpid,
            buyerUserid: this.buyerUserid,
            baseCount: 0,
            externalContactCount: this.form.buyCount,
            accountDuration: this.form.buyPeriod,
          };
          this.selectLoading = true;
          const res = await this.$request.post(orderAndUserApi.createOrder.url, params);
          this.selectLoading = false;
          if (res.code === 0) {
            this.$message.success('下单成功');
            this.$emit('success');
          } else {
            this.$message.error('下单失败');
          }
        }
      }).catch((err) => {
        console.log(err);
      });
    },
  }
}
</script>
<style scoped lang="less">
.buy-count {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .span-content {
     margin-top: 10px;
    font-size: 12px;
    color: #999;
  }
}
.copy-camp-content {
  position: relative;
  padding: 20px;
  .copy-camp-head {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .copy-camp-body {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .mt-10 {
    margin-top: 10px;
  }
}
</style>
