<template>
  <div class="task-library">
    <div class="desc-container">
      <div class="desc-container-left">
        <div class="left-container">
          <t-alert
            class="alert-message"
            theme="info"
            message="根据腾讯政策要求，服务商需完成：1. 为每位用户开通并激活企微互通账号 2. 保障与企业微信平台的接口调用 3. 确保跨平台数据互通能力"
            :close="true"
          />
        </div>
      </div>
      <div class="add-button">
        <!--   选择绑定的企业微信账号     -->
        <t-select disabled v-model="enterpriseWechatNickname" :options="enterpriseWechatNicknameOptions" placeholder="请选择绑定的企业微信账号"></t-select>
        <t-button @click="addModel">购买激活账号</t-button>
      </div>
    </div>

    <t-card class="card-container">
      <div class="right">
        <div class="search-input">
          <t-form :data="form" ref="formSearch" layout="inline">
            <t-form-item label="订单ID">
              <t-input v-model="form.orderId" :clearable="true" placeholder="请输入订单ID"></t-input>
            </t-form-item>
            <t-form-item label="订单类型">
              <t-select v-model="form.orderType" :options="activeOptions" placeholder="请选择订单类型"></t-select>
            </t-form-item>
            <t-form-item label="订单状态">
              <t-select v-model="form.payStatus" :options="accountStatusOptions" placeholder="请选择订单状态"></t-select>
            </t-form-item>
          </t-form>
          <div class="search-btn">
            <t-button theme="primary" @click="onSubmit">查询</t-button>
            <t-button theme="primary" ghost @click="onReset">重置</t-button>
          </div>
        </div>

        <common-table
          ref="commonTable"
          :columns="columns"
          :url="url"
          :isRequest="!!activeId"
          :params="tableParams"
          :operations="operations"
        >
        </common-table>
      </div>
    </t-card>
    <buy-activated-account :corpid="enterpriseWechatNickname" :visible="buyAccountVisible" @success="successBuyAccount" @close="buyAccountVisible = false"></buy-activated-account>
    <order-detail :visible="showDetail" :orderId="orderId" @close="closeDetail"></order-detail>
  </div>
</template>

<script>
import CommonTable from '@/components/common-table/index.vue';
import search from "tdesign-icons-vue/lib/components/search";
import {getMenuChildrenList, getOperationTypeList} from "@/utils/utils";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";
import queryExternalAccountOrderPage from "@/constants/api/hxsy-admin/account-management.api";
import BuyActivatedAccount from "@/pages/enterprise-wechat-management/components/buy-activated-account.vue";
import {ORDER_TYPE_TABLE, PAY_STATUS_TABLE} from "@/constants/enum/admin/order.enum";
import OrderDetail from "@/pages/enterprise-wechat-management/components/order-detail.vue";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

export default {
  name: 'OrderManagement',
  components: { CommonTable, BuyActivatedAccount, OrderDetail },
  props: {
    qyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      url: queryExternalAccountOrderPage.queryExternalAccountOrderPage.url,
      showDetail: false,
      orderId: '',
      buyAccountVisible: false,
      companyId: '',
      companyTreeData: [],
      rowData: {},
      params: {
        orderId: '',
        orderType: '',
        payStatus: '',
      },
      enterpriseWechatNickname: this.qyData.corpId || '', // 绑定的企业微信账号
      form: {
        orderId: '',
        orderType: '',
        payStatus: '',
      },
      departmentOptions: [],
      accountStatusOptions: PAY_STATUS_TABLE,
      activeOptions: ORDER_TYPE_TABLE,
      columns: [
        {
          colKey: 'orderId',
          title: '订单ID',
          width: 150,
        },
        {
          colKey: 'orderType',
          title: '订单类型',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            const data = ORDER_TYPE_TABLE;
            return data.find((item) => +item.value === +val)?.label || '';
          },
        },
        {
          colKey: 'payStatus',
          title: '订单状态',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            const data = PAY_STATUS_TABLE;
            return data.find((item) => +item.value === +val)?.label || '';
          },
        },
        {
          colKey: 'price',
          title: '订单金额（元）',
          width: 80,
        },
        {
          colKey: 'externalContactCount',
          title: '互通账号个数',
          width: 100,
        },
        {
          colKey: 'accountDuration',
          title: '账号购买时长（天）',
          width: 100,
        },
        {
          colKey: 'createdAt',
          title: '创建时间',
          width: 150,
        },
        {
          colKey: 'payTime',
          title: '支付时间',
          width: 150,
        },
        {
          colKey: 'op',
          title: '操作',
          width: 150,
          cell: 'op',
          fixed: 'right',
        },
      ],
      activeId: 'ALL',
      parentId: '',
      childrenButton: [], // 子节点按钮
      userOperationTab: [], // 用户可操作按钮
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      },
    };
  },
  computed: {
    search() {
      return search
    },
    operations(){
      return [
        {
          type: 'detail',
          onClick: this.onDetail,
        },
        {
          type: 'getActivationCode',
          onClick: this.onGetActivationCode,
        }
      ]
    },
    tableParams() {
      return {
        corpId: this.enterpriseWechatNickname,
        ...this.params,
      };
    },
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    },
    enterpriseWechatNicknameOptions() {
      const qyData = [this.qyData];
      const optionsData = qyData.map((item) => ({
        label: item.corpName,
        value: item.corpId,
      }));
      return optionsData || [];
    }
  },
  mounted() {
    // 登录后用户具有的菜单已经存入了storage中，从中获取
    this.childrenButton = getMenuChildrenList();
    // console.log("获取到具有按钮菜单：", this.childrenButton);
    if(this.childrenButton && this.childrenButton.length > 0) {
      this.childrenButton.forEach((item) => {
        if (item.menuType && item.menuType === SYSTEM_MENU_CONST.O.value && item.menuUrl) {
          // 截取操作按钮，区分类型
          const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
          this.userOperationTab.push(operationType);
        }
      })
    }
    this.userOperation = getOperationTypeList(this.userOperationTab);
    // this.getEnterpriseWechatNicknameList();
  },
  methods: {
    // async getEnterpriseWechatNicknameList() {
    //   const { code, data } = await this.$request.get(orderAndUserApi.queryQyList.url);
    //   if (code === 0) {
    //     this.enterpriseWechatNicknameOptions = data.map((item) => ({
    //       label: item.corpName,
    //       value: item.corpId,
    //     }));
    //   }
    // },
    addModel() {
      this.buyAccountVisible = true;
    },
    async onDetail(row) {
      const { orderId } = row;
      this.orderId = orderId;
      this.showDetail = true;
    },
    async onGetActivationCode(row) {
      if (row.payStatus !== 0) {
        await this.$message.error('订单支付状态为待支付，才能手动获取');
        return;
      }
      const { orderId } = row;
      const params = {
        orderId
      };
      const { code, msg } = await this.$request.get(orderAndUserApi.getActivationCode.url, { params });
      if (code !== 0) {
        await this.$message.error(msg);
      } else {
        await this.$message.success('获取激活码成功');
      }
    },
    closeDetail() {
      this.orderId = '';
      this.showDetail = false
    },
    successBuyAccount() {
      this.buyAccountVisible = false;
      this.$refs.commonTable.refreshTable();
    },
    onSubmit() {
      this.params.orderId = this.form.orderId;
      this.params.orderType = this.form.orderType;
      this.params.payStatus = this.form.payStatus;
    },
    onReset() {
      this.form.orderId = '';
      this.form.orderType = '';
      this.form.payStatus = '';
      this.params.orderId = this.form.orderId;
      this.params.orderType = this.form.orderType;
      this.params.payStatus = this.form.payStatus;
    }
  },
};
</script>

<style lang="less" scoped>
.task-library {
  position: relative;
  .card-container {
    margin-top: 20px;

    /deep/ .t-card__body {
      display: flex;
      padding: 0;
    }

    .left {
      width: 250px;
      border-right: 1px solid #dcdcdc;

      background: #f9fbff;

      & > p {
        border-bottom: 1px solid #eee;
        margin-bottom: 10px;
        line-height: 30px;
        display: flex;
        justify-content: space-between;
      }

      .model-type {
        margin-top: 10px;
        padding-left: 10px;
      }
    }

    .right {
      flex: 1;
      overflow: hidden;
      .search-input {
        margin: 15px 0;
        .search-btn {
          text-align: right;
          margin-right: 20px;
          margin-top: -35px;
        }
      }
      /deep/ .t-table {
        height: 100%;
        overflow: hidden;

        /deep/ .t-table__content {
          height: 450px;
        }
      }
      &::after {
        content: '';
        clear: both;
        display: block;
      }
    }

    .t-list-item {
      padding: 12px 0;
      background: #f9fbff;
      cursor: pointer;

      &:hover {
        background: #0594fa;
        color: #fff;
      }

      /deep/ .t-list-item-main {
        padding: 0 10px;
      }
    }
  }
}
.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.desc-container {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .desc-container-left {
    display: flex;
    .left-container {
      display: flex;
    }
    .icon-container {
      background: url('@/assets/png/video-icon.png') no-repeat center;
      width: 50px;
      height: 50px;
      background-size: 100% 100%;
      margin: 0 20px;
    }
    .text-container {
      display: flex;
      flex-direction: column;
      .text-title {
        font-size: 18px;
        font-weight: bold;
      }
      .text-context {
        margin-top: 10px;
        font-size: 14px;
        color: #5e5b5b;
      }
    }
  }
}
/deep/ .alert-message {
  margin-bottom: 20px !important;
}
</style>
