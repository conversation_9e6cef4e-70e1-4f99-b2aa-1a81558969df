<template>
  <div class="task-library">
    <div class="desc-container">
      <div class="desc-container-left">
        <div class="left-container">
          <t-alert
            class="alert-message"
            theme="info"
            message="根据腾讯政策要求，服务商需完成：1. 为每位用户开通并激活企微互通账号 2. 保障与企业微信平台的接口调用 3. 确保跨平台数据互通能力"
            :close="true"
          />
        </div>
      </div>
      <div class="add-button">
        <!--   选择绑定的企业微信账号     -->
        <t-select disabled v-model="enterpriseWechatNickname" :options="enterpriseWechatNicknameOptions" placeholder="请选择绑定的企业微信账号"></t-select>
        <t-button @click="addModel">购买激活账号</t-button>
      </div>
    </div>

    <t-card class="card-container">
      <div class="right">
        <div class="search-input">
          <t-form :data="form" ref="formSearch" layout="inline">
            <t-form-item label="姓名">
              <t-input v-model="form.systemUserName" :clearable="true" placeholder="请输入姓名"></t-input>
            </t-form-item>
            <t-form-item label="企业微信昵称">
              <t-input v-model="form.qyName" :clearable="true" placeholder="请输入企业微信昵称"></t-input>
            </t-form-item>
            <t-form-item label="业务账号状态">
              <t-select v-model="form.systemUserStatus" :options="enterpriseWechatStatusOptions" placeholder="请选择业务账号状态"></t-select>
            </t-form-item>
            <t-form-item label="企业微信状态">
              <t-select v-model="form.qyStatus" :options="enterpriseWechatStatusOptions" placeholder="请选择企业微信状态"></t-select>
            </t-form-item>
            <t-form-item label="互通账号状态">
              <t-select v-model="form.accountStatus" :options="unionAccountStatusOptions" placeholder="请选择互通账号状态"></t-select>
            </t-form-item>
          </t-form>
          <div class="search-btn">
            <t-button theme="primary" @click="onSubmit">查询</t-button>
            <t-button theme="primary" ghost @click="onReset">重置</t-button>
          </div>
        </div>

        <t-row class="left-operation-container">
          <t-tooltip content="批量激活企业用户">
            <t-button :disabled="batchAssignDisabled" class="primary-button" @click="onBatchActivate">批量激活</t-button>
          </t-tooltip>
        </t-row>

        <common-table
          ref="commonTable"
          :columns="columns"
          :url="url"
          :isRequest="!!activeId"
          :params="tableParams"
          :operations="operations"
          @getValue="handleGetValue"
        >
        </common-table>
      </div>
    </t-card>
    <buy-activated-account :visible="buyAccountVisible" :corpid="enterpriseWechatNickname" @success="successBuyAccount" @close="buyAccountVisible = false"></buy-activated-account>
    <t-dialog
      :visible="previewVisible"
      header="图片预览"
      :onClose="() => previewVisible = false"
      :confirmBtn="null"
    >
      <img
        :src="previewImageUrl"
        style="max-width: 100%; max-height: 80vh; display: block; margin: 0 auto;"
      />
    </t-dialog>
    <t-dialog :visible="inheritVisible" header="账号继承" :width="'70%'" placement="center" :closeOnOverlayClick="false" @close="onCancelInherit" :footer="false">
      <div style="max-height: 800px; overflow: hidden; display: flex; flex-direction: column;">
        <!-- 原账号信息展示 -->
        <t-card v-if="inheritRow" style="margin-bottom: 16px; flex-shrink: 0;" theme="info">
          <div style="padding: 12px;">
            <h4 style="margin: 0 0 8px 0; color: #0052d9;">原账号信息</h4>
            <div style="display: flex; flex-wrap: wrap; gap: 16px;">
              <div><strong>业务账号名称：</strong>{{ inheritRow.systemUserName || '未设置' }}</div>
              <div><strong>企微昵称：</strong>{{ inheritRow.qyName || '未设置' }}</div>
              <div><strong>激活码：</strong>{{ inheritRow.activeCode || '未设置' }}</div>
              <div><strong>互通账号状态：</strong>
                <t-tag :theme="inheritRow.accountStatus == 1 ? 'success' : 'warning'">
                  {{ inheritRow.accountStatus == 1 ? '已分配' : '未分配' }}
                </t-tag>
              </div>
            </div>
          </div>
        </t-card>

        <p style="margin-bottom: 10px;color: #666; flex-shrink: 0;">请选择继承目标用户（将把该行互通账号继承给所选用户）</p>
        <div style="flex: 1; overflow: hidden;">
          <choose-inherit-users :visible="inheritVisible" :corpId="enterpriseWechatNickname" @onSelectChange="onInheritUsersChange" />
        </div>
        <div style="text-align: center; margin-top: 16px; flex-shrink: 0;">
          <t-button variant="outline" style="margin-right: 8px" @click="onCancelInherit">取消</t-button>
          <t-button theme="primary" :disabled="inheritConfirmDisabled" @click="onConfirmInherit">确认继承</t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script>
import CommonTable from '@/components/common-table/index.vue';
import search from "tdesign-icons-vue/lib/components/search";
import {getMenuChildrenList, getOperationTypeList} from "@/utils/utils";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";
import dayjs from "dayjs";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";
import BuyActivatedAccount from "@/pages/enterprise-wechat-management/components/buy-activated-account.vue";
import ChooseInheritUsers from '@/pages/enterprise-wechat-management/components/choose-inherit-users.vue';

export default {
  name: 'MemberList',
  components: {BuyActivatedAccount, CommonTable, ChooseInheritUsers },
  props: {
    qyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      previewVisible: false,
      previewImageUrl: '',
      url: orderAndUserApi.queryQyAccountList.url,
      shareVisible: false,
      buyAccountVisible: false,
      selectTableRows: [], // 选中的行数据
      companyTreeData: [],
      rowData: {},
      // 账号继承弹窗相关
      inheritVisible: false,
      inheritSelectedUsers: [],
      inheritRow: null,
      params: {
        systemUserName: '',
        qyName: '',
        systemUserStatus: '',
        qyStatus: '',
        accountStatus: '',
      },
      enterpriseWechatNickname: this.qyData.corpId || '',
      form: {
        systemUserName: '',
        qyName: '',
        systemUserStatus: '',
        qyStatus: '',
        accountStatus: '',
      },
      accountStatusOptions: [
        { value: '', label: '未绑定' },
        { value: '1', label: '启用' },
        { value: '2', label: '停用' },
      ],
      enterpriseWechatStatusOptions: [
        { value: 0, label: '无效' },
        { value: 1, label: '有效' },
      ],
      unionAccountStatusOptions: [
        { value: 0, label: '未分配' },
        { value: 1, label: '已分配' },
      ],
      transferStatusOptions: [
        { value: '0', label: '无可转移账号' },
        { value: '1', label: '可转移' },
        { value: '2', label: '冷却期' }
      ],
      presets: {
        '最近7天': [
          dayjs().subtract(6, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '最近3天': [
          dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '今天': [
          dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ]
      },
      activeOptions: [],
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
        },
        {
          colKey: 'qyName',
          title: '用户企业名称',
          width: 150,
        },
        {
          colKey: 'status',
          title: '企业账号状态',
          width: 150,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return +val === 1 ? '有效' : '无效';
          },
        },
        {
          colKey: 'systemUserStatus',
          title: '账号状态',
          width: 150,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return +val === 0 ? '无效' : '有效';
          },
        },
        {
          colKey: 'systemUserName',
          title: '业务账号名称',
          width: 100,
        },
        {
          colKey: 'activeCode',
          title: '激活码',
          width: 100,
        },
        {
          colKey: 'accountStatus',
          title: '互通账号状态',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return +val === 0 ? '未分配' : '已分配';
          },
        },
        {
          colKey: 'qrCode',
          title: '企业微信二维码',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            if (val) {
              return h('img', {
                attrs: {
                  src: val
                },
                style: {
                  width: '60px',
                  height: '60px'
                },
                on: {
                  click: () => this.handlePreviewImage(val) // 添加点击事件
                }
              });
            }
            return null;
          },
        },
        {
          colKey: 'op',
          title: '操作',
          cell: 'op',
          width: 300,
          fixed: 'right',
        },
      ],
      activeId: 'ALL',
      parentId: '',
      childrenButton: [], // 子节点按钮
      userOperationTab: [], // 用户可操作按钮
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      },
    };
  },
  computed: {
    batchAssignDisabled() {
      return this.selectTableRows.length === 0;
    },
    search() {
      return search
    },
    operations(){
      return [
        // 激活互通账号
        {
          type: 'activate',
          onClick: this.onActivate,
        },
        // 生成企微二维码
        {
          type: 'generateCode',
          onClick: this.onGenerateCode,
        },
        // 账号继承
        {
          type: 'inherit',
          onClick: this.onInherit,
        }
      ]
    },
    tableParams() {
      return {
        corpId: this.enterpriseWechatNickname,
        ...this.params,
      };
    },
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    },
    enterpriseWechatNicknameOptions() {
      const qyData = [this.qyData];
      const optionsData = qyData.map((item) => ({
        label: item.corpName,
        value: item.corpId,
      }));
      return optionsData || [];
    },
    inheritConfirmDisabled() {
      return this.inheritSelectedUsers.length === 0;
    }
  },
  mounted() {
    // 登录后用户具有的菜单已经存入了storage中，从中获取
    this.childrenButton = getMenuChildrenList();
    // console.log("获取到具有按钮菜单：", this.childrenButton);
    if(this.childrenButton && this.childrenButton.length > 0) {
      this.childrenButton.forEach((item) => {
        if (item.menuType && item.menuType === SYSTEM_MENU_CONST.O.value && item.menuUrl) {
          // 截取操作按钮，区分类型
          const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
          this.userOperationTab.push(operationType);
        }
      })
    }
    this.userOperation = getOperationTypeList(this.userOperationTab);
    // this.getEnterpriseWechatNicknameList();
  },
  methods: {
    handlePreviewImage(url) {
      this.previewImageUrl = url;
      this.previewVisible = true;
    },
    // async getEnterpriseWechatNicknameList() {
    //   const { code, data } = await this.$request.get(orderAndUserApi.queryQyList.url);
    //   if (code === 0) {
    //     this.enterpriseWechatNicknameOptions = data.map((item) => ({
    //       label: item.corpName,
    //       value: item.corpId,
    //     }));
    //   }
    // },
    addModel() {
      this.buyAccountVisible = true;
    },
    /**
     * @Description: 账号继承
     * <AUTHOR>
     * @date 2025/8/9
     * @time 16:27
     */
    onInherit(row) {
      // 校验原账号是否已激活互通账号
      if (+row.accountStatus !== 1) {
        this.$message.error('该账号未激活互通账号，无法进行账号继承!');
        return;
      }

      // 记录当前行，弹窗选择将要分配的账号
      this.inheritRow = row;
      this.inheritSelectedUsers = [];
      this.inheritVisible = true;
    },
    /**
     * @Description: 生成企微联系人二维码
     * <AUTHOR>
     * @date 2025/7/23
     * @time 23:07
     */
    async onGenerateCode(row) {
      if (+row.accountStatus !== 1) {
        await this.$message.error('该账号未激活互通账号，请先激活互通账号!');
        return;
      }
      if (+row.systemUserStatus !== 1) {
        await this.$message.error('该业务账号未绑定企微或该业务账号已失效!');
        return;
      }
      console.log('生成企微联系人二维码');
      const params = {
        qyUserId: row.qyUserId,
        corpId: row.corpId,
        id: row.id,
      }
      const {code, data, msg} = await this.$request.post(orderAndUserApi.generateQyContactQrCode.url, params);
      if (code === 0) {
        this.$refs.commonTable.refreshTable();
        await this.$message.success('生成企微联系人二维码成功!');
      } else {
        await this.$message.error(`生成企微联系人二维码失败!${msg}`);
      }
    },
    /**
     * @Description: 激活账号
     * <AUTHOR>
     * @date 2025/7/23
     * @time 23:08
     */
    async onActivate(row) {
      if (+row.accountStatus === 1) {
        await this.$message.error('该账号已激活!');
        return;
      }
      const params = {
        corpId: row.corpId,
        users: [row],
      }

      console.log('激活互通账号');
      const {code, data, msg} = await this.$request.post(orderAndUserApi.batchActivateAccount.url, params);
      if (code === 0) {
        this.$refs.commonTable.refreshTable();
        await this.$message.success('激活互通账号成功!');
      } else {
        await this.$message.error(`激活互通账号失败!${msg}`);
      }
    },
    async onBatchActivate() {
      const params = {
        corpId: this.selectTableRows[0].corpId,
        users: this.selectTableRows,
      }
      const res = await this.$request.post(orderAndUserApi.batchActivateAccount.url, params);
      if (res.code === 0) {
        await this.$message.success('批量激活成功!');
        this.selectTableRows = [];
        this.$refs.commonTable.refreshTable();
      } else {
        await this.$message.error(`批量激活失败!${res.msg}`);
      }
    },
    handleGetValue(data) {
      this.selectTableRows = data;
    },
    onInheritUsersChange(val) {
      this.inheritSelectedUsers = val || [];
    },
    onCancelInherit() {
      this.inheritVisible = false;
      this.inheritSelectedUsers = [];
      this.inheritRow = null;
    },
    async onConfirmInherit() {
      if (!this.inheritRow || this.inheritSelectedUsers.length === 0) return;
      const target = this.inheritSelectedUsers[0]; // 只支持单选
      const params = {
        qyUserId: this.inheritRow.qyUserId,
        corpId: this.inheritRow.corpId || this.enterpriseWechatNickname,
        toQyUserId: target.qyUserId,
      };
      try {
        const { code, msg } = await this.$request.post(orderAndUserApi.transferExternalAccount.url, params);
        if (code === 0) {
          await this.$message.success('账号继承成功!');
          this.inheritVisible = false;
          this.inheritSelectedUsers = [];
          this.inheritRow = null;
          this.$refs.commonTable && this.$refs.commonTable.refreshTable();
        } else {
          await this.$message.error(`账号继承失败!${msg || ''}`);
        }
      } catch (e) {
        await this.$message.error('账号继承失败!');
      }
    },
    successBuyAccount() {
      this.buyAccountVisible = false;
      this.$refs.commonTable.refreshTable();
    },
    onSubmit() {
      this.params.systemUserName = this.form.systemUserName;
      this.params.qyName = this.form.qyName;
      this.params.systemUserStatus = this.form.systemUserStatus;
      this.params.qyStatus = this.form.qyStatus;
      this.params.accountStatus = this.form.accountStatus;
    },
    onReset() {
      this.form.systemUserName = '';
      this.form.qyName = '';
      this.form.systemUserStatus = '';
      this.form.qyStatus = '';
      this.form.accountStatus = '';
      this.params.accountStatus = this.form.accountStatus;
      this.params.systemUserStatus = this.form.systemUserStatus;
      this.params.qyStatus = this.form.qyStatus;
      this.params.qyName = this.form.qyName;
      this.params.systemUserName = this.form.systemUserName;
    }
  },
};
</script>

<style lang="less" scoped>
.task-library {
  position: relative;
  .card-container {
    margin-top: 20px;

    /deep/ .t-card__body {
      display: flex;
      padding: 0;
    }

    .left {
      width: 250px;
      border-right: 1px solid #dcdcdc;

      background: #f9fbff;

      & > p {
        border-bottom: 1px solid #eee;
        margin-bottom: 10px;
        line-height: 30px;
        display: flex;
        justify-content: space-between;
      }

      .model-type {
        margin-top: 10px;
        padding-left: 10px;
      }
    }

    .right {
      flex: 1;
      overflow: hidden;
      .search-input {
        margin: 15px 0;
        .search-btn {
          text-align: right;
          margin-right: 20px;
          margin-top: -35px;
        }
      }
      /deep/ .t-table {
        height: 100%;
        overflow: hidden;

        /deep/ .t-table__content {
          height: 450px;
        }
      }
      &::after {
        content: '';
        clear: both;
        display: block;
      }
    }

    .t-list-item {
      padding: 12px 0;
      background: #f9fbff;
      cursor: pointer;

      &:hover {
        background: #0594fa;
        color: #fff;
      }

      /deep/ .t-list-item-main {
        padding: 0 10px;
      }
    }
  }
}
.left-operation-container {
  padding: 10px 0 10px 0;
}
.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.desc-container {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .desc-container-left {
    display: flex;
    .left-container {
      display: flex;
    }
    .icon-container {
      background: url('@/assets/png/video-icon.png') no-repeat center;
      width: 50px;
      height: 50px;
      background-size: 100% 100%;
      margin: 0 20px;
    }
    .text-container {
      display: flex;
      flex-direction: column;
      .text-title {
        font-size: 18px;
        font-weight: bold;
      }
      .text-context {
        margin-top: 10px;
        font-size: 14px;
        color: #5e5b5b;
      }
    }
  }
}
/deep/ .alert-message {
  margin-bottom: 20px !important;
}
</style>
