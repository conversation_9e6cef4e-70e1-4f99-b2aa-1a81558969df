<template>
  <div class="task-library">
    <div class="desc-container">
      <div class="desc-container-left">
        <div class="left-container">
          <t-alert
            class="alert-message"
            theme="info"
            message="根据腾讯政策要求，服务商需完成：1. 为每位用户开通并激活企微互通账号 2. 保障与企业微信平台的接口调用 3. 确保跨平台数据互通能力"
            :close="true"
          />
        </div>
      </div>
      <div class="add-button">
        <!--   选择绑定的企业微信账号     -->
        <t-select disabled v-model="enterpriseWechatNickname" :options="enterpriseWechatNicknameOptions" placeholder="请选择绑定的企业微信账号"></t-select>
        <t-button @click="addModel">购买激活账号</t-button>
      </div>
    </div>

    <t-card class="card-container">
      <div class="right">
        <div class="search-input">
          <t-form :data="form" ref="formSearch" layout="inline">
            <t-form-item label="员工姓名">
              <t-input v-model="form.qyName" :clearable="true" placeholder="请输入姓名"></t-input>
            </t-form-item>
            <t-form-item label="账号状态">
              <t-select v-model="form.accountStatus" :options="accountStatusOptions" placeholder="请选择账号状态"></t-select>
            </t-form-item>
          </t-form>
          <div class="search-btn">
            <t-button theme="primary" @click="onSubmit">查询</t-button>
            <t-button theme="primary" ghost @click="onReset">重置</t-button>
          </div>
        </div>

        <common-table
          ref="commonTable"
          :columns="columns"
          :url="url"
          :isRequest="!!activeId"
          :params="tableParams"
          :operations="operations"
        >
        </common-table>
      </div>
    </t-card>
    <buy-activated-account :visible="buyAccountVisible" :corpid="enterpriseWechatNickname" @success="successBuyAccount" @close="buyAccountVisible = false"></buy-activated-account>
  </div>
</template>

<script>
import CommonTable from '@/components/common-table/index.vue';
import search from "tdesign-icons-vue/lib/components/search";
import {getMenuChildrenList, getOperationTypeList} from "@/utils/utils";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";
import BuyActivatedAccount from "@/pages/enterprise-wechat-management/components/buy-activated-account.vue";

export default {
  name: 'ActivationAccountManagement',
  components: {BuyActivatedAccount, CommonTable },
  props: {
    qyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      url: orderAndUserApi.queryAccountList.url,
      shareVisible: false,
      buyAccountVisible: false,
      companyId: '',
      companyTreeData: [],
      rowData: {},
      params: {
        qyName: '',
        accountStatus: '',
      },
      enterpriseWechatNickname: this.qyData.corpId || '',
      form: {
        qyName: '',
        accountStatus: '',
      },
      accountStatusOptions: [
        { value: 0, label: '未绑定' },
        { value: 1, label: '已绑定' },
      ],
      activeOptions: [],
      columns: [
        {
          colKey: 'activeCode',
          title: '账号激活码',
          width: 150,
        },
        {
          colKey: 'accountType',
          title: '账号类型',
          width: 150,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return +val === 1 ? '基础账号' : '互通账号';
          }
        },
        {
          colKey: 'accountStatus',
          title: '账号状态',
          width: 100,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return +val === 0 ? '未绑定' : '已绑定';
          }
        },
        {
          colKey: 'qyUserId',
          title: '激活绑定员工userId（已加密）',
          width: 80,
        },
        {
          colKey: 'qyName',
          title: '激活绑定员工（企微昵称）',
          width: 120,
        },
        {
          colKey: 'activeTime',
          title: '激活时间',
          width: 100,
        },
        {
          colKey: 'serviceStartTime',
          title: '服务开始时间',
          width: 100,
        },
        {
          colKey: 'serviceExpireTime',
          title: '服务到期时间',
          width: 100,
        },
      ],
      activeId: 'ALL',
      parentId: '',
      childrenButton: [], // 子节点按钮
      userOperationTab: [], // 用户可操作按钮
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      },
    };
  },
  computed: {
    search() {
      return search
    },
    operations(){
      return []
    },
    tableParams() {
      return {
        corpId: this.enterpriseWechatNickname,
        ...this.params,
      };
    },
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    },
    enterpriseWechatNicknameOptions() {
      const qyData = [this.qyData];
      const optionsData = qyData.map((item) => ({
        label: item.corpName,
        value: item.corpId,
      }));
      return optionsData || [];
    },
  },
  mounted() {
    // 登录后用户具有的菜单已经存入了storage中，从中获取
    this.childrenButton = getMenuChildrenList();
    if(this.childrenButton && this.childrenButton.length > 0) {
      this.childrenButton.forEach((item) => {
        if (item.menuType && item.menuType === SYSTEM_MENU_CONST.O.value && item.menuUrl) {
          // 截取操作按钮，区分类型
          const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
          this.userOperationTab.push(operationType);
        }
      })
    }
    this.userOperation = getOperationTypeList(this.userOperationTab)
    // this.getEnterpriseWechatNicknameList();
  },
  methods: {
    // async getEnterpriseWechatNicknameList() {
    //   const { code, data } = await this.$request.get(orderAndUserApi.queryQyList.url);
    //   if (code === 0) {
    //     this.enterpriseWechatNicknameOptions = data.map((item) => ({
    //       label: item.corpName,
    //       value: item.corpId,
    //     }));
    //   }
    // },
    successBuyAccount() {
      this.buyAccountVisible = false;
      this.$refs.commonTable.refreshTable();
    },
    addModel() {
      this.buyAccountVisible = true;
    },
    onSubmit() {
      this.params.qyName = this.form.qyName;
      this.params.accountStatus = this.form.accountStatus;
    },
    onReset() {
      this.form.qyName = "";
      this.form.accountStatus = "";
      this.params.qyName = this.form.qyName;
      this.params.accountStatus = this.form.accountStatus;
    }
  },
};
</script>

<style lang="less" scoped>
.task-library {
  position: relative;
  .card-container {
    margin-top: 20px;

    /deep/ .t-card__body {
      display: flex;
      padding: 0;
    }

    .left {
      width: 250px;
      border-right: 1px solid #dcdcdc;

      background: #f9fbff;

      & > p {
        border-bottom: 1px solid #eee;
        margin-bottom: 10px;
        line-height: 30px;
        display: flex;
        justify-content: space-between;
      }

      .model-type {
        margin-top: 10px;
        padding-left: 10px;
      }
    }

    .right {
      flex: 1;
      overflow: hidden;
      .search-input {
        margin: 15px 0;
        .search-btn {
          text-align: right;
          margin-right: 20px;
          margin-top: -35px;
        }
      }
      /deep/ .t-table {
        height: 100%;
        overflow: hidden;

        /deep/ .t-table__content {
          height: 450px;
        }
      }
      &::after {
        content: '';
        clear: both;
        display: block;
      }
    }

    .t-list-item {
      padding: 12px 0;
      background: #f9fbff;
      cursor: pointer;

      &:hover {
        background: #0594fa;
        color: #fff;
      }

      /deep/ .t-list-item-main {
        padding: 0 10px;
      }
    }
  }
}
.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.desc-container {
  display: flex;
  align-content: center;
  justify-content: space-between;
  .desc-container-left {
    display: flex;
    .left-container {
      display: flex;
    }
    .icon-container {
      background: url('@/assets/png/video-icon.png') no-repeat center;
      width: 50px;
      height: 50px;
      background-size: 100% 100%;
      margin: 0 20px;
    }
    .text-container {
      display: flex;
      flex-direction: column;
      .text-title {
        font-size: 18px;
        font-weight: bold;
      }
      .text-context {
        margin-top: 10px;
        font-size: 14px;
        color: #5e5b5b;
      }
    }
  }
}
/deep/ .alert-message {
  margin-bottom: 20px !important;
}
</style>
