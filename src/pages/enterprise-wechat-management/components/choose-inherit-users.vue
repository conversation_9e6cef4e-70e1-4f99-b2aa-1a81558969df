<template>
  <div class="choose-inherit-users">
    <div class="search-section">
      <t-form :data="searchForm" layout="inline" style="margin-bottom: 16px;">
        <t-form-item label="业务名称">
          <t-input v-model="searchForm.systemUserName" placeholder="请输入业务名称" clearable />
        </t-form-item>
        <t-form-item label="企微名称">
          <t-input v-model="searchForm.qyName" placeholder="请输入企微名称" clearable />
        </t-form-item>
        <t-form-item style="margin-left: 20px !important;">
          <t-button theme="primary" @click="handleSearch">查询</t-button>
          <t-button theme="default" @click="handleReset" style="margin-left: 8px;">重置</t-button>
        </t-form-item>
      </t-form>
    </div>

    <t-table
      :columns="columns"
      :data="userList"
      :loading="loading"
      :pagination="pagination"
      :selected-row-keys="selectedKeys"
      row-key="qyUserId"
      @select-change="onSelectChange"
      @page-change="handlePageChange"
      max-height="300px"
      bordered
      stripe
    />
  </div>
</template>

<script>
import orderAndUserApi from '@/constants/api/hxsy-admin/account-management.api';

export default {
  name: 'ChooseInheritUsers',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    corpId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      userList: [],
      selectedKeys: [],
      searchForm: {
        systemUserName: '',
        qyName: '',
        systemUserStatus: '',
        qyStatus: ''
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      columns: [
        {
          colKey: 'row-select',
          type: 'single',
          width: 64,
          fixed: 'left',
        },
        {
          colKey: 'systemUserName',
          title: '业务账号名称',
          width: 120,
        },
        {
          colKey: 'qyName',
          title: '企微名称',
          width: 120,
        },
        {
          colKey: 'activeCode',
          title: '激活码',
          width: 100,
        },
        {
          colKey: 'accountStatus',
          title: '互通账号状态',
          width: 120,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return h('t-tag', {
              props: {
                theme: +val === 1 ? 'success' : 'warning'
              }
            }, +val === 1 ? '已分配' : '未分配');
          },
        },
        {
          colKey: 'systemUserStatus',
          title: '业务账号状态',
          width: 120,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return +val === 1 ? '有效' : '无效';
          },
        },
        {
          colKey: 'status',
          title: '企微账号状态',
          width: 120,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return +val === 1 ? '有效' : '无效';
          },
        },
        {
          colKey: 'serviceExpireTime',
          title: '服务过期时间',
          width: 160,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return val ? new Date(val).toLocaleString() : '-';
          },
        }
      ]
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.handleSearch();
      }
    }
  },
  methods: {
    async fetchUserList() {
      if (!this.corpId) return;

      this.loading = true;
      try {
        const params = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          corpId: this.corpId,
          ...this.searchForm
        };

        const { code, data, msg } = await this.$request.get(orderAndUserApi.queryInactiveAndExpiredUsers.url, { params });

        if (code === 0) {
          this.userList = data.records || [];
          this.pagination.total = data.total || 0;
        } else {
          this.$message.error(msg || '查询用户列表失败');
        }
      } catch (error) {
        this.$message.error('查询用户列表失败');
      } finally {
        this.loading = false;
      }
    },

    handleSearch() {
      this.pagination.current = 1;
      this.fetchUserList();
    },

    handleReset() {
      this.searchForm = {
        systemUserName: '',
        qyName: '',
        systemUserStatus: '',
        qyStatus: ''
      };
      this.selectedKeys = [];
      this.handleSearch();
    },

    handlePageChange(pageInfo) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
      this.fetchUserList();
    },

    onSelectChange(selectedRowKeys, { selectedRowData }) {
      this.selectedKeys = selectedRowKeys;
      this.$emit('onSelectChange', selectedRowData);
    }
  }
};
</script>

<style lang="less" scoped>
.choose-inherit-users {
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .search-section {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  /deep/ .t-table {
    flex: 1;
    overflow: hidden;

    .t-table__content {
      max-height: 600px;
      overflow-y: auto;
    }
  }
}
</style>
