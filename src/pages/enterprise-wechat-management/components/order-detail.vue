<template>
  <div>
    <t-dialog
      width="35%"
      header="订单详情"
      :closeOnOverlayClick="false"
      :visible="modalVisible"
      @close="closeModal"
      class="edit-task-dialog"
      :confirmBtn="null"
      :cancelBtn="null"
    >
      <div>
        <t-table :bordered="true" rowKey="id" :data="currentPageData" :columns="columns" :pagination="pagination" @page-change="handlePageChange"></t-table>
      </div>
    </t-dialog>
  </div>
</template>
<script lang="ts">
import queryExternalAccountOrderPage from "@/constants/api/hxsy-admin/account-management.api";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

export default {
  name: "OrderDetail",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      currentPageData: [],
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },
      modalVisible: false,
      columns: [
        {
          colKey: 'activeCode',
          title: '激活码',
          width: 150,
        },
      ],
    };
  },
  computed: {
    tableParams() {
      return {};
    },
    operations() {
      return [];
    },
  },
  watch: {
    visible(val) {
      this.modalVisible = val;
    },
    orderId(val) {
      if (val) {
        this.getDetailData();
      }
    },
  },
  methods: {
    handlePageChange(pageInfo: any) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
      this.getDetailData();
    },
    closeModal() {
      this.modalVisible = false;
      this.$emit("close");
    },
    async getDetailData() {
      const res = await this.$request.get(orderAndUserApi.queryActivationCode.url + this.orderId);
      if (res.code !== 0) {
        this.$message.error(res.msg);
      } else {
        this.currentPageData = res.data;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.search-input {
  margin: 15px 0;
  .search-btn {
    text-align: right;
    margin-right: 20px;
    margin-top: -35px;
  }
}
</style>
