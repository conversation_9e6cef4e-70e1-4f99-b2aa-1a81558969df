<template>
  <div>
    <t-card class="header-card">
      <div class="header-info">
        <h3>企业微信标签管理</h3>
        <p>企业名称：{{ corpName }}</p>
        <p class="tag-stats">共 {{ tagGroups.length }} 个标签组，{{ totalTags }} 个标签</p>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="syncAllTags" :loading="syncAllLoading">同步所有标签</t-button>
        <t-button theme="success" @click="showAddTagGroupDialog">新增标签组</t-button>
        <t-button theme="warning" @click="showAddTagGroupByCampDialog" v-if="isAdmin">按营期添加标签组</t-button>
        <t-button theme="warning" @click="showAddTagGroupByCourseDialog" v-if="isAdmin">按课程添加标签组</t-button>
        <t-button theme="default" @click="showSyncRecordsDialog">查看同步记录</t-button>
        <t-button theme="default" @click="fetchTagGroups" :loading="loading" class="refresh-button">
          <t-icon name="refresh" />
          <span>刷新</span>
        </t-button>
      </div>
    </t-card>

    <t-loading :loading="loading" style="width: 100%">
      <div class="tag-groups-container">
        <template v-if="tagGroups.length > 0">
          <t-card v-for="group in tagGroups" :key="group.groupId" class="tag-group-card">
            <div class="tag-group-header">
              <div class="tag-group-info">
                <h4>{{ group.name }}</h4>
                <p class="tag-group-id">ID: {{ group.groupId }}</p>
                <p class="tag-count">标签数量: {{ group.tags ? group.tags.length : 0 }}</p>
              </div>
              <div class="tag-group-actions">
                <t-button theme="primary" size="small" @click="showAddTagDialog(group)" style="margin-right: 8px">
                  新增标签
                </t-button>
                <t-button theme="primary" size="small" @click="syncGroupTags(group.groupId)" :loading="syncingGroups[group.groupId]">
                  同步此标签组
                </t-button>
                <t-button theme="warning" size="small" variant="outline" @click="showEditTagGroupDialog(group)" style="margin-left: 8px">
                  编辑
                </t-button>
                <t-popconfirm content="确定要删除此标签组吗？" @confirm="() => deleteTagGroup(group)">
                  <t-button theme="danger" size="small" variant="outline" style="margin-left: 8px">
                    删除
                  </t-button>
                </t-popconfirm>
                <t-button
                  theme="default"
                  size="small"
                  variant="text"
                  @click="toggleTagGroupExpand(group.groupId)"
                  style="margin-left: 8px"
                >
                  {{ expandedGroups[group.groupId] ? '收起' : '展开' }}
                  <t-icon :name="expandedGroups[group.groupId] ? 'chevron-up' : 'chevron-down'" />
                </t-button>
              </div>
            </div>
            <div v-if="expandedGroups[group.groupId]" class="tag-list-container">
              <t-input
                v-model="tagSearchText[group.groupId]"
                placeholder="搜索标签"
                clearable
                style="margin-bottom: 12px; width: 200px;"
                @input="(value) => handleSearchInput(value, group.groupId)"
                @clear="() => handleSearchClear(group.groupId)"
              />
              <div class="tag-list">
                <template v-if="group.tags && group.tags.length > 0">
                  <!-- 分页显示标签 -->
                  <div class="tag-pagination-container">
                    <div class="tag-page-info">
                      显示 {{ paginatedTags(group).length }} / {{ filteredTags(group).length }} 个标签
                      <t-pagination
                        v-if="filteredTags(group).length > pageSize"
                        :total="filteredTags(group).length"
                        :page-size="pageSize"
                        :current="tagPagination[group.groupId] || 1"
                        @change="(pageInfo) => handleTagPageChange(pageInfo, group.groupId)"
                        size="small"
                        :show-total="false"
                        :show-page-size-options="false"
                        :key="`pagination-${group.groupId}-${tagSearchText[group.groupId] || ''}-${tagPagination[group.groupId] || 1}`"
                      />
                    </div>
                    <div class="paginated-tags" v-if="paginatedTags(group).length > 0">
                      <t-tag
                        v-for="tag in paginatedTags(group)"
                        :key="tag.tagId"
                        theme="primary"
                        variant="light"
                        class="tag-item"
                      >
                        {{ tag.name }}
                        <t-icon name="edit" size="12px" @click="() => showEditTagDialog(tag, group)" class="edit-icon" />
                        <t-popconfirm content="确定要删除此标签吗？" @confirm="() => deleteTag(tag, group)">
                          <t-icon name="close" class="close-icon" />
                        </t-popconfirm>
                      </t-tag>
                    </div>
                    <div v-else-if="tagSearchText[group.groupId] && tagSearchText[group.groupId].trim()" class="no-search-results">
                      没有找到匹配的标签
                    </div>
                  </div>
                </template>
                <div v-else class="no-tags">
                  暂无标签
                </div>
              </div>
            </div>
          </t-card>
        </template>
        <t-empty v-else description="暂无标签组数据" />
      </div>
    </t-loading>

    <!-- 新增/编辑标签组对话框 -->
    <t-dialog
      :visible="tagGroupDialog.visible"
      :header="tagGroupDialog.isEdit ? '编辑标签组' : '新增标签组'"
      :on-confirm="confirmTagGroupDialog"
      :on-close="closeTagGroupDialog"
      :confirm-btn="{ content: tagGroupDialog.isEdit ? '保存' : '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default' }"
    >
      <t-form :data="tagGroupForm" ref="tagGroupForm" :rules="tagGroupRules">
        <t-form-item label="标签组名称" name="name">
          <t-input v-model="tagGroupForm.name" placeholder="请输入标签组名称" />
        </t-form-item>

        <t-form-item label="标签列表" name="tags" v-if="!tagGroupDialog.isEdit">
          <div class="tag-inputs-container">
            <div v-for="(tag, index) in tagGroupForm.tags" :key="index" class="tag-input-row">
              <t-input v-model="tag.name" placeholder="请输入标签名称" style="flex: 1; margin-right: 8px;" />
              <t-button theme="danger" variant="text" @click="removeTagFromForm(index)">
                <t-icon name="close" />
              </t-button>
            </div>
          </div>
          <t-button theme="primary" variant="outline" size="small" @click="addTagToForm" style="margin-top: 8px;">
            <add-icon slot="icon" />
            添加标签
          </t-button>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 新增/编辑标签对话框 -->
    <t-dialog
      :visible="tagDialog.visible"
      :header="tagDialog.isEdit ? '编辑标签' : '新增标签'"
      :on-confirm="confirmTagDialog"
      :on-close="closeTagDialog"
      :confirm-btn="{ content: tagDialog.isEdit ? '保存' : '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default' }"
    >
      <t-form :data="tagForm" ref="tagForm" :rules="tagRules">
        <t-form-item label="标签名称" name="name">
          <t-input v-model="tagForm.name" placeholder="请输入标签名称" />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 按营期添加标签组对话框 -->
    <t-dialog
      :visible="campTagGroupDialog.visible"
      header="按营期添加标签组"
      :on-confirm="confirmCampTagGroupDialog"
      :on-close="closeCampTagGroupDialog"
      :confirm-btn="{ content: '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default' }"
      width="700px"
    >
      <t-form :data="campTagGroupForm" ref="campTagGroupForm" :rules="campTagGroupRules">
        <t-form-item label="标签组名称" name="groupName">
          <t-input v-model="campTagGroupForm.groupName" placeholder="请输入标签组名称" />
        </t-form-item>

        <t-form-item label="选择公司" name="companyId">
          <t-tree-select
            v-model="campTagGroupForm.companyId"
            :data="companyTreeData"
            :treeProps="{
              checkStrictly: true,
            }"
            filterable
            placeholder="请选择公司"
            clearable
            @change="handleCompanyChange"
          />
        </t-form-item>

        <t-form-item label="营期信息" name="campPeriods">
          <div v-if="campTagGroupForm.campPeriods.length > 0" class="camp-periods-info">
            <p>已找到 {{ campTagGroupForm.campPeriods.length }} 个营期，将自动添加为标签</p>
            <div class="camp-periods-preview">
              <t-tag
                v-for="(camp, index) in campTagGroupForm.campPeriods.slice(0, 5)"
                :key="camp.id"
                theme="primary"
                variant="light"
                class="preview-tag"
              >
                {{ camp.campperiodName }}
              </t-tag>
              <span v-if="campTagGroupForm.campPeriods.length > 5" class="more-tag">
                等 {{ campTagGroupForm.campPeriods.length }} 个营期
              </span>
            </div>
          </div>
          <t-empty v-else description="请先选择公司" />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 按课程添加标签组对话框 -->
    <t-dialog
      :visible="courseTagGroupDialog.visible"
      header="按课程添加标签组"
      :on-confirm="confirmCourseTagGroupDialog"
      :on-close="closeCourseTagGroupDialog"
      :confirm-btn="{ content: '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消', theme: 'default' }"
      width="700px"
    >
      <t-form :data="courseTagGroupForm" ref="courseTagGroupForm" :rules="courseTagGroupRules">
        <t-form-item label="标签组名称" name="groupName">
          <t-input v-model="courseTagGroupForm.groupName" placeholder="请输入标签组名称" />
        </t-form-item>

        <t-form-item label="选择课程分组" name="courseGroupIds">
          <t-select
            v-model="courseTagGroupForm.courseGroupIds"
            :options="courseGroupOptions"
            placeholder="请选择课程分组"
            clearable
            multiple
            @change="handleCourseGroupChange"
          />
        </t-form-item>

        <t-form-item label="课程信息" name="courses">
          <div v-if="courseTagGroupForm.courses.length > 0" class="camp-periods-info">
            <p>已找到 {{ courseTagGroupForm.courses.length }} 个课程，将为每个课程生成"已到课"和"已完课"标签</p>
            <div class="camp-periods-preview">
              <t-tag
                v-for="(course, index) in courseTagGroupForm.courses.slice(0, 3)"
                :key="course.id"
                theme="primary"
                variant="light"
                class="preview-tag"
              >
                {{ course.courseName }}
              </t-tag>
              <span v-if="courseTagGroupForm.courses.length > 3" class="more-tag">
                等 {{ courseTagGroupForm.courses.length }} 个课程
              </span>
            </div>
            <div class="tag-example">
              <p>将生成的标签示例：</p>
              <div class="tag-examples-preview" v-if="courseTagGroupForm.courses.length > 0">
                <t-tag theme="success" variant="light" class="preview-tag">
                  {{ courseTagGroupForm.courses[0].courseName }}已到课
                </t-tag>
                <t-tag theme="warning" variant="light" class="preview-tag">
                  {{ courseTagGroupForm.courses[0].courseName }}已完课
                </t-tag>
              </div>
            </div>
          </div>
          <t-empty v-else description="请先选择课程分组" />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 同步记录弹窗 -->
    <t-dialog
      :visible="syncRecordsDialog.visible"
      header="标签同步记录"
      :on-close="closeSyncRecordsDialog"
      width="1200px"
      :footer="false"
    >
      <div class="sync-records-container">
        <div class="sync-records-filter">
          <t-form layout="inline">
            <t-form-item label="同步类型">
              <t-select v-model="syncRecordsFilter.syncType" clearable placeholder="全部" :options="syncTypeOptions" />
            </t-form-item>
            <t-form-item label="同步状态">
              <t-select v-model="syncRecordsFilter.status" clearable placeholder="全部" :options="statusOptions" />
            </t-form-item>
            <t-form-item>
              <t-button theme="primary" @click="fetchSyncRecords(1)">查询</t-button>
              <t-button theme="default" @click="resetSyncRecordsFilter" style="margin-left: 8px">重置</t-button>
            </t-form-item>
          </t-form>
        </div>

        <t-table
          :data="syncRecords"
          :columns="syncRecordsColumns"
          :loading="syncRecordsLoading"
          :pagination="syncRecordsPagination"
          rowKey="id"
          @page-change="onSyncRecordsPageChange"
          @page-size-change="onSyncRecordsPageSizeChange"
          stripe
          bordered
        />
      </div>
    </t-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { MessagePlugin, Dialog } from 'tdesign-vue';
import customerWecomTagsApi from '@/constants/api/hxsy-admin/customer-wecom-tags.api';
import sysCompanyGroupApi from '@/constants/api/back/sys-company.api';
import sysCampGroupApi from '@/constants/api/back/sys-camp-group.api';
import sysCourseGroupApi from '@/constants/api/back/sys-course-group.api';
import { AddIcon } from 'tdesign-icons-vue';

interface TagInfo {
  id: number;
  tagId: string;
  name: string;
  groupId: string;
  corpId: string;
  createdAt: string;
  updatedAt: string;
}

interface WecomTagSyncRecord {
  id: string;
  corpId: string;
  syncType?: number; // 1-全量同步，2-指定标签组同步
  status?: number; // 0-进行中，1-成功，2-失败
  createdAt: string;
  updatedAt: string;
  groupIds?: string | null; // 可能包含的标签组ID列表
  errorMsg?: string | null; // 失败原因
  remark?: string | null;
  createdBy?: string | null;
  updatedBy?: string | null;
  startTime?: string;
  endTime?: string;
  groupCount?: number;
  tagCount?: number;
}

interface TagGroup {
  id: number;
  groupId: string;
  name: string;
  corpId: string;
  createdAt: string;
  updatedAt: string;
  tags: TagInfo[];
}

interface TagFormItem {
  name: string;
  order?: number;
}

export default Vue.extend({
  name: 'EnterpriseWechatTagManagement',
  components: {
    AddIcon,
  },
  data() {
    return {
      corpId: '',
      corpName: '',
      loading: false,
      syncAllLoading: false,
      syncingGroups: {} as Record<string, boolean>,
      tagGroups: [] as TagGroup[],
      expandedGroups: {} as Record<string, boolean>,
      tagSearchText: {} as Record<string, string>,
      tagPagination: {} as Record<string, number>, // 存储每个标签组的当前页码
      pageSize: 50, // 每页显示的标签数量
      // 按营期添加标签组相关
      campTagGroupDialog: {
        visible: false,
        loading: false
      },
      campTagGroupForm: {
        groupName: '',
        companyId: '',
        campPeriods: []
      },
      campTagGroupRules: {
        groupName: [{ required: true, message: '请输入标签组名称', trigger: 'blur' }],
        companyId: [{ required: true, message: '请选择公司', trigger: 'change' }],
        campPeriods: [{
          validator: (val) => {
            if (!val || val.length === 0) {
              return { result: false, message: '请选择一个有营期数据的公司', type: 'error' };
            }
            return { result: true };
          },
          trigger: 'change'
        }]
      },
      companyTreeData: [],
      // 按课程添加标签组相关
      courseTagGroupDialog: {
        visible: false,
        loading: false
      },
      courseTagGroupForm: {
        groupName: '',
        courseGroupIds: [],
        courses: []
      },
      courseGroupOptions: [],
      courseTagGroupRules: {
        groupName: [{ required: true, message: '请输入标签组名称', trigger: 'blur' }],
        courseGroupIds: [{ required: true, message: '请选择课程分组', trigger: 'change' }],
        courses: [{
          validator: (val) => {
            if (!val || val.length === 0) {
              return { result: false, message: '请选择有课程的分组', type: 'error' };
            }
            return { result: true };
          },
          trigger: 'change'
        }]
      },
      // 标签组对话框相关
      tagGroupDialog: {
        visible: false,
        isEdit: false,
        currentGroup: null as TagGroup | null,
      },
      tagGroupForm: {
        name: '',
        tags: [{ name: '' }] as TagFormItem[],
      },
      tagGroupRules: {
        name: [{ required: true, message: '请输入标签组名称', trigger: 'blur' }],
        tags: [
          {
            validator: (val: TagFormItem[]) => {
              if (val.length === 0) {
                return { result: false, message: '至少需要一个标签', type: 'error' };
              }
              for (const tag of val) {
                if (!tag.name.trim()) {
                  return { result: false, message: '标签名称不能为空', type: 'error' };
                }
              }
              return { result: true };
            },
            trigger: 'blur',
          },
        ],
      },
      // 标签对话框相关
      tagDialog: {
        visible: false,
        isEdit: false,
        currentTag: null as TagInfo | null,
        currentGroup: null as TagGroup | null,
      },
      tagForm: {
        name: '',
      },
      tagRules: {
        name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
      },
      // 同步记录对话框相关
      syncRecordsDialog: {
        visible: false,
      },
      syncRecordsLoading: false,
      syncRecords: [] as WecomTagSyncRecord[],
      syncRecordsFilter: {
        syncType: null as number | null,
        status: null as number | null,
      },
      syncTypeOptions: [
        { value: 1, label: '全量同步' },
        { value: 2, label: '指定标签组同步' }
      ],
      statusOptions: [
        { value: 0, label: '进行中' },
        { value: 1, label: '成功' },
        { value: 2, label: '失败' }
      ],
      syncRecordsPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showJumper: true,
        showPageSize: true,
        pageSizeOptions: [10, 20, 50],
      },
      syncRecordsColumns: [
        {
          colKey: 'id',
          title: 'ID',
          width: 80,
        },
        {
          colKey: 'syncType',
          title: '同步类型',
          width: 100,
          cell: (h, { row }) => {
            const map = {
              1: { label: '全量同步', theme: 'primary' },
              2: { label: '指定标签组同步', theme: 'warning' }
            };
            const item = map[row?.syncType] || { label: '--', theme: 'default' };
            return h('t-tag', { props: { theme: item.theme, variant: 'light' } }, item.label);
          }
        },
        {
          colKey: 'status',
          title: '同步状态',
          width: 100,
          cell: (h, { row }) => {
            const map = {
              0: { label: '进行中', theme: 'primary' },
              1: { label: '成功', theme: 'success' },
              2: { label: '失败', theme: 'danger' }
            };
            const item = map[row?.status] || { label: '--', theme: 'default' };
            return h('t-tag', { props: { theme: item.theme, variant: 'light' } }, item.label);
          }
        },
        {
          colKey: 'groupCount',
          title: '同步标签组数量',
          width: 80,
        },
        {
          colKey: 'createdBy',
          title: '操作人',
          width: 100,
        },
        {
          colKey: 'createdAt',
          title: '操作时间',
          width: 120,
        },
        {
          colKey: 'errorMsg',
          title: '失败原因',
          width: 80,
          cell: ({ row }: { row: WecomTagSyncRecord }) => {
            if (!row) return '-';
            return row?.status === 2 && row?.errorMsg ? row.errorMsg : '-';
          },
        },
      ],
    };
  },
  computed: {
    totalTags(): number {
      return this.tagGroups.reduce((total, group) => total + (group.tags ? group.tags.length : 0), 0);
    },
    isAdmin(): boolean {
      // 获取当前用户信息
      const userStr = window.localStorage.getItem('core:user');
      if (!userStr) return false;

      try {
        const user = JSON.parse(userStr);
        // roleType: 1-超管, 2-管理员
        return user && (user.roleType === 1 || user.roleType === 2);
      } catch (e) {
        console.error('解析用户信息失败:', e);
        return false;
      }
    }
  },
  watch: {
    // 监听搜索文本变化，重置分页状态
    tagSearchText: {
      handler(newVal, oldVal) {
        console.log('搜索文本变化监听器触发', { newVal, oldVal });
        // 找出变化的groupId
        Object.keys(newVal || {}).forEach(groupId => {
          if (newVal[groupId] !== oldVal?.[groupId]) {
            const currentPage = this.tagPagination[groupId] || 1;
            console.log(`搜索文本变化: 组 ${groupId}, 新值: "${newVal[groupId]}", 旧值: "${oldVal?.[groupId] || ''}", 当前页: ${currentPage}`);

            // 如果不在第一页，强制重置到第一页
            if (currentPage !== 1) {
              console.log(`强制重置页码: 组 ${groupId} 从第 ${currentPage} 页重置到第 1 页`);
              this.$set(this.tagPagination, groupId, 1);
            }
          }
        });
      },
      deep: true,
      immediate: false
    }
  },
  created() {
    this.corpId = this.$route.query.corpId as string;
    this.corpName = this.$route.query.corpName as string;

    console.log("corpId:", this.corpId)
    if (!this.corpId) {
      MessagePlugin.error('缺少企业ID参数');
      this.goBack();
      return;
    }

    this.fetchTagGroups();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },

    // 切换标签组的展开/收起状态
    toggleTagGroupExpand(groupId) {
      this.$set(this.expandedGroups, groupId, !this.expandedGroups[groupId]);

      // 如果是首次展开，初始化搜索文本和分页
      if (this.expandedGroups[groupId]) {
        if (!this.tagSearchText[groupId]) {
          this.$set(this.tagSearchText, groupId, '');
        }
        if (!this.tagPagination[groupId]) {
          this.$set(this.tagPagination, groupId, 1);
        }
      }
    },

    // 获取当前页的标签
    paginatedTags(group) {
      const filtered = this.filteredTags(group);
      let currentPage = this.tagPagination[group.groupId] || 1;

      // 如果当前页超出了过滤结果的范围，重置到第一页
      const maxPage = Math.ceil(filtered.length / this.pageSize) || 1;
      if (currentPage > maxPage) {
        console.log(`页码超出范围: 组 ${group.groupId}, 当前页 ${currentPage}, 最大页 ${maxPage}, 重置到第1页`);
        currentPage = 1;
        this.$set(this.tagPagination, group.groupId, 1);
      }

      const start = (currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      const result = filtered.slice(start, end);

      // 添加调试日志
      console.log(`分页计算: 组 ${group.groupId}, 当前页 ${currentPage}, 过滤后总数 ${filtered.length}, 显示 ${start}-${end}, 结果数量 ${result.length}`);

      return result;
    },

    // 确保搜索时重置分页
    ensureSearchPagination(groupId) {
      const currentPage = this.tagPagination[groupId] || 1;
      if (currentPage !== 1) {
        console.log(`搜索时重置分页: 组 ${groupId} 从第 ${currentPage} 页重置到第 1 页`);
        this.$set(this.tagPagination, groupId, 1);
        return true; // 表示进行了重置
      }
      return false; // 表示没有重置
    },

    // 处理标签页码变化
    handleTagPageChange(pageInfo, groupId) {
      // TDesign 分页组件的 change 事件传递的是包含 current 和 pageSize 的对象
      let currentPage;
      if (typeof pageInfo === 'object' && pageInfo !== null) {
        currentPage = pageInfo.current || 1;
      } else {
        currentPage = pageInfo || 1;
      }

      console.log(`页码变化: 组 ${groupId} 切换到第 ${currentPage} 页`, pageInfo);

      // 使用 Vue.set 确保响应式更新
      this.$set(this.tagPagination, groupId, currentPage);

      // 验证分页数据
      this.$nextTick(() => {
        const group = this.tagGroups.find(g => g.groupId === groupId);
        if (group) {
          const tags = this.paginatedTags(group);
          console.log(`第 ${currentPage} 页实际显示 ${tags.length} 个标签`);
        }
      });
    },

    // 处理搜索输入
    handleSearchInput(value, groupId) {
      console.log(`搜索输入: 组 ${groupId}, 输入值: "${value}", 当前页码: ${this.tagPagination[groupId] || 1}`);

      // 确保搜索时重置分页
      this.ensureSearchPagination(groupId);

      // 更新搜索文本
      this.$set(this.tagSearchText, groupId, value);

      // 确保在下一个事件循环中更新视图
      this.$nextTick(() => {
        // 再次确保分页正确
        this.ensureSearchPagination(groupId);

        // 验证搜索结果
        const group = this.tagGroups.find(g => g.groupId === groupId);
        if (group) {
          const filtered = this.filteredTags(group);
          const paginated = this.paginatedTags(group);
          console.log(`搜索后: 过滤结果 ${filtered.length} 个，当前页显示 ${paginated.length} 个，页码: ${this.tagPagination[groupId]}`);
        }

        // 强制更新视图
        this.$forceUpdate();
      });
    },

    // 处理搜索清除
    handleSearchClear(groupId : string) {
      console.log(`搜索清除: 组 ${groupId}`);

      // 重置分页和搜索文本
      this.$set(this.tagPagination, groupId, 1);
      this.$set(this.tagSearchText, groupId, '');

      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    // 根据搜索文本过滤标签
    filteredTags(group: TagGroup) {
      if (!group.tags) return [];

      const searchText = this.tagSearchText[group.groupId] || '';
      console.log(`搜索过滤: 组 ${group.groupId}, 搜索文本: "${searchText}", 原始标签数: ${group.tags.length}`);

      if (!searchText.trim()) {
        console.log(`无搜索文本，返回所有 ${group.tags.length} 个标签`);
        return group.tags;
      }

      const filtered = group.tags.filter(tag =>
        tag.name.toLowerCase().includes(searchText.toLowerCase())
      );

      console.log(`搜索结果: 找到 ${filtered.length} 个匹配的标签`);
      return filtered;
    },
    async fetchTagGroups() {
      if (!this.corpId) return;

      this.loading = true;
      try {
        const res = await this.$request({
          url: customerWecomTagsApi.getLocalTags.url,
          method: customerWecomTagsApi.getLocalTags.method,
          params: {
            corpId: this.corpId
          }
        });

        if (res.code === 0 && res.data) {
          this.tagGroups = res.data;
        } else {
          MessagePlugin.error(res.msg || '获取标签组数据失败');
        }
      } catch (error) {
        console.error('获取标签组数据出错:', error);
        MessagePlugin.error('获取标签组数据出错');
      } finally {
        this.loading = false;
      }
    },
    async syncAllTags() {
      if (!this.corpId) return;

      this.syncAllLoading = true;
      try {
        const res = await this.$request({
          url: customerWecomTagsApi.syncTags.url,
          method: customerWecomTagsApi.syncTags.method,
          params: {
            corpId: this.corpId,
            // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg"
          },
          data: {
            group_id: [],
            tag_id: []
          }
        });

        if (res.code === 0) {
          MessagePlugin.success('同步所有标签成功');
          // 重新获取标签数据
          this.fetchTagGroups();
        } else {
          MessagePlugin.error(res.msg || '同步所有标签失败');
        }
      } catch (error) {
        console.error('同步所有标签出错:', error);
        MessagePlugin.error('同步所有标签出错');
      } finally {
        this.syncAllLoading = false;
      }
    },
    async syncGroupTags(groupId: string) {
      if (!this.corpId || !groupId) return;

      // 设置当前标签组的同步状态
      this.$set(this.syncingGroups, groupId, true);

      try {
        const res = await this.$request({
          url: customerWecomTagsApi.syncTags.url,
          method: customerWecomTagsApi.syncTags.method,
          params: {
            corpId: this.corpId,
            // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
          },
          data: {
            group_id: [groupId],
            tag_id: []
          }
        });

        if (res.code === 0) {
          MessagePlugin.success('同步标签组成功');
          // 重新获取标签数据
          this.fetchTagGroups();
        } else {
          MessagePlugin.error(res.msg || '同步标签组失败');
        }
      } catch (error) {
        console.error('同步标签组出错:', error);
        MessagePlugin.error('同步标签组出错');
      } finally {
        this.$set(this.syncingGroups, groupId, false);
      }
    },

    // 标签组表单相关方法
    addTagToForm() {
      this.tagGroupForm.tags.push({ name: '' });
    },
    removeTagFromForm(index: number) {
      if (this.tagGroupForm.tags.length > 1) {
        // 如果有多个标签，直接删除当前标签
        this.tagGroupForm.tags.splice(index, 1);
      } else {
        // 如果只有一个标签，清空它而不是删除它
        this.tagGroupForm.tags[0].name = '';
      }
    },

    // 标签组对话框相关方法
    showAddTagGroupDialog() {
      this.tagGroupDialog = {
        visible: true,
        isEdit: false,
        currentGroup: null,
      };
      this.tagGroupForm = {
        name: '',
        tags: [{ name: '' }], // 默认添加一个空标签
      };
    },
    showEditTagGroupDialog(group: TagGroup) {
      this.tagGroupDialog = {
        visible: true,
        isEdit: true,
        currentGroup: group,
      };
      this.tagGroupForm = {
        name: group.name,
        tags: [], // 编辑时不显示标签输入
      };
    },
    closeTagGroupDialog() {
      this.tagGroupDialog.visible = false;
    },
    // 标签对话框相关方法
    showAddTagDialog(group: TagGroup) {
      this.tagDialog = {
        visible: true,
        isEdit: false,
        currentTag: null,
        currentGroup: group,
      };
      this.tagForm = {
        name: '',
      };
    },
    showEditTagDialog(tag: TagInfo, group: TagGroup) {
      this.tagDialog = {
        visible: true,
        isEdit: true,
        currentTag: tag,
        currentGroup: group,
      };
      this.tagForm = {
        name: tag.name,
      };
    },
    closeTagDialog() {
      this.tagDialog.visible = false;
    },

    // 确认标签组对话框
    async confirmTagGroupDialog() {
      const formValid = await (this.$refs.tagGroupForm as any).validate();
      if (!formValid) return;
      if (!this.tagGroupDialog.isEdit && this.tagGroupForm.tags.length === 0) {
        MessagePlugin.warning('请至少添加一个标签');
        return;
      }

      try {
        // 过滤掉空标签
        const validTags = this.tagGroupForm.tags.filter(tag => tag.name.trim() !== '');

        if (validTags.length === 0 && !this.tagGroupDialog.isEdit) {
          MessagePlugin.warning('请至少添加一个有效的标签');
          return;
        }

        const api = this.tagGroupDialog.isEdit
          ? this.$request({
            url: customerWecomTagsApi.updateTag.url,
            method: customerWecomTagsApi.updateTag.method,
            params: {
              corpId: this.corpId,
              // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
              id: this.tagGroupDialog.currentGroup?.groupId,
              name: this.tagGroupForm.name,
              order: 0,
              isGroup: true
            }
          })
          : this.$request({
            url: customerWecomTagsApi.addTag.url,
            method: customerWecomTagsApi.addTag.method,
            params: {
              corpId: this.corpId,
              // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
              groupName: this.tagGroupForm.name,
              order: 0
            },
            data: validTags.map(tag => ({
              name: tag.name,
              order: tag.order || 0
            }))
          });

        const res = await api;

        if (res.code === 0) {
          MessagePlugin.success(this.tagGroupDialog.isEdit ? '更新标签组成功' : '添加标签组成功');
          this.fetchTagGroups();
          this.closeTagGroupDialog();
        } else {
          MessagePlugin.error(res.msg || (this.tagGroupDialog.isEdit ? '更新标签组失败' : '添加标签组失败'));
        }
      } catch (error) {
        console.error('操作标签组出错:', error);
        MessagePlugin.error(this.tagGroupDialog.isEdit ? '更新标签组出错' : '添加标签组出错');
      }
    },

    // 确认标签对话框
    async confirmTagDialog() {
      const formValid = await (this.$refs.tagForm as any).validate();
      if (!formValid) return;

      try {
        const api = this.tagDialog.isEdit
          ? this.$request({
            url: customerWecomTagsApi.updateTag.url,
            method: customerWecomTagsApi.updateTag.method,
            params: {
              corpId: this.corpId,
              // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
              id: this.tagDialog.currentTag?.tagId,
              name: this.tagForm.name,
              order: 0,
              groupId: this.tagDialog.currentGroup?.groupId,
              isGroup: false
            }
          })
          : this.$request({
            url: customerWecomTagsApi.addTag.url,
            method: customerWecomTagsApi.addTag.method,
            params: {
              corpId: this.corpId,
              // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
              groupId: this.tagDialog.currentGroup?.groupId,
              isGroup: false
            },
            data: [{
              name: this.tagForm.name,
              order: 0
            }]
          });

        const res = await api;

        if (res.code === 0) {
          MessagePlugin.success(this.tagDialog.isEdit ? '更新标签成功' : '添加标签成功');
          this.fetchTagGroups();
          this.closeTagDialog();
        } else {
          MessagePlugin.error(res.msg || (this.tagDialog.isEdit ? '更新标签失败' : '添加标签失败'));
        }
      } catch (error) {
        console.error('操作标签出错:', error);
        MessagePlugin.error(this.tagDialog.isEdit ? '更新标签出错' : '添加标签出错');
      }
    },

    // 删除标签组
    async deleteTagGroup(group: TagGroup) {
      try {
        const res = await this.$request({
          url: customerWecomTagsApi.deleteTag.url,
          method: customerWecomTagsApi.deleteTag.method,
          data: {
            corpId: this.corpId,
            // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
            groupIds: [group.groupId],
            deleteTagWithGroup: true
          }
        });

        if (res.code === 0) {
          MessagePlugin.success('删除标签组成功');
          this.fetchTagGroups();
        } else {
          MessagePlugin.error(res.msg || '删除标签组失败');
        }
      } catch (error) {
        console.error('删除标签组出错:', error);
        MessagePlugin.error('删除标签组出错');
      }
    },

    // 删除标签
    async deleteTag(tag: TagInfo) {
      try {
        const res = await this.$request({
          url: customerWecomTagsApi.deleteTag.url,
          method: customerWecomTagsApi.deleteTag.method,
          data: {
            corpId: this.corpId,
            // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
            tagIds: [tag.tagId],
            deleteTagWithGroup: false
          }
        });

        if (res.code === 0) {
          MessagePlugin.success('删除标签成功');
          this.fetchTagGroups();
        } else {
          MessagePlugin.error(res.msg || '删除标签失败');
        }
      } catch (error) {
        console.error('删除标签出错:', error);
        MessagePlugin.error('删除标签出错');
      }
    },

    // 同步记录相关方法
    showSyncRecordsDialog() {
      this.syncRecordsDialog.visible = true;
      this.fetchSyncRecords(1);
    },

    closeSyncRecordsDialog() {
      this.syncRecordsDialog.visible = false;
    },

    async fetchSyncRecords(page: number) {
      if (page) {
        this.syncRecordsPagination.current = page;
      }

      this.syncRecordsLoading = true;
      try {
        const params: any = {
          current: this.syncRecordsPagination.current,
          size: this.syncRecordsPagination.pageSize,
          // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg"
          corpId: this.corpId
        };

        // 添加可选的筛选参数
        if (this.syncRecordsFilter.syncType !== null) {
          params.syncType = this.syncRecordsFilter.syncType;
        }
        if (this.syncRecordsFilter.status !== null) {
          params.status = this.syncRecordsFilter.status;
        }

        const res = await this.$request({
          url: customerWecomTagsApi.getSyncRecords.url,
          method: customerWecomTagsApi.getSyncRecords.method,
          params
        });

        if (res.code === 0 && res.data) {
          this.syncRecords = res.data.records || [];
          this.syncRecordsPagination.total = Number(res.data.total) || 0;
        } else {
          MessagePlugin.error(res.msg || '获取同步记录失败');
        }
      } catch (error) {
        console.error('获取同步记录出错:', error);
        MessagePlugin.error('获取同步记录出错');
      } finally {
        this.syncRecordsLoading = false;
      }
    },

    onSyncRecordsPageChange(current: number) {
      this.fetchSyncRecords(current);
    },

    // 处理分页大小变化
    onSyncRecordsPageSizeChange(size: number) {
      this.syncRecordsPagination.pageSize = size;
      this.fetchSyncRecords(1); // 切换每页数量后，重置为第一页
    },

    // 重置筛选条件
    resetSyncRecordsFilter() {
      this.syncRecordsFilter = {
        syncType: null,
        status: null
      };
      this.fetchSyncRecords(1); // 重置后查询第一页数据
    },

    // 按营期添加标签组相关方法
    showAddTagGroupByCampDialog() {
      this.campTagGroupDialog.visible = true;
      this.campTagGroupForm = {
        groupName: '',
        companyId: '',
        campPeriods: []
      };
      this.fetchCompanyTree();
    },

    // 按课程添加标签组相关方法
    showAddTagGroupByCourseDialog() {
      this.courseTagGroupDialog.visible = true;
      this.courseTagGroupForm = {
        groupName: '',
        courseGroupIds: [],
        courses: []
      };
      this.fetchCourseGroups();
    },

    closeCourseTagGroupDialog() {
      this.courseTagGroupDialog.visible = false;
    },

    async fetchCourseGroups() {
      try {
        const { code, data } = await this.$request.get(sysCourseGroupApi.queryCourseGroupList.url);

        if (code === 0 && data) {
          this.courseGroupOptions = data.map(item => ({
            label: item.groupName,
            value: item.id
          }));
        } else {
          MessagePlugin.error('获取课程分组列表失败');
        }
      } catch (error) {
        console.error('获取课程分组列表出错:', error);
        MessagePlugin.error('获取课程分组列表出错');
      }
    },



    closeCampTagGroupDialog() {
      this.campTagGroupDialog.visible = false;
    },

    async fetchCompanyTree() {
      try {
        const params = {
          id: 31000, // 总公司ID
          level: 2, // 公司层级
        };
        const { code, data } = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });

        if (code === 0 && data) {
          // 处理公司树形数据，按照示例格式处理
          const companyTreeData = data.columns.map(column => ({
            label: column.name,
            value: column.id,
            disabled: true, // 禁用父级节点选择
            children: column.companies?.map(company => ({
              label: company.name,
              value: company.id,
              // 不设置disabled，确保子节点可选
            }))
          }));

          this.companyTreeData = companyTreeData;
        } else {
          MessagePlugin.error('获取公司列表失败');
        }
      } catch (error) {
        console.error('获取公司列表出错:', error);
        MessagePlugin.error('获取公司列表出错');
      }
    },

    async handleCompanyChange(value) {
      if (!value) {
        this.campTagGroupForm.campPeriods = [];
        return;
      }

      try {
        const { code, data } = await this.$request.get(sysCampGroupApi.getCampPeriodsByCompanyId.url, {
          params: { companyId: value }
        });

        if (code === 0 && data) {
          this.campTagGroupForm.campPeriods = data;
          if (data.length === 0) {
            MessagePlugin.warning('该公司下没有营期数据');
          }
        } else {
          MessagePlugin.error('获取营期列表失败');
        }
      } catch (error) {
        console.error('获取营期列表出错:', error);
        MessagePlugin.error('获取营期列表出错');
      }
    },

    async handleCourseGroupChange(value) {
      if (!value || value.length === 0) {
        this.courseTagGroupForm.courses = [];
        return;
      }

      try {
        // 查询所有选中的课程分组
        const allCourses = [];
        for (const groupId of value) {
          const { code, data } = await this.$request.get(sysCourseGroupApi.queryCourseGroupListByGroupId.url, {
            params: {
              id: groupId,
              pageNum: 1,
              pageSize: 9999,
            }
          });

          if (code === 0 && data && data.records) {
            allCourses.push(...data.records);
          }
        }

        // 去重
        const uniqueCourses = [];
        const courseIds = new Set();
        for (const course of allCourses) {
          if (!courseIds.has(course.id)) {
            courseIds.add(course.id);
            uniqueCourses.push(course);
          }
        }

        this.courseTagGroupForm.courses = uniqueCourses;
        if (uniqueCourses.length === 0) {
          MessagePlugin.warning("所选课程分组没有课程数据");
        }
      } catch (error) {
        console.error("获取课程列表出错:", error);
        MessagePlugin.error("获取课程列表出错");
      }
    },

    async confirmCourseTagGroupDialog() {
      const formValid = await (this.$refs.courseTagGroupForm as any).validate();
      if (!formValid) return;

      this.courseTagGroupDialog.loading = true;
      try {
        // 1. 检查是否有课程数据
        const { courses } = this.courseTagGroupForm;
        if (courses.length === 0) {
          MessagePlugin.warning("所选课程分组没有课程数据");
          this.courseTagGroupDialog.loading = false;
          return;
        }

        // 2. 准备标签数据 - 为每个课程生成两个标签："已到课"和"已完课"
        const tagData = [];
        courses.forEach(course => {
          tagData.push({
            name: `${course.courseName}已到课`,
            order: 0
          });
          tagData.push({
            name: `${course.courseName}已完课`,
            order: 0
          });
        });

        // 3. 一次性创建标签组并添加标签
        const res = await this.$request({
          url: customerWecomTagsApi.addTag.url,
          method: customerWecomTagsApi.addTag.method,
          params: {
            corpId: this.corpId,
            // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
            groupName: this.courseTagGroupForm.groupName,
            order: 0
          },
          data: tagData
        });

        if (res.code === 0) {
          MessagePlugin.success(`按课程添加标签组成功，已添加 ${tagData.length} 个标签`);
          this.fetchTagGroups();
          this.closeCourseTagGroupDialog();
        } else {
          MessagePlugin.error(res.msg || "添加标签组失败");
        }
      } catch (error) {
        console.error("按课程添加标签组出错:", error);
        MessagePlugin.error("按课程添加标签组出错");
      } finally {
        this.courseTagGroupDialog.loading = false;
      }
    },


    async confirmCampTagGroupDialog() {
      const formValid = await (this.$refs.campTagGroupForm as any).validate();
      if (!formValid) return;

      this.campTagGroupDialog.loading = true;
      try {
        // 1. 检查是否有营期数据
        const {campPeriods} = this.campTagGroupForm;
        if (campPeriods.length === 0) {
          MessagePlugin.warning("所选公司没有营期数据");
          this.campTagGroupDialog.loading = false;
          return;
        }

        // 2. 准备标签数据
        const tagData = campPeriods.map(item => ({
          name: item.campperiodName,
          order: 0
        }));

        // 3. 一次性创建标签组并添加标签
        const res = await this.$request({
          url: customerWecomTagsApi.addTag.url,
          method: customerWecomTagsApi.addTag.method,
          params: {
            corpId: this.corpId,
            // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg",
            groupName: this.campTagGroupForm.groupName,
            order: 0
          },
          data: tagData
        });

        if (res.code === 0) {
          MessagePlugin.success(`按营期添加标签组成功，已添加 ${tagData.length} 个标签`);
          this.fetchTagGroups();
          this.closeCampTagGroupDialog();
        } else {
          MessagePlugin.error(res.msg || "添加标签组失败");
        }
      } catch (error) {
        console.error("按营期添加标签组出错:", error);
        MessagePlugin.error("按营期添加标签组出错");
      } finally {
        this.campTagGroupDialog.loading = false;
      }
    },

  }
});
</script>

<style lang="less" scoped>
/* 虚拟滚动列表样式 */
.tag-list-container {
  margin-top: 16px;
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.virtual-list-wrap {
  overflow-y: auto;
  position: relative;
}

.virtual-tag-item {
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
}

.header-card {
  margin-bottom: 20px;

  .header-info {
    margin-bottom: 16px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
    }

    p {
      margin: 0;
      color: rgba(0, 0, 0, 0.6);
    }

    .tag-stats {
      margin-top: 4px;
      color: rgba(0, 0, 0, 0.5);
      font-size: 13px;
    }
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.tag-groups-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tag-group-card {
  .tag-group-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    flex-wrap: wrap;

    .tag-group-info {
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        white-space: normal;
        word-break: break-word;
        overflow: visible;
      }

      .tag-group-id {
        margin: 0;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }

  .tag-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .tag-item {
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .no-tags {
      color: rgba(0, 0, 0, 0.4);
      font-style: italic;
    }

    .no-search-results {
      color: rgba(0, 0, 0, 0.4);
      font-style: italic;
      text-align: center;
      padding: 20px;
    }

    .tag-pagination-container {
      width: 100%;

      .tag-page-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        color: rgba(0, 0, 0, 0.6);
        font-size: 13px;
      }

      .paginated-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }
}

.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;

  .t-icon {
    margin-right: 4px;
    font-size: 16px;
  }
}

.tag-inputs-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.tag-input-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
  width: 100%;
}

.tag-input-row .t-input {
  flex: 1;
  margin-right: 8px;
}

.tag-input-row .t-button {
  flex-shrink: 0;
}
.edit-icon, .close-icon {
  cursor: pointer;
  margin-left: 4px;

  &:hover {
    opacity: 0.8;
  }
}

/* 同步记录对话框样式 */
.sync-records-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.sync-records-filter {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.sync-records-filter .t-form {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.sync-records-filter .t-form-item {
  margin-right: 16px;
  margin-bottom: 0;
}

/* 营期预览样式 */
.camp-periods-info {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.camp-periods-info p {
  margin: 0 0 8px 0;
  color: rgba(0, 0, 0, 0.6);
}

.camp-periods-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.preview-tag {
  margin-right: 4px;
}

.more-tag {
  color: rgba(0, 0, 0, 0.5);
  font-size: 13px;
}

.tag-example {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #e0e0e0;
}

.tag-example p {
  margin: 0 0 8px 0;
  color: rgba(0, 0, 0, 0.6);
}

.tag-examples-preview {
  display: flex;
  gap: 8px;
}
</style>
