<!-- @format -->

<template>
  <t-dialog
    header="任务类型分配"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '确定',
            theme: 'primary',
          }
    "
  >
    <div class="transfer-container">
      <t-transfer
        :data="dataList"
        :value="dataTarget"
        :checked.sync="checked"
        @change="onChange"
        @checked-change="handleCheckedChange"
      >
        <template v-slot:title="props" :name="123">
          <div>{{ props.type === 'target' ? '已分配' : '待分配' }}</div>
        </template>
      </t-transfer>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';

import {} from '@/constants';
export default Vue.extend({
  name: 'EditModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    id: {
      type: String,
      default: '',
      // required: false,
    },

    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
  },

  data() {
    return {
      // list,
      dataScopeTask: [],
      loading: false,
      defaultDataTarget: [], // 默认目标节点数据
      dataTarget: [], // 目标节点数据
      checked: [],
      // 可授权的应用视图信息
      dataList: []
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
  },


  watch: {
    data: {
      handler(newValue) {
        if (newValue.comId && newValue.roleId) {
          // this.dataTarget=[];
          this.dataTarget = newValue.dataScopeTask
          this.dataList = taskTypeOptions;
        }
      },

      deep: true,
    },
  },

  mounted() {
    // console.log(111)
  },

  methods: {
    async getData() {
      const loading = await this.$message.loading('请稍后...', 0);
      // await this.getRoleIds();
      loading.close();
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      this.editUser();
    },
    handleCheckedChange() {
      // console.log(checked, sourceChecked, targetChecked, type);
    },
    onChange(newTargetValue) {
      this.dataTarget = newTargetValue;
    },
    async editUser() {
      try {
        const query = {
          roleId: this.data.roleId,
          dataScopeTask: this.dataTarget,
        };
        // const res = await this.$request.post(apiRoleInfo.setDataScopeTask.url, query);
        // const {code, msg} = res;
        // if (code === 1) {
        //   // console.log(data, 444444);
        //   this.$message.success(msg || '操作成功');
        //   this.$emit('ok');
        // } else {
        //   this.$message.error(msg || '请求失败');
        // }
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    // async getRoleIds() {
    //   try {
    //     this.dataTarget = this.dataScopeTask.map((item) => ({
    //       value: item,
    //       label: arrList[Number(item)-1 ]
    //     }));
    //     console.log("this.dataTarget",this.dataTarget )
    //     return
    //   } catch (e) {
    //     console.log(e);
    //     // this.$message.error('请求失败');
    //   }
    // },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.transfer-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
