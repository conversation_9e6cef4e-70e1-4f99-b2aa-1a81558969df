<!-- @format -->
<template>
  <t-dialog
    header="菜单授权"
    :width="700"
    :height="500"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '确定',
            theme: 'primary',
          }
    "
  >
    <div class="transfer-container">
      <div class="transfer-container-operation">
        <t-button theme="primary" size="medium" @click="handleSelectAll">全选</t-button>
        <t-button theme="primary" size="medium" @click="handleSelectReverse">反选</t-button>
      </div>
      <div class="tree-scroll-container">
        <t-tree
          :data="nzTreeNodeOptions"
          hover
          checkStrictly
          ref="tree"
          checkable
          expand-all
          valueMode="all"
          v-model="checkedKeys"
          @change="change"
        />
      </div>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import {} from '@/constants';
import systemRoleMenuApi from "@/constants/api/hxsy-admin/system-role-menu.api";

export default Vue.extend({
  name: 'EditModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    id: {
      type: String,
      default: '',
      // required: false,
    },

    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
  },

  data() {
    return {
      // list,
      loading: false,
      defaultDataTarget: [], // 默认目标节点数据
      dataTarget: [], // 目标节点数据
      // 可授权的应用视图信息
      dataList: [],
      nzTreeNodeOptions: [], // 树所有节点数据
      checkedKeys: [], // 选中的节点
      checkSubmitKeys: [], // 最终提交的确定节点，要包括子节点下的父节点
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
  },

  watch: {
    data: {
      handler(newValue) {
        // this.dataTarget=["932604188621541376"]
        // console.log(this.innerDataSource, 333);
        this.getList();
      },

      deep: true,
    },
  },

  mounted() {
    // console.log(111)
  },

  methods: {
    transferItem(h, { data, type }) {
      const sourceLabel = `${data.label} - ${data.description}`;
      const targetLabel = `${data.label} - ${data.description}`;
      return type === 'source' ? sourceLabel : targetLabel;
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      this.saveRole();
    },
    change(value: any, context: any) {
      // console.log("选择节点已改动:", this.checkedKeys);
    },
    handleSelectAll() {
      const flattenValues = (nodes: any[]) => nodes.reduce((acc: any, node: any) => {
        acc.push(node.value);
        if (node.children && node.children.length) {
          acc.push(...flattenValues(node.children));
        }
        return acc;
      }, []);

      this.checkedKeys = flattenValues(this.nzTreeNodeOptions);
    },
    handleSelectReverse() {
      const flatten = (nodes: any[]) => nodes.reduce((acc: any, node: any) => acc.concat(
        node.value,
        ...(node.children ? flatten(node.children) : [])
      ), [])
      const allValues = flatten(this.nzTreeNodeOptions)
      this.checkedKeys = allValues.filter((v: any) => !this.checkedKeys.includes(v))
      // 如果需要强制更新
      this.$forceUpdate()
      // console.error("反选节点:", this.checkedKeys, this.nzTreeNodeOptions)
    },
    getRoleType(type: any) {
      let result = '';
      if (type === '1') {
        result = '超级管理员';
      } else if (type === '2') {
        result = '普通管理员';
      } else if (type === '3') {
        result = '普通角色';
      }
      return result;
    },
    async getList() {
      try {
        const query = {
          roleId: this.data.id,
        };
        const res = await (this as any).$request.post(systemRoleMenuApi.getRoleMenu.url, query);
        const { code, data = [], msg } = res;
        // 每次都需要清空数据，防止用到之前的角色菜单
        this.nzTreeNodeOptions = [];
        this.checkedKeys = [];
        if (code === 0) {
          // 将未授权菜单与已授权菜单，转换后端字段为组件需求字段
          const {allSysAppInfos} = data;
          const {authAppInfos} = data;
          allSysAppInfos.forEach((item: any) => {
            const noAuthTreeNodeOption = {
              key: item.id,
              label: item.name,
              title: item.name,
              value: item.id,
              id: item.id,
              parentId: item.parentId,
              expanded: true,
              menuType: 3,
            };
            this.nzTreeNodeOptions.push(noAuthTreeNodeOption);
          });
          // 当角色未分配菜单时，授权菜单会为空，需要判空
          if (authAppInfos) {
            authAppInfos.forEach((item: any) => {
              const authTreeNodeOption: any = {
                key: item.id,
                label: item.name,
                title: item.name,
                value: item.id,
                id: item.id,
                parentId: item.parentId,
                expanded: true,
                menuType: 3,
              };
              // 设置默认的选中值，如果是只有一层的父级结构，需要等待树形结构转换完成后再判断
              if (authTreeNodeOption.parentId) {
                authTreeNodeOption.checked = true;
                this.checkedKeys.push(item.id);
              }
              this.nzTreeNodeOptions.push(authTreeNodeOption);
            });
          }
          // console.log("初始化list结构:", this.nzTreeNodeOptions);
          // 未授权菜单与已授权菜单合并转化为树形结构用于前端展示
          const treeNode: any[] = this.listToTree(this.nzTreeNodeOptions);
          this.nzTreeNodeOptions = treeNode;
          // console.log("转换为树形结构:", this.nzTreeNodeOptions);
          // 判断只有一层的结构，并且处于后端的选中状态，前端要补充选中属性
          this.nzTreeNodeOptions.forEach((item: any) => {
            // if(!item.children || item.children.length === 0){
            authAppInfos.some((authItem: any) => {
              if(item.id === authItem.id){
                item.checked = true;
                this.checkedKeys.push(item.id);
                return true;
              }
            })
            // }
          });
          // console.log("已选中结构:", this.checkedKeys);
        } else {
          this.$message.error(msg || '请求失败');
        }
      } catch (e) {
        console.log(e);
      }
    },
    async saveRole() {
      try {
        // 复制一个当前选择节点用于提交，防止引用对象重复
        this.checkSubmitKeys = [ ...this.checkedKeys ];
        // 对选择的子节点需要补充一下他的父节点，因为父节点的值只有其下子节点全部选中，父节点才会被包括，但实际上选择了一个子节点，父节点也要被选中
        const { tree } = this.$refs;
        // console.log("当前选择节点:", this.checkedKeys);
        this.checkedKeys.forEach((item: any) => {
          const parentNode = tree.getParent(item);
          // 添加父节点，并且需要去重
          if(parentNode && !this.checkSubmitKeys.includes(parentNode.value)){
            this.checkSubmitKeys.push(parentNode.value);
          }
        })
        // console.log("最终提交节点:", this.checkSubmitKeys);
        const res = await (this as any).$request.post(systemRoleMenuApi.saveRoleMenu.url, {
          roleId: this.data.roleId,
          menuIds: this.checkSubmitKeys,
        });
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success('操作成功');
          this.$emit('ok');
        } else {
          this.$message.error(msg || '请求失败');
        }
      } catch (e) {
        this.$message.error('请求失败');
      }
    },
    /**
     * @description: list菜单转换树形结构
     * 根据parentId划分递归父级结构
     * 未传入parentId，则表示正在找出最顶层菜单，后续再开始递归
     * <AUTHOR>
     * @date 2025/5/8 23:24
     */
    listToTree(list: any [], parentId?: string) {
      const tree: any[] = [];
      if (parentId) {
        // console.log("当前正在获取节点:", parentId , "的子级节点");
        const rootNodes = list.filter(nodeItem => nodeItem.parentId === parentId);
        // console.log("获取到子级节点:", rootNodes);
        rootNodes.forEach(rootNode => {
          const children = this.listToTree(list, rootNode.id);
          if (children) {
            rootNode.children = children;
          }
          tree.push(rootNode);
        });
        return tree;
      }
      const FirstNodes: any[] = [];
      list.forEach(item => {
        if (!item.parentId || item.parentId == 'null' || item.parentId == ''){
          FirstNodes.push(item)
          // 记录一下所有的父节点，用于提交时选择了其下的子节点，父节点也可以置为选中
        }
      })
      // console.log("获取到顶层节点:", FirstNodes);
      // 先寻找二级菜单，再开始递归其下的子菜单
      FirstNodes.forEach(item => {
        const rootNodes = list.filter(nodeItem => nodeItem.parentId === item.id);
        item.children = rootNodes;
        rootNodes.forEach(rootNode => {
          const children = this.listToTree(list, rootNode.id);
          if (children) {
            rootNode.children = children;
          }
          tree.push(rootNode);
        });
      })
      return FirstNodes;

    }
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.transfer-container {
  max-height: 400px;
  display: flex;
  justify-content: left;
  align-items: flex-start;
  flex-direction: column;
  .tree-scroll-container {
    height: 100%;
    overflow: auto;
  }
  .transfer-container-operation {
    position: sticky;
    top: 0;
    width: 100%;
    background: #ffffff;
    z-index: 100;
  }
}
</style>
