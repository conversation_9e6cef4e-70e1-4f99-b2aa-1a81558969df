<!-- @format -->

<template>
  <t-dialog
    header="分配用户"
    :width="700"
    :visible="modalVisible"
    @confirm="handleOk"
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '确定',
            theme: 'primary',
          }
    "
  >
    <div v-if="modalVisible" class="transfer-container">
      <!-- {{ dataList }}
      {{ dataTarget }} -->
      <!-- {{ innerDataSource }} -->
      <t-transfer
        :data="dataList"
        :value="dataTarget"
        :checked.sync="checked"
        :search="true"
        :pagination="pagination"
        :onPageChange="handlePageChange"
        @change="onChange"
        @checked-change="handleCheckedChange"
      >
        <template v-slot:title="props" :name="123">
          <div>{{ props.type === 'target' ? '已授权' : '待授权' }}</div>
        </template>
      </t-transfer>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
// import { SearchIcon } from 'tdesign-icons-vue';
// import Trend from '@/components/trend/index.vue';
// import { prefix } from '@/config/global';

import {} from '@/constants';
import apiUser from '@/constants/api/back/system-user.api';

export default Vue.extend({
  name: 'EditModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    id: {
      type: String,
      default: '',
      // required: false,
    },

    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
  },

  data() {
    return {
      // list,
      loading: false,
      defaultDataTarget: [], // 默认目标节点数据
      dataTarget: [], // 目标节点数据
      checked: [],
      // 可授权的应用视图信息
      dataList: [],
      pagination: [
        {
          pageSize: 100,
          defaultCurrent: 1,
        },
        {
          pageSize: 100,
          defaultCurrent: 1,
        },
      ],
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
  },

  watch: {
    data: {
      handler(newValue) {
        // this.dataTarget=["932604188621541376"]
        // console.log(this.innerDataSource, 333);

        if (newValue.comId && newValue.roleId) {
          this.dataTarget = [];
          this.dataList = [];
          this.getData();
        }
      },

      deep: true,
    },
  },

  mounted() {
    // console.log(111)
  },

  methods: {
    async getData() {
      const loading = await this.$message.loading('请稍后...',0);
      await this.getList();
      await this.getRoleIds();
      loading.close();
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      this.editUser();
    },
    handleCheckedChange() {
      // console.log(checked, sourceChecked, targetChecked, type);
    },
    handlePageChange(args) {
      // console.log('handlePageChange', args);
    },
    onChange(newTargetValue) {
      // console.log(this.defaultDataTarget);
      // const arr = [...this.defaultDataTarget, ...newTargetValue];
      // const newArr = [...new Set(arr)]; // 利用了Set结构不能接收重复数据的特点
      this.dataTarget = newTargetValue;
      // this.checked = newArr;
    },
    getRoleType(type) {
      let result = '';
      if (type === '1') {
        result = '超级管理员';
      } else if (type === '2') {
        result = '普通管理员';
      } else if (type === '3') {
        result = '普通角色';
      }
      return result;
    },
    async getList() {
      // sys-user-info/query-urole?userId=988505226831990784&comId=HTHX
      try {
        const query = {
          comId: this.data.comId,
        };
        // console.log(query,3333333)
        const res = await this.$request.get(apiUser.queryBrieflyByComId.url, { params: query });
        const { code, data = [], msg } = res;
        if (code === 1) {
          this.dataList = data.map((item) => ({
            value: item.userId,
            label: `${item.userCode  }-${  item.userName}`,
          }));
          // console.log(data, 444444);
        } else {
          this.$message.error(msg || '请求失败');
        }
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
      }
    },
    async getRoleIds() {
      // sys-user-info/query-urole?userId=988505226831990784&comId=HTHX
      try {
        const query = {
          roleId: this.data.roleId,
        };
        // console.log(query,3333333)
        const res = await this.$request.get(apiUser.queryByRoleId.url, { params: query });
        const { code, data = [], msg } = res;
        if (code === 1) {
          const dataTargetToSet = new Set(); // 使用Set数据结构
          const dataTargetTempSet = new Set(data.map(item => item.userId)); // 将userId转换为Set

          if (this.dataList.length) {
            const valueSet = new Set(this.dataList.map(dataEle => dataEle.value)); // 将this.dataList的value属性转换为Set
            dataTargetTempSet.forEach((userId) => {
              if (valueSet.has(userId)) { // 使用Set的has方法快速检查元素
                dataTargetToSet.add(userId); // 使用Set的add方法添加元素
              }
            });
          }
          const dataTargetToSetArray = Array.from(dataTargetToSet);
          this.dataTarget = dataTargetToSetArray;
        } else {
          this.$message.error(msg || '请求失败');
        }
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
      }
    },
    async editUser() {
      // sys-user-info/add-batch-urel?userId=946877264780201984
      try {
        const query = {
          roleId: this.data.roleId,
          userIds: this.dataTarget,
        };
        // console.log(query,3333333)
        const res = await this.$request.post(apiRUser.saveForRole.url, query);
        const { code, msg } = res;
        if (code === 1) {
          // console.log(data, 444444);
          this.$message.success(msg || '操作成功');
          this.$emit('ok');
        } else {
          this.$message.error(msg || '请求失败');
        }
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.transfer-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
