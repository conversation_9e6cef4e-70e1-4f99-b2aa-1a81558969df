<!-- @format -->
<template>
  <t-dialog
    :header="data.id ? '编辑角色' : '新增角色'"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <t-form :data="form" :rules="rules" :labelWidth="140" :statusIcon="true" ref="formRef" @submit="onSubmit">
        <t-form-item label="角色名称" name="roleName">
          <t-input v-model="form.roleName" placeholder="请输入角色"></t-input>
        </t-form-item>
        <t-form-item label="角色类型" name="roleType">
          <t-select v-model="form.roleType" :options="typeOptions" />
        </t-form-item>
        <t-form-item label="角色编码" name="roleCode">
          <t-input :disabled="!!data.roleId" v-model="form.roleCode" placeholder="请输入角色编码"></t-input>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';

import { prefix } from '@/config/global';
import systemRoleApi from "@/constants/api/hxsy-admin/system-role.api";
import {SYSTEM_ROLE_CONST_OPTIONS} from "@/constants/enum/system/system-role.enum";

export default Vue.extend({
  name: 'EditModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // required: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
  },

  data() {
    return {
      prefix,
      loading: false,
      form: {
        id: '',
        status: '1',
        userFrom: '1',
        roleType: '3',
        busType: '',
        roleCode: '',
        roleName: '',
        remark: '',
      },
      rules: {
        roleName: [{ required: true }, { min: 2 }, { max: 50, type: 'error' },{ pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change' },],
        roleCode: [{ required: true ,type: 'error'},{ max: 50, type: 'error'},{ pattern: /^[^\s]*$/, message: '禁止输入空格', trigger: 'change' }],
        status: [{ required: true, type: 'error' }],
      },
      typeOptions: SYSTEM_ROLE_CONST_OPTIONS,
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
        // this.countData = v
      },
    },
  },

  watch: {
    data: {
      handler(newValue) {
        // console.log("获取到传入角色信息：", newValue);
        (this as any).form = {
          id: newValue.id,
          roleName: newValue.roleName,
          roleCode: newValue.roleCode,
          roleType: newValue.roleType,
          userFrom: newValue.userFrom || '1',
          status: newValue.status || '1',
        };
        // this.$refs.formRef.reset();
      },
      deep: true,
    },
  },

  async mounted() {
    this.roleTypeName = this.typeOptions.find((item: any) => item.value === this.form.roleType)?.label;
  },

  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    handleOk() {
      // console.log('ok')
      this.$refs.formRef.submit();
    },
    async saveData() {
      this.loading = true;
      let params = {
        roleName: this.form.roleName,
        roleCode: this.form.roleCode,
        status: this.form.status,
        userFrom: this.form.userFrom,
        roleType: this.form.roleType,
        remark: this.form.remark,
        id: this.form.id,
      };
      let url;
      let method = 'post';
      url = systemRoleApi.saveOrUpdateRole.url;
      params = {
        ...params,
      };
      try {
        const res = await (this as any).$request[method](url, params);
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success('保存成功');
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.loading = false;
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
        this.loading = false;
      }
    },
    onSubmit({ validateResult }) {
      if (validateResult === true) {
        this.saveData();
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.t-input-number {
  width: 200px;
}
</style>
