<template>
  <div>
    <t-card class="list-card-container" header-bordered>
      <t-form
        ref="form"
        :data="formData"
        :label-width="80"
        colon
        @reset="onReset"
        @submit="onSubmit"
      >
        <t-row class="search-container">
          <t-col :span="10">
            <t-row :gutter="[16, 24]">
              <t-col :span="4">
                <t-form-item label="角色名称" name="roleName">
                  <t-input
                    v-model="formData.roleName"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入角色名称"
                    :style="{ minWidth: '134px' }"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="角色编码" name="roleCode">
                  <t-input
                    v-model="formData.roleCode"
                    class="form-item-content"
                    type="search"
                    placeholder="请输入角色编码"
                    :style="{ minWidth: '134px' }"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="使用状态" name="useState">
                  <t-select
                    v-model="formData.status"
                    class="form-item-content`"
                    :options="USER_STATE_OPTIONS"
                    placeholder="请选择使用状态"
                  />
                </t-form-item>
              </t-col>
            </t-row>
          </t-col>

          <t-col :span="2" class="operation-container">
            <t-button theme="primary" class="search-button" type="submit" :style="{ marginLeft: '8px' }"> 查询 </t-button>
            <t-button type="reset" class="reset-button" variant="base" theme="default"> 重置 </t-button>
          </t-col>
        </t-row>
        <t-row justify="space-between" class="left-operation-container">
          <div>
            <p v-if="!!selectedRowKeys.length" class="selected-count">已选{{ selectedRowKeys.length }}项</p>
          </div>
          <div>
            <t-dropdown
              trigger="click"
              :options="changeStatusOption"
              @click="clickHandlerChangeStatus"
              :minColumnWidth="88"
            >
              <t-button theme="default" :disabled="selectedRowKeys.length == 0" variant="outline">改变状态</t-button>
            </t-dropdown>
            <t-button @click="handleAdd"> <add-circle-icon slot="icon"/> 新增 </t-button>
          </div>
        </t-row>
      </t-form>
      <div class="table-container">
        <t-table
          :columns="columns"
          :data="data"
          :rowKey="rowKey"
          :verticalAlign="verticalAlign"
          :hover="true"
          :pagination="pagination"
          :selected-row-keys="selectedRowKeys"
          :loading="dataLoading"
          @page-change="rehandlePageChange"
          @select-change="rehandleSelectChange"
          :headerAffixedTop="true"
          :stripe="true"
          :bordered="true"
          :headerAffixProps="{ offsetTop: offsetTop, container: 'tdesign-starter-layout' }"
        >
          <template #roleType="{ row }">
            <span v-if="row.roleType === '1'"> 超级管理员 </span>
            <span v-else-if="row.roleType === '2'"> 普通管理员 </span>
            <span v-else-if="row.roleType === '3'"> 普通角色 </span>
          </template>

          <template #busType="{ row }">
            <span v-if="row.busType === '1'"> 功能 </span>
            <span v-else-if="row.busType === '2'"> 数据 </span>
          </template>
<!--          <template #comId="{ row }">-->
<!--            {{ getComName(row.comId) }}-->
<!--          </template>-->
          <template #status="{ row }">
            <t-tag v-if="row.status === '1'" theme="success">有效</t-tag>
            <t-tag v-else-if="row.status === '2'">禁用</t-tag>
          </template>

          <template #op="slotProps">
            <template v-if="slotProps.row.roleType !== '1'">
              <a class="t-button-link" @click="handleClickDetail(slotProps)">修改</a>
              <t-divider layout="vertical" style="background: #e4e4e4"/>
              <t-dropdown
                trigger="click"
                :options="moreButtons"
                @click="
                  (e) => {
                    moreButtonsClick(e, slotProps.row);
                  }
                "
                :minColumnWidth="88"
              >
                <t-button variant="text">
                  <span class="tdesign-demo-dropdown__text">
                    更多
                    <chevron-down-icon size="16" />
                  </span>
                </t-button>
              </t-dropdown>
            </template>
          </template>
        </t-table>
      </div>
    </t-card>
    <edit-modal
      :data="editModalVar.data"
      :allSysCompanyInfos="allSysCompanyInfos"
      :visible="editModalVar.visible"
      @cancel="handelCloseEditModal"
      @success="handelEditModalSuccess"
    ></edit-modal>
    <edit-menu-modal
      :data="menuVar.data"
      :visible="menuVar.visible"
      @cancel="handelCloseEditMenuModal"
      @ok="handelEditModalMenuSuccess"
    ></edit-menu-modal>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { ChevronDownIcon, AddCircleIcon } from 'tdesign-icons-vue';
import { treeNodeConvertService } from '@/service/tree-node-convert.service';
import EditModal from '../components/edit-modal.vue';
import EditMenuModal from '../components/edit-menu-modal.vue';
import EditUserModal from '../components/edit-user-modal.vue';
import EditRoleModal from '../components/edit-role-modal.vue';
import {USER_STATE_OPTIONS} from "@/constants";
import systemRoleApi from "@/constants/api/hxsy-admin/system-role.api";

export default Vue.extend({
  name: 'TenantManagementMain',
  components: {
    EditModal,
    EditMenuModal,
    EditUserModal,
    EditRoleModal,
    AddCircleIcon,
    ChevronDownIcon,
  },
  data() {
    return {
      USER_STATE_OPTIONS,
      allSysCompanyInfos: [],
      nodeContrast: {
        id: 'comId',
        parentId: 'supComId',
        children: 'subSysCompanyInfos',
        cascadeField: 'sysDeptInfos',
        subNodeContrast: { id: 'deptId', parentId: 'supDeptId', children: 'subSysDeptInfo' },
      },
      authCompany: {},
      treeNodeOptions: [],
      nzNodeContrast: {
        key: 'comId',
        title: 'comName',
        label: 'comName',
        children: 'subSysCompanyInfos',
        value: 'comId',
        cascadeField: 'sysDeptInfos',
        subNzNodeContrast: {
          key: 'deptId',
          label: 'deptName',
          title: 'deptName',
          children: 'subSysDeptInfo',
          value: 'deptId',
        },
      },
      formData: {
        roleCode: '',
        roleName: '',
        status: 1,
      },
      changeStatusOption: [
        {
          content: '有效',
          value: '1',
        },
        {
          content: '禁用',
          value: '2',
        },
      ],

      moreButtons: [
        {
          content: '菜单授权',
          value: 1,
        },
        {
          content: '删除',
          value: 3,
        },
      ],

      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
        },
        {
          title: '角色名称',
          align: 'left',
          width: 260,
          colKey: 'roleName',
          ellipsis: true,
          // fixed: 'left',
        },
        {
          title: '角色编码',
          align: 'left',
          colKey: 'roleCode',
          cell: 'roleCode',
          width: 260,
        },
        // {
        //   title: '所属机构',
        //   align: 'left',
        //   colKey: 'comId',
        //   cell: 'comId',
        //   width: 260,
        // },
        {
          title: '使用状态',
          align: 'center',
          colKey: 'status',
          cell: 'status',
          width: 200,
        },
        {
          title: '操作',
          align: 'center',
          width: 220,
          colKey: 'op',
          fixed: 'right',
          cell: 'op',
        },
      ],
      rowKey: 'id',
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',

      editModalVar: {
        visible: false,
        data: {},
      },
      menuVar: {
        visible: false,
        data: {},
      },
      userVar: {
        visible: false,
        data: {},
      },
      roleVar: {
        visible: false,
        data: {},
      },

      pagination: {
        // defaultPageSize: 10,
        total: 0,
        // defaultCurrent: 1,
        current: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
  },
  async mounted() {
    this.getList();
  },

  methods: {
    rehandlePageChange(curr: any) {
      this.pagination.current = curr.current;
      this.pagination.pageSize = curr.pageSize;
      this.getList();
    },
    /**
     * 设置状态
     */
    clickHandlerChangeStatus(status: any) {
      const confirm = this.$dialog.confirm({
        theme: 'warning',
        header: '温馨提示',
        body: `是否状态变更为：${status.value === '1' ? '有效' : '禁用'}?`,
        onConfirm: async () => {
          (confirm as any).destroy();
          const res = await this.changeStatusIds(this.selectedRowKeys, status.value);
          if (res === 'success') {
            this.selectedRowKeys = [];
          }
        },
      });
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      this.editModalVar.visible = true;
      this.editModalVar.data = {};
    },
    handleClickDetail(slotProps: any) {
      if (slotProps?.row) {
        // console.log("当前操作角色", slotProps?.row);
        this.editModalVar.data = {...slotProps?.row};
      } else {
        this.editModalVar.data = {};
      }
      this.editModalVar.visible = true;
    },
    /**
     * 搜索提交
     */
    onSubmit(result: any) {
      this.pagination.current = 1;
      if (result.validateResult === true) {
        // alert(111);
        this.pagination.current = 1;
        this.getList();
      }
    },
    onReset() {
      // console.log(222);
      this.pagination.current = 1;
      this.formData = {
        roleCode: '',
        roleName: '',
        status: '',
      };
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    moreButtonsClick(data: any, rowData: any) {
      if (data.value === 1) {
        this.menuVar.visible = true;
        // console.log("rowData:", rowData);
        this.menuVar.data = { ...rowData, roleId: rowData.id };
      } else if (data.value === 2) {
        this.userVar.visible = true;
        this.userVar.data = { ...rowData };
      } else if (data.value === 3) {
        const confirm = this.$dialog.confirm({
          theme: 'warning',
          header: '温馨提示',
          body: '确认删除该项？',
          onConfirm: async () => {
            (confirm as any).destroy();
            this.updateUserState(rowData.id);
          },
        });
      }else if (data.value === 4) {
        this.roleVar.visible = true;
        this.roleVar.data = { ...rowData };
      }
    },

    /**
     * 关闭对话框
     */
    handelCloseEditModal() {
      // alert(11);
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
    },
    /**
     * 关闭角色对话框
     */
    handelCloseEditMenuModal() {
      // alert(11);
      this.menuVar.visible = false;
      // this.menuVar.data = {};
    },
    /**
     * 关闭用户对话框
     */
    handelCloseEditUserModal() {
      // alert(11);
      this.userVar.visible = false;
      // this.menuVar.data = {};
    },
    handelEditModalUserSuccess() {
      this.userVar.visible = false;
      this.userVar.data = {};
      this.getList();
    },
    handelEditModalSuccess() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
      this.getList();
    },
    /**
     * 添加role成功
     */
    handelEditModalMenuSuccess() {
      this.menuVar.visible = false;
      this.editModalVar.data = {};
      this.getList();
    },
    handelCloseEditRoleModal() {
      this.roleVar.visible = false;
    },
    handelEditRoleModalMenuSuccess() {
      this.roleVar.visible = false;
      this.roleVar.data = {};
      this.getList();
    },
    getComName(compId: any) {
      let result = '';
      const traversal = (list: any = [], compId: any) => {
        for (let i = 0; i < list.length; i++) {
          if (list[i].value === compId) {
            result = list[i].label;
            break;
          }
          if (list[i].children && list[i].children.length > 0) {
            traversal(list[i].children, compId);
          }
        }
      };
      traversal(this.treeNodeOptions, compId);
      return result;
    },
    async getList() {
      const query = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.post(
          systemRoleApi.queryPage.url,
          {
            roleCode: this.formData.roleCode,
            roleName: this.formData.roleName,
            userFrom: '1',
            roleType: '',
            status: this.formData.status,
          },
          {
            params: query,
          },
        );
        const { code, data, msg } = res;
        if (code === 0) {
          // console.log();
          this.data = data.records || [];
          this.pagination.total = Number(data.total);
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },
    /**
     * 批量改变状态
     */
    async changeStatusIds(ids: string[], status: any) {
      try {
        const res = await (this as any).$request.put(apiRoleInfo.updateUseStateBatch.url, {
          dictIds: [],
          state: '',
          ids,
          useState: status,
        });
        console.log(this.selectedRowKeys);
        const { code, msg } = res;
        if (code === 1) {
          this.$message.success(msg || '状态改变成功');
          this.getList();
          return 'success';
        }
        this.$message.error(msg || '状态改变失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
      // back/api/v1/sys-tenant-info/update-usestate-batch
    },
    /**
     * 删除
     */
    async updateUserState(id: string) {
      try {
        const res = await (this as any).$request.post(systemRoleApi.saveOrUpdateRole.url, {
          id: id,
          status: '0',
        });
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success(msg || '删除成功');
          this.getList();
          return 'success';
        }
        this.$message.error(msg || '删除失败');
      } catch (e) {
        this.$message.error('删除失败，请稍后重试');
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

::v-deep .t-button--variant-text {
  padding: 0 !important;
}
.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

.left-operation-container {
  padding: 0 0 10px 0;

  .selected-count {
    display: inline-block;
    color: @text-color-secondary;
  }
}

.search-input {
  width: 360px;
}
.operation-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.more-search-row {
  margin: 10px 0 0 0;
}
.tdesign-demo-dropdown__text {
  color: #4094F3;
  line-height: 0;
}
</style>
