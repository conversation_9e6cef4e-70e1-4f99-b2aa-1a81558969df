<template>
  <view class="order-detail-page" :style="{ height: contentHeight + 'px', paddingTop: topHeight + 'px' }">
    <view class="fixed-header">
      <navigation-bar style="width: 100%; z-index: 99" :showIcon="true" :showText="true" text="订单详情"></navigation-bar>
    </view>

    <scroll-view scroll-y class="detail-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <van-loading type="spinner" color="#007aff" />
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 订单内容 -->
      <view v-else-if="order">
        <!-- 订单状态 -->
        <view class="status-section">
          <view class="status-icon">
            <van-icon :name="statusIcon" size="40" :color="statusColor" />
          </view>
          <view class="status-info">
            <text class="status-text">{{ order.statusText }}</text>
            <text class="status-desc">{{ statusDesc }}</text>
            <!-- 支付倒计时 -->
            <view v-if="order.orderStatus === 'pending' && remainingSeconds > 0" class="payment-countdown">
              <text class="countdown-text">支付剩余时间：{{ countdownDisplay }}</text>
            </view>
            <view v-else-if="order.orderStatus === 'pending' && remainingSeconds <= 0" class="payment-expired">
              <text class="expired-text">支付已过期，请重新下单</text>
            </view>
          </view>
        </view>

        <!-- 物流信息 -->
        <view v-if="order.orderStatus === 'shipped' || order.orderStatus === 'delivered'" class="logistics-section">
          <view class="section-title">物流信息</view>
          <view class="logistics-info">
            <text class="logistics-company">{{ logistics.company }}</text>
            <text class="logistics-number">{{ logistics.trackingNumber }}</text>
          </view>
          <view class="logistics-progress">
            <view v-for="(step, index) in logistics.steps" :key="index" class="progress-item" :class="index === 0 ? 'current' : ''">
              <view class="progress-dot"></view>
              <view class="progress-content">
                <text class="progress-desc">{{ step.desc }}</text>
                <text class="progress-time">{{ step.time }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 收货地址 -->
        <view class="address-section">
          <view class="section-title">收货地址</view>
          <view class="address-info">
            <view class="address-header">
              <text class="receiver-name">{{ order.receiverName }}</text>
              <text class="receiver-phone">{{ order.receiverPhone }}</text>
            </view>
            <text class="address-detail">{{ order.receiverAddress }}</text>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="products-section">
          <view class="section-title">商品信息</view>
          <view class="product-list">
            <view v-for="(item, index) in order.items" :key="index" class="product-item">
              <image :src="item.image" class="product-image" mode="aspectFill" />
              <view class="product-info">
                <text class="product-name">{{ item.name }}</text>
                <text class="product-spec">{{ item.spec }}</text>
                <view class="product-bottom">
                  <text class="product-price">¥{{ item.price }}</text>
                  <text class="product-quantity">x{{ item.quantity }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 退款信息 -->
        <view v-if="refundInfo" class="refund-section">
          <view class="section-title">退款信息</view>

          <!-- 退款状态信息 -->
          <view class="refund-status-info">
            <view class="refund-status-header">
              <view class="refund-status-icon" :class="getRefundStatusIconClass(refundInfo.refundStatus)">
                <van-icon :name="getRefundStatusIcon(refundInfo.refundStatus)" size="16" color="#fff" />
              </view>
              <view class="refund-status-content">
                <text class="refund-status-title">{{ getRefundStatusText(refundInfo.refundStatus) }}</text>
                <text class="refund-status-desc">{{ getRefundStatusDescription(refundInfo.refundStatus) }}</text>
              </view>
              <view class="refund-amount-display">
                <text class="refund-amount-value">¥{{ refundInfo.refundAmount }}</text>
              </view>
            </view>
          </view>

          <!-- 退款详细信息 -->
          <view class="info-list">
            <view class="info-item">
              <text class="info-label">退款单号</text>
              <text class="info-value">{{ refundInfo.refundNo }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">退款原因</text>
              <text class="info-value">{{ refundInfo.refundReason }}</text>
            </view>
            <view v-if="refundInfo.auditTime" class="info-item">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ formatRefundTime(refundInfo.auditTime) }}</text>
            </view>
            <view v-if="refundInfo.wxSuccessTime" class="info-item">
              <text class="info-label">退款时间</text>
              <text class="info-value">{{ formatRefundTime(refundInfo.wxSuccessTime) }}</text>
            </view>
          </view>

          <!-- 退款进度 -->
          <view v-if="refundTimelineSteps.length > 0" class="refund-progress">
            <view class="progress-title">退款进度</view>
            <view class="progress-timeline">
              <view v-for="(step, index) in refundTimelineSteps" :key="index" class="progress-item" :class="{ active: step.active, completed: step.completed }">
                <view class="progress-dot">
                  <van-icon v-if="step.completed" name="success" size="8" color="#fff" />
                </view>
                <view class="progress-content">
                  <text class="progress-text">{{ step.title }}</text>
                  <text v-if="step.time" class="progress-time">{{ step.time }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 评价信息 -->
        <view v-if="order.orderStatus === 'completed'" class="review-section">
          <view class="section-title">
            <text>商品评价</text>
            <text v-if="!hasReviewed" class="review-action" @click="goToReview">去评价</text>
          </view>

          <!-- 已评价状态 -->
          <view v-if="hasReviewed && reviews.length > 0" class="review-content">
            <view v-for="(review, index) in reviews" :key="index" class="review-item">
              <view class="review-header">
                <view class="review-rating">
                  <text v-for="star in 5" :key="star" class="star" :class="{ 'star-filled': star <= review.rating }">★</text>
                </view>
                <text class="review-date">{{ formatDate(review.createdAt) }}</text>
              </view>
              <text class="review-content-text">{{ review.content }}</text>
              <view v-if="review.images && review.images.length > 0" class="review-images">
                <image v-for="(image, imgIndex) in review.images" :key="imgIndex" :src="image" class="review-image" mode="aspectFill" @click="previewReviewImage(imgIndex, review.images)" />
              </view>
              <view v-if="review.tags && review.tags.length > 0" class="review-tags">
                <text v-for="tag in review.tags" :key="tag.id" class="review-tag">{{ tag.name }}</text>
              </view>
            </view>
          </view>

          <!-- 未评价状态 -->
          <view v-else-if="!hasReviewed" class="no-review">
            <text class="no-review-text">您还未对此订单进行评价</text>
            <van-button type="primary" size="small" @click="goToReview">立即评价</van-button>
          </view>
        </view>

        <!-- 订单信息 -->
        <view class="order-info-section">
          <view class="section-title">订单信息</view>
          <view class="info-list">
            <view class="info-item">
              <text class="info-label">订单号</text>
              <text class="info-value">{{ order.orderNumber }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">下单时间</text>
              <text class="info-value">{{ order.createTime }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">支付方式</text>
              <text class="info-value">{{ order.paymentMethod || "微信支付" }}</text>
            </view>

            <view class="info-item total">
              <text class="info-label">实付金额</text>
              <text class="info-value total-amount">¥{{ order.totalAmount }}</text>
            </view>
          </view>
        </view>

        <!-- 订单内容结束 -->
      </view>

      <!-- 错误状态 -->
      <view v-else-if="!loading" class="error-state">
        <van-empty description="订单不存在或已删除" />
        <van-button type="primary" @click="goBack">返回</van-button>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions" v-if="hasActions">
      <!-- 待支付状态 -->
      <van-button v-if="order && order.orderStatus === 'pending'" type="default" @click="cancelOrder"> 取消订单 </van-button>
      <van-button v-if="order && order.orderStatus === 'pending'" type="primary" @click="payOrder"> 立即支付 </van-button>

      <!-- 支付失败状态 -->
      <van-button v-if="order && order.orderStatus === 'failed'" type="default" @click="cancelOrder"> 取消订单 </van-button>
      <van-button v-if="order && order.orderStatus === 'failed'" type="primary" @click="payOrder"> 重新支付 </van-button>

      <!-- 其他状态 -->
      <van-button v-if="order && order.orderStatus === 'shipped'" type="primary" @click="confirmReceive"> 确认收货 </van-button>
      <van-button v-if="order && order.orderStatus === 'completed'" type="default" @click="contactService"> 联系客服 </van-button>
      <van-button v-if="order && order.orderStatus === 'completed'" type="primary" @click="buyAgain"> 再次购买 </van-button>
    </view>
  </view>
</template>

<script lang="ts">
import Vue from "vue";
import { Component } from "vue-property-decorator";
import NavigationBar from "../../components/common/NavigationBar.vue";
import { getOrderDetail, cancelOrder as cancelOrderApi, confirmOrder, getOrderPaymentStatus } from "../../api/mall.api";
import { getRefundByOrderId } from "../../api/refund.api";
import { Order } from "../../types/mall.types";
import { RefundOrder, REFUND_STATUS_MAP } from "../../types/refund.types";
import { Code } from "../../constants/enum/code.enum";
import { checkWechatPayEnvironment } from "../../utils/wechat-pay";
import { getAppId } from "@/api/auth.api";
import { getLayoutHeights } from "@/utils/get-page-height.util";

interface TimelineStep {
  title: string;
  time?: string;
  active: boolean;
  completed: boolean;
}

@Component({
  name: "order-detail",
  components: { NavigationBar },
})
export default class OrderDetail extends Vue {
  topHeight = 88;
  contentHeight = 0;
  loading = true;
  orderId = "";
  customerId = "";
  order: Order | null = null;
  paymentInfo: any = null; // 支付信息
  remainingSeconds = 0; // 支付倒计时（秒）
  countdownTimer: any = null; // 倒计时定时器
  reviews: any[] = [];
  hasReviewed = false;
  refundInfo: RefundOrder | null = null; // 退款信息

  onShow() {
    const { topHeight, contentHeight } = getLayoutHeights();
    this.topHeight = topHeight;
    this.contentHeight = contentHeight;
  }

  // 获取用户ID
  getUserId(): string {
    try {
      const userInfo = uni.getStorageSync("userInfo");
      console.log("订单详情页面 - 获取用户信息:", userInfo);

      if (userInfo && userInfo.id) {
        const customerId = String(userInfo.id);
        console.log("订单详情页面 - 用户ID:", customerId);
        return customerId;
      } else {
        console.log("订单详情页面 - 用户信息不存在，需要登录");
        // 如果没有用户信息，返回空字符串，让页面处理登录逻辑
        return "";
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
      // 返回空字符串，让页面处理登录逻辑
      return "";
    }
  }

  // 加载订单详情
  async loadOrderDetail() {
    console.log("订单详情页面 - 准备加载订单详情");
    console.log("订单详情页面 - orderId:", this.orderId);
    console.log("订单详情页面 - customerId:", this.customerId);

    if (this.orderId === "" || this.customerId === "") {
      console.log("订单详情页面 - 参数为空，不加载订单详情");
      uni.showToast({
        title: "参数错误，无法加载订单详情",
        icon: "none",
      });
      return;
    }

    this.loading = true;
    try {
      // 先加载订单基本信息
      console.log("订单详情页面 - 调用API获取订单详情");
      const response = await getOrderDetail(this.orderId, this.customerId);
      console.log("订单详情页面 - API响应:", response);

      if (response.code === Code.OK.code) {
        this.order = response.data;
        console.log("订单详情页面 - 订单数据:", this.order);

        // 如果订单状态为待支付，加载支付信息
        if (this.order && this.order.orderStatus === "pending") {
          await this.loadPaymentInfo();
        }

        // 加载退款信息（如果存在）
        await this.loadRefundInfo();
      } else {
        console.error("订单详情页面 - API返回错误:", response.msg);
        uni.showToast({
          title: response.msg || "加载订单详情失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载订单详情失败:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    } finally {
      this.loading = false;
    }
  }

  // 加载支付信息
  async loadPaymentInfo() {
    try {
      console.log("订单详情页面 - 加载支付信息");
      const appId = getAppId();
      const response = await getOrderPaymentStatus(this.orderId, this.customerId, appId);
      console.log("订单详情页面 - 支付状态响应:", response);

      if (response.code === Code.OK.code && response.data) {
        this.paymentInfo = response.data;

        // 设置倒计时
        if (this.paymentInfo.remainingSeconds && this.paymentInfo.remainingSeconds > 0) {
          this.remainingSeconds = this.paymentInfo.remainingSeconds;
          this.startCountdown();
        }

        console.log("订单详情页面 - 支付信息加载成功:", this.paymentInfo);
      } else {
        console.warn("订单详情页面 - 支付信息加载失败:", response.msg);
      }
    } catch (error) {
      console.error("订单详情页面 - 加载支付信息异常:", error);
    }
  }

  // 开始倒计时
  startCountdown() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }

    this.countdownTimer = setInterval(() => {
      if (this.remainingSeconds > 0) {
        this.remainingSeconds--;
      } else {
        this.clearCountdown();
        // 倒计时结束，重新加载订单信息
        this.loadOrderDetail();
      }
    }, 1000);
  }

  // 清除倒计时
  clearCountdown() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  }

  // 加载退款信息
  async loadRefundInfo() {
    try {
      console.log("订单详情页面 - 加载退款信息");
      const response = await getRefundByOrderId(this.orderId, this.customerId);
      console.log("订单详情页面 - 退款信息响应:", response);

      if (response.code === Code.OK.code && response.data) {
        this.refundInfo = response.data;
        console.log("订单详情页面 - 退款信息加载成功:", this.refundInfo);
      } else {
        // 没有退款信息是正常的，不需要显示错误
        console.log("订单详情页面 - 该订单没有退款信息");
        this.refundInfo = null;
      }
    } catch (error) {
      console.error("订单详情页面 - 加载退款信息异常:", error);
      this.refundInfo = null;
    }
  }

  // 格式化倒计时显示
  get countdownDisplay() {
    if (this.remainingSeconds <= 0) return "已过期";

    const minutes = Math.floor(this.remainingSeconds / 60);
    const seconds = this.remainingSeconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }

  logistics = {
    company: "顺丰速运",
    trackingNumber: "SF1234567890",
    steps: [
      {
        desc: "快件已送达，感谢使用顺丰速运",
        time: "2023-12-16 10:30",
      },
      {
        desc: "快件正在派送中，派送员：李师傅 13800138000",
        time: "2023-12-16 08:00",
      },
      {
        desc: "快件已到达【上海浦东新区营业点】",
        time: "2023-12-16 06:30",
      },
      {
        desc: "快件已发出【上海转运中心】",
        time: "2023-12-15 20:15",
      },
      {
        desc: "快件已收件",
        time: "2023-12-15 15:00",
      },
    ],
  };

  get hasActions() {
    return this.order && ["pending", "shipped", "completed"].includes(this.order.orderStatus);
  }

  async onLoad(options: any) {
    console.log("订单详情页面 - onLoad 开始");
    console.log("订单详情页面 - options:", options);

    this.customerId = this.getUserId();
    this.orderId = options.id || "";

    console.log("订单详情页面 - 初始化完成");
    console.log("订单详情页面 - customerId:", this.customerId);
    console.log("订单详情页面 - orderId:", this.orderId);

    // 检查订单ID
    if (!this.orderId) {
      uni.showToast({
        title: "订单ID无效",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack({});
      }, 1500);
      return;
    }

    // 检查用户登录状态
    if (!this.customerId) {
      uni.showModal({
        title: "登录提示",
        content: "查看订单详情需要登录，是否立即登录？",
        confirmText: "立即登录",
        cancelText: "返回",
        success: async (res) => {
          if (res.confirm) {
            // 导入认证管理器并尝试登录
            const { authManager } = await import("../../utils/auth");
            const userId = await authManager.getUserId(true);
            if (userId) {
              this.customerId = userId;
              await this.loadOrderDetail();
            } else {
              // 用户取消登录，返回上一页
              uni.navigateBack({});
            }
          } else {
            // 用户选择返回
            uni.navigateBack({});
          }
        },
      });
      return;
    }

    // 加载订单详情
    await this.loadOrderDetail();

    // 如果订单已完成，加载评价信息
    if (this.order && this.order.orderStatus === "completed") {
      await this.loadOrderReviews();
    }
  }

  get statusIcon() {
    if (!this.order) return "clock-o";
    const iconMap = {
      pending: "clock-o",
      paid: "logistics",
      shipped: "logistics",
      completed: "checked",
    };
    return iconMap[this.order.orderStatus] || "clock-o";
  }

  get statusColor() {
    if (!this.order) return "#666";
    const colorMap = {
      pending: "#ff4444",
      paid: "#1890ff",
      shipped: "#52c41a",
      completed: "#52c41a",
    };
    return colorMap[this.order.orderStatus] || "#666";
  }

  get statusDesc() {
    if (!this.order) return "";
    const descMap = {
      pending: "请尽快完成支付",
      paid: "商家正在准备发货",
      shipped: "商品正在配送中，请耐心等待",
      completed: "订单已完成",
    };
    return descMap[this.order.orderStatus] || "";
  }

  goBack() {
    uni.navigateBack({});
  }

  // 加载订单评价
  async loadOrderReviews() {
    try {
      const mallExtendedApi = await import("../../api/mall-extended.api");
      const result = await mallExtendedApi.getOrderReviews(this.orderId);

      if (result.code === 0) {
        this.reviews = result.data || [];
        this.hasReviewed = this.reviews.length > 0;
        console.log("订单评价加载成功:", this.reviews);
      } else {
        console.error("获取订单评价失败:", result.msg);
        this.reviews = [];
        this.hasReviewed = false;
      }
    } catch (error) {
      console.error("加载订单评价异常:", error);
      this.reviews = [];
      this.hasReviewed = false;
    }
  }

  // 跳转到评价页面
  goToReview() {
    uni.navigateTo({
      url: `/pages/orders/order-review?id=${this.orderId}`,
    });
  }

  // 预览评价图片
  previewReviewImage(index: number, images: string[]) {
    uni.previewImage({
      current: index,
      urls: images,
    });
  }

  // 格式化日期
  formatDate(dateStr: string): string {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
  }

  // 格式化退款时间
  formatRefundTime(time: string): string {
    if (!time) return "-";
    return new Date(time).toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // 获取退款状态文本
  getRefundStatusText(status: string): string {
    return REFUND_STATUS_MAP[status as keyof typeof REFUND_STATUS_MAP]?.label || "未知状态";
  }

  // 获取退款状态描述
  getRefundStatusDescription(status: string): string {
    const descriptions: Record<string, string> = {
      PENDING: "退款申请正在审核中，请耐心等待",
      APPROVED: "退款申请已通过审核",
      REJECTED: "退款申请已被拒绝",
      PROCESSING: "退款正在处理中，请耐心等待",
      SUCCESS: "退款已成功，已原路退回到您的账户",
      FAILED: "退款处理失败",
    };
    return descriptions[status] || "未知状态";
  }

  // 获取退款状态图标
  getRefundStatusIcon(status: string): string {
    const icons: Record<string, string> = {
      PENDING: "clock",
      APPROVED: "success",
      REJECTED: "cross",
      PROCESSING: "clock",
      SUCCESS: "success",
      FAILED: "cross",
    };
    return icons[status] || "info";
  }

  // 获取退款状态图标样式类
  getRefundStatusIconClass(status: string): string {
    const classes: Record<string, string> = {
      PENDING: "refund-icon-pending",
      APPROVED: "refund-icon-approved",
      REJECTED: "refund-icon-rejected",
      PROCESSING: "refund-icon-processing",
      SUCCESS: "refund-icon-success",
      FAILED: "refund-icon-failed",
    };
    return classes[status] || "refund-icon-default";
  }

  // 获取退款时间轴步骤
  get refundTimelineSteps(): TimelineStep[] {
    if (!this.refundInfo) return [];

    const steps: TimelineStep[] = [
      {
        title: "提交退款申请",
        time: this.formatRefundTime(this.refundInfo.applyTime),
        active: true,
        completed: true,
      },
    ];

    if (this.refundInfo.auditTime) {
      const auditResultText = this.refundInfo.auditResult === "APPROVED" ? "已同意" : "已拒绝";
      steps.push({
        title: `商家审核${auditResultText}`,
        time: this.formatRefundTime(this.refundInfo.auditTime),
        active: true,
        completed: true,
      });
    }

    if (this.refundInfo.refundStatus === "PROCESSING") {
      steps.push({
        title: "退款处理中",
        active: true,
        completed: false,
      });
    }

    if (this.refundInfo.wxSuccessTime) {
      steps.push({
        title: "退款成功",
        time: this.formatRefundTime(this.refundInfo.wxSuccessTime),
        active: true,
        completed: true,
      });
    }

    return steps;
  }

  // 跳转到退款详情页面
  goToRefundDetail() {
    if (!this.refundInfo) return;
    uni.navigateTo({
      url: `/pages/refund/refund-detail?refundId=${this.refundInfo.id}&customerId=${this.customerId}`,
    });
  }

  async cancelOrder() {
    if (!this.order) return;

    // 防止重复点击
    if (this.loading) {
      return;
    }

    uni.showModal({
      title: "取消订单",
      content: "确定要取消这个订单吗？",
      success: async (res) => {
        if (res.confirm) {
          this.loading = true;
          try {
            const response = await cancelOrderApi(this.order!.id, this.customerId);
            if (response.code === Code.OK.code) {
              uni.showToast({ title: "订单已取消", icon: "success" });
              await this.loadOrderDetail(); // 重新加载订单详情
            } else {
              uni.showToast({
                title: response.msg || "取消订单失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("取消订单失败:", error);
            uni.showToast({
              title: "网络错误，请重试",
              icon: "none",
            });
          } finally {
            this.loading = false;
          }
        }
      },
    });
  }

  async payOrder() {
    if (!this.order) return;

    // 防止重复点击
    if (this.loading) {
      return;
    }
    // 检查微信支付环境
    if (!checkWechatPayEnvironment()) {
      uni.showToast({
        title: "当前环境不支持微信支付",
        icon: "none",
      });
      return;
    }
    try {
      console.log("订单详情 - 开始支付流程", this.order);
      // 如果有支付信息且包含微信支付参数，直接使用
      if (this.paymentInfo && this.paymentInfo.paymentParams) {
        console.log("订单详情 - 使用缓存的支付参数", this.paymentInfo.paymentParams);
        // 直接调用微信支付
        uni.requestPayment({
          provider: "wxpay",
          timeStamp: this.paymentInfo.paymentParams.timeStamp,
          nonceStr: this.paymentInfo.paymentParams.nonceStr,
          package: this.paymentInfo.paymentParams.packageValue,
          signType: this.paymentInfo.paymentParams.signType || "RSA",
          paySign: this.paymentInfo.paymentParams.paySign,
          success: async (res: any) => {
            console.log("订单详情 - 支付成功:", res);
            uni.showToast({
              title: "支付成功",
              icon: "success",
            });
            this.clearCountdown();
            await this.loadOrderDetail(); // 重新加载订单详情
          },
          fail: (err: any) => {
            console.error("订单详情 - 支付失败:", err);
            if (err.errMsg && err.errMsg.includes("cancel")) {
              uni.showToast({
                title: "支付已取消",
                icon: "none",
              });
            } else {
              uni.showToast({
                title: "支付失败，请重试",
                icon: "none",
              });
            }
          },
        });
        return;
      }
      uni.showToast({
        title: "支付失败，订单已过期",
        icon: "none",
      });
    } catch (error) {
      console.error("订单详情 - 支付异常:", error);
      uni.showToast({
        title: "网络错误，请重试",
        icon: "none",
      });
    }
  }

  async confirmReceive() {
    if (!this.order) return;

    uni.showModal({
      title: "确认收货",
      content: "确认已收到商品吗？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const response = await confirmOrder(this.order!.id, this.customerId);
            if (response.code === Code.OK.code) {
              uni.showToast({ title: "确认收货成功", icon: "success" });
              await this.loadOrderDetail(); // 重新加载订单详情
            } else {
              uni.showToast({
                title: response.msg || "确认收货失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("确认收货失败:", error);
            uni.showToast({
              title: "网络错误，请重试",
              icon: "none",
            });
          }
        }
      },
    });
  }

  contactService() {
    uni.showToast({ title: "联系客服功能开发中", icon: "none" });
  }

  buyAgain() {
    uni.showToast({ title: "已添加到购物车", icon: "success" });
  }

  // 组件销毁时清除定时器
  beforeDestroy() {
    this.clearCountdown();
  }
}
</script>

<style lang="scss" scoped>
.order-detail-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
}

.status-section {
  background-color: #fff;
  padding: 30px 20px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.status-icon {
  margin-right: 20px;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.status-desc {
  font-size: 14px;
  color: #666;
}

.payment-countdown {
  margin-top: 8px;
}

.countdown-text {
  font-size: 14px;
  color: #ff4444;
  font-weight: bold;
}

.payment-expired {
  margin-top: 8px;
}

.expired-text {
  font-size: 14px;
  color: #999;
}

.logistics-section,
.address-section,
.products-section,
.refund-section,
.order-info-section {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 20px;
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  margin-bottom: 15px;
}

.logistics-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.logistics-company {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.logistics-number {
  font-size: 14px;
  color: #666;
}

.logistics-progress {
  position: relative;
}

.progress-item {
  display: flex;
  margin-bottom: 20px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  &.current {
    .progress-dot {
      background-color: #52c41a;
      border-color: #52c41a;
    }

    .progress-desc {
      color: #52c41a;
      font-weight: bold;
    }
  }

  &:not(:last-child)::after {
    content: "";
    position: absolute;
    left: 6px;
    top: 20px;
    width: 1px;
    height: 30px;
    background-color: #e8e8e8;
  }
}

.progress-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e8e8e8;
  border: 2px solid #e8e8e8;
  margin-right: 15px;
  margin-top: 2px;
  flex-shrink: 0;
}

.progress-content {
  flex: 1;
}

.progress-desc {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: block;
}

.progress-time {
  font-size: 12px;
  color: #999;
}

.address-info {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.address-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.receiver-name {
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.receiver-phone {
  font-size: 14px;
  color: #666;
}

.address-detail {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.product-list {
  border-top: 1px solid #f5f5f5;
}

.product-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #f8f8f8;

  &:last-child {
    border-bottom: none;
  }
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  margin-right: 15px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.product-spec {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 14px;
  color: #ff4444;
  font-weight: bold;
}

.product-quantity {
  font-size: 12px;
  color: #666;
}

.info-list {
  border-top: 1px solid #f5f5f5;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid #f8f8f8;

  &:last-child {
    border-bottom: none;
  }

  &.total {
    background-color: #f8f9fa;
    margin: 0 -20px;
    padding: 15px 20px;
  }
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;

  &.total-amount {
    color: #ff4444;
    font-weight: bold;
    font-size: 16px;
  }
}

.loading-state {
  padding: 100px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.error-state {
  padding: 100px 20px;
  text-align: center;

  .van-button {
    margin-top: 20px;
    width: 200px;
  }
}

/* 评价区域 */
.review-section {
  margin: 12px 16px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.review-action {
  color: #007aff;
  font-size: 14px;
  margin-left: auto;
}

.review-content {
  padding: 16px;
}

.review-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.review-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.review-rating {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 14px;
  color: #e5e5e5;
}

.star-filled {
  color: #ffd700;
}

.review-date {
  font-size: 12px;
  color: #999;
}

.review-content-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.review-images {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.review-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
}

.review-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.review-tag {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 12px;
  font-size: 12px;
  color: #666;
}

.no-review {
  padding: 32px 16px;
  text-align: center;
}

.no-review-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 16px;
  display: block;
}

.bottom-actions {
  background-color: #fff;
  padding: 15px 20px;
  display: flex;
  gap: 15px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);

  .van-button {
    flex: 1;
  }

/* 退款相关样式 */
.refund-status-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.refund-status-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.refund-status-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;

  &.refund-icon-pending {
    background-color: #fa8c16;
  }

  &.refund-icon-approved {
    background-color: #52c41a;
  }

  &.refund-icon-rejected {
    background-color: #ff4d4f;
  }

  &.refund-icon-processing {
    background-color: #1890ff;
  }

  &.refund-icon-success {
    background-color: #52c41a;
  }

  &.refund-icon-failed {
    background-color: #ff4d4f;
  }

  &.refund-icon-default {
    background-color: #999999;
  }
}

.refund-status-content {
  flex: 1;
}

.refund-status-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.refund-status-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.refund-amount-display {
  text-align: right;
}

.refund-amount-value {
  font-size: 16px;
  font-weight: bold;
  color: #ff4444;
}

.refund-progress {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f5f5f5;
}

.progress-title {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  margin-bottom: 12px;
}

.progress-timeline {
  .progress-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      .progress-dot {
        background-color: #1890ff;
        border-color: #1890ff;
      }

      .progress-text {
        color: #333;
        font-weight: 500;
      }
    }

    &.completed {
      .progress-dot {
        background-color: #52c41a;
        border-color: #52c41a;
      }
    }
  }
}

.progress-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #f0f0f0;
  border: 2px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-top: 1px;
  flex-shrink: 0;
}

.progress-content {
  flex: 1;
}

.progress-text {
  display: block;
  color: #666;
  font-size: 12px;
  margin-bottom: 2px;
  line-height: 1.4;
}

.progress-time {
  display: block;
  color: #999;
  font-size: 10px;
  line-height: 1.3;
}

.refund-actions {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #f5f5f5;
  margin-top: 15px;
}

.refund-detail-link {
  color: #007aff;
  font-size: 14px;
  cursor: pointer;

  &:active {
    opacity: 0.7;
  }
}
}
</style>
