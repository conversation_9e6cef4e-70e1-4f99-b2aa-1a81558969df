<template>
  <div class="marketing-campaign-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="discount" />
          {{ campaignType === 'edit' ? '编辑营销活动' : '新建营销活动' }}
        </div>
        <div class="page-breadcrumb">
          <t-breadcrumb>
            <t-breadcrumb-item>营销管理</t-breadcrumb-item>
            <t-breadcrumb-item>营销活动</t-breadcrumb-item>
            <t-breadcrumb-item>{{ campaignType === 'edit' ? '编辑活动' : '新建活动' }}</t-breadcrumb-item>
          </t-breadcrumb>
        </div>
      </div>
    </div>

    <!-- 基础配置卡片 -->
    <div class="config-card">
      <div class="card-header">
        <h3 class="card-title">
          <t-icon name="setting" />
          基础配置
        </h3>
      </div>
      <div class="card-content">
        <t-form :data="campaign" :rules="rules" ref="videoForm" validate-trigger="blur" class="config-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <div class="form-row">
              <t-form-item class="form-item" label="活动类型" name="activityType" required>
                <t-select
                  :disabled="campaignType === 'edit'"
                  v-model="campaign.activityType"
                  placeholder="请选择活动类型"
                  :options="activityList"
                  @change="handleActivityTypeChange"
                  class="activity-type-select"
                >
                  <template #prefixIcon>
                    <t-icon name="layers" />
                  </template>
                </t-select>
              </t-form-item>
              <t-form-item class="form-item" label="营销活动分组" name="groupId" required>
                <t-select
                  v-model="campaign.groupId"
                  :options="courseList"
                  placeholder="请选择营销活动分组"
                  class="group-select"
                >
                  <template #prefixIcon>
                    <t-icon name="folder" />
                  </template>
                </t-select>
              </t-form-item>
            </div>
            <t-form-item class="form-item" label="活动名称" name="title">
              <t-textarea
                v-model="campaign.title"
                placeholder="请输入活动名称"
                :autosize="{ minRows: 2, maxRows: 4 }"
                class="activity-title"
              ></t-textarea>
            </t-form-item>
          </div>
          <!-- 数量配置 -->
          <div class="form-section" v-if="campaign.activityType !== '4' && campaign.activityType !== '5' && campaign.activityType !== '6'">
            <div class="section-title">数量配置</div>
            <div class="form-row">
              <t-form-item class="form-item" label="总发放数量" name="totalQuantity" required>
                <t-input
                  v-model="campaign.totalQuantity"
                  type="number"
                  clearable
                  placeholder="请输入总发放数量"
                  class="quantity-input"
                >
                  <template #prefixIcon>
                    <t-icon name="numbers" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">个</span>
                  </template>
                </t-input>
              </t-form-item>
              <t-form-item class="form-item" label="剩余数量" name="remainingQuantity" required>
                <t-input
                  v-model="campaign.remainingQuantity"
                  clearable
                  type="number"
                  placeholder="请输入剩余数量"
                  class="quantity-input"
                >
                  <template #prefixIcon>
                    <t-icon name="numbers" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">个</span>
                  </template>
                </t-input>
              </t-form-item>
            </div>
          </div>
          <!-- 问答活动配置 -->
          <div class="form-section" v-if="campaign.activityType === '5'">
            <div class="section-title">问答活动配置</div>

            <!-- 智能识别区域 -->
            <div class="question-parser">
              <div class="parser-header">
                <t-icon name="auto-fit" />
                <span>智能识别</span>
              </div>
              <div class="parser-content">
                <t-textarea
                  v-model="tempQuestionText"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  placeholder="【粘贴并识别】或输入问题（示例单选：问题描述，选项A内容，B选项内容，C选项，D选项，正确答案（如：A）示例多选：问题描述，选项A内容，B选项内容，C选项，D选项，正确答案（如：A,B 或 AB））"
                  class="parser-textarea"
                ></t-textarea>
                <div class="parser-actions">
                  <t-button theme="primary" @click="handleParse" class="parse-btn">
                    <t-icon name="auto-fit" />
                    {{ tempQuestionText ? '智能识别' : '粘贴并识别' }}
                  </t-button>
                </div>
              </div>
            </div>

            <!-- 问题基本设置 -->
            <div class="form-row">
              <t-form-item class="form-item" label="问题题型" name="questionType" required>
                <t-select
                  v-model="campaign.questionType"
                  placeholder="请选择问题题型"
                  :options="questionTypeList"
                  class="question-type-select"
                >
                  <template #prefixIcon>
                    <t-icon name="help-circle" />
                  </template>
                </t-select>
              </t-form-item>
              <t-form-item class="form-item" label="通过分数" name="passScore" required>
                <t-input
                  v-model="campaign.passScore"
                  type="number"
                  placeholder="请输入通过分数"
                  class="score-input"
                >
                  <template #prefixIcon>
                    <t-icon name="star" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">分</span>
                  </template>
                </t-input>
              </t-form-item>
            </div>

            <!-- 问题内容 -->
            <t-form-item class="form-item" label="问题标题" name="questionText" required>
              <t-textarea
                v-model="campaign.questionText"
                placeholder="请输入问题标题"
                :autosize="{ minRows: 2, maxRows: 4 }"
                class="question-title"
              ></t-textarea>
            </t-form-item>

            <!-- 选项配置 -->
            <div class="options-section">
              <div class="options-title">答案选项</div>
              <div class="options-grid">
                <t-form-item
                  v-for="option in questionOption"
                  :key="option.value"
                  class="option-item"
                  :name="`option${option.value}`"
                  :label="`选项${option.value}`"
                  required
                >
                  <t-input
                    v-model="campaign[`option${option.value}`]"
                    placeholder="请输入选项内容"
                    class="option-input"
                  >
                    <template #prefixIcon>
                      <span class="option-label">{{ option.value }}</span>
                    </template>
                  </t-input>
                </t-form-item>
              </div>
            </div>

            <!-- 正确答案 -->
            <t-form-item class="form-item" label="正确答案" name="correctAnswer" required>
              <t-select
                v-model="campaign.correctAnswer"
                placeholder="请选择正确答案"
                :multiple="campaign.questionType === '2'"
                :options="questionOption"
                class="answer-select"
              >
                <template #prefixIcon>
                  <t-icon name="check-circle" />
                </template>
              </t-select>
            </t-form-item>
          </div>
        </t-form>
      </div>
    </div>

    <!-- 奖励配置卡片 -->
    <div class="config-card">
      <div class="card-header">
        <h3 class="card-title">
          <t-icon name="money-circle" />
          奖励配置
        </h3>
      </div>
      <div class="card-content">
        <t-form :data="campaign" :rules="configRules" ref="configForm" class="config-form">
          <!-- 奖励类型配置 -->
          <div class="form-section">
            <div class="section-title">奖励类型</div>
            <div class="form-row">
              <t-form-item
                class="form-item"
                :label="campaignTypeLabel"
                name="type"
                required
                v-if="campaign.activityType !== '4'"
              >
                <t-select
                  :disabled="campaignType === 'edit'"
                  v-model="campaign.type"
                  :placeholder="campaignTypePlaceholder"
                  :options="typeList"
                  @change="handleTypeChange"
                  class="reward-type-select"
                >
                  <template #prefixIcon>
                    <t-icon name="gift" />
                  </template>
                </t-select>
              </t-form-item>
            </div>
          </div>

          <!-- 金额配置 -->
          <div class="form-section" v-if="['1','5','6'].includes(campaign.activityType) || campaign.activityType === '2'">
            <div class="section-title">金额配置</div>
            <div class="form-row">
              <!-- 固定金额 -->
              <t-form-item
                class="form-item"
                label="金额（元）"
                name="amount"
                required
                v-if="['1','5','6'].includes(campaign.activityType) && campaign.type === 'FIXED'"
              >
                <t-input
                  v-model="campaign.amount"
                  clearable
                  type="number"
                  placeholder="请输入红包金额"
                  class="amount-input"
                >
                  <template #prefixIcon>
                    <t-icon name="money-circle" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">元</span>
                  </template>
                </t-input>
              </t-form-item>

              <!-- 最小金额 -->
              <t-form-item
                class="form-item"
                label="最小金额（元）"
                name="minAmount"
                v-if="(['1','5','6'].includes(campaign.activityType) && campaign.type === 'RANDOM') || (campaign.activityType === '2' && campaign.type === 'PERCENT')"
              >
                <t-input
                  v-model="campaign.minAmount"
                  clearable
                  type="number"
                  placeholder="请输入最小金额"
                  class="amount-input"
                >
                  <template #prefixIcon>
                    <t-icon name="money-circle" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">元</span>
                  </template>
                </t-input>
              </t-form-item>

              <!-- 最大金额 -->
              <t-form-item
                class="form-item"
                label="最大金额（元）"
                name="maxAmount"
                v-if="['1','5','6'].includes(campaign.activityType) && campaign.type === 'RANDOM'"
              >
                <t-input
                  v-model="campaign.maxAmount"
                  clearable
                  type="number"
                  placeholder="请输入最大金额"
                  class="amount-input"
                >
                  <template #prefixIcon>
                    <t-icon name="money-circle" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">元</span>
                  </template>
                </t-input>
              </t-form-item>
            </div>
          </div>

          <!-- 其他配置 -->
          <div class="form-section" v-if="campaign.activityType === '2' || campaign.activityType === '3' || campaign.activityType === '4'">
            <div class="section-title">其他配置</div>
            <div class="form-row">
              <!-- 优惠券折现率 -->
              <t-form-item
                class="form-item"
                label="优惠券折现率"
                name="discountValue"
                v-if="campaign.activityType === '2'"
              >
                <t-input
                  v-model="campaign.discountValue"
                  clearable
                  type="number"
                  placeholder="请输入优惠券折现率"
                  class="discount-input"
                >
                  <template #prefixIcon>
                    <t-icon name="discount" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">%</span>
                  </template>
                </t-input>
              </t-form-item>

              <!-- 礼品描述 -->
              <t-form-item
                class="form-item"
                label="礼品描述"
                name="describe"
                v-if="campaign.activityType === '3'"
              >
                <t-input
                  v-model="campaign.describe"
                  clearable
                  placeholder="请输入礼品描述"
                  class="gift-input"
                >
                  <template #prefixIcon>
                    <t-icon name="gift" />
                  </template>
                </t-input>
              </t-form-item>

              <!-- 积分 -->
              <t-form-item
                class="form-item"
                label="积分"
                name="points"
                v-if="campaign.activityType === '4'"
              >
                <t-input
                  v-model="campaign.points"
                  type="number"
                  clearable
                  placeholder="请输入积分"
                  class="points-input"
                >
                  <template #prefixIcon>
                    <t-icon name="star" />
                  </template>
                  <template #suffix>
                    <span class="input-suffix">分</span>
                  </template>
                </t-input>
              </t-form-item>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <t-button theme="primary" @click="submit" class="submit-btn">
              <t-icon name="check" />
              {{ campaignType === 'edit' ? '更新活动' : '创建活动' }}
            </t-button>
            <t-button variant="outline" @click="reset" class="reset-btn">
              <t-icon name="refresh" />
              重置
            </t-button>
          </div>
        </t-form>
      </div>
    </div>
  </div>
</template>

<script>
import sysCampaignGroupApi from "@/constants/api/back/sys-campaign-group.api";
import {CAMPAIGN_TYPE_OPTIONS, MARKETING_OPTIONS} from "@/constants/enum/business/marketing-campaign-array.enum";
import sysMarketingCampaignApi from "@/constants/api/back/sys-marketing-campaing.api";
import BeanUtilsService from "@/service/bean-utils.service";

export default {
  name: 'EditMarketingCampaign',
  components: {},
  data() {
    return {
      campaign: {
        activityType: '5',
        type: '',
      },
      campaignType: '',
      tempQuestionText: '',
      rules: {
        activityType: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
        groupId: [{ required: true, message: '请选择营销活动分组', trigger: 'change' }],
        title: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
        startTime: [{ required: true, message: '请选择生效时间', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择失效时间', trigger: 'change' }],
        totalQuantity: [{ required: true, message: '请输入总发放数量', trigger: 'change' }],
        remainingQuantity: [{ required: true, message: '请输入剩余数量', trigger: 'change' }],
        questionText: [{ required: true, message: '请输入问题标题', trigger: 'change' }],
        questionType: [{ required: true, message: '请选择问题类型', trigger: 'change' }],
        passScore: [{ required: true, message: '请输入通过分数', trigger: 'change' }],
        optionA: [{ required: true, message: '请输入选项A内容', trigger: 'change' }, { whitespace: true, message: '选项A内容不能为空', trigger: 'change' }],
        optionB: [{ required: true, message: '请输入选项B内容', trigger: 'change' }, { whitespace: true, message: '选项B内容不能为空', trigger: 'change' }],
        optionC: [{ required: true, message: '请输入选项C内容', trigger: 'change' }, { whitespace: true, message: '选项C内容不能为空', trigger: 'change' }],
        optionD: [{ required: true, message: '请输入选项D内容', trigger: 'change' }, { whitespace: true, message: '选项D内容不能为空', trigger: 'change' }],
        correctAnswer: [{ required: true, message: '请选择正确答案', trigger: 'change' }],
      },
      courseList: [],
      activityList: MARKETING_OPTIONS,
      configRules: {
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        amount: [{ required: true, message: '请输入红包金额', trigger: 'change' }],
        minAmount: [{ required: true, message: '请输入随机红包最小金额', trigger: 'change' }],
        maxAmount: [{ required: true, message: '请输入随机红包最大金额', trigger: 'change' }],
        discountValue: [{ required: true, message: '请输入优惠券折现率', trigger: 'change' }],
        describe: [{ required: true, message: '请输入礼品描述', trigger: 'change' }],
        points: [{ required: true, message: '请输入积分', trigger: 'change' }],
      },
      presets: {
        今天: [new Date(), new Date()],
      },
      questionTypeList: [
        { label: '单选题', value: '1' },
        { label: '多选题', value: '2' },
      ],
      questionOption: [
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' },
        { label: 'D', value: 'D' }
      ]
    };
  },
  computed: {
    typeList() {
      return CAMPAIGN_TYPE_OPTIONS[this.campaign.activityType] || [];
    },
    campaignTypeLabel() {
      const data = MARKETING_OPTIONS.find(item => item.value === this.campaign.activityType)?.label;
      return `${data ? `${data}类型` : "类型"}`;
    },
    campaignTypePlaceholder() {
      const data = MARKETING_OPTIONS.find(item => item.value === this.campaign.activityType)?.label;
      return `请选择${data ? `${data}类型` : "类型"}`;
    },
  },
  watch: {
    'campaign.activityType': {
      handler(val, oldVal) {
        if (val === oldVal) return;
        if (val === '1' && this.campaign.type !== 'FIXED' && this.campaignType !== 'edit') this.campaign.type = 'FIXED';
        if (val === '2' && this.campaign.type !== 'PERCENT' && this.campaignType !== 'edit') this.campaign.type = 'PERCENT';
        if (val === '3' && this.campaign.type !== 'PHYSICAL' && this.campaignType !== 'edit') this.campaign.type = 'PHYSICAL';
        if (val === '5' && this.campaign.type !== 'FIXED' && this.campaignType !== 'edit') {
          this.campaign.type = 'FIXED';
          this.$set(this.campaign, 'questionType', '1');
          this.$set(this.campaign, 'passScore', 100);
        }
        if (val === '6' && this.campaign.type !== 'FIXED' && this.campaignType !== 'edit') {
          this.campaign.type = 'FIXED';
        }
      },
      immediate: true,
      deep: true,
    },
    'campaign.amount': {
      handler(val, oldVal) {
        if (val && (val < 0.1 || val > 0.3)) {
          this.$message.error('金额必须介于0.1到0.3元之间');
          // 使用nextTick确保DOM更新后恢复值
          this.$nextTick(() => {
            this.$set(this.campaign, 'amount', oldVal);
          });
        }
      },
      deep: true,
    },
    'campaign.minAmount': {
      handler(val, oldVal) {
        // 后续类型根据activityType进行判断
        if (val && (val < 0.1 || val > 1)) {
          this.$message.error('最小金额必须介于0.1到1元之间');
          // 使用nextTick确保DOM更新后恢复值
          this.$nextTick(() => {
            this.$set(this.campaign,'minAmount', oldVal);
          });
        } else if (val === 0) {
          this.$message.error('最小金额不能为0');
        } else if (val && this.campaign.maxAmount && val > this.campaign.maxAmount) {
          this.$message.error('最小金额不能大于最大金额');
          // 使用nextTick确保DOM更新后恢复值
          this.$nextTick(() => {
            this.$set(this.campaign,'minAmount', oldVal);
          });
        }
      },
      deep: true,
    },
    'campaign.maxAmount': {
      handler(val, oldVal) {
        // 后续类型根据activityType进行判断
        if (val && (val < 0.1 || val > 1)) {
          this.$message.error('最大金额必须介于0.1到1元之间');
          // 使用nextTick确保DOM更新后恢复值
          this.$nextTick(() => {
            this.$set(this.campaign,'maxAmount', oldVal);
          });
        } else if (val === 0) {
          this.$message.error('最大金额不能为0');
        } else if (val && this.campaign.minAmount && val < this.campaign.minAmount) {
          this.$message.error('最大金额不能小于最小金额');
          // 使用nextTick确保DOM更新后恢复值
          this.$nextTick(() => {
            this.$set(this.campaign,'maxAmount', oldVal);
          });
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.checkCampaignType();
    this.getCategoryList();
  },
  methods: {
    checkCampaignType() {
      const data = this.$route.params;
      if (data && Object.keys(data).length > 0) {
        switch (data.type) {
        case 'edit':
          this.campaign = BeanUtilsService.copy(data.data);
          this.dealActivityEditData(this.campaign);
          this.campaignType = data.type;
          break;
        default:
          break;
        }
      }
    },
    async getCategoryList() {
      const { data } = await this.$request.get(sysCampaignGroupApi.queryCampaignGroupList.url);
      this.courseList = data.map((item) => ({ label: item.groupName, value: item.id }));
    },
    // 禁用今天之前的日期（单日期选择器）
    disableDate(date) {
      return date < new Date(new Date().setHours(0, 0, 0, 0))
    },
    handleActivityTypeChange() {
      this.campaign.type = "";
      // campaign的属性值除了activityType及type之外，其他都需要删除
      Object.keys(this.campaign).forEach((key) => {
        if (key !== 'activityType' && key !== 'type') {
          delete this.campaign[key];
        }
      });
    },
    handleTypeChange() {
      // campaign的属性值删除amount、minAmount、maxAmount、discountValue、describe、points属性
      Object.keys(this.campaign).forEach((key) => {
        if (key === 'amount' || key ==='minAmount' || key ==='maxAmount' || key === 'discountValue' || key === 'describe' || key === 'points') {
          delete this.campaign[key];
        }
      });
    },
    async handleParse() {
      if (!this.tempQuestionText.trim()) {
        try {
          // 请求剪贴板权限
          const clipboardText = await navigator.clipboard.readText();
          if (!clipboardText.trim()) {
            this.$message.warning('剪贴板内容为空');
            return;
          }
          // 将剪贴板内容填入输入框
          this.tempQuestionText = clipboardText;
        } catch (error) {
          this.$message.error('剪贴板读取失败，请确保已授予权限');
          return;
        }
      }
      // 执行解析逻辑
      this.parseQuestion()
    },
    parseQuestion() {
      try {
        const text = this.tempQuestionText.trim();
        // 增强分割逻辑（支持中英文逗号混合使用）
        const parts = text.split(/[，,]/) // 先按所有逗号分割
          .map(p => p.trim())
          .filter(p => p)
          // 重组结构：前5部分+合并剩余部分
          .reduce((acc, cur, index) => {
            index < 5 ? acc.push(cur) : (acc[5] = (acc[5] || '') + cur)
            return acc
          }, []);
        // 格式基础校验（至少包含6个部分）
        if (parts.length < 6) {
          throw new Error('输入内容不完整')
        }
        // 问题标题（首个部分）
        const questionText = parts[0].replace(/^问题标题[:：]?/, '').trim();
        this.$set(this.campaign, 'questionText', questionText);
        // 选项处理（第2-5部分）
        const optionKeys = ['A', 'B', 'C', 'D'];
        optionKeys.forEach((key, index) => {
          // 支持多种格式：1.直接内容 2.A:内容 3.A：内容
          const optionText = parts[index + 1].replace(new RegExp(`^${key}\\s*[:：]?`), '');
          const option = optionText.trim();
          this.$set(this.campaign, `option${key}`, option);
        });
        // 正确答案处理（最后部分）
        const lastPart = parts[parts.length - 1];
        // 增强正则匹配（兼容单选和多选）
        const match = lastPart.match(
          /(?:正确答案?[:：\s]*)?([A-Da-d][A-Da-d\s,]*)/i
        );
        if (!match) throw new Error('无法识别正确答案');
        const answerProcessor = (raw) => {
          // 标准化处理
          const processed = raw.toUpperCase()
            .replace(/[^A-D]/g, '') // 关键：移除非字母字符
            .split('') // 按字符分割
            .filter(c => c.match(/^[A-D]$/))
            .reduce((acc, cur) => { // 去重并排序
              if (!acc.includes(cur)) acc.push(cur);
              return acc.sort();
            }, []);
          // 结果校验
          if (processed.length === 0) throw new Error('未找到有效答案');
          if (processed.length > 4) throw new Error('最多支持四个选项');
          // 返回单选/多选统一格式
          return processed.length === 1 ? processed[0] : processed;
        };
        this.$set(this.campaign, 'correctAnswer', answerProcessor(match[1]));
      } catch (e) {
        console.error('解析失败，请检查格式')
        this.$message.error('格式解析失败，请按照示例格式填写')
      }
    },
    async submit() {
      try {
        // 校验表单
        const validateResults = await Promise.all([
          this.$refs.videoForm.validate(),
          this.$refs.configForm.validate()
        ]);
        if (!validateResults.every((item) => item === true)) {
          throw new Error('FORM_VALIDATE_ERROR');
        }
        // 数据处理
        const campaignData = BeanUtilsService.copy(this.campaign);
        await this.dealActivityData(campaignData); // 改为异步调用

        // 活动数据校验失败
        if (campaignData._valid === false) {
          return;
        }

        // 请求提交
        const apiPath = this.campaignType === 'edit'
          ? sysMarketingCampaignApi.updateMarketingCampaign.url
          : sysMarketingCampaignApi.addCampCourse.url;

        const { code, msg } = await this.$request.post(apiPath, campaignData);

        // 处理响应
        if (code !== 0) throw new Error(msg);
        this.$message.success(this.campaignType === 'edit' ? "编辑成功" : "提交成功");
        this.$router.push({ name: 'marketing-campaign-main', params: { type: 'refresh' } });
      } catch (error) {
        const errorMap = {
          'FORM_VALIDATE_ERROR': '表单校验未通过',
          'MULTI_ANSWER_ERROR': '问题类型与正确答案不匹配， 请重新选择问题类型或正确答案',
          'SINGLE_ANSWER_ERROR': '问题类型为多选，正确答案为单个， 请重新选择问题类型或正确答案'
        };
        if (error.message in errorMap) {
          this.$message.error(errorMap[error.message]);
        } else {
          console.error('提交异常:', error);
          this.$message.error(`操作失败：${error.message || '未知错误'}`);
        }
      }
    },
    async dealActivityData(data) {
      switch (data.activityType) {
      case '1':
      case '6':
        this.dealRedPacketData(data);
        break;
      case '2':
        this.dealCouponData(data);
        break;
      case '3':
        this.dealGiftData(data);
        break;
      case '4':
        this.dealIntegralData(data);
        break;
      case '5':
        this.dealQuestionData(data);
        break;
      default:
        return [];
      }
    },
    dealActivityEditData(data) {
      switch (data.activityType) {
      case '1':
      case '6':
        this.dealRedPacketEditData(data);
        break;
      case '2':
        this.dealCouponEditData(data);
        break;
      case '3':
        this.dealGiftEditData(data);
        break;
      case '4':
        this.dealIntegralEditData(data);
        break;
      case '5':
        this.dealQuestionEditData(data);
        break;
      default:
        return [];
      }
    },
    dealRedPacketEditData(data) {
      const config = JSON.parse(data.config);
      if (config.type === 'FIXED') {
        this.$set(data, 'type', 'FIXED');
        this.$set(data, 'amount', config.amount);
      } else if (config.type === 'RANDOM') {
        this.$set(data, 'type', 'RANDOM');
        this.$set(data, 'minAmount', config.minAmount || 0.1);
        this.$set(data, 'maxAmount', config.maxAmount || 1);
      }
    },
    dealCouponEditData(data) {
      const config = JSON.parse(data.config);
      if (config.discountType === 'PERCENT') {
        this.$set(data, 'type', 'PERCENT');
        this.$set(data, 'discountValue', config.discountValue);
        this.$set(data, 'minAmount', config.minAmount);
      } else if (config.discountType === 'FIXED') {
        this.$set(data, 'type', 'FIXED');
        this.$set(data, 'discountValue', config.discountValue);
      }
    },
    dealGiftEditData(data) {
      const config = JSON.parse(data.config);
      this.$set(data, 'type', config.giftType);
      this.$set(data, 'describe', config.describe);
    },
    dealIntegralEditData(data) {
      const config = JSON.parse(data.config);
      this.$set(data, 'points', config.points);
    },
    dealQuestionEditData(data) {
      const config = JSON.parse(data.config);
      if (config.type === 'FIXED') {
        this.$set(data, 'type', 'FIXED');
        this.$set(data, 'amount', config.amount);
      } else if (config.type === 'RANDOM') {
        this.$set(data, 'type', 'RANDOM');
        this.$set(data, 'minAmount', config.minAmount || 0.1);
        this.$set(data, 'maxAmount', config.maxAmount || 1);
      }
      // 处理问题答案把选项转换成数组对象
      const options = JSON.parse(data.answerOptions);
      const optionKeys = ['A', 'B', 'C', 'D'];
      optionKeys.forEach(key => {
        const option = options.find(item => item[key]);
        if (option) {
          this.$set(this.campaign, `option${key}`, option[key]);
        }
      });
      // 处理正确答案 如果数组只有一个值则转成字符串，否则直接赋值
      const correctAnswer = JSON.parse(data.correctAnswer);
      const dealAnswer = data.questionType === '1'? correctAnswer.join('') : correctAnswer
      this.$set(this.campaign, 'correctAnswer', dealAnswer);
    },
    dealRedPacketData(data) {
      if (data.type === 'FIXED') {
        const config = {
          type: 'FIXED',
          amount: data.amount,
        }
        // 删除data的type及amount属性并替换为config
        delete data.type;
        delete data.amount;
        data.config = JSON.stringify(config);
      } else if (data.type === 'RANDOM') {
        const config = {
          type: 'RANDOM',
          minAmount: data.minAmount || 0.1,
          maxAmount: data.maxAmount || 1,
        }
        // 删除data的type及minAmount、maxAmount属性并替换为config
        delete data.type;
        delete data.minAmount;
        delete data.maxAmount;
        data.config = JSON.stringify(config);
      }
    },
    dealCouponData(data) {
      if (data.type === 'PERCENT') {
        const config = {
          discountType: data.type,
          discountValue: data.discountValue,
          minAmount: data.minAmount,
        }
        // 删除data的type、discountValue、minAmount属性并替换为config
        delete data.type;
        delete data.discountValue;
        delete data.minAmount;
        data.config = JSON.stringify(config);
      } else if (data.type === 'FIXED') {
        const config = {
          discountType: data.type,
          discountValue: data.discountValue,
        }
        // 删除data的type、discountValue属性并替换为config
        delete data.type;
        delete data.discountValue;
        data.config = JSON.stringify(config);
      }
    },
    dealGiftData(data) {
      const config = {
        giftType: data.type,
        describe: data.describe,
      }
      // 删除data的type、describe属性并替换为config
      delete data.type;
      delete data.describe;
      data.config = JSON.stringify(config);
    },
    dealIntegralData(data) {
      const config = {
        points: data.points,
      }
      // 删除data的points属性并替换为config
      delete data.points;
      data.config = JSON.stringify(config);
    },
    dealQuestionData(data) {
      // 将正确答案转换成数组，如果是数组则直接赋值，如果是单选则转换成数组
      if (Array.isArray(data.correctAnswer)) {
        // 如果questionType为1，与当前正确答案不一致，则提示错误
        if (data.correctAnswer.length <= 1 || data.questionType === '1') {
          data._valid = false;
          throw new Error('MULTI_ANSWER_ERROR');
        }
      } else {
        // 如果questionType为2，与当前正确答案不一致，则提示错误
        if (data.correctAnswer.split('').length === 1 && data.questionType === '2') {
          this.$message.error({content: "多选问题不能有单个答案"});
          throw new Error('SINGLE_ANSWER_ERROR');
        }
        data.correctAnswer = [data.correctAnswer];
      }
      if (data.type === 'FIXED') {
        const config = {
          type: data.type,
          amount: data.amount,
        }
        delete data.type;
        delete data.amount;
        data.config = JSON.stringify(config);
      } else if (data.type === 'RANDOM') {
        const config = {
          type: data.type,
          minAmount: data.minAmount || 0.1,
          maxAmount: data.maxAmount || 1,
        }
        delete data.type;
        delete data.minAmount;
        delete data.maxAmount;
        data.config = JSON.stringify(config);
      }
      // 处理问题答案把选项转换成数组对象
      const options = [];
      for (let i = 1; i <= 4; i++) {
        const option = data[`option${String.fromCharCode(65 + i - 1)}`];
        if (option) {
          const optionData = {[String.fromCharCode(64 + i)]: option, isCorrect: data.correctAnswer.includes(String.fromCharCode(64 + i))}
          options.push(optionData);
        }
        delete data[`option${String.fromCharCode(65 + i - 1)}`];
      }
      data.answerOptions = JSON.stringify(options);
    },
    reset() {
      this.$refs.videoForm.reset();
      this.$refs.configForm.reset();
    }
  },
};
</script>

<style lang="less" scoped>
.marketing-campaign-page {
  background: #f8f9fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 12px;

        .t-icon {
          font-size: 28px;
          color: #f59e0b;
        }
      }

      .page-breadcrumb {
        /deep/ .t-breadcrumb {
          .t-breadcrumb__item {
            color: #6b7280;
            font-size: 14px;

            &:last-child {
              color: #3b82f6;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  // 配置卡片
  .config-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
    overflow: hidden;

    .card-header {
      padding: 20px 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;

      .card-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        .t-icon {
          font-size: 20px;
        }
      }
    }

    .card-content {
      padding: 24px;
    }
  }

  // 表单样式
  .config-form {
    .form-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e5e7eb;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 40px;
          height: 2px;
          background: #3b82f6;
        }
      }

      .form-row {
        display: flex;
        gap: 24px;
        margin-bottom: 20px;

        .form-item {
          flex: 1;
          min-width: 0;
        }

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 16px;
        }
      }
    }

    // 智能识别区域
    .question-parser {
      margin-bottom: 24px;
      padding: 20px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 12px;
      border: 1px solid #bae6fd;

      .parser-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #0c4a6e;

        .t-icon {
          font-size: 18px;
          color: #0ea5e9;
        }
      }

      .parser-content {
        .parser-textarea {
          margin-bottom: 16px;
          border-radius: 8px;
          border: 1px solid #bae6fd;

          /deep/ .t-textarea__inner {
            background: #fff;
            border: none;
          }
        }

        .parser-actions {
          text-align: right;

          .parse-btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            .t-icon {
              margin-right: 6px;
            }
          }
        }
      }
    }

    // 选项配置
    .options-section {
      margin-bottom: 24px;

      .options-title {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 16px;
      }

      .options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;

        .option-item {
          .option-input {
            /deep/ .option-label {
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              background: #3b82f6;
              color: #fff;
              border-radius: 50%;
              font-size: 12px;
              font-weight: 600;
            }
          }
        }
      }
    }

    // 操作按钮
    .form-actions {
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #e5e7eb;
      display: flex;
      gap: 16px;

      .t-button {
        border-radius: 8px;
        font-weight: 500;
        padding: 10px 24px;
        transition: all 0.3s ease;

        .t-icon {
          margin-right: 6px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }

      .submit-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }
      }
    }
  }
}

// 全局表单组件优化
/deep/ .t-form-item {
  margin-bottom: 20px;

  .t-form__label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }

  .t-input,
  .t-select,
  .t-textarea {
    border-radius: 8px;

    transition: all 0.2s ease;

    &:hover {
      border-color: #9ca3af;
    }

    &:focus,
    &.t-is-focused {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  .t-textarea {
    resize: vertical;
  }

  .input-suffix {
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
  }
}

/deep/ .t-select {
  .t-select__prefix {
    color: #6b7280;
  }
}

/deep/ .t-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.t-button--theme-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    }
  }

  &.t-button--variant-outline {

    background: #fff;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .marketing-campaign-page {
    padding: 16px;

    .page-header {
      padding: 20px;

      .header-content {
        .page-title {
          font-size: 20px;
        }
      }
    }

    .config-card {
      .card-header {
        padding: 16px 20px;

        .card-title {
          font-size: 16px;
        }
      }

      .card-content {
        padding: 16px 20px;
      }
    }

    .config-form {
      .question-parser {
        padding: 16px;

        .parser-header {
          font-size: 14px;
        }
      }

      .options-section {
        .options-grid {
          grid-template-columns: 1fr;
          gap: 12px;
        }
      }

      .form-actions {
        flex-direction: column;
        gap: 12px;

        .t-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
