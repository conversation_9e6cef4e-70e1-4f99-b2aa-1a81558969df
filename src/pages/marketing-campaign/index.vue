<template>
  <div class="marketing-campaign-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">
          <t-icon name="chart-bubble" />
          营销活动管理
        </div>
        <div class="page-desc">新建营销活动，关联课程时设置营销规则，推广效果更佳</div>
      </div>
      <div class="header-actions">
        <t-button theme="primary" @click="addModel" size="large">
          <t-icon name="add" />
          新建营销活动
        </t-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧分组列表 -->
      <div class="sidebar">
        <action-list ref="actionListRef" @select="onSelected"></action-list>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索筛选区域 -->
        <div class="filter-section">
          <div class="filter-content">
            <t-form :data="form" ref="formSearch" layout="inline" class="filter-form">
              <div class="filter-row">
                <t-form-item label="营销活动名称" name="title" class="filter-item">
                  <t-input
                    v-model="form.title"
                    :clearable="true"
                    placeholder="请输入营销活动名称"
                    class="filter-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="search" />
                    </template>
                  </t-input>
                </t-form-item>

                <t-form-item label="活动类型" name="type" class="filter-item">
                  <t-select
                    v-model="form.type"
                    :clearable="true"
                    :options="activeOptions"
                    placeholder="请选择活动类型"
                    class="filter-select"
                  >
                    <template #prefixIcon>
                      <t-icon name="layers" />
                    </template>
                  </t-select>
                </t-form-item>

                <!-- 操作按钮 -->
                <div class="filter-buttons">
                  <t-button theme="primary" @click="onSubmit" class="search-btn">
                    <t-icon name="search" />
                    查询
                  </t-button>
                  <t-button variant="outline" @click="onReset" class="reset-btn">
                    <t-icon name="refresh" />
                    重置
                  </t-button>
                </div>
              </div>
            </t-form>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-section">
          <common-table
            ref="commonTable"
            :columns="columns"
            :url="url"
            :isRequest="!!activeId"
            :params="tableParams"
            :operations="operations"
            class="campaign-table"
          >
          </common-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ActionList from './components/action-list.vue';
import CommonTable from '@/components/common-table/index.vue';
import sysCampaignGroupApi from "@/constants/api/back/sys-campaign-group.api";
import { MARKETING_OPTIONS, MARKETING_STATE_OPTIONS } from "@/constants/enum/business/marketing-campaign-array.enum";
import sysMarketingCampaingApi from "@/constants/api/back/sys-marketing-campaing.api";
import {getMenuChildrenList, getOperationTypeList} from "@/utils/utils";
import {SYSTEM_MENU_CONST} from "@/constants/enum/system/system-menu.enum";

export default {
  name: 'MarketingCampaign',
  components: { ActionList, CommonTable },
  data() {
    return {
      url: sysCampaignGroupApi.queryCampaignGroupListByGroupId.url,
      activeOptions: MARKETING_OPTIONS,
      params: {
        title: '',
        type: '',
      },
      form: {
        title: '',
        activeId: '',
        type: '',
      },
      columns: [
        {
          colKey: 'title',
          title: '营销活动名称',
          width: 120,
        },
        {
          colKey: 'activityType',
          title: '活动类型',
          width: 120,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getCampaignType(val);
          },
        },
        {
          colKey: 'groupId',
          title: '营销活动分组',
          width: 80,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getModality(val, row);
          },
        },
        {
          colKey: 'status',
          title: '状态',
          width: 80,
          cell: (h, { col, row }) => {
            const val = row[col.colKey];
            return this.getCourseStatus(val);
          },
        },
        {
          colKey: 'op',
          title: '操作',
          width: 80,
          cell: 'op',
        },
      ],
      addModalVisible: false,
      activeId: 'ALL',
      userOperation: {
        hasAdd: false,
        hasEdit: false,
        hasDelete: false,
      },
      userOperationTab: [], // 用户可操作按钮
    };
  },
  computed: {
    tableParams() {
      return {
        id: this.activeId === 'ALL' ? 'ALL' : this.activeId,
        title: this.params.title,
        type: this.params.type,
      };
    },
    operations(){
      return [
        {
          type: 'edit',
          onClick: this.onEdit,
          visible: this.userOperation.hasEdit,
        },
        {
          type: 'delete',
          onClick: this.onDel,
          visible: this.userOperation.hasDelete,
        },
      ]
    },
  },
  mounted() {
    // 获取用户操作类型按钮列表
    this.getOperationTypeList();
  },
  methods: {
    addModel() {
      this.$router.push({ name: 'new-marketing-campaign', params: { type: 'new' } });
    },
    onSelected(val) {
      this.activeId = val;
    },
    async onDel(data) {
      // 根据id删除数据
      const { code } = await this.$request.post(sysMarketingCampaingApi.deleteMarketingCampaign.url, { id: data.id });
      if (code === 0) {
        this.$message.success({ content: '操作成功' });
        this.$refs.commonTable.getList();
      } else {
        this.$message.error({ content: '操作失败' });
      }
    },

    getModality(val, row) {
      const data = this.$refs.actionListRef.actionTypeList;
      return data.find((item) => item.id === val)?.groupName || '';
    },
    getCampaignType(val) {
      return MARKETING_OPTIONS.find((item) => item.value === val)?.label || '未知';
    },
    getCourseStatus(val) {
      const data = MARKETING_STATE_OPTIONS;
      return data.find((item) => item.value === val)?.label || '停用';
    },
    onEdit(row) {
      this.$router.push({ name: 'new-marketing-campaign', params: { data: row, type: 'edit' } });
    },
    onSubmit() {
      this.params = {
        title: this.form.title,
        type: this.form.type,
      };
    },
    onReset() {
      this.form = {
        title: '',
        activeId: '',
        type: '',
      };
      this.onSubmit();
    },

    // 刷新数据
    refreshData() {
      this.$refs.commonTable.getList();
    },
    /**
     * @description: 获取操作类型按钮列表
     * <AUTHOR>
     * @date 2025/6/2 11:29
     */
    getOperationTypeList() {
      // 登录后用户具有的菜单已经存入了storage中，从中获取
      this.childrenButton = getMenuChildrenList();
      // console.log("获取到具有按钮菜单：", this.childrenButton);
      if(this.childrenButton && this.childrenButton.length > 0) {
        this.childrenButton.map((item) => {
          if (item.menuType && item.menuType == SYSTEM_MENU_CONST.O.value && item.menuUrl) {
            // 截取操作按钮，区分类型
            const operationType = item.menuUrl.substring(1, item.menuUrl.length).split('/')[1];
            this.userOperationTab.push(operationType);
          }
        })
      }
      this.userOperation = getOperationTypeList(this.userOperationTab)
      // console.log("获取到可操作按钮类型：", this.userOperation);
      // console.log("操作栏：", this.operations);
    }
  },
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

// ==================== 页面主体样式 ====================
.marketing-campaign-page {
  padding: 12px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .header-actions {
      .t-button {
        height: 44px;
        padding: 0 24px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 20px;
    min-height: calc(100vh - 300px);
  }

  // 左侧边栏
  .sidebar {
    width: 250px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
  }

  // 右侧内容区域
  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-width: 0;
  }

  // 筛选区域
  .filter-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .filter-content {
      padding: 20px 24px;

      .filter-form {
        .filter-row {
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          align-items: flex-end;

          .filter-item {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            margin-bottom: 16px;

            .filter-input,
            .filter-select {
              width: 100%;
              border-radius: 8px;

              transition: all 0.2s ease;

              &:hover {
                border-color: #9ca3af;
              }

              &:focus,
              &.t-is-focused {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
              }
            }
          }

          .filter-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-left: auto;
            flex-shrink: 0;

            .t-button {
              padding: 0 16px;
              border-radius: 8px;
              font-weight: 500;
              transition: all 0.3s ease;

              .t-icon {
                margin-right: 4px;
              }

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
              }
            }

            .search-btn {
              background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
              border: none;
              color: #fff;

              &:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
              }
            }

            .reset-btn {

              background: #fff;
              color: #374151;

              &:hover {
                background: #f9fafb;
                border-color: #9ca3af;
              }
            }
          }
        }
      }
    }
  }

  // 表格区域
  .table-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    flex: 1;
    min-height: 0;
    overflow: hidden;

    .campaign-table {
      height: 100%;

      /deep/ .t-table {
        height: 100%;

        .t-table__header {
          th {
            background: #f8f9fa;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
          }
        }

        .t-table__body {
          tr {
            transition: all 0.2s ease;

            &:hover {
              background: #f8f9fa;
            }

            td {
              border-bottom: 1px solid #f3f4f6;
              color: #374151;
            }
          }
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .marketing-campaign-page {
    .main-content {
      flex-direction: column;

      .sidebar {
        width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .marketing-campaign-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .filter-section {
      .filter-content {
        .filter-form {
          .filter-row {
            flex-direction: column;

            .filter-item {
              min-width: 100%;
              max-width: 100%;
            }

            .filter-buttons {
              margin-left: 0;
              justify-content: center;
            }
          }
        }
      }
    }

    .toolbar {
      flex-direction: column;
      gap: 12px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
