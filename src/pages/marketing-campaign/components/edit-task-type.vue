<template>
  <t-dialog :visible.sync='modalVisible' width="35%" @close='closeModal' :header="form.id ? '编辑营期活动分组' : '新增营期活动分组'"
            class='action-type-edit-dialog' @confirm='onValid'>

    <t-form ref='form' :data='form' :rules='rules' @submit='onSubmit'>
      <t-form-item label='营销活动分组名称' name='groupName'>
        <t-input v-model='form.groupName' placeholder='请输入营销活动分组名称' :maxlength='15'></t-input>
      </t-form-item>
    </t-form>

  </t-dialog>
</template>

<script>
import sysCourseGroupApi from "@/constants/api/back/sys-course-group.api";
import sysCampaignGroupApi from "@/constants/api/back/sys-campaign-group.api";

export default {
  name: 'edit-marketing-campaign-type',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    initForm: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      rules: {
        groupName: [{ required: true, message: '营销活动分组必填' },{message: '请输入有效信息', whitespace: true}],
      },
      form: { ...this.initForm },
      modalVisible: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.form = { ...this.initForm };
      } else {
        this.$refs.form.reset();
        this.form = {};
      }
      this.modalVisible = val;
    },
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },
    async onValid() {
      const data = await this.$refs.form.validate();
      if (typeof data === 'boolean') {
        this.$refs.form.submit();
      }
    },
    async onSubmit() {
      const { id = '', groupName } = this.form;

      if (id) {
        await this.$request.post(sysCampaignGroupApi.updateCampaignGroup.url, { id, groupName });
      } else {
        await this.$request.post(sysCampaignGroupApi.addCampaignGroup.url, { groupName });
      }

      this.$message.success({ content: '操作成功' });
      this.$emit('success');
    },


  },
};
</script>

<style lang='less' scoped>
.action-type-edit-dialog {
  /deep/ .t-dialog__body {
    padding-top: 30px;
  }
}
/deep/ .t-form__controls {
  margin-left: 125px !important;
}
</style>

