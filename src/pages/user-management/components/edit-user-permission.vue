<template>
  <t-dialog
    :header="data.username ? `编辑用户${data.username}权限` : '编辑用户权限'"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div class="cascader-container">
      <div class="cascader-operation">
        <t-button theme="primary" size="medium" @click="handleSelectAll">全选</t-button>
        <t-button theme="primary" size="medium" @click="handleSelectReverse">反选</t-button>
        <t-button theme="default" size="medium" @click="handleClearAll">清空</t-button>
      </div>
      <div class="cascader-scroll-container">
        <t-cascader
          v-model="selectedValues"
          :options="cascaderOptions"
          :keys="{
            value: 'key',
            label: 'label',
            children: 'children',
          }"
          multiple
          clearable
          :showAllLevels="true"
          :size="'medium'"
          :placeholder="'请选择权限'"
          :value-mode="'parentFirst'"
          :checkStrictly="false"
          @change="handleCascaderChange"
          class="permission-cascader"
        />
        <div class="selected-items">
          <h4>已拥有权限：</h4>
          <div class="selected-tags">
            <t-tag
              v-for="item in selectedItems"
              :key="item.key"
              :theme="getTagTheme(item.level)"
              closable
              @close="handleRemoveTag(item.key)"
              class="permission-tag"
            >
              {{ item.label }}
            </t-tag>
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import { Code } from "@/constants/enum/general/code.enum";
import userPermissionApi from "@/constants/api/hxsy-admin/user-permission.api";

export default Vue.extend({
  name: 'EditUserPermission',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
    allSysCompanyInfos: {
      type: Array,
      default() {
        return [];
      },
      required: false,
    },
  },
  data() {
    return {
      loading: false,
      selectedValues: [], // 级联选择器选中的值
      selectedItems: [], // 用于显示已选择的标签
      form: {},
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    cascaderOptions: {
      get() {
        if (!this.allSysCompanyInfos.length) return [];
        const permissionData = this.allSysCompanyInfos.map((item: any) => item.children).flat(1) || [];
        const processNodes = (nodes: any[], level = 0) => nodes.map(node => ({
          key: node.key,
          label: node.label,
          level,
          children: node.children ? processNodes(node.children, level + 1) : [],
        }));
        return processNodes(permissionData);
      },
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getUserDataPermission();
      }
    },
  },
  methods: {
    // 获取标签主题颜色
    getTagTheme(level: number) {
      const themes = ['primary', 'warning', 'success'];
      return themes[level] || 'default';
    },

    // 处理级联选择器变化
    handleCascaderChange(value: string[], context: any) {
      console.log('value:', value, 'context:', context)
      if (!context) return;

      const { node, checked, trigger } = context;

      if (trigger === 'check') {
        if (checked) {
          const parentKeys = this.getParentNodes(node.value);
          const childKeys = this.getNodeAndChildren(node.value);
          const relatedKeys = new Set<string>([node.value, ...parentKeys, ...childKeys]);

          const newSelected = Array.from(new Set([...this.selectedValues, ...relatedKeys]));
          this.selectedValues = [];
          this.$nextTick(() => {
            this.selectedValues = newSelected;
            this.updateSelectedItems(newSelected, context, true);
          });
        } else {
          const childKeys = this.getNodeAndChildren(node.value);
          const relatedKeys = new Set<string>([node.value, ...childKeys]);

          const newSelected = this.selectedValues.filter(v => !relatedKeys.has(v));
          this.selectedValues = [];
          this.$nextTick(() => {
            this.selectedValues = newSelected;
            this.updateSelectedItems(newSelected, context, true);
          });
        }
      }

      // 更新显示的标签（已在 $nextTick 里更新）
    },

    // 更新已选择项
    updateSelectedItems(values: string[], context: any, expand = true) {
      // 清空当前选中项
      this.selectedItems = [];

      console.log('values:', values)
      // 补全所有父节点和子节点（可选）
      const allKeys = new Set(values);
      if (expand) {
        values.forEach(value => {
          const parentKeys = this.getParentNodes(value);
          parentKeys.forEach(p => allKeys.add(p));
        });
        // 新增：补全所有子节点（只补一次）
        values.forEach(value => {
          const childKeys = this.getNodeAndChildren(value);
          childKeys.forEach(k => allKeys.add(k));
        });
      }

      // 获取所有选中节点及其父节点的信息
      const selectedNodes = Array.from(allKeys)
        .map(value => this.findNode(this.cascaderOptions, value))
        .filter(node => node !== null);
      // 按层级分组
      const nodesByLevel = {
        0: [], // 栏目
        1: [], // 公司
        2: []  // 销售组
      };

      console.log('selectedNodes:', selectedNodes);
      // 分组
      selectedNodes.forEach(node => {
        if (node.level === 0 || node.level === 1 || node.level === 2) {
          nodesByLevel[node.level].push({
            key: node.key,
            label: node.label,
            level: node.level
          });
        }
      });

      // 合并所有层级的节点，按层级排序
      this.selectedItems = [
        ...nodesByLevel[0],
        ...nodesByLevel[1],
        ...nodesByLevel[2]
      ];

      console.log('更新后的选中项:', this.selectedItems);
    },

    // 获取节点及其所有子节点
    getNodeAndChildren(nodeKey: string): string[] {
      const result: string[] = [];

      const collectAllChildren = (node: any) => {
        if (!node) return;

        // 添加当前节点
        result.push(node.key);

        // 递归添加所有子节点
        if (node.children && node.children.length > 0) {
          node.children.forEach((child: any) => {
            collectAllChildren(child);
          });
        }
      };

      // 找到目标节点
      const targetNode = this.findNode(this.cascaderOptions, nodeKey);
      if (targetNode) {
        collectAllChildren(targetNode);
      }

      return result;
    },

    // 获取节点的所有父节点
    getParentNodes(nodeKey: string): string[] {
      const result: string[] = [];
      const findParent = (nodes: any[], targetKey: string, path: any[] = []): boolean => {
        for (const node of nodes) {
          const currentPath = [...path, node];
          if (node.key === targetKey) {
            path.forEach(p => result.push(p.key));
            return true;
          }
          if (node.children && findParent(node.children, targetKey, currentPath)) {
            return true;
          }
        }
        return false;
      };

      findParent(this.cascaderOptions, nodeKey, []);
      return result;
    },

    // 查找节点信息
    findNode(nodes: any[], targetKey: string): any {
      for (const node of nodes) {
        if (node.key === targetKey) return node;
        if (node.children) {
          const found = this.findNode(node.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    },

    // 处理移除标签
    handleRemoveTag(key: string) {
      console.log('移除标签:', key);

      // 获取要移除的节点信息
      const nodeToRemove = this.findNode(this.cascaderOptions, key);
      if (!nodeToRemove) {
        console.error('未找到要移除的节点:', key);
        return;
      }

      // 获取要移除的节点及其所有子节点
      const nodesToRemove = this.getNodeAndChildren(key);
      console.log('要移除的节点及子节点:', nodesToRemove);

      // 获取父节点
      const parentNodes = this.getParentNodes(key);
      console.log('父节点:', parentNodes);

      // 移除前的选中值
      console.log('移除前的选中值:', [...this.selectedValues]);

      // 从选中值中移除这些节点
      const newSelectedValues = this.selectedValues.filter(value => !nodesToRemove.includes(value));

      // 检查父节点的其他子节点是否还有选中的
      parentNodes.forEach(parentKey => {
        const parentNode = this.findNode(this.cascaderOptions, parentKey);
        if (parentNode && parentNode.children) {
          const otherChildrenSelected = parentNode.children.some(child =>
            newSelectedValues.includes(child.key) && !nodesToRemove.includes(child.key)
          );
          if (!otherChildrenSelected) {
            // 如果父节点的所有其他子节点都未选中，也移除父节点
            const index = newSelectedValues.indexOf(parentKey);
            if (index > -1) {
              newSelectedValues.splice(index, 1);
            }
          }
        }
      });

      // 更新选中值
      this.selectedValues = newSelectedValues;

      // 更新已选择项
      this.updateSelectedItems(newSelectedValues, {
        node: nodeToRemove,
        checked: false,
        trigger: 'check'
      },true);

      // 确保视图更新
      this.$nextTick(() => {
        console.log('更新后的选中值:', this.selectedValues);
        console.log('更新后的选中项:', this.selectedItems);
      });
    },

    // 获取所有节点的值
    getAllNodeValues(nodes: any[]): string[] {
      let values: string[] = [];
      nodes.forEach(node => {
        values.push(node.key);
        if (node.children && node.children.length > 0) {
          values = values.concat(this.getAllNodeValues(node.children));
        }
      });
      return values;
    },

    // 全选
    handleSelectAll() {
      // 获取所有节点的值
      const allValues = this.getAllNodeValues(this.cascaderOptions);
      this.selectedValues = allValues;
      this.$nextTick(() => {
        this.updateSelectedItems(allValues, null);
      });
    },

    // 反选
    handleSelectReverse() {
      // 获取所有可能的节点值
      const allValues = this.getAllNodeValues(this.cascaderOptions);

      // 创建一个新的选中值数组
      const newValues = allValues.filter(value => {
        const node = this.findNode(this.cascaderOptions, value);
        if (!node) return false;

        // 如果是叶子节点，直接判断是否需要反选
        if (!node.children || node.children.length === 0) {
          return !this.selectedValues.includes(value);
        }

        // 如果是父节点，只有当其所有子节点都未选中时才选中
        const childrenValues = this.getNodeAndChildren(value).filter(v => v !== value);
        const hasSelectedChildren = childrenValues.some(v => this.selectedValues.includes(v));
        return !hasSelectedChildren && !this.selectedValues.includes(value);
      });

      this.selectedValues = newValues;
      this.$nextTick(() => {
        this.updateSelectedItems(newValues, null);
      });
    },

    // 清空
    handleClearAll() {
      this.selectedValues = [];
      this.selectedItems = [];

      // 确保UI更新
      this.$nextTick(() => {
        console.log('已清空所有选中项');
      });
    },

    // 获取用户权限
    async getUserDataPermission() {
      const { id } = this.data;
      const res = await this.$request.post(userPermissionApi.getUserPermissionByUserId.url, { userId: id });
      const { code, data } = res;
      if (code === Code.OK.code) {
        // 初始化 selectedValues 时仅设置 perSalesGroupId，不包含公司 ID，避免选中公司导致 UI 误判为全选
        const selectedValues = [...(data.perSalesGroupId || [])];

        // 构建展示项：销售组 + 其所有父节点
        const allKeys = new Set(selectedValues);
        selectedValues.forEach(id => {
          const parents = this.getParentNodes(id);
          parents.forEach(p => allKeys.add(p));
        });

        this.$nextTick(() => {
          this.selectedValues = selectedValues; // 仅销售组用于 t-cascader 勾选
          this.updateSelectedItems(Array.from(allKeys), null, false); // 展示项包括公司和栏目
        });
      }
    },

    handleCancel() {
      this.selectedValues = [];
      this.selectedItems = [];

      // 确保UI更新
      this.$nextTick(() => {
        console.log('已取消并清空所有选中项');
        this.$emit('cancel');
      });
    },

    handleOk() {
      this.saveData();
    },

    async saveData() {
      // 直接根据节点层级分类
      const result = {
        perColumnId: new Set<string>(),
        perCompanyId: new Set<string>(),
        perSalesGroupId: new Set<string>()
      };

      // 获取所有实际勾选的节点（包括每个选中节点的所有父节点和子节点）
      const allKeys = new Set(this.selectedValues);
      this.selectedValues.forEach(value => {
        const parents = this.getParentNodes(value);
        parents.forEach(p => allKeys.add(p));
        const children = this.getNodeAndChildren(value);
        children.forEach(c => allKeys.add(c));
      });
      const allNodes = Array.from(allKeys)
        .map(value => this.findNode(this.cascaderOptions, value))
        .filter(node => node !== null);

      console.log('所有选中节点:', allNodes);

      // 先找出所有叶子节点（销售组）
      const leafNodes = allNodes.filter(node => !node.children || node.children.length === 0);
      console.log('叶子节点:', leafNodes);

      // 按层级分类，只处理当前选中的节点
      allNodes.forEach(node => {
        switch (node.level) {
        case 0:
          result.perColumnId.add(node.key);
          break;
        case 1:
          result.perCompanyId.add(node.key);
          break;
        case 2:
          result.perSalesGroupId.add(node.key);
          break;
        }
      });

      // 特殊处理：确保所有叶子节点都被添加到销售组中
      // 有些叶子节点可能是销售组，但level属性可能不是2
      leafNodes.forEach(node => {
        // 如果是最底层节点且不在任何集合中，添加到销售组
        if (!result.perColumnId.has(node.key) && !result.perCompanyId.has(node.key)) {
          result.perSalesGroupId.add(node.key);
        }
      });

      // 重要：处理公司节点，确保其所有子节点（销售组）都被添加到perSalesGroupId中
      // 但只处理当前选中的公司节点
      allNodes.filter(node => node.level === 1).forEach(companyNode => {
        // 找到该公司下的所有销售组节点
        const companyChildren = this.getNodeAndChildren(companyNode.key).filter(key => key !== companyNode.key);

        // 只处理当前选中的子节点
        companyChildren.forEach(childKey => {
          if (!allKeys.has(childKey)) {
            return;
          }

          const childNode = this.findNode(this.cascaderOptions, childKey);
          if (childNode) {
            // 如果是叶子节点或level为2，添加到销售组
            if (!childNode.children || childNode.children.length === 0 || childNode.level === 2) {
              result.perSalesGroupId.add(childKey);
            }
          }
        });
      });

      // 确保每个子节点的父节点也被包含在对应层级中
      // 处理销售组的父节点，但只处理当前选中的销售组节点
      const salesGroupNodes = [
        ...allNodes.filter(node => node.level === 2),
        ...leafNodes
      ];

      salesGroupNodes.forEach(node => {
        const parents = this.getParentNodes(node.key);
        parents.forEach(parentKey => {
          // 只处理当前选中的父节点
          if (!allKeys.has(parentKey)) {
            return;
          }

          const parentNode = this.findNode(this.cascaderOptions, parentKey);
          if (parentNode) {
            if (parentNode.level === 0) {
              result.perColumnId.add(parentKey);
            } else if (parentNode.level === 1) {
              result.perCompanyId.add(parentKey);
            }
          }
        });
      });

      // 处理公司的父节点，但只处理当前选中的公司节点
      allNodes.filter(node => node.level === 1).forEach(node => {
        const parents = this.getParentNodes(node.key);
        parents.forEach(parentKey => {
          // 只处理当前选中的父节点
          if (!allKeys.has(parentKey)) {
            return;
          }

          const parentNode = this.findNode(this.cascaderOptions, parentKey);
          if (parentNode && parentNode.level === 0) {
            result.perColumnId.add(parentKey);
          }
        });
      });

      console.log('分类结果:', {
        perColumnId: Array.from(result.perColumnId),
        perCompanyId: Array.from(result.perCompanyId),
        perSalesGroupId: Array.from(result.perSalesGroupId)
      });

      // 添加详细日志，显示每个销售组节点的信息
      console.log('销售组节点详情:');
      Array.from(result.perSalesGroupId).forEach(id => {
        const node = this.findNode(this.cascaderOptions, id);
        if (node) {
          console.log(`ID: ${id}, 名称: ${node.label}, 层级: ${node.level}`);
        } else {
          console.log(`ID: ${id}, 未找到节点信息`);
        }
      });

      this.loading = true;
      const params = {
        userId: this.data.id,
        perColumnId: Array.from(result.perColumnId),
        perCompanyId: Array.from(result.perCompanyId),
        perSalesGroupId: Array.from(result.perSalesGroupId)
      };

      console.log('保存的权限数据:', params);

      try {
        const res = await this.$request.post(userPermissionApi.saveOrUpdateSystemUserPermission.url, params);
        const { code, msg } = res;
        this.loading = false;
        if (code === Code.OK.code) {
          this.$message.success('保存成功');
          this.selectedValues = [];
          this.selectedItems = [];
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
      } catch (e) {
        console.log(e);
        this.loading = false;
      }
    },
  },
});
</script>

<style lang="less" scoped>
.cascader-container {
  max-height: 500px;
  display: flex;
  justify-content: left;
  align-items: flex-start;
  flex-direction: column;
  width: 100%;

  .cascader-operation {
    position: sticky;
    top: 0;
    width: 100%;
    background: #ffffff;
    z-index: 100;
    margin-bottom: 16px;
    display: flex;
    gap: 8px;
  }

  .cascader-scroll-container {
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  .permission-cascader {
    width: 100%;
    margin-bottom: 16px;
  }

  .selected-items {
    margin-top: 16px;
    width: 100%;

    h4 {
      margin-bottom: 8px;
      font-weight: 500;
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .permission-tag {
        margin-bottom: 8px;
      }
    }
  }
}
</style>
