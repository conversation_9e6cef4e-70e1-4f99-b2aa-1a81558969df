<template>
  <t-dialog class="meet-part-modal" header="选择企微及业务人员" :visible.sync="modalVisible" :width="'60%'" placement="center" :maskClosable="false" :closeOnOverlayClick="false" :footer="false" @close="onCancel">
    <div class="modal-body">
      <t-steps :current="currentStep" separator="arrow" theme="default">
        <t-step-item title="选择企微" />
        <t-step-item title="选择业务人员" />
      </t-steps>
      <div class="modal-body__content">
        <choose-qy v-show="contractStep" :value="formValue" @onTableSelectChange="onQyTableSelectChange"></choose-qy>
        <choose-users v-show="contactStep" :visible="contactStep" :value="formValue" :selectData="selectQyData" @onSelectChange="onUsersTableSelectChange"></choose-users>
      </div>
    </div>

    <div class="buttons">
      <t-button v-if="!firstStep" theme="primary" @click="onPreStep">上一步</t-button>
      <t-button v-if="!lastStep" theme="primary" @click="onNextStep">下一步</t-button>
      <t-button v-if="lastStep" theme="primary" :disabled="confirmDisabled" @click="onConfirm">确认</t-button>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import {Prop, Vue, Component, Watch} from "vue-property-decorator";
import chooseQy from "@/pages/user-management/components/choose-qy.vue";
import chooseUsers from "@/pages/user-management/components/choose-users.vue";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

const ChooseStep = {
  participant: 0,
  contact: 1,
};

@Component({
  name: "ChooseQyUserModal",
  components: {
  chooseQy,
  chooseUsers,
  },
  })
export default class ChooseQyUserModal extends Vue {
  @Prop({ default: () => false }) visible!: boolean;

  @Prop({ default: () => {} }) data!: any;

  formValue: any = {};

  selectQyData: any = [];

  selectUsersData: any = [];

  currentStep = ChooseStep.participant;

  @Watch("data", {deep: true})
  onDataChange(val: any) {
    this.formValue = val;
  }

  get modalVisible(): boolean {
    return this.visible;
  }

  set modalVisible(nval) {
    this.$emit("update:visible", nval);
  }

  get firstStep(): boolean {
    return !this.contactStep;
  }

  get lastStep(): boolean {
    return !this.contractStep;
  }

  get contractStep(): boolean {
    return this.currentStep === ChooseStep.participant;
  }

  get contactStep(): boolean {
    return this.currentStep === ChooseStep.contact;
  }

  get confirmDisabled(): boolean {
    return this.selectUsersData.length === 0;
  }

  onPreStep(): void {
    this.selectUsersData = [];
    if (this.currentStep > ChooseStep.participant) {
      this.currentStep--;
    }
  }

  onQyTableSelectChange(val: any): void {
    this.selectQyData = val;
  }

  onUsersTableSelectChange(val: any): void {
    this.selectUsersData = val;
  }

  onNextStep(): void {
    if (this.selectQyData.length === 0) {
      this.$message.info("请选择一个企微");
      return;
    }

    if (this.currentStep < ChooseStep.contact) {
      this.currentStep++;
    }
  }

  async onConfirm() {
    if (!this.selectUsersData) {
      this.$message.info("请选择一位业务人员");
      return;
    }
    const userQyRelationRequestList = this.selectUsersData.map((user: any) => ({
      corpId: user.corpId,
      qyUserId: user.qyUserId,
    }));
    const systemUserId = this.data.userId;
    const params = {
      companyId: this.data.companyId,
      systemUserId,
      systemUserQyRelationRequest: userQyRelationRequestList,
    };
    const res = await (this as any).$request.post(orderAndUserApi.userBindQy.url, params);
    if (res.code === 0) {
      this.$message.success("绑定成功");
      this.modalVisible = false;
      this.$emit("success");
    } else {
      this.$message.error(res.msg || "绑定失败");
    }
  }

  onCancel(): void {
    this.modalVisible = false;
    this.$emit("close");
  }
}
</script>

<style lang="less" scoped>
::v-deep {
  .t-dialog__header .t-dialog__header-content {
    font-size: 14px;
    color: #333333;
  }
  .t-dialog__body {
    max-height: 660px;
    margin-bottom: 60px;
  }
  .t-steps {
    justify-content: center;
  }
  .t-steps .t-steps-item {
    flex: none;
  }
  .t-steps .t-steps-item__title {
    font-size: 14px;
  }
}
.meet-part-modal {
  .modal-body {
    height: 500px;
  }
  .modal-body__content {
    margin-top: 20px;
  }
  .buttons {
    position: absolute;
    width: 100%;
    bottom: 30px;
    text-align: center;
    button {
      margin-left: 10px;
    }
  }
}
</style>
