<template>
  <t-dialog
    :header="data.id ? '编辑账户' : '新增账户'"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <t-form :data="form" :rules="rules" :labelWidth="140" :statusIcon="true" ref="formRef" @submit="onSubmit">
        <t-form-item label="登录账户" name="accountId">
          <t-input v-model="form.accountId" placeholder="由数字和字母组成"></t-input>
        </t-form-item>
        <t-form-item label="用户名" name="username">
          <t-input v-model="form.username" placeholder="请输入用户名"></t-input>
        </t-form-item>
        <t-form-item label="手机号码" name="phone">
          <t-input v-model="form.phone" placeholder="请输入"></t-input>
        </t-form-item>
        <t-form-item label="出生日期" name="birthDate">
          <t-date-picker
            placeholder="请选择"
            theme="primary"
            mode="date"
            v-model="form.birthDate"
            :clearable="true"
          ></t-date-picker>
        </t-form-item>
        <t-form-item label="角色" name="roleId">
          <t-select placeholder="请选择角色"
                    :disabled="form.roleId && sysRoleListIds && sysRoleListIds.length != 0 && !sysRoleListIds.includes(form.roleId)"
                    v-model="form.roleId"
                    v-if="!form.roleId || (sysRoleListIds && sysRoleListIds.length != 0 && sysRoleListIds.includes(form.roleId))"
                    :options="sysRoleOptions">
          </t-select>
          <t-input v-model="form.roleName"
                   :disabled="true"
                    v-else>
          </t-input>
        </t-form-item>
        <t-form-item label="密码" name="password">
          <t-input v-model="form.password" placeholder="请输入密码" type="password"></t-input>
        </t-form-item>
        <t-form-item label="启用状态" name="auditStatus">
          <t-select v-model="form.status" placeholder="请选择">
            <t-option v-for="item in USER_STATE_OPTIONS" :key="item.value" :label="item.label" :value="item.value"></t-option>
          </t-select>
          </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import { USER_STATE_OPTIONS } from '@/constants';
import { prefix } from '@/config/global';
import apiUser from '@/constants/api/back/system-user.api';
import {Code} from "@/constants/enum/general/code.enum";
import systemRoleApi from "@/constants/api/hxsy-admin/system-role.api";
import {SYSTEM_ROLE_CONST} from "@/constants/enum/system/system-role.enum";

export default Vue.extend({
  name: 'EditModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
    allSysCompanyInfos: {
      type: Array,
      default() {
        return [];
      },
      required: false,
    },
  },

  data() {
    return {
      USER_STATE_OPTIONS,
      prefix,
      loading: false,
      form: {
        id: '',
        accountId: '',
        unionId: '',
        accountType: 1,
        username: '',
        headquartersId: null,
        columnId: null,
        companyId: null,
        salesGroupId: null,
        roleName: '',
        roleId: null,
        phone: '',
        birthDate: '',
        password: '',
        auditStatus: 2,
        status: 1,
      },
      sysPostInfosRun: [],
      sysPostInfos: [],
      sysRoleList: [], // 系统角色列表
      sysRoleOptions: [], // 根据角色列表转换为系统角色选项
      sysRoleListIds: [], // 当前用户可查询出的角色列表id
      rules: {
        accountId: [{ required: true }, { pattern: /(^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]+$)|(^[A-Za-z\d]+$)/, message: '请输入正确格式的登录账户' }],
        username: [{ required: true }, { pattern: /^[^\s]*$/, message: '禁止输入空格' }],
        phone: [{ pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: '请输入正确格式的手机号码' }],
      },
      userRoleType: '',
    };
  },
  computed: {
    SYSTEM_ROLE_CONST() {
      return SYSTEM_ROLE_CONST
    },
    modalVisible: {
      get() {
        return this.visible;
      },
      set() {
      },
    },
  },

  watch: {
    data: {
      handler(newValue) {
        this.form = {
          id: newValue.id || '',
          accountId: newValue.accountId || '',
          unionId: newValue.unionId || '',
          accountType: newValue.accountType || 1,
          username: newValue.username || '',
          headquartersId: newValue.headquartersId || null,
          columnId: newValue.columnId || null,
          companyId: newValue.companyId || null,
          salesGroupId: newValue.salesGroupId || null,
          roleName: newValue.roleName || '',
          roleId: newValue.roleId || null,
          phone: newValue.phone || '',
          birthDate: newValue.birthDate || '',
          password: newValue.password || '',
          auditStatus: newValue.auditStatus || 2,
          status: newValue.status || 0,
        };
        // (this as any).$refs.formRef.reset();
      },
      deep: true,
    },
  },

  async mounted() {
    // 获取角色列表
    this.getRoleList();
    this.userRoleType = this.getUser() ? this.getUser().roleType : '';
  },
  methods: {
    handleCancel() {
      this.$emit('cancel');
    },
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    },
    handleOk() {
      this.$refs.formRef.submit();
    },
    /**
     * @description: 获取系统角色列表
     * <AUTHOR>
     * @date 2025/5/10 17:46
     */
    async getRoleList() {
      // 直接复用分页查询接口，设置无限大就行
      const query = {
        pageNum: 1,
        pageSize: 9999,
      };
      try {
        const res = await (this as any).$request.post(
          systemRoleApi.queryPage.url,
          {
            roleCode: '',
            roleName: '',
            roleType: '',
            status: '1',
          },
          {
            params: query,
          },
        );
        const { code, data, msg } = res;
        if (code === 0) {
          this.sysRoleList = data.records || [];
          // 非超管和普管，先过滤掉自身角色，防止可以分配同级角色
          if (this.sysRoleList && this.sysRoleList.length > 0 && this.userRoleType != SYSTEM_ROLE_CONST.ADMIN.value && this.userRoleType != SYSTEM_ROLE_CONST.COMMON_ADMIN.value) {
            this.sysRoleList = this.sysRoleList.filter((item: any) => item.id !== this.getUser().roleId);
          }
          // console.log("获取到系统角色：", this.sysRoleList);
          // 转为options需要的格式
          if (this.sysRoleList && this.sysRoleList.length > 0) {
            this.sysRoleOptions = this.sysRoleList.map((item: any) => {
              return { label: item.roleName, value: item.id };
            });
            this.sysRoleListIds = this.sysRoleList.map((item: any) => {
              return item.id;
            });
          }
          // console.log("当前可查询角色选项：", this.sysRoleOptions);
          // console.log("当前可查询角色id：", this.sysRoleListIds);
        } else {
          this.$message.error(msg || '请求失败');
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
      }
    },
    async saveData() {
      this.loading = true;
      // 设置一下角色名称
      if (this.form.roleId) {
        this.form.roleName = this.sysRoleList.find((item: any) => item.id === this.form.roleId)?.roleName;
      }
      const params = { ...this.form };
      console.log(params);
      const method = 'put';
      try {
        const res = await this.$request[method](apiUser.updateUser.url, params);
        const { code, msg } = res;
        if (code === Code.OK.code) {
          this.$message.success('保存成功');
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.loading = false;
      } catch (e) {
        console.log(e);
        this.loading = false;
      }
    },

    onSubmit({ validateResult }) {
      // 判断当前修改员工信息是否比自身权限更大，更大则不允许修改（其实很简单，我查不出来就是权限比他更小，就不给修改）
      // console.log("this.form:", this.form);
      if (!this.sysRoleListIds || this.sysRoleListIds.length === 0) {
        this.$message.error("尚未具备该员工修改权限");
        return;
      }else if (this.form.roleId && !this.sysRoleListIds.includes(this.form.roleId)) {
        this.$message.error("尚未具备该员工修改权限");
        return;
      }
      if (validateResult === true) {
        this.saveData();
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.t-input-number {
  width: 200px;
}
</style>
