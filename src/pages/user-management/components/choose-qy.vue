<template>
  <div class="sys-gc-table-box">
    <t-table class="sys-gc-table" bordered :columns="columns" row-key="corpId" :scroll="{ y: '27em' }" :pagination="pagination" @page-change="handlePageChange" :reserve-selected-row-on-paginate="true" :data="bindQyList" :loading="loading" size="small" :selected-row-keys="selectedRowKeys" @select-change="onTableSelectChange"> </t-table>
  </div>
</template>
<script lang="ts">
import Vue from "vue";
import {Component, Prop, Watch} from "vue-property-decorator";

// 表头
const TableColumns: any[] = [
  {
    colKey: "row-select",
    type: "single",
    className: "meet-cust-select-cell",
    // 允许单选(Radio)取消行选中
    checkProps: { allowUncheck: true },
    width: 20,
  },
  {
    title: "企业名称",
    colKey: "corpName",
    ellipsis: true,
    width: 50,
    align: "center",
  },
  {
    title: "企业ID（corpId）",
    colKey: "corpId",
    ellipsis: true,
    width: 50,
    align: "center",
  },
];

@Component({
  name: "chooseQy",
  components: {},
  })

export default class chooseQy extends Vue {
  @Prop({ default: () => {} }) value!: any;

  columns = TableColumns;

  loading = false;

  bindQyList: any[] = [];

  selectedRowKeys: any[] = []; // 表格数据选中项Key

  pagination = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  @Watch("value", { deep: true })
  onValueChange(val: any): void {
    if (val && val.qyData) {
      this.pagination.total = val.qyData.length;
      this.bindQyList = val.qyData || [];
    }
  }

  /**
   * 选中行的数据
   */
  get selectedRowData(): any[] {
    const arr: any = [];
    const allArr: any[] = this.bindQyList || [];
    const selectedRowKeys = this.selectedRowKeys || [];
    for (let i = 0; i < allArr.length; i++) {
      const item: any = allArr[i];
      if (selectedRowKeys.includes(item.corpId) && arr.every((k: any) => k.corpId !== item.corpId)) {
        arr.push(item);
      }
    }
    return arr;
  }

  handlePageChange(pageInfo: any): void {
    this.pagination.current = pageInfo.current;
    this.pagination.pageSize = pageInfo.pageSize;
  }

  /**
   * 表格选中状态变更处理
   */
  onTableSelectChange(selectedRowKeys: any): void {
    this.selectedRowKeys = selectedRowKeys || [];
    this.$emit("onTableSelectChange", this.selectedRowData);
  }
}
</script>
<style lang="less" scoped>
/deep/ .t-tag {
  margin-left: 10px;
}
.sys-gc-table-card {
  height: 400px;
  border: 1px solid #dddddd;
}
</style>
