<template>
  <t-dialog
    :header="data.username ? `编辑用户${data.username}分组` : '编辑用户分组'"
    :width="700"
    :visible.sync="modalVisible"
    @confirm="handleOk"
    destroyOnClose
    :onCancel="handleCancel"
    :closeOnOverlayClick="false"
    :onClose="handleCancel"
    :draggable="true"
    :confirmBtn="
      loading
        ? {
            content: '保存中...',
            theme: 'primary',
            loading: true,
          }
        : {
            content: '提交',
            theme: 'primary',
          }
    "
  >
    <div>
      <t-tree
        :value="activeNode"
        class="tree-area"
        :data="sysCompanyInfos"
        activable
        checkable
        hover
        transition
        :expandLevel="3"
        :checkStrictly="true"
        @change="companyTreeChange"
        :keys="{
          value: 'key',
          label: 'label',
          children: 'children',
        }"
      />
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import apiUser from '@/constants/api/back/system-user.api';
import {Code} from "@/constants/enum/general/code.enum";

export default Vue.extend({
  name: 'EditUserModal',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
      required: false,
    },
    allSysCompanyInfos: {
      type: Array,
      default() {
        return [];
      },
      required: false,
    },
  },

  data() {
    return {
      loading: false,
      activeNode: [],
      form: {
        id: '',
        accountId: '',
        unionId: '',
        accountType: 1,
        username: '',
        headquartersId: null,
        columnId: null,
        companyId: null,
        salesGroupId: null,
        roleName: '',
        roleId: null,
        phone: '',
        birthDate: '',
        password: '',
        auditStatus: 2,
        status: 1,
      },
    };
  },
  computed: {
    modalVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    sysCompanyInfos: {
      get() {
        if (!this.allSysCompanyInfos.length) return [];
        const processNodes = (nodes: any[], level = 1) => nodes.map(node => ({
          ...node,
          disabled: level < 4,  // 前3级禁用
          level,                // 添加层级标记
          children: node.children ? processNodes(node.children, level + 1) : []
        }));
        const data = processNodes(this.allSysCompanyInfos);
        return data;
      },
    }
  },

  watch: {
    data: {
      handler(newValue) {
        this.form = {
          id: newValue.id || '',
          accountId: newValue.accountId || '',
          unionId: newValue.unionId || '',
          accountType: newValue.accountType || 1,
          username: newValue.username || '',
          headquartersId: newValue.headquartersId || null,
          columnId: newValue.columnId || null,
          companyId: newValue.companyId || null,
          salesGroupId: newValue.salesGroupId || null,
          roleName: newValue.roleName || '',
          roleId: newValue.roleId || null,
          phone: newValue.phone || '',
          birthDate: newValue.birthDate || '',
          password: newValue.password || '',
          auditStatus: newValue.auditStatus || 2,
          status: newValue.status || 0,
        };
        this.activeNode.push(newValue.salesGroupId);
      },
      deep: true,
    },
  },
  methods: {
    companyTreeChange(data: any, context: any) {
      if (!data.length) return;
      this.activeNode = [context.node.value]; // 选中节点的ID
      const findParents = (nodes: any[], targetId: any, parents: any[] = []) => {
        for (const node of nodes) {
          if (node.key === targetId) {
            return parents;
          }
          if (node.children) {
            const found = findParents(node.children, targetId, [...parents, node]);
            if (found) return found;
          }
        }
        return null;
      };

      const activeNode = this.findNodeById(this.activeNode[0]);
      if (activeNode?.level === 4) {
        const parents = findParents(this.sysCompanyInfos, activeNode.key) || [];
        const secondLevelId = parents[1]?.key;  // 第二级ID
        const thirdLevelId = parents[2]?.key;   // 第三级ID
        const fourthLevelId = activeNode.key;   // 第四级ID

        this.form.salesGroupId = fourthLevelId;
        this.form.companyId = thirdLevelId;
        this.form.columnId = secondLevelId;
      }
    },
    findNodeById(id: any, nodes = this.sysCompanyInfos): any {
      for (const node of nodes) {
        if (node.key === id) return node;
        if (node.children) {
          const found = this.findNodeById(id, node.children);
          if (found) return found;
        }
      }
      return null;
    },
    handleCancel() {
      this.activeNode = [];
      this.$emit('cancel');
    },
    handleOk() {
      this.saveData();
    },
    async saveData() {
      this.loading = true;
      const params = {...this.form};
      console.log(params);
      const method = 'put';
      try {
        const res = await this.$request[method](apiUser.updateUser.url, params);
        const {code, msg} = res;
        if (code === Code.OK.code) {
          this.$message.success('保存成功');
          this.activeNode = [];
          this.$emit('success');
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.loading = false;
      } catch (e) {
        console.log(e);
        this.loading = false;
      }
    },
  },
});
</script>

<style lang="less" scoped>
.tree-area {
  overflow: auto;
  max-height: 500px;
}
</style>
