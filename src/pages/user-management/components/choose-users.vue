<template>
  <div class="project-within-role-modal-table">
    <div class="cm-gc-search d-flex flex-row">
      <div class="cm-gc-search-box">
        <div class="search-input">
          <div class="search-label"><span>姓名</span></div>
          <t-input v-model="searchUserInfo.userName" placeholder="请输入"></t-input>
        </div>
        <div class="search-btn">
          <t-button class="btn-item" theme="primary" @click="onSearchClick">查询</t-button>
          <t-button class="btn-item" theme="default" @click="onResetClick">重置</t-button>
        </div>
      </div>
    </div>
    <t-table class="sys-gc-table" :columns="tableColumns" :data="userList" :loading="loading" :pagination="pagination" @page-change="handlePageChange" :reserve-selected-row-on-paginate="true" :selected-row-keys="selectedKeys" row-key="qyUserId" @select-change="onSelectChange"> </t-table>
  </div>
</template>

<script lang="ts">
import {Prop, Component, Vue, Watch} from "vue-property-decorator";
import { PrimaryTableCol } from "tdesign-vue/lib/table";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";

const tableColumns: PrimaryTableCol[] = [
  {
    colKey: "row-select",
    type: "multiple",
    width: 40,
  },
  {
    title: "姓名",
    colKey: "qyName",
    ellipsis: true,
    align: "left",
  },
  {
    title: "企业ID(corpId)",
    colKey: "corpId",
    ellipsis: true,
    align: "left",
  },
  {
    title: "已关联业务人员",
    colKey: "systemUserResponse",
    ellipsis: true,
    align: "left",
    cell: (h, { col, row }) => {
      const val = row[col.colKey];
      return val? `${val.username}` : '';
    },
  }
];

@Component({
  name: "ChooseUsers",
  components: {},
  })
export default class ChooseUsers extends Vue {
  @Prop({ default: () => {} }) value!: any;

  @Prop({ default: () => [] }) selectData!: any[]; // 表单数据

  @Prop({ default: () => false }) visible!: any;

  tableColumns: PrimaryTableCol[] = tableColumns;

  searchUserInfo = {
    userName: "",
  };

  userList: any[] = [];

  userListBackup: any[] = [];

  selectedKeys: Array<string> = [];

  selectedRowData: any[] = [];

  loading = false;

  pagination = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  @Watch("visible", {deep: true})
  onSelectDataChange(value: boolean) {
    if (value) this.search();
  }

  onSelectChange(value: Array<string>, { selectedRowData }: any): void {
    this.selectedKeys = value;
    this.$emit("onSelectChange", selectedRowData);
  }

  /**
   * 重置
   */
  onResetClick(): void {
    this.selectedKeys = [];
    this.$emit("onSelectChange", []);
    this.searchUserInfo.userName = "";
    this.userList = JSON.parse(JSON.stringify(this.userListBackup));
  }

  handlePageChange(pageInfo: any): void {
    this.pagination.current = pageInfo.current;
    this.pagination.pageSize = pageInfo.pageSize;
  }

  /**
   * 搜索
   */
  onSearchClick(): void {
    const searchData = JSON.parse(JSON.stringify(this.userListBackup));
    this.userList = searchData.filter((item: any) => item.qyName.includes(this.searchUserInfo.userName));
  }

  /**
   * 模态框内搜索
   */
  async search() {
    if (!this.value.companyId || this.selectData.length === 0) return;
    this.loading = true;
    const params = {
      companyId: this.value.companyId,
      corpId: this.selectData[0].corpId,
    };
    const res = await (this as any).$request.post(orderAndUserApi.queryBindQyUserList.url, params);
    this.loading = false;
    const { code, msg, data } = res;
    if (code === 0) {
      this.pagination.total = data.length;
      this.userList = data;
      this.userListBackup = JSON.parse(JSON.stringify(data));
    } else {
      this.$message.error(msg);
    }
  }
}
</script>

<style scoped lang="less">
.project-within-role-modal-table {
  .cm-gc-search {
    position: relative;
    margin-bottom: 10px;
    background-color: #f8fbfe;
    height: 64px;
    border: 1px solid #f3f3f3;

    .cm-gc-search-box {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 10px;
      .search-input {
        .sys-search-form-model {
          display: flex;
          width: 280px;
        }
        display: flex;
        margin-right: 10px;
        .search-label {
          text-align: right;
          width: 100px;
          height: 32px;
          line-height: 32px;
          margin-right: 10px;
          font-size: 16px;
          color: #999999;
        }
      }

      .search-btn {
        display: flex;
        button {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
