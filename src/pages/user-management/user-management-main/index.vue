<template>
  <div class="user-management-page">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧组织架构 -->
      <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-header">
          <h3 class="sidebar-title" v-show="!sidebarCollapsed">
            <t-icon name="layers" />
            组织架构
          </h3>
          <div class="sidebar-actions">
            <t-button
              size="small"
              variant="text"
              @click="toggleSidebar"
              :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
            >
              <t-icon :name="sidebarCollapsed ? 'chevron-right' : 'chevron-left'" />
            </t-button>
          </div>
        </div>
        <div class="sidebar-content" v-show="!sidebarCollapsed">
          <t-tree
            ref="organizationTree"
            class="organization-tree"
            :data="companyTreeData"
            activable
            hover
            transition
            :expand-level="1"
            @active="companyTreeChange"
            :loading="dataLoading"
          />
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 搜索筛选区域 -->
        <div class="filter-section">
          <div class="filter-content">
            <t-form
              ref="form"
              :data="formData"
              layout="inline"
              @reset="onReset"
              @submit="onSubmit"
              class="filter-form"
            >
              <!-- 所有筛选条件 -->
              <div class="filter-row">
                <t-form-item name="idValue" class="filter-item">
                  <t-input-group class="id-search-group">
                    <t-select
                      v-model="formData.idType"
                      :options="ID_TYPE_OPTIONS"
                      class="id-type-select"
                      clearable
                    />
                    <t-input
                      v-model="formData.idValue"
                      type="search"
                      :placeholder="formData.idType === 'accountId' ? '请输入账户ID' : '请输入系统ID'"
                      clearable
                      class="id-value-input"
                    >
                      <template #prefixIcon>
                        <t-icon name="search" />
                      </template>
                    </t-input>
                  </t-input-group>
                </t-form-item>

                <t-form-item label="用户名" name="userName" class="filter-item">
                  <t-input
                    v-model="formData.userName"
                    type="search"
                    placeholder="请输入用户名"
                    clearable
                    class="filter-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="user" />
                    </template>
                  </t-input>
                </t-form-item>

                <t-form-item label="手机号" name="phone" class="filter-item">
                  <t-input
                    v-model="formData.phone"
                    type="search"
                    placeholder="请输入手机号"
                    clearable
                    class="filter-input"
                  >
                    <template #prefixIcon>
                      <t-icon name="mobile" />
                    </template>
                  </t-input>
                </t-form-item>

                <t-form-item label="用户状态" name="useState" class="filter-item">
                  <t-select
                    v-model="formData.status"
                    :options="USER_STATE_OPTIONS"
                    placeholder="请选择用户状态"
                    clearable
                    class="filter-select"
                  >
                    <template #prefixIcon>
                      <t-icon name="check-circle" />
                    </template>
                  </t-select>
                </t-form-item>

                <t-form-item label="审核状态" name="auditStatus" class="filter-item">
                  <t-select
                    v-model="formData.auditStatus"
                    :options="auditStateOptionsComputed"
                    placeholder="请选择审核状态"
                    clearable
                    class="filter-select"
                  >
                    <template #prefixIcon>
                      <t-icon name="assignment" />
                    </template>
                  </t-select>
                </t-form-item>

                <t-form-item label="创建时间" name="createdAt" class="filter-item">
                  <t-date-range-picker
                    v-model="formData.createdAt"
                    allow-input
                    placeholder="请选择创建时间"
                    clearable
                    class="filter-date"
                  />
                </t-form-item>

                <!-- 操作按钮 -->
                <div class="filter-buttons">
                  <t-button theme="primary" type="submit" class="search-btn">
                    <t-icon name="search" />
                    查询
                  </t-button>
                  <t-button type="reset" variant="outline" class="reset-btn">
                    <t-icon name="refresh" />
                    重置
                  </t-button>
                </div>
              </div>
            </t-form>
          </div>
        </div>
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <div class="selection-info" v-if="selectedRowKeys.length > 0">
              <t-icon name="check-circle" />
              <span class="selected-count">已选择 {{ selectedRowKeys.length }} 项</span>
              <t-button size="small" variant="text" @click="clearSelection">
                <t-icon name="close" />
                清空
              </t-button>
            </div>
            <div class="table-info" v-else>
              <t-icon name="view-list" />
              <span>用户列表</span>
              <span class="total-count">（共 {{ pagination.total }} 条）</span>
            </div>
          </div>

          <div class="toolbar-right">
            <t-dropdown
              trigger="click"
              :options="changeStatusOption"
              @click="clickHandlerChangeStatus"
              :minColumnWidth="120"
              class="batch-action-dropdown"
            >
              <t-button
                theme="default"
                :disabled="selectedRowKeys.length == 0"
                variant="outline"
                class="batch-btn"
              >
                <t-icon name="edit" />
                批量操作
                <t-icon name="chevron-down" />
              </t-button>
            </t-dropdown>

            <t-button @click="refreshData" variant="outline" class="refresh-btn">
              <t-icon name="refresh" />
              刷新
            </t-button>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-section">
          <t-table
            ref="userTable"
            stripe
            :columns="columns"
            :data="data"
            :rowKey="userId"
            :verticalAlign="verticalAlign"
            :hover="true"
            :pagination="pagination"
            :selected-row-keys="selectedRowKeys"
            :loading="dataLoading"
            bordered
            @change="rehandleChange"
            @select-change="rehandleSelectChange"
            :max-height="600"
            :headerAffixedTop="true"
            :headerAffixProps="{ offsetTop: 0 }"
            class="user-table"
          >
              <!-- 用户状态 -->
              <template #useState="{ row }">
                <t-tag
                  :theme="row.useState === '1' ? 'success' : 'danger'"
                  variant="light"
                  class="status-tag"
                >
                  <t-icon :name="row.useState === '1' ? 'check-circle' : 'close-circle'" />
                  {{ row.useState === '1' ? '有效' : '禁用' }}
                </t-tag>
              </template>

              <!-- 审核状态 -->
              <template #auditStatus="{ row }">
                <t-tag
                  :theme="row.auditStatus === 2 ? 'success' : row.auditStatus === 1 ? 'danger' : 'warning'"
                  variant="light"
                  class="audit-tag"
                >
                  <t-icon
                    :name="row.auditStatus === 2 ? 'check-circle' : row.auditStatus === 1 ? 'close-circle' : 'time'"
                  />
                  {{ row.auditStatus === 0 ? '待审核' : row.auditStatus === 1 ? '已拒绝' : '已通过' }}
                </t-tag>
              </template>
              <template #systemUserQyRelationResponses="{ row }">
                <div class="wechat-binding-status">
                  <!-- 已绑定企微的情况 -->
                  <template v-if="row.systemUserQyRelationResponses && row.systemUserQyRelationResponses.length > 0">
                    <div class="binding-tags-container">
                      <div
                        v-for="(item, index) in row.systemUserQyRelationResponses.slice(0, 2)"
                        :key="index"
                        class="binding-tag-wrapper"
                      >
                        <t-tooltip
                          :content="`${item.qyName}${item.corpName ? `(${item.corpName})` : ''}`"
                          placement="top"
                        >
                          <t-tag
                            theme="success"
                            variant="light"
                            class="binding-tag bound"
                          >
                            <t-icon name="check-circle" class="binding-icon" />
                            {{ item.qyName }}{{ item.corpName ? `(${item.corpName})` : '' }}
                          </t-tag>
                        </t-tooltip>
                        <t-button
                          variant="text"
                          theme="danger"
                          size="small"
                          class="unbind-btn"
                          @click="handleUnbindQy(row, item)"
                          :title="'解绑企微账号'"
                        >
                          <t-icon name="close" size="12px" />
                        </t-button>
                      </div>

                      <!-- 如果有更多绑定，显示更多标签 -->
                      <t-popup v-if="row.systemUserQyRelationResponses.length > 2">
                        <t-tag theme="success" variant="outline" class="more-tag">
                          <t-icon name="ellipsis" />
                          更多({{ row.systemUserQyRelationResponses.length - 2 }})
                        </t-tag>
                        <template #content>
                          <div class="popup-tags">
                            <div
                              v-for="(item, index) in row.systemUserQyRelationResponses.slice(2)"
                              :key="index + 2"
                              class="binding-tag-wrapper popup-tag-wrapper"
                            >
                              <t-tag
                                theme="success"
                                variant="light"
                                class="popup-tag"
                              >
                                <t-icon name="check-circle" class="binding-icon" />
                                {{ item.qyName }}{{ item.corpName ? `(${item.corpName})` : '' }}
                              </t-tag>
                              <t-button
                                variant="text"
                                theme="danger"
                                size="small"
                                class="unbind-btn"
                                @click="handleUnbindQy(row, item)"
                                :title="'解绑企微账号'"
                              >
                                <t-icon name="close" size="12px" />
                              </t-button>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
                  </template>

                  <!-- 暂未绑定企微的情况 -->
                  <template v-else>
                    <t-tag theme="warning" variant="light" class="binding-tag unbound">
                      <t-icon name="close-circle" class="binding-icon" />
                      暂未绑定企微
                    </t-tag>
                  </template>
                </div>
              </template>
              <!-- 操作按钮 -->
              <template #op="slotProps">
                <div v-if="slotProps.row.accountId !== 'admin'" class="operation-buttons">
                  <t-button
                    size="small"
                    theme="primary"
                    variant="text"
                    @click="handleClickDetail(slotProps)"
                    class="edit-btn"
                  >
                    <t-icon name="edit" />
                    修改
                  </t-button>

                  <t-dropdown
                    trigger="click"
                    :options="moreButtonsComputed"
                    @click="(e) => { moreButtonsClick(e, slotProps.row); }"
                    :minColumnWidth="120"
                    class="more-actions-dropdown"
                  >
                    <t-button
                      size="small"
                      variant="text"
                      class="more-btn"
                    >
                      <t-icon name="more" />
                      更多
                      <t-icon name="chevron-down" size="12px" />
                    </t-button>
                  </t-dropdown>
                </div>
                <div v-else class="admin-tag">
                  <t-tag theme="primary" variant="light">
                    <t-icon name="crown" />
                    系统管理员
                  </t-tag>
                </div>
              </template>
            </t-table>
          </div>
        </div>
      </div>
    <edit-modal
      :data="editModalVar.data"
      :allSysCompanyInfos="allSysCompanyInfos"
      :visible="editModalVar.visible"
      @cancel="handelCloseEditModal"
      @success="handelEditModalSuccess"
    ></edit-modal>
    <edit-user-group
      :data="editUserRowData"
      :allSysCompanyInfos="companyTreeData"
      :visible="editUserGroupVisible"
      @cancel="handelCloseUserGroupModal"
      @success="handelUserGroupModalSuccess"
    ></edit-user-group>
    <edit-user-permission
      :data="editUserRowData"
      :allSysCompanyInfos="companyTreeData"
      :visible="editUserPermission"
      @cancel="handelCloseUserPermissionModal"
      @success="handelUserPermissionModalSuccess"
    >
    </edit-user-permission>
    <choose-qy-user-modal :visible="chooseQyUserModalVisible" :data="chooseQyUserData" @close="handleCancelChooseQyUser" @success="handleChooseQyUserSuccess"></choose-qy-user-modal>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import {ChevronDownIcon} from 'tdesign-icons-vue';
import {treeNodeConvertService} from '@/service/tree-node-convert.service';
import EditModal from '../components/edit-modal.vue';
import apiUserLogin from '@/constants/api/back/sys-user-login.api';
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import {USER_STATE_OPTIONS, AUDIT_STATE_OPTIONS} from '@/constants';
import {Code} from "@/constants/enum/general/code.enum";
import systemUserApi from "@/constants/api/back/system-user.api";
import editUserGroup from "@/pages/user-management/components/edit-user-group.vue";
import {SYSTEM_ROLE_CONST} from "@/constants/enum/system/system-role.enum";
import editUserPermission from "@/pages/user-management/components/edit-user-permission.vue";
import orderAndUserApi from "@/constants/api/hxsy-admin/account-management.api";
import customerSalesRelationApi from "@/constants/api/hxsy-admin/customer-sales-relation.api";
import ChooseQyUserModal from "@/pages/user-management/components/choose-qy-user-modal.vue";

export default Vue.extend({
  name: 'UserManagementMain',
  components: {
    EditModal,
    ChevronDownIcon,
    editUserGroup,
    editUserPermission,
    ChooseQyUserModal,
  },
  data() {
    return {
      USER_STATE_OPTIONS,
      AUDIT_STATE_OPTIONS,
      ID_TYPE_OPTIONS: [
        { value: 'accountId', label: '账户ID' },
        { value: 'systemId', label: '系统ID' },
      ],
      companyTreeData: [],
      allSysCompanyInfos: [],
      nodeContrast: {
        id: 'id',
        parentId: '',
        children: 'columns',
        cascadeField: 'columns',
        subNodeContrast: {
          id: 'id',
          parentId: '',
          children: 'companies',  // 注意这里必须与实际结构一致
          cascadeField: 'companies',
          subNodeContrast: {
            id: 'id',
            parentId: '',
            children: 'salesGroups', // 最后一层没有 children
            cascadeField: 'salesGroups',
            subNodeContrast: {
              id: 'id',
              parentId: '',
              children: '', // 最后一层没有 children
              cascadeField: '',
            }
          }
        }
      },
      nzNodeContrast: {
        key: 'id',
        title: 'name',
        label: 'name',
        children: 'columns',
        headquartersId: 'id',
        cascadeField: 'columns',
        subNzNodeContrast: {
          key: 'id',
          title: 'name',
          label: 'name',
          children: 'companies',
          columnId: 'id',
          cascadeField: 'companies',
          subNzNodeContrast: {
            key: 'id',
            label: 'name',
            children: 'salesGroups',
            companyId: 'id',
            cascadeField: 'salesGroups',
            subNzNodeContrast: {
              key: 'id',
              title: 'name',
              label: 'name',
              salesGroupId: 'id',
            }
          }
        }
      },
      formData: {
        idType: 'accountId',
        idValue: '',
        userName: '',
        phone: '',
        status: '',
        auditStatus: '',
        createdAt: [],
      },
      changeStatusOption: [
        {
          content: '拒绝',
          value: 1,
        },
        {
          content: '通过',
          value: 2,
        },
      ],
      editUserGroupVisible: false,
      editUserRowData: {},
      editUserPermission: false,
      chooseQyUserModalVisible: false,
      qyData: [],
      chooseQyUserData: {},

      moreButtons: [
        // {
        //   content: '分配角色',
        //   value: 1,
        // },
        {
          content: '权限分配',
          value: 5,
        },
        {
          content: '用户转组',
          value: 2,
        },
        {
          content: '重置密码',
          value: 3,
        },
        {
          content: '绑定企微',
          value: 6,
        },
        {
          content: '删除',
          value: 4,
        },
      ],

      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
          disabled: (data: any) => data.row.accountId === 'admin',
        },
        {
          title: '账户',
          align: 'left',
          width: 160,
          colKey: 'accountId',
        },
        {
          title: '用户名',
          align: 'left',
          width: 150,
          colKey: 'username',
        },
        {
          title: '手机号',
          align: 'left',
          width: 160,
          colKey: 'phone',
        },
        {
          title: '账号角色',
          align: 'left',
          width: 120,
          colKey: 'roleName',
        },
        {
          title: '已绑定企微信息',
          align: 'left',
          width: 200,
          colKey: 'systemUserQyRelationResponses',
        },
        {
          title: '用户状态',
          align: 'left',
          width: 120,
          colKey: 'status',
          cell: (h, {col, row}) => {
            const val = row[col.colKey];
            return this.getStatusText(val, row);
          },
        },
        {
          title: '审核状态',
          align: 'left',
          width: 120,
          colKey: 'auditStatus',
          cell: (h, {col, row}) => {
            const val = row[col.colKey];
            if (val === 0) return '待审核';
            if (val === 1) return '驳回';
            if (val === 2) return '已通过';
            return '未知';
          },
        },
        {
          title: '系统ID',
          align: 'left',
          width: 120,
          colKey: 'id',
        },
        {
          title: 'UnionID',
          align: 'left',
          width: 180,
          colKey: 'unionId',
        },
        {
          title: '操作',
          align: 'center',
          width: 200,
          colKey: 'op',
          fixed: 'right',
          cell: 'op',
        },
      ],
      rowKey: 'userId',
      userId: '',
      active: {
        headquartersId: '',
        columnId: '',
        companyId: '',
        salesGroupId: '',
      },
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',

      editModalVar: {
        visible: false,
        data: {},
      },
      userLogVar: {
        visible: false,
        data: {},
      },
      userRoleVar: {
        visible: false,
        data: {},
      },

      pagination: {
        // defaultPageSize: 10,
        total: 0,
        // defaultCurrent: 1,
        current: 1,
        pageSize: 10,
      },

      // 新增的UI状态
      sidebarCollapsed: false, // 侧边栏收起状态
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
    auditStateOptionsComputed() {
      // 只有超管可以看到查询null值的选项
      const isAdmin = String(this.getUser().roleType) === SYSTEM_ROLE_CONST.ADMIN.value;
      if (isAdmin) {
        // 添加一个查询null值的选项，后端使用-1接收
        return [...AUDIT_STATE_OPTIONS, { label: '未设置', value: -1 }];
      }
      return AUDIT_STATE_OPTIONS;
    },
    moreButtonsComputed() {
      const isAdmin = String(this.getUser().roleType) === SYSTEM_ROLE_CONST.ADMIN.value;

      switch (String(this.getUser().roleType)) {
      case SYSTEM_ROLE_CONST.OTHER.value:
        // 非管理员不显示用户转组、权限分配和删除按钮
        return this.moreButtons.filter((item: any) => item.value !== 2 && item.value !== 5 && item.value !== 4);
      case SYSTEM_ROLE_CONST.ADMIN.value:
        return this.moreButtons; // 超管显示所有按钮
      case SYSTEM_ROLE_CONST.COMMON_ADMIN.value:
        return this.moreButtons.filter((item: any) => item.value !== 4); // 普通管理员不显示删除按钮
      default:
        // 默认不显示权限分配和删除按钮
        return this.moreButtons.filter((item: any) => item.value !== 5 && item.value !== 4);
      }
    }
  },
  async mounted() {
    await this.getCompanyAuth();
    this.getList();
  },

  methods: {
    // sys-company-info/query-company-auth-view-info
    /**
     * 获取组织架构列表
     */
    async getCompanyAuth() {
      try {
        this.dataLoading = true;
        const params = {
          id: 31000, // 总公司ID
          level: 4, // 公司层级
        };
        const {code, data} = await this.$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, {params});
        console.error(data);
        if (code === 0 && data) {
          const companyTreeData = [
            {
              id: data.id, // 31000
              name: data.name || '总部',
              columns: data.columns.map((column: any) => ({
                ...column,
                companies: column.companies.map((company: any) => ({
                  ...company,
                  salesGroups: [...company.salesGroups],
                })),
              })),
            },
          ];
          this.allSysCompanyInfos = data.columns;
          const treeData = treeNodeConvertService.arrayConvertToNzTreeNode(
            companyTreeData,
            this.nodeContrast,
            this.nzNodeContrast,
          );
          console.log(treeData);
          this.companyTreeData = treeData;
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    },

    // 新增的UI交互方法
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
    },





    clearSelection() {
      this.selectedRowKeys = [];
    },

    refreshData() {
      this.getList();
    },


    getStatusText(val: any, col: any) {
      return USER_STATE_OPTIONS.find((item) => item.value === val)?.label;
    },
    rehandleChange(changeParams: any) {
      this.pagination.pageSize = changeParams.pagination.pageSize;
      this.pagination.current = changeParams.pagination.current;
      this.getList();
    },
    /**
     * 设置状态
     */
    clickHandlerChangeStatus(status: any) {
      const confirm = this.$dialog.confirm({
        theme: 'warning',
        header: '温馨提示',
        body: `是否状态变更为：${status.value === 2 ? '通过' : '拒绝'}?`,
        onConfirm: async () => {
          (confirm as any).destroy();
          const res = await this.changeStatusIds((this as any).selectedRowKeys, status.value);
          if (res === 'success') {
            this.selectedRowKeys = [];
          }
        },
      });
    },
    /**
     * 点击新增按钮
     */
    handleAdd() {
      this.editModalVar.visible = true;
      this.editModalVar.data = {};
    },
    handleClickDetail(slotProps: any) {
      if (slotProps?.row) {
        const list = this.allSysCompanyInfos.filter((item) => item.companyId === slotProps?.row.companyId)
        const subName = list[0]?.sysDeptInfos.filter((item) => item.salesGroupId === slotProps?.row.salesGroupId)[0]?.deptName
        this.editModalVar.data = {...slotProps?.row, sub: list[0]?.comName, subName}
      } else {
        this.editModalVar.data = {};
      }
      this.editModalVar.visible = true;
    },
    /**
     * 搜索提交
     */
    onSubmit(result: any) {
      this.pagination.current = 1;
      if (result.validateResult === true) {
        this.getList();
      }
    },
    onReset() {
      this.pagination.current = 1;
      this.formData = {
        idType: 'accountId',
        idValue: '',
        userName: '',
        phone: '',
        status: '',
        auditStatus: '',
        createdAt: [],
      };
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      this.selectedRowKeys = selectedRowKeys;
    },
    async moreButtonsClick(data: any, rowData: any) {
      if (data.value === 1) {
        // 分配角色
        this.userRoleVar.visible = true;
        this.userRoleVar.data = {...rowData};
      } else if (data.value === 2) {
        this.editUserRowData = rowData;
        this.editUserGroupVisible = true;
      } else if (data.value === 3) {
        const confirm = this.$dialog.confirm({
          theme: 'warning',
          header: `温馨提示`,
          body: '是否确定重置该用户的密码?',
          onConfirm: async () => {
            (confirm as any).destroy();
            this.resetRasswordBatch([rowData.userId]);
          },
        });
      } else if (data.value === 4) {
        const confirm = this.$dialog.confirm({
          theme: 'warning',
          header: '温馨提示',
          body: '确认删除该项？',
          onConfirm: async () => {
            (confirm as any).destroy();
            this.updateUserState(rowData.id);
          },
        });
      } else if (data.value === 5) {
        this.editUserRowData = rowData;
        this.editUserPermission = true;
      } else if (data.value === 6) {
        // 判断当前公司是否绑定企微
        const bindInfo = await this.getCompanyInfo(rowData.companyId);
        if (bindInfo) {
          this.getUserAndQyData(rowData.companyId, rowData.id);
        } else {
          this.$message.error('该账号所属公司未绑定企微，请绑定后再进行操作');
        }
      }
    },
    async getCompanyInfo(companyId: string) {
      const { code, data } = await this.$request.post(
        orderAndUserApi.queryCompanyUnionQy.url,
        {
          queryScene: "0",
          companyId,
        }
      );
      this.qyData = data;
      return !!(code === 0 && data && data.length > 0);
    },
    async getUserAndQyData(companyId: string, userId: string) {
      this.chooseQyUserData = {
        companyId,
        qyData: this.qyData,
        userId,
      };
      this.chooseQyUserModalVisible = true;
    },
    companyTreeChange(value: any, context: any) {
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      const {headquartersId, columnId, companyId, salesGroupId} = context.node.data;
      this.active.headquartersId = headquartersId;
      this.active.columnId = columnId;
      this.active.companyId = companyId;
      this.active.salesGroupId = salesGroupId;
      this.getList(); // 查询拉取列表
    },
    /**
     * 关闭对话框
     */
    handelCloseEditModal() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
    },
    handelCloseUserGroupModal() {
      this.editUserGroupVisible = false;
      this.editUserRowData = {};
    },
    handelUserGroupModalSuccess() {
      this.editUserGroupVisible = false;
      this.getList();
    },
    handelCloseUserPermissionModal() {
      this.editUserPermission = false;
      this.editUserRowData = {};
    },
    handelUserPermissionModalSuccess() {
      this.editUserPermission = false;
      this.getList();
    },
    handleCancelChooseQyUser() {
      this.chooseQyUserModalVisible = false;
      this.chooseQyUserData = {};
    },
    handleChooseQyUserSuccess(data: any) {
      this.chooseQyUserModalVisible = false;
      this.chooseQyUserData = {};
      this.getList();
    },
    /**
     * 关闭角色对话框
     */
    handelCloseEditRoleModal() {
      this.userRoleVar.visible = false;
      // this.userRoleVar.data = {};
    },
    handelEditModalSuccess() {
      this.editModalVar.visible = false;
      this.editModalVar.data = {};
      this.getList();
    },
    async getList() {
      const query = {};
      try {
        this.dataLoading = true;
        // 构建基本查询参数
        const queryParams = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          companyId: this.active.companyId || '',
          salesGroupId: this.active.salesGroupId,
          startTime: this.formData.createdAt?.[0] || '',
          endTime: this.formData.createdAt?.[1] || '',
          status: this.formData.status,
          headquartersId: this.active.headquartersId,
          columnId: this.active.columnId,
          username: this.formData.userName,
          phone: this.formData.phone,
          auditStatus: this.formData.auditStatus,
        };

        // 根据查询类型设置不同的查询参数
        if (this.formData.idType === 'accountId') {
          queryParams.accountId = this.formData.idValue;
        } else if (this.formData.idType === 'systemId') {
          queryParams.id = this.formData.idValue;
        }

        const res = await (this as any).$request.post(
          systemUserApi.queryUserByPage.url,
          queryParams,
        );
        const {code, data, msg} = res;
        if (code === Code.OK.code) {
          this.data = data?.records || [];
          this.pagination.total = Number(data?.total || 0);
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        // this.$message.error('请求失败');
        this.dataLoading = false;
      }
    },
    /**
     * 批量改变状态
     */
    async changeStatusIds(ids: [], status: any) {
      try {
        const res = await (this as any).$request.post(systemUserApi.updateAuditStatusBatch.url, {
          ids,
          auditStatus: status,
        });
        console.log(this.selectedRowKeys);
        const {code, msg} = res;
        if (code === Code.OK.code) {
          this.$message.success(msg || '状态改变成功');
          this.getList();
          return 'success';
        }
        this.$message.error(msg || '状态改变失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    /**
     * 批量改变状态
     */
    async resetRasswordBatch(ids: string[]) {
      try {
        const res = await (this as any).$request.put(apiUserLogin.resetPasswordBatch.url, ids);
        const {code, msg} = res;
        if (code === 1) {
          this.$message.success('重置成功，新密码为 pw@32! ');
          return 'success';
        }
        this.$message.error(msg || '重置失败');
      } catch (e) {
        // this.$message.error('请求失败');
      }
    },
    /**
     * 删除用户
     */
    async updateUserState(id: string) {
      try {
        // 获取用户的unionId
        const userData = this.data.find((item: any) => item.id === id);
        const unionId = userData?.unionId || '';

        const res = await (this as any).$request.delete(
          `${systemUserApi.deleteUser.url}/${id}`,
          {
            params: {
              unionId,
            },
          },
        );
        const {code, msg} = res;
        if (code === 0) { // 假设成功代码是0
          this.$message.success(msg || '删除成功');
          this.getList();
          return 'success';
        }
        this.$message.error(msg || '删除失败');
      } catch (e) {
        console.error('删除用户失败:', e);
        this.$message.error('删除用户失败');
      }
    },

    /**
     * 处理企微解绑
     */
    handleUnbindQy(user, qyBinding) {
      console.log("user", user);
      console.log("qyBinding", qyBinding);
      const confirm = this.$dialog.confirm({
        header: '确认解绑',
        body: `确认要解绑用户 "${user.username}" 与企微账号 "${qyBinding.qyName}${qyBinding.corpName ? `(${qyBinding.corpName})` : ''}" 的绑定关系吗？`,
        confirmBtn: '确认解绑',
        cancelBtn: '取消',
        theme: 'warning',
        onConfirm: async () => {
          try {
            // 构造解绑参数
            const params = {
              systemUserId: user.id, // 要解绑的用户ID
              qyUserId: qyBinding.qyUserId, // 企微用户ID
              companyId: user.companyId, // 公司ID
              corpId: qyBinding.corpId, // 企业ID
            };

            console.log('企微解绑参数:', params);

            // 调用解绑接口
            const { code, msg } = await this.$request.post(customerSalesRelationApi.unbindQyUserToSystemUser.url, params);

            if (code === 0) {
              this.$message.success('企微解绑成功');
              // 刷新列表
              await this.getList();
            } else {
              this.$message.error(msg || '企微解绑失败');
            }

            confirm.destroy();
          } catch (error) {
            console.error('企微解绑失败:', error);
            this.$message.error('企微解绑失败，请重试');
            confirm.destroy();
          }
        },
        onCancel: () => {
          confirm.destroy();
        }
      });
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.user-management-page {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 12px;


  // 页面头部
  .page-header {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 8px;

        .t-icon {
          font-size: 28px;
          color: #3b82f6;
        }
      }

      .page-desc {
        color: #6b7280;
        font-size: 14px;
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 20px;
    transition: all 0.3s ease;

    // 左侧边栏
    .sidebar {
      width: 250px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;

      &.collapsed {
        width: 60px;
        min-width: 60px;

        .sidebar-header {
          padding: 20px 8px;
          justify-content: center;

          .sidebar-actions {
            gap: 0;
          }
        }
      }

      .sidebar-header {
        padding: 16px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;

        .sidebar-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.3s ease;

          .t-icon {
            font-size: 18px;
          }
        }

        .sidebar-actions {
          display: flex;
          gap: 4px;

          .t-button {
            color: #fff;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            transition: all 0.2s ease;
            min-width: 32px;
            height: 32px;
            padding: 0;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              transform: scale(1.05);
            }

            .t-icon {
              font-size: 16px;
            }
          }
        }
      }

      .sidebar-content {
        flex: 1;
        overflow-y: auto;
        padding: 0;
        min-height: 0;

        .organization-tree {
          height: 100%;

          /deep/ .t-tree {
            height: 100%;
          }

          /deep/ .t-tree__item {
            position: relative;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;

            &:hover {
              background: #f8f9fa;
            }

            &.t-is-active {
              background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
              color: #1565c0 !important;
              border: 1px solid #90caf9 !important;
              box-shadow: 0 2px 8px rgba(25, 101, 192, 0.15) !important;
              padding-left: 20px; // 因为有左边框，所以增加左内边距
              border-left: 4px solid #1565c0 !important;

              .t-tree__label {
                color: #1e40af;
                font-weight: 500;
              }
            }

            // 层级缩进优化
            &[data-level="1"] {
              padding-left: 16px;

              .t-tree__label {
                font-weight: 600;
                color: #1f2937;
              }
            }

            &[data-level="2"] {
              padding-left: 32px;

              .t-tree__label {
                font-weight: 500;
                color: #374151;
              }

              &::before {
                content: '';
                position: absolute;
                left: 20px;
                top: 0;
                bottom: 0;
                width: 1px;
                background: #e5e7eb;
              }
            }

            &[data-level="3"] {
              padding-left: 48px;

              .t-tree__label {
                color: #6b7280;
              }

              &::before {
                content: '';
                position: absolute;
                left: 36px;
                top: 0;
                bottom: 0;
                width: 1px;
                background: #e5e7eb;
              }
            }

            .t-tree__label {
              font-size: 14px;
              line-height: 1.5;
              padding: 8px 4px;
              display: block;
              word-break: break-all;
            }

            .t-tree__icon {
              color: #6b7280;
              margin-right: 6px;
              transition: all 0.2s ease;

              &.t-tree__icon--expanded {
                transform: rotate(90deg);
                color: #3b82f6;
              }
            }

            // 连接线样式
            .t-tree__line {
              &::before,
              &::after {
                border-color: #e5e7eb;
              }
            }
          }

          /deep/ .t-tree__loading {
            padding: 40px 20px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
          }

          /deep/ .t-tree__empty {
            padding: 40px 20px;
            text-align: center;
            color: #9ca3af;
            font-size: 14px;
          }
        }
      }
    }

    // 右侧内容区域
    .content-area {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-width: 0;
      min-height: 0;
      overflow: hidden;

      // 筛选区域
      .filter-section {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        .filter-content {
          padding: 20px 24px;

          .filter-form {
            .filter-row {
              display: flex;
              gap: 16px;
              flex-wrap: wrap;
              align-items: flex-end;

              .filter-item {
                flex: 1;
                min-width: 200px;
                max-width: 250px;

                .t-form__label {
                  font-weight: 500;
                  color: #374151;
                }

                .id-search-group {
                  display: flex;


                  .id-type-select {
                    width: 120px;

                    border-top-right-radius: 0;
                    border-bottom-right-radius: 0;
                  }

                  .id-value-input {
                    flex: 1;

                    border-top-left-radius: 0;
                    border-bottom-left-radius: 0;
                    border-left: none;
                  }
                }

                .filter-input,
                .filter-select,
                .filter-date {
                  width: 100%;

                  border-radius: 8px;

                  transition: all 0.2s ease;

                  &:hover {
                    border-color: #9ca3af;
                  }

                  &:focus,
                  &.t-is-focused {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                  }
                }
              }
            }

            .filter-buttons {
              display: flex;
              gap: 12px;
              align-items: center;
              margin-left: auto;
              flex-shrink: 0;


              .t-button {
                padding: 0 16px;
                border-radius: 8px;
                font-weight: 500;
                transition: all 0.3s ease;

                .t-icon {
                  margin-right: 4px;
                }

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }
              }

              .search-btn {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border: none;
                color: #fff;

                &:hover {
                  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
                }
              }

              .reset-btn {

                background: #fff;
                color: #374151;

                &:hover {
                  background: #f9fafb;
                  border-color: #9ca3af;
                }
              }
            }

              .t-button {
                border-radius: 8px;
                font-weight: 500;
                padding: 8px 16px;
                transition: all 0.3s ease;

                .t-icon {
                  margin-right: 4px;
                }

                &:hover {
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }
              }

              .search-btn {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border: none;

                &:hover {
                  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
                }
              }
            }
          }


        }
      }

      // 工具栏
      .toolbar {
        background: #fff;
        border-radius: 12px;
        padding: 16px 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .toolbar-left {
          .selection-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #3b82f6;
            font-weight: 500;

            .t-icon {
              color: #10b981;
            }

            .selected-count {
              font-size: 14px;
            }

            .t-button {
              color: #6b7280;
              font-size: 12px;

              &:hover {
                color: #ef4444;
              }
            }
          }

          .table-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #374151;
            font-weight: 500;

            .t-icon {
              color: #6b7280;
            }

            .total-count {
              color: #6b7280;
              font-size: 12px;
            }
          }
        }

        .toolbar-right {
          display: flex;
          gap: 12px;

          .t-button {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;

            .t-icon {
              margin-right: 4px;
            }

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
              transform: none;
              box-shadow: none;
            }
          }

          .batch-btn {
            .t-icon:last-child {
              margin-left: 4px;
              margin-right: 0;
            }
          }
        }
      }

      // 表格区域
      .table-section {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        padding: 16px;

        .user-table {
          // 简化样式，让TDesign表格自己处理布局和分页
        }

          /deep/ .t-table__header {
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;

            th {
              font-weight: 600;
              color: #374151;
              border-bottom: 1px solid #e5e7eb;
              padding: 16px 12px;
            }
          }

          /deep/ .t-table__body {
            tr {
              transition: all 0.2s ease;

              &:hover {
                background: #f8f9fa;
              }

              td {
                border-bottom: 1px solid #f3f4f6;
                color: #374151;
                padding: 12px;
                vertical-align: middle;
              }
            }
          }

          // 状态标签样式
          .status-tag,
          .audit-tag {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 6px;

            .t-icon {
              font-size: 12px;
            }
          }

          // 操作按钮样式
          .operation-buttons {
            display: flex;
            align-items: center;
            gap: 8px;

            .edit-btn {
              color: #3b82f6;
              font-weight: 500;

              &:hover {
                background: rgba(59, 130, 246, 0.1);
              }
            }

            .more-btn {
              color: #6b7280;
              font-weight: 500;

              &:hover {
                color: #374151;
                background: #f3f4f6;
              }

              .t-icon:last-child {
                margin-left: 4px;
              }
            }
          }

          .admin-tag {
            .t-tag {
              font-size: 12px;
              font-weight: 500;
            }
          }
        }
      }
    }




// 企微绑定状态样式优化
.wechat-binding-status {
  max-width: 100%;
  overflow: visible;

  .binding-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
    max-width: 100%;
  }

  .binding-tag-wrapper {
    display: flex;
    align-items: center;
    gap: 2px;

    .unbind-btn {
      padding: 2px;
      min-width: auto;
      height: auto;
      margin-left: 2px;
      opacity: 0.6;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }

  .popup-tag-wrapper {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .binding-tag {
    display: inline-flex;
    align-items: center;
    font-size: 11px;
    border-radius: 4px;
    padding: 2px 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    max-width: 100%;
    word-break: break-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .binding-icon {
      margin-right: 4px;
      font-size: 12px;
    }

    // 已绑定样式
    &.bound {
      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
      border: 1px solid #bbf7d0;
      color: #166534;

      .binding-icon {
        color: #10b981;
      }
    }

    // 未绑定样式
    &.unbound {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      border: 1px solid #fde68a;
      color: #92400e;

      .binding-icon {
        color: #f59e0b;
      }
    }
  }

  .more-tag {
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  .popup-tags {
    display: flex;
    flex-direction: column;
    gap: 6px;
    max-width: 250px;
    padding: 8px;

    .popup-tag {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 6px;
      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
      border: 1px solid #bbf7d0;
      color: #166534;

      .binding-icon {
        margin-right: 4px;
        font-size: 12px;
        color: #10b981;
      }
    }
  }
}

// 全局组件优化
/deep/ .t-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.t-button--theme-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    }
  }

  &.t-button--variant-outline {

    background: #fff;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }

  &.t-button--variant-text {
    padding: 4px 8px !important;
    border: none;
    background: transparent;

    &:hover {
      background: rgba(59, 130, 246, 0.1);
    }
  }
}

/deep/ .t-dropdown__menu {
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;

  .t-dropdown__item {
    padding: 8px 16px;
    font-size: 14px;
    transition: all 0.2s ease;

    &:hover {
      background: #f8f9fa;
      color: #3b82f6;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .user-management-page {
    .main-content {
      .sidebar {
        width: 240px;
      }

      .content-area {
        .filter-section {
          .filter-content {
            .filter-form {
              .filter-row {
                .filter-item {
                  min-width: 180px;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .user-management-page {
    padding: 16px;

    .page-header {
      padding: 20px;

      .header-content {
        .page-title {
          font-size: 20px;
        }
      }
    }

    .main-content {
      flex-direction: column;
      height: auto;

      .sidebar {
        width: 100%;
        height: auto;
        max-height: 300px;

        .sidebar-content {
          max-height: 200px;
        }
      }

      .content-area {
        .filter-section {
          .filter-content {
            .filter-form {
              .filter-row {
                flex-direction: column;
                gap: 12px;

                .filter-item {
                  min-width: auto;
                  width: 100%;

                  .id-search-group {
                    flex-direction: column;

                    .id-type-select,
                    .id-value-input {
                      width: 100%;
                      border-radius: 8px;

                    }
                  }
                }
              }

              .filter-buttons {
                justify-content: stretch;

                .t-button {
                  flex: 1;
                }
              }
            }
          }
        }

        .toolbar {
          padding: 12px 16px;
          flex-direction: column;
          gap: 12px;
          align-items: stretch;

          .toolbar-left,
          .toolbar-right {
            justify-content: center;
          }

          .toolbar-right {
            .t-button {
              flex: 1;
            }
          }
        }

        .table-section {
          .user-table {
            /deep/ .t-table__header th,
            /deep/ .t-table__body td {
              padding: 8px 6px;
              font-size: 12px;
            }

            .operation-buttons {
              flex-direction: column;
              gap: 4px;

              .t-button {
                font-size: 12px;
                padding: 4px 8px;
              }
            }
          }
        }
      }
    }
  }
}

// 滚动条优化
.sidebar-content,
.table-section {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    transition: all 0.2s ease;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 加载状态优化
/deep/ .t-loading {
  .t-loading__text {
    color: #6b7280;
    font-size: 14px;
  }

  .t-loading__indicator {
    color: #3b82f6;
  }
}
</style>
