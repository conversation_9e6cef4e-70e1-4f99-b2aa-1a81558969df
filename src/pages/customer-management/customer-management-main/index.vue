<template>
  <div>
    <div class="search-input">
      <t-form :data="formData" layout="inline">
        <t-form-item label="微信昵称" name="nickname">
          <t-input-adornment>
            <template #prepend>
              <t-select
                autoWidth
                :options="[
                  { label: '模糊查询', value: '1' },
                  { label: '精确查询', value: '2' }
                 ]"
                v-model="formData.nicknameQueryType"
              />
            </template>
            <t-input
              v-model="formData.nickname"
              type="search"
              placeholder="请输入昵称"
              :style="{ minWidth: '134px' }"
            />
          </t-input-adornment>
        </t-form-item>
        <t-form-item label="企微备注" name="wechatRemark">
          <t-input
            v-model="formData.wechatRemark"
            type="search"
            placeholder="请输入企微备注"
            :style="{ minWidth: '134px' }"
          />
        </t-form-item>
        <t-form-item label="客户创建时间">
          <t-date-range-picker v-model="createTime" :clearable="true" enable-time-picker allow-input :presets="presets"/>
        </t-form-item>
        <t-form-item label="客户手机号" name="mobile">
          <t-input
            v-model="formData.mobile"
            type="search"
            placeholder="请输入手机号"
            :style="{ minWidth: '134px' }"
          />
        </t-form-item>
        <t-form-item label="客户ID" name="mobile">
          <t-input
            v-model="formData.customerId"
            type="search"
            placeholder="请输入客户ID"
            :style="{ minWidth: '134px' }"
          />
        </t-form-item>
        <t-form-item label="UnionId" name="mobile">
          <t-input
            v-model="formData.unionId"
            type="search"
            placeholder="请输入UnionId"
            :style="{ minWidth: '134px' }"
          />
        </t-form-item>
        <t-form-item label="栏目" name="campPeriodId">
          <t-select
            v-model="formData.columnId"
            placeholder="请选择栏目"
            :options="columnOptions"
            clearable
            :filter="filterColumn"
            @change="onColumnChange"
          />
        </t-form-item>
        <t-form-item label="训练营" name="companyId">
          <t-select
            :disabled="!formData.columnId ||formData.columnId === ''"
            :options="companyData"
            v-model="formData.companyId"
            clearable
            placeholder="请选择训练营"
            :filter="filterCompany"
            @change="onCompanyChange"
          />
        </t-form-item>
        <template v-if="hasAllocationAuth">
          <t-form-item label="销售组" name="salesGroupId">
            <t-select
              :disabled="!formData.companyId || formData.companyId === ''"
              v-model="formData.salesGroupId"
              clearable
              placeholder="请选择销售组"
              :filter="filterSalesGroup"
              :options="salesGroupOptions"
              @change="getSalesList(formData.salesGroupId)"
            />
          </t-form-item>
          <t-form-item label="销售" name="salesId">
            <t-select
              :disabled="!formData.salesGroupId || formData.salesGroupId === ''"
              v-model="formData.salesId"
              clearable
              placeholder="请选择销售"
              :filter="filterSales"
              :options="salesOptions"
            />
          </t-form-item>
        </template>

        <t-form-item label="营期" name="campPeriodId">
          <t-select
            :disabled="!formData.companyId || formData.companyId === ''"
            v-model="formData.campPeriodId"
            clearable
            placeholder="请选择营期"
            :filter="filterCampPeriod"
            :options="campPeriodOptions"
          />
        </t-form-item>
        <t-form-item label="企微添加状态" name="weworkStatus">
          <t-select
            v-model="formData.weworkStatus"
            clearable
            placeholder="请选择企微添加状态"
            :options="weworkStatusOptions"
          />
        </t-form-item>
        <t-form-item label="客户活跃时间">
          <t-date-range-picker v-model="activeTime" :clearable="true" enable-time-picker allow-input :presets="presets"/>
        </t-form-item>

        <t-form-item label="账号状态" name="forbiddenStatus">
          <t-select
            v-model="formData.forbiddenStatus"
            clearable
            placeholder="请选择账号状态"
            :options="statusOptions"
          />
        </t-form-item>

        <t-form-item label="红包状态" name="redPacketStatus">
          <t-select
            v-model="formData.redPacketStatus"
            clearable
            placeholder="请选择红包状态"
            :options="statusOptions"
          />
        </t-form-item>


      </t-form>
      <div class="search-btn">
        <t-button theme="primary" @click="onSubmit">查询</t-button>
        <t-button theme="primary" @click="onReset" ghost>重置</t-button>
      </div>
    </div>

    <t-row class="left-operation-container">
      <t-tooltip content="按训练营筛选查询以后才能批量分配" v-if="hasAllocationAuth">
        <t-button :disabled="batchAssignDisabled" class="primary-button" @click="onBatchAssign">批量分配</t-button>
      </t-tooltip>

      <t-tooltip content="按营期筛选查询以后才能批量设置标签">
        <t-button :disabled="batchSetTagDisabled" class="primary-button" @click="onBatchSetTag">批量设置标签</t-button>
      </t-tooltip>

      <t-tooltip content="按企微添加状态筛选查询后才能批量设置企微标签">
        <t-button :disabled="batchSetWecomTagDisabled" class="primary-button" @click="onBatchSetWecomTag">批量设置企微标签</t-button>
      </t-tooltip>

      <t-tooltip content="需要选择企业和营期筛选查询后才能批量设置备注（选择自己的客户才能修改成功）">
        <t-button :disabled="batchSetRemarkDisabled" class="primary-button" @click="onBatchSetRemark">批量设置备注</t-button>
      </t-tooltip>

      <t-tooltip content="同步更新客户备注信息">
        <t-button :disabled="updateRemarkDisabled" :loading="updateRemarkLoading" class="primary-button" @click="onUpdateRemark">同步备注</t-button>
      </t-tooltip>

      <t-dropdown :options="batchOptions" @click="selectBatchOptions">
        <t-button theme="default" variant="outline">批量操作</t-button>
      </t-dropdown>
    </t-row>
    <select-user-dialog :visible.sync="allocationVisible" :data="{ ...selectedCompanyInfo }"
                        @cancel="closeAllocationVisible" @confirm="confirmAllocation"/>

    <set-tag-dialog :visible.sync="tagVisible" @cancel="closeTagVisible" @confirm="confirmTag"/>

    <batch-wecom-tag-dialog
      :visible.sync="wecomTagVisible"
      :customer-ids="selectedRowKeys"
      @cancel="closeWecomTagVisible"
      @confirm="confirmWecomTag"
    />

    <batch-remark-dialog
      ref="batchRemarkDialog"
      :visible.sync="remarkVisible"
      :customer-ids="selectedRowKeys"
      :selected-customers="selectedCustomers"
      :qy-relations="getQyRelations()"
      @cancel="closeRemarkVisible"
      @confirm="confirmRemark"
    />

    <t-card>
      <t-table
        :columns="columns"
        :data="data"
        :rowKey="rowKey"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        :loading="dataLoading"
        :disableDataPage=true
        @change="rehandleChange"
        @select-change="rehandleSelectChange"
      >
        <template #nickname="{ row }">
          <div @click="showDrawer(row)">
            <t-comment :avatar="row.avatarUrl" :author="row.nickname">
              <template #content>
                <t-icon name="logo-wechat-stroke" color="#07C160">微信</t-icon>
              </template>
            </t-comment>
          </div>
        </template>
        <template #tags="{ row }">
          <t-popup :overlayInnerStyle="{ width: '98%', textAlign: 'left', margin: '0 auto', padding: '15px' }"
                   :overlayStyle="{ width: '100%' }">
            <t-space direction="vertical">
              <t-space break-line>
                <t-tag theme="primary" size="small" v-for="(tag, index) in getSomeTags(row.tags, 5, 'tagsName')"
                       :key="index" max-width="50" :title="tag">
                  {{ tag }}
                </t-tag>
              </t-space>
              <t-space break-line>
                <t-tag theme="success" size="small" v-for="(tag, index) in getSomeTags(row.tags, 5, 'manualTagsName')"
                       :key="index" max-width="50" :title="tag">
                  {{ tag }}
                </t-tag>
              </t-space>
            </t-space>
            <div slot="content" class="operator-content">
              <t-space direction="vertical">
                <t-space direction="vertical" v-for="item in row.tags" :key="item.id">
                  <t-tooltip content="自动标签">
                    <t-space break-line>
                      <template v-for="(tag, index) in getTags(item, 'tagsName')">
                        <t-tag :theme="index === 0? 'primary' : 'default'" :key="index" :title="tag">
                          {{ tag }}
                        </t-tag>
                      </template>
                    </t-space>
                  </t-tooltip>
                </t-space>
                <t-tooltip content="手动标签">
                  <t-space break-line>
                    <t-tag theme="success" size="small"
                           v-for="(tag, index) in getSomeTags(row.tags, undefined, 'manualTagsName')" :key="index"
                           max-width="50" :title="tag">
                      {{ tag }}
                    </t-tag>
                  </t-space>
                </t-tooltip>
              </t-space>
            </div>
          </t-popup>
        </template>
        <template #op="{ row }">
          <t-popconfirm :content="'确认' + ((+row.forbiddenStatus === 1 || +row.forbiddenStatus === 2) ? '启用账号' : '禁用账号') + '吗' " @confirm="handleDisable(row)">
            <t-button variant="text" theme="danger">
              {{ (+row.forbiddenStatus === 1 || +row.forbiddenStatus === 2) ? '启用账号' : '禁用账号' }}
            </t-button>
          </t-popconfirm>
          <t-popconfirm :content="'确认' + ((+row.redPacketStatus === 1 || +row.redPacketStatus === 2) ? '启用红包' : '禁用红包') + '吗' " @confirm="handleDisableRedPacket(row)">
            <t-button variant="text" theme="danger">
              {{ (+row.redPacketStatus === 1 || +row.redPacketStatus === 2) ? '启用红包' : '禁用红包'}}
            </t-button>
          </t-popconfirm>
          <t-dropdown
            v-if="isAdmin"
            trigger="click"
            :options="moreButtonsComputed"
            @click="
                    (e) => {
                      moreButtonsClick(e, row);
                    }
                  "
            :minColumnWidth="88"
          >
            <t-button variant="text">
                    <span class="tdesign-demo-dropdown__text">
                      更多
                      <chevron-down-icon size="16"/>
                    </span>
            </t-button>
          </t-dropdown>

        </template>
      </t-table>
    </t-card>

    <customer-drawer :hasAllocationAuth="hasAllocationAuth" :visible="drawerVar.visible" :data="drawerVar.data" @cancel="drawerCancel"></customer-drawer>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import {ACTIVE_TYPE_OPTIONS} from '@/constants';
import customerApi from "@/constants/api/hxsy-admin/customer.api";
import dayjs from 'dayjs';
import sysCompanyGroupApi from '@/constants/api/back/sys-company.api';
import sysCampGroupApi from '@/constants/api/back/sys-camp-group.api';
import CustomerDrawer from "@/pages/customer-management/components/customer-drawer.vue";
import SelectUserDialog from "@/pages/customer-management/components/select-user-dialog.vue";
import customerSalesRelationApi from '@/constants/api/hxsy-admin/customer-sales-relation.api';
import SetTagDialog from "@/pages/customer-management/components/set-tag-dialog.vue";
import BatchWecomTagDialog from "@/pages/customer-management/components/batch-wecom-tag-dialog.vue";
import BatchRemarkDialog from "@/pages/customer-management/components/batch-remark-dialog.vue";
import customerTagsApi from "@/constants/api/hxsy-admin/customer-tags.api";
import {SYSTEM_ROLE_CONST} from "@/constants/enum/system/system-role.enum";
import salesGroupApi from "@/constants/api/back/sales-group.api";
import apiUser from "@/constants/api/back/system-user.api";

export default Vue.extend({
  name: 'CustomerManagementMain',
  components: {
    SetTagDialog,
    SelectUserDialog,
    CustomerDrawer,
    BatchWecomTagDialog,
    BatchRemarkDialog
  },
  data() {
    return {
      ACTIVE_TYPE_OPTIONS,
      allocationVisible: false, // 选择分配销售人员弹窗
      tagVisible: false,
      wecomTagVisible: false, // 批量设置企微标签弹窗
      remarkVisible: false, // 批量设置备注弹窗
      batchAssignDisabled: true, // 批量分配按钮是否禁用
      batchSetTagDisabled: true, // 批量设置标签按钮是否禁用
      batchSetWecomTagDisabled: true, // 批量设置企微标签按钮是否禁用
      batchSetRemarkDisabled: true, // 批量设置备注按钮是否禁用
      updateRemarkLoading: false, // 更新备注加载状态
      // 企微添加状态选项
      weworkStatusOptions: [
        { label: '未添加', value: 0 },
        { label: '已添加', value: 1 },
        { label: '已删除', value: 9 },
      ],
      selectedCompanyInfo: {}, // 选择的训练营信息
      selectedCampPeriodInfo: {}, // 选择的营期信息
      isAdmin: false, // 是否为超级管理员
      statusOptions: [
        {
          label: '待启用',
          value: 2
        },
        {
          label: '启用',
          value: 0
        },
        {
          label: '禁用',
          value: 1
        },
      ],
      batchOptions: [
        {
          content: '禁用账号',
          value: 1,
        },
        {
          content: '启用账号',
          value: 2,
        },
        {
          content: '禁用红包',
          value: 3,
        },
        {
          content: '启用红包',
          value: 4,
        },
      ],
      presets: {
        '最近7天': [
          dayjs().subtract(6, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '最近3天': [
          dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        '今天': [
          dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ]
      },
      // 侧边栏
      drawerVar: {
        visible: false,
        data: {},
      },
      // 活跃区间 数据
      activeTime: [],
      // 客户创建时间
      createTime: [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
      ],
      moreButtons: [
        {
          content: '删除账户',
          value: 1,
        },
      ],

      // 搜索条件
      formData: {
        behaviorType: '', // 活跃行为类型
        activeStartTime: '', // 活跃开始时间
        activeEndTime: '', // 活跃结束时间
        nickname: '', // 微信昵称
        nicknameQueryType: '2', // 微信昵称查询类型（1-模糊查询，2-精确查询）
        createStartTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        createEndTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
        mobile: '', // 手机号
        columnId: '', // 栏目Id
        campPeriodId: '', // 营期Id
        companyId: '', // 公司（训练营）Id
        salesGroupId: '', // 销售组Id
        salesId: '', // 销售Id
        customerId: '', // 客户Id
        unionId: '', // 微信unionId
        weworkStatus: '', // 企微添加状态（0未添加/1已添加/9已删除）
        wechatRemark: '', // 企微备注
        forbiddenStatus: '', // 账号禁用状态
        redPacketStatus: '', // 红包禁用状态
      },
      // 批量操作选项
      batchOperationOption: [
        {
          content: '暂未开放',
          value: '1',
        },
      ],

      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
          disabled: (data: any) => {
            const {row} = data;
            return !!row.superAdmin;
          },
        },
        {
          title: '客户ID',
          align: 'left',
          width: 190,
          colKey: 'customerId',
        },
        {
          title: '客户',
          align: 'left',
          width: 240,
          colKey: 'nickname',
        },
        {
          title: '手机号',
          align: 'left',
          width: 120,
          colKey: 'mobile',
        },
        {
          title: '微信备注',
          align: 'left',
          width: 140,
          colKey: 'wechatRemark',
        },
        {
          title: '注册时间',
          align: 'left',
          width: 150,
          colKey: 'customerCreatedAt',
        },
        {
          title: '最近活跃时间',
          align: 'left',
          width: 150,
          colKey: 'lastActiveTime',
        },
        // {
        //   title: '性别',
        //   align: 'left',
        //   width: 80,
        //   colKey: 'gender',
        //   cell: 'gender',
        // },
        {
          title: '标签',
          align: 'left',
          width: 400,
          colKey: 'tags',
          cell: 'tags',
        },
        {
          title: 'unionId',
          align: 'left',
          width: 180,
          colKey: 'unionId',
        },
        {
          title: '操作',
          align: 'center',
          width: 160,
          colKey: 'op',
          cell: 'op',
          fixed: 'right',
        },
      ],
      rowKey: 'customerId',
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',
      pagination: {
        total: 0,
        current: 1,
        pageSize: 20,
        pageSizeOptions: ['20', '40', '60', '80', '100'],
      },
      companyData: [], // 公司
      campPeriodOptions: [], // 营期
      columnOptions: [], // 栏目
      salesGroupOptions: [], // 销售组
      salesOptions: [], // 销售
      // 是否有分配和筛选销售组的权限
      hasAllocationAuth: false,
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
    moreButtonsComputed() {
      return this.moreButtons;
    },
    // 更新备注按钮是否禁用
    updateRemarkDisabled() {
      // 检查当前用户是否有企微账号信息
      const qyRelations = this.getQyRelations();
      return !qyRelations || qyRelations.length === 0;
    },
    // 选中的客户数据
    selectedCustomers() {
      return this.data.filter(customer => this.selectedRowKeys.includes(customer.customerId));
    },
  },
  created() {
    this.getColumnList();
    const user = this.getUser();
    const roleType = user ? String(user.roleType) : '';
    console.log('roleType', roleType)

    // 设置是否为超级管理员
    this.isAdmin = roleType === SYSTEM_ROLE_CONST.ADMIN.value;

    if (roleType === SYSTEM_ROLE_CONST.ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.COMMON_ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.COL_ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.COM_ADMIN.value
      || roleType === SYSTEM_ROLE_CONST.SALE_ADMIN.value) {
      this.hasAllocationAuth = true;
    } else {
      this.hasAllocationAuth = false;
    }
  },

  methods: {
    async handleDisable(row: any) {
      // 0 启用账号  1 禁用账号
      const params: any = {
        customerIds: [ row.customerId ],
        forbiddenStatus: (+row.forbiddenStatus === 1 || +row.forbiddenStatus === 2) ? 0 : 1,
      };
      const {data, code} = await this.$request.post(customerApi.updStatus.url, params);
      console.log('code', code);
      if (code !== 0) {
        await this.$message.error({content: '操作失败'});
        return;
      }
      await this.$message.success({content: '操作成功'});
      await this.getList();
    },
    async handleDisableRedPacket(row: any) {
      // 0 启用红包  1 禁用红包
      const params: any = {
        customerIds: [ row.customerId ],
        redPacketStatus: (+row.redPacketStatus === 1 || +row.redPacketStatus === 2) ? 0 : 1,
      };
      const { code } = await this.$request.post(customerApi.updStatus.url, params);
      if (code !== 0) {
        await this.$message.error({content: '操作失败'});
        return;
      }
      await this.$message.success({content: '操作成功'});
      await this.getList();
    },
    getUser() {
      const user = window.localStorage.getItem('core:user');
      return JSON.parse(user || '{}');
    },
    // 获取指定数量的标签名
    getSomeTags(tags: any, max: number | undefined, name: string) {
      if (!tags || !Array.isArray(tags)) {
        return [];
      }
      const someTags: string[] = [];
      let count = 0;

      if (max && max > 0) {
        for (let i = 0; i < tags.length && count < max; i++) {
          const tagsNameArr = this.getTags(tags[i], name);
          for (let j = 0; j < tagsNameArr.length && count < max; j++) {
            someTags.push(tagsNameArr[j]);
            count += 1;
            if (count === max) {
              someTags.push(" ... ");
            }
          }
        }
      } else {
        tags.forEach((item: any) => {
          const tagsNameArr = this.getTags(item, name);
          tagsNameArr.forEach((item: any) => {
            someTags.push(item);
          })
        })
      }
      return someTags;
    },
    moreButtonsClick(data: any, rowData: any) {
      if (data.value === 1) {
        // 删除账户
        const dialog = this.$dialog.confirm({
          header: '删除确认',
          body: '确认删除此账户吗？此操作不可恢复，将永久删除该用户所有数据。',
          confirmBtn: '确认删除',
          cancelBtn: '取消',
          onConfirm: async () => {
            await this.delCustomer(rowData);
            dialog.hide(); // 手动关闭对话框
            return true;
          },
        });
      }
    },

    /**
     * 删除客户账户
     * @param row 要删除的客户数据行
     * @description 永久删除客户账户及其所有相关数据，此操作不可恢复
     */
    async delCustomer(row: any) {
      console.log('删除客户：', row)
      if (!row?.customerId || !row?.unionId) {
        await this.$message.error('客户信息不完整，无法执行删除操作');
        return;
      }

      try {
        this.dataLoading = true;
        const params = {
          customerId: row.customerId,
          unionId: row.unionId,
        };

        const { code, msg } = await this.$request.post(customerApi.delCustomer.url, params);

        if (code === 0) {
          await this.$message.success({
            content: '账户删除成功',
            duration: 3000,
          });
          await this.getList();
        } else {
          await this.$message.error({
            content: msg || '删除失败，请稍后重试',
            duration: 3000,
          });
        }
      } catch (error) {
        console.error('删除账户时发生错误:', error);
        await this.$message.error({
          content: '系统错误，请联系管理员',
          duration: 3000,
        });
      } finally {
        this.dataLoading = false;
      }
    },
    // 获取并处理标签名
    getTags(item: any, name: string): string[] {
      if (!item || typeof item?.[name] !== 'string' || item?.[name] === '') {
        return [];
      }
      if (item?.[name].indexOf(',') === -1) {
        return [item?.[name]];
      }
      const itemTags = item?.[name].split(',');
      return itemTags.map((tag: string) => tag.trim());
    },
    // 显示详情
    showDrawer(slotProps: any) {
      this.drawerVar.visible = true;
      this.drawerVar.data = slotProps;
      console.log("slotProps", slotProps);
    },
    // 关闭详情
    drawerCancel() {
      this.drawerVar.visible = false;
    },

    rehandleChange(changeParams: any) {
      this.pagination.pageSize = changeParams.pagination.pageSize;
      this.pagination.current = changeParams.pagination.current;
      this.getList();
    },

    /**
     * 点击新增按钮
     */
    handleAdd() {
      // 暂未开放
      this.$message.warning('暂未开放');
    },

    /**
     * 搜索提交
     */
    onSubmit(result: any) {
      [this.formData.createStartTime, this.formData.createEndTime] = this.createTime;
      [this.formData.activeStartTime, this.formData.activeEndTime] = this.activeTime;
      if (!this.formData.createStartTime && !this.formData.activeStartTime) {
        this.$message.warning('请选择客户创建时间或活跃时间!');
        return;
      }
      console.log("this.formData", this.formData);
      this.pagination.current = 1;
      this.selectedRowKeys = [];
      this.getList();
    },
    onReset() {
      this.pagination.current = 1;
      this.createTime = [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
      ];
      this.formData = {
        behaviorType: '', // 活跃行为类型
        activeStartTime: '', // 活跃开始时间
        activeEndTime: '', // 活跃结束时间
        nickname: '', // 微信昵称
        nicknameQueryType: '2', // 微信昵称查询类型（1-模糊查询，2-精确查询）
        createStartTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        createEndTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
        mobile: '', // 手机号
        campPeriodId: '', // 营期Id
        columnId: '', // 栏目Id
        salesGroupId: '', // 销售组Id
        salesId: '', // 销售Id
        companyId: '', // 公司（训练营）Id
        customerId: '', // 客户Id
        unionId: '', // 微信unionId
        forbiddenStatus: '', // 账号禁用状态
        redPacketStatus: '', // 红包禁用状态
        weworkStatus: '', // 企微添加状态
        wechatRemark: '', // 企微备注
      };
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    /**
     * @Description: 查询客户列表
     * <AUTHOR>
     * @date 2025/4/21
     * @time 15:13
     */
    async getList() {
      const query = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize / 4,
      };
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.post(
          customerApi.queryPage.url,
          this.formData,
          {
            params: query,
          },
        );
        const {code, data, msg} = res;
        if (code === 0) {
          this.data = data.records || [];
          this.pagination.total = +data.total || 0;
          if (this.data.length === 0 || !this.formData.companyId || this.formData.companyId === '') {
            this.batchAssignDisabled = true;
          } else {
            this.batchAssignDisabled = false;
          }
          if (this.data.length === 0 || !this.formData.campPeriodId || this.formData.campPeriodId === '') {
            this.batchSetTagDisabled = true;
            this.batchSetRemarkDisabled = true;
          } else {
            this.batchSetTagDisabled = false;
            // 批量设置备注还需要选择企业
            this.batchSetRemarkDisabled = !this.formData.companyId || this.formData.companyId === '';
          }

          // 批量设置企微标签：需要按企微添加状态筛选且有数据
          if (this.data.length === 0 || this.formData.weworkStatus === '' || this.formData.weworkStatus === undefined) {
            this.batchSetWecomTagDisabled = true;
          } else {
            this.batchSetWecomTagDisabled = false;
          }

        } else {
          await this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.dataLoading = false;
      }
    },
    // 查询销售组列表
    async getSalesGroupList(companyId: any) {
      const params = {
        companyId
      };
      const {
        code,
        data
      } = await (this as any).$request.get(salesGroupApi.listByCompanyId.url, { params });
      if (code === 0 && data) {
        this.salesGroupOptions = data.map((item: any) => ({
          label: item.salesGroupName,
          value: item.id,
        }));
      }
    },
    filterSales(search: any, option: any) {
      return option.label.indexOf(search) !== -1;
    },
    filterCampPeriod(search: any, option: any) {
      return option.label.indexOf(search) !== -1;
    },
    filterSalesGroup(search: any, option: any) {
      return option.label.indexOf(search) !== -1;
    },
    filterCompany(search: any, option: any) {
      return option.label.indexOf(search) !== -1;
    },
    filterColumn(search: any, option: any) {
      return option.label.indexOf(search) !== -1;
    },
    // 查询销售列表
    async getSalesList(salesGroupId: any) {
      this.formData.salesId = '';
      this.salesOptions = [];
      const res = await (this as any).$request.post(
        apiUser.queryUserByPage.url,
        {
          current: 1,
          size: 100,
          companyId: this.formData.companyId || '',
          salesGroupId,
          columnId: this.formData.columnId,
          status: 1, // 启用状态
          auditStatus: 2, // 审核通过状态
        },
      );
      const { code, data, msg } = res;
      if (code === 0 && data) {
        this.salesOptions = data.records.map((item: any) => ({
          label: item.username,
          value: item.id,
        }));
      } else {
        await this.$message.error(msg || '请求失败');
      }
    },
    /**
     * @Description: 查询公司列表
     * <AUTHOR>
     * @date 2025/4/21
     * @time 15:13
     */
    async getColumnList() {
      const params = {
        id: 31000, // 总公司ID
        level: 2, // 公司层级
      };
      const {
        code,
        data
      } = await (this as any).$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, {params});
      if (code === 0 && data) {
        this.columnOptions = data.columns.map((item: any) => ({
          label: item.name,
          value: item.id,
          child: item.companies
        }));
        if (this.columnOptions.length > 0) {
          this.formData.columnId = this.columnOptions[0].value;
          this.companyData = this.columnOptions[0].child.map((item: any) => ({label: item.name, value: item.id}));
        }
      }
    },
    async onColumnChange(value: any, context: any) {
      console.log('onColumnChange:', value, context);
      this.companyData = [];
      this.campPeriodOptions = [];
      this.formData.companyId = '';
      this.formData.campPeriodId = '';
      this.companyData = context.selectedOptions[0].child.map((item: any) => ({label: item.name, value: item.id}));
    },
    async onCompanyChange(value: any, context: any) {
      console.log('onCompanyChange:', value, context, context.node?.getPath());
      this.formData.campPeriodId = '';
      this.formData.salesGroupId = '';
      this.formData.salesId = '';
      this.salesGroupOptions = [];
      this.salesOptions = [];
      const params = {
        companyId: value,
      }
      const {code, data} = await (this as any).$request.get(sysCampGroupApi.getCampPeriodsByCompanyId.url, {params});
      console.log("code", code, "data", data);
      if (code === 0 && data) {
        this.campPeriodOptions = data.map((item: any) => ({label: item.campperiodName, value: item.id}));
        console.log("campPeriodOptions", this.campPeriodOptions);
      }
      await this.getSalesGroupList(value);
    },
    /**
     * @Description: 批量分配
     * <AUTHOR>
     * @date 2025/5/25
     * @time 10:56
     */
    async onBatchAssign() {
      // 处理空选择场景
      if (this.selectedRowKeys.length === 0) {
        await this.$message.warning('请先选择客户数据');
        return;
      }
      try {
        console.log("this.data", this.data);
        console.log("this.selectedRowKeys", this.selectedRowKeys);
        // 先根据 selectedRowKeys 中 id 从data中过滤出待分配的数据
        const assignData = this.data.filter((item: any) => this.selectedRowKeys.includes(item.customerId));
        if (assignData.length === 0) {
          await this.$message.warning('请选择正确的客户数据');
          return;
        }
        this.selectedCompanyInfo = this.companyData;
        this.allocationVisible = true;
      } catch (error) {
        // 异常处理
        console.error('批量分配失败:', error);
        await this.$message.error('操作失败，请重试');
      }
    },
    closeAllocationVisible() {
      this.allocationVisible = false;
    },
    confirmAllocation(userData: any) {
      console.log("confirmAllocation", userData);
      // 这里可以添加分配用户的逻辑
      this.assignCustomer(this.selectedRowKeys, userData.columnId, userData.companyId, userData.salesGroupId, userData.userId, userData.userName);
    },
    // 给客户分配销售
    async assignCustomer(customerIds: string[], columnId: string, companyId: string, salesGroupId: string, salesId: string, salesName: string) {
      const res = await (this as any).$request.post(
        customerSalesRelationApi.assignCustomer.url,
        {
          oldColumnId:  this.formData.columnId,
          customerIds,
          columnId,
          companyId,
          salesGroupId,
          salesId,
          salesName,
        },
      );
      if (res.code === 0) {
        await this.$message.success('分配成功');
        this.selectedRowKeys = [];
      } else {
        await this.$message.error(res.msg || '分配失败');
      }
      this.closeAllocationVisible();
    },
    /**
     * @Description: 批量操作
     * <AUTHOR>
     * @date 2025/7/1
     * @time 12:52
     */
    async selectBatchOptions(e: any) {
      console.log("selectBatchOptions", e);
      const {value} = e;
      if (this.selectedRowKeys.length === 0) {
        await this.$message.warning('请先选择客户数据');
        return;
      }
      const params: any = {
        customerIds: this.selectedRowKeys,
        forbiddenStatus: '', // 账号禁用状态
        redPacketStatus: '', // 红包禁用状态
      };
      let confirmTitle = "";
      switch (value) {
      case 1:
        params.forbiddenStatus = 1;
        confirmTitle = '账号禁用';
        break;
      case 2:
        params.forbiddenStatus = 0;
        confirmTitle = '账号启用';
        break;
      case 3:
        params.redPacketStatus = 1;
        confirmTitle = '红包禁用';
        break;
      case 4:
        params.redPacketStatus = 0;
        confirmTitle = '红包启用';
        break;
      default:
        break;
      }
      // 弹窗确认
      const dialog = this.$dialog.confirm({
        header: '提示',
        body: `你确认要将选择的客户的${confirmTitle}吗？`,
        confirmBtn: '确认',
        cancelBtn: '取消',
        onConfirm: async () => {
          dialog.hide();
          const {data, code} = await this.$request.post(customerApi.updStatus.url, params);
          if (code !== 0) {
            await this.$message.error({content: '操作失败'});
          } else {
            await this.$message.success({content: '操作成功'});
            await this.getList();
          }
        },
      });
    },
    /**
     * @Description: 批量设置标签
     * <AUTHOR>
     * @date 2025/5/25
     * @time 17:31
     */
    async onBatchSetTag() {
      // 处理空选择场景
      if (this.selectedRowKeys.length === 0) {
        await this.$message.warning('请先选择客户数据');
        return;
      }
      try {
        console.log("this.data", this.data);
        console.log("this.selectedRowKeys", this.selectedRowKeys);
        // 先根据 selectedRowKeys 中 id 从data中过滤出待分配的数据
        const assignData = this.data.filter((item: any) => this.selectedRowKeys.includes(item.customerId));
        if (assignData.length === 0) {
          await this.$message.warning('请选择正确的客户数据');
          return;
        }
        this.tagVisible = true;
      } catch (error) {
        // 异常处理
        console.error('批量分配失败:', error);
        await this.$message.error('操作失败，请重试');
      }
    },
    closeTagVisible() {
      this.tagVisible = false;
    },
    async confirmTag(tagData: any) {
      console.log("confirmTag", tagData.tag);
      // 这里可以添加分配用户的逻辑
      const campPeriodOption: any = this.campPeriodOptions.find((item: any) =>
        item.value === tagData.campPeriodId
      );
      const res = await (this as any).$request.post(
        customerTagsApi.saveManualTag.url,
        {
          customerIds: this.selectedRowKeys,
          manualTagsName: tagData.tag,
          campPeriodId: this.formData.campPeriodId,
          campPeriodName: campPeriodOption?.label,
        },
      );
      if (res.code === 0) {
        await this.$message.success('设置标签成功');
        this.selectedRowKeys = [];
      } else {
        await this.$message.error(res.msg || '设置标签失败');
      }
      this.closeTagVisible();
    },

    /**
     * 批量设置企微标签
     */
    onBatchSetWecomTag() {
      // 检查是否按企微添加状态筛选
      if (this.formData.weworkStatus === '' || this.formData.weworkStatus === undefined) {
        this.$message.warning('请先按企微添加状态筛选查询后再进行批量设置企微标签');
        return;
      }

      // 处理空选择场景
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请先选择客户数据');
        return;
      }

      this.wecomTagVisible = true;
    },

    /**
     * 关闭批量设置企微标签弹窗
     */
    closeWecomTagVisible() {
      this.wecomTagVisible = false;
    },

    /**
     * 确认批量设置企微标签
     */
    async confirmWecomTag(tagData: any) {
      console.log("确认批量设置企微标签", tagData);
      this.closeWecomTagVisible();
      // 操作完成后刷新列表
      await this.getList();
      // 清空选择
      this.selectedRowKeys = [];
    },

    /**
     * 批量设置备注
     */
    onBatchSetRemark() {
      // 处理空选择场景
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请先选择客户数据');
        return;
      }

      // 检查企微用户绑定
      if (!this.checkQyUserBinding()) {
        return;
      }

      // 检查选中的客户是否都已添加企微
      this.checkWeworkStatus();
    },

    /**
     * 检查企微用户绑定
     */
    checkQyUserBinding() {
      const user = this.getUser();
      const qyRelations = user.systemUserQyRelationResponses || [];

      // TODO
      // if (!qyRelations || qyRelations.length === 0) {
      //   this.$message.warning('该账号没有绑定企微用户，无法设置备注');
      //   return false;
      // }

      return true;
    },

    /**
     * 获取企微关系数据
     */
    getQyRelations() {
      const user = this.getUser();
      return user.systemUserQyRelationResponses || [];
    },



    /**
     * 检查企微添加状态
     */
    async checkWeworkStatus() {
      try {
        // 获取选中客户的企微状态
        const selectedCustomers = this.data.filter(item => this.selectedRowKeys.includes(item.customerId));

        // TODO: 临时测试代码 - 生产环境需要移除
        // // 检查是否有客户未添加企微（weworkStatus为0或undefined）
        // const notAddedCustomers = selectedCustomers.filter(customer =>
        //   // 如果没有weworkStatus字段或者值为0，认为未添加企微
        //   !customer.weworkStatus || customer.weworkStatus === 0 || customer.weworkStatus === '0'
        // );
        //
        // if (notAddedCustomers.length > 0) {
        //   const customerNames = notAddedCustomers.map(customer => customer.nickname || customer.customerId).slice(0, 5).join('、');
        //   const moreText = notAddedCustomers.length > 5 ? `等${notAddedCustomers.length}个客户` : '';
        //   this.$message.warning(`${customerNames}${moreText}尚未添加企微，无法设置备注`);
        //   return;
        // }

        // 所有客户都已添加企微，可以设置备注
        this.remarkVisible = true;
      } catch (error) {
        console.error('检查企微状态失败:', error);
        this.$message.error('检查企微状态失败');
      }
    },

    /**
     * 关闭批量设置备注弹窗
     */
    closeRemarkVisible() {
      this.remarkVisible = false;
    },

    /**
     * 确认批量设置备注
     */
    async confirmRemark(remarkData: any) {
      try {
        console.log("确认批量设置备注", remarkData);
        console.log("客户备注关联数据", remarkData.customerRemarks);

        // 获取当前用户信息
        const user = this.getUser();

        // 构造API请求数据
        const apiData = {
          customerIds: remarkData.customerIds, // 保留原有字段以兼容
          remark: remarkData.remark, // 保留原有字段以兼容
          corpId: remarkData.corpId, // 使用对话框选择的企业ID
          qyUserId: remarkData.qyUserId,
          salesId: user.id, // 当前用户ID
          customerRemarks: remarkData.customerRemarks, // 客户ID和备注的关联数组
          processedRemarks: remarkData.processedRemarks, // 完整的处理信息（可选）
        };

        // 如果只选择了一个客户且有手机号数据，添加手机号
        if (remarkData.customerIds.length === 1 && remarkData.remarkMobiles && remarkData.remarkMobiles.length > 0) {
          apiData.remarkMobiles = remarkData.remarkMobiles;
        }

        // 调用批量设置备注API
        const res = await (this as any).$request.post(
          customerApi.batchRemark.url,
          apiData
        );

        const { code, msg, data } = res;
        if (code === 0) {
          // 处理响应结果
          if (data.failureCount === 0) {
            await this.$message.success(`批量设置备注成功，共处理 ${data.successCount} 个客户`);
          } else {
            // 部分成功的情况
            await this.$message.warning(`批量设置备注完成，成功 ${data.successCount} 个，失败 ${data.failureCount} 个`);

            // 显示失败详情
            if (data.failureDetails && data.failureDetails.length > 0) {
              const failureReasons = data.failureDetails.map(item =>
                `客户ID ${item.customerId}: ${item.failureReason}`
              ).join('\n');

              this.$dialog.confirm({
                header: '部分客户设置失败',
                body: `以下客户设置备注失败：\n${failureReasons}`,
                confirmBtn: null,
                cancelBtn: null,
              });
            }
          }
        } else {
          await this.$message.error(msg || '批量设置备注失败');
        }
      } catch (error) {
        console.error('批量设置备注失败:', error);
        await this.$message.error('批量设置备注失败，请重试');
      } finally {
        // 重置对话框提交状态
        this.$nextTick(() => {
          if (this.$refs.batchRemarkDialog) {
            this.$refs.batchRemarkDialog.submitting = false;
          }
        });

        this.closeRemarkVisible();
        // 操作完成后刷新列表
        await this.getList();
        // 清空选择
        this.selectedRowKeys = [];
      }
    },

    /**
     * 更新备注（同步备注）
     */
    async onUpdateRemark() {
      // 检查当前用户是否有企微账号信息
      const qyRelations = this.getQyRelations();
      if (!qyRelations || qyRelations.length === 0) {
        await this.$message.warning('您没有绑定企微账号，无法更新客户备注');
        return;
      }

      try {
        this.updateRemarkLoading = true;

        // 调用更新客户备注接口（无参GET请求）
        const { code, msg } = await this.$request.get(apiUser.updateCustomerRemark.url);

        if (code === 0) {
          await this.$message.success('更新客户备注成功');
          // 刷新列表
          await this.getList();
        } else {
          await this.$message.error(msg || '更新客户备注失败');
        }
      } catch (error) {
        console.error('更新客户备注失败:', error);
        await this.$message.error('更新客户备注失败，请重试');
      } finally {
        this.updateRemarkLoading = false;
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

/deep/ .t-table {
  height: 100%;
  overflow: hidden;

  /deep/ .t-table__content {
    height: 450px;
  }
}

/deep/ table {
  tr th {
    background: #fff;
  }
}

/deep/ .t-comment__avatar {
  margin-right: 10px;
}

///deep/ .t-table__pagination {
//  background: #f3f3f3;
//  padding: 10px 20px;
//  border-top: 1px solid #eee;
//}
/deep/ .pla {
  color: #0052d9;
  padding: 0 3px;
}

/deep/ .t-table--striped {
  background: #999999;
  color: red;
}


::v-deep .t-button--variant-text {
  padding: 0 !important;
}

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

.left-operation-container {
  padding: 10px 0 10px 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
  .selected-count {
    display: inline-block;
    color: @text-color-secondary;
  }
}

.search-input {
  margin: 15px 0;
  display: flex;
  align-items: center;

  .search-btn {
    display: flex;
    text-align: right;
  }
}

.operator-content {
  height: 320px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
