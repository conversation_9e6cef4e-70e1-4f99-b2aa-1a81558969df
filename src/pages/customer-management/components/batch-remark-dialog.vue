<template>
  <t-dialog
    :visible.sync="dialogVisible"
    class="batch-remark-dialog-wrapper"
    @close="onCancel"
    @cancel="onCancel"
    @confirm="onConfirm"
    width="600px"
    :confirm-btn="{ loading: submitting, content: submitting ? '设置中...' : '确认设置' }"
    :style="{ maxHeight: '80vh' }"
  >
    <template #header>
      <span>批量设置备注</span>
    </template>

    <div class="batch-remark-dialog">
      <div class="dialog-content" style="max-height: 70vh; overflow-y: auto; display: flex; flex-direction: column;">
        <div class="customer-info">
          <div class="info-row">
            <t-icon name="info-circle" />
            <span>已选择 <span class="highlight">{{ customerIds.length }}</span> 个客户，将以"客户昵称-备注内容"的形式设置备注，总长度超过20字会自动截断</span>
          </div>
          <div v-if="qyRelations.length > 1" class="info-row corp-selection">
            <t-icon name="info-circle" />
            <span>检测到您绑定了 <span class="highlight">{{ qyRelations.length }}</span> 个企微账号，请选择要设置备注的企业</span>
          </div>
        </div>

        <div style="flex: 1; overflow: hidden;">
          <t-form
            ref="form"
            :data="formData"
            :rules="rules"
            label-width="100px"
            @submit="onSubmit"
            style="margin-bottom: 0;"
          >
          <!-- 企微账号选择（多个企微绑定时显示） -->
          <t-form-item
            v-if="qyRelations.length > 1"
            label="选择企微账号"
            name="selectedCorpId"
          >
            <t-select
              v-model="formData.selectedCorpId"
              placeholder="请选择要设置备注的企业"
              :options="corpOptions"
            />
          </t-form-item>

          <!-- 备注基础选择 -->
          <t-form-item label="备注基础" name="remarkBase">
            <t-radio-group v-model="formData.remarkBase">
              <t-radio value="nickname">客户昵称</t-radio>
              <!-- <t-radio value="current">当前备注</t-radio> 暂不开放 -->
            </t-radio-group>
            <div class="form-tip">
              基于客户昵称进行备注设置
            </div>
          </t-form-item>

          <!-- 选中客户信息预览 -->
          <t-form-item label="客户预览" v-if="selectedCustomers.length > 0" style="margin-bottom: 16px;">
            <div class="customer-preview">
              <div class="preview-header">
                <span>{{ selectedCustomers.length }} 个客户</span>
                <span v-if="selectedCustomers.length <= 3" class="show-all">（显示全部）</span>
                <span v-else class="show-partial">（显示前3个）</span>
              </div>
              <div class="customer-list">
                <div
                  v-for="(customer, index) in displayCustomers"
                  :key="customer.customerId"
                  class="customer-item"
                >
                  <div class="customer-info">
                    <span class="customer-nickname">{{ customer.nickname || '未设置昵称' }}</span>
                    <span class="customer-id">{{ customer.customerId }}</span>
                  </div>
                  <div class="customer-preview-remark" v-if="formData.remark.trim()">
                    <span class="remark-label">预览:</span>
                    <span class="remark-value preview-final" :class="{ 'truncated': getPreviewRemark(customer).truncated }">
                      {{ getPreviewRemark(customer).finalRemark }}
                      <span v-if="getPreviewRemark(customer).truncated" class="truncated-tip">(截断)</span>
                    </span>
                  </div>
                </div>
                <div v-if="selectedCustomers.length > 3" class="more-customers">
                  还有 {{ selectedCustomers.length - 3 }} 个客户...
                </div>
              </div>
            </div>
          </t-form-item>

          <t-form-item label="备注内容" name="remark" style="margin-bottom: 16px;">
            <t-input
              v-model="formData.remark"
              placeholder="输入备注内容（格式：客户昵称-备注内容）"
              :maxlength="20"
              show-limit
            />
          </t-form-item>

          <!-- 只有选择一个客户时才显示手机号设置 -->
          <template v-if="customerIds.length === 1">
            <t-form-item label="备注手机号" name="remarkMobiles">
              <div class="mobile-input-container">
                <div
                  v-for="(mobile, index) in formData.remarkMobiles"
                  :key="index"
                  class="mobile-input-row"
                >
                  <t-input
                    v-model="formData.remarkMobiles[index]"
                    placeholder="请输入手机号"
                    :maxlength="11"
                    class="mobile-input"
                  />
                  <t-button
                    theme="default"
                    variant="outline"
                    size="small"
                    @click="removeMobile(index)"
                    :disabled="formData.remarkMobiles.length <= 1"
                  >
                    <t-icon name="remove" />
                  </t-button>
                </div>
                <t-button
                  theme="default"
                  variant="outline"
                  size="small"
                  @click="addMobile"
                  :disabled="formData.remarkMobiles.length >= 3"
                  class="add-mobile-btn"
                >
                  <add-icon slot="icon" />
                  添加手机号
                </t-button>
              </div>
            </t-form-item>
          </template>
          </t-form>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import { AddIcon } from 'tdesign-icons-vue';

export default Vue.extend({
  name: 'BatchRemarkDialog',
  components: {
    AddIcon,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerIds: {
      type: Array,
      default: () => [],
    },
    selectedCustomers: {
      type: Array,
      default: () => [],
    },
    qyRelations: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      submitting: false, // 提交状态
      formData: {
        remark: '',
        remarkMobiles: [''], // 备注手机号数组，默认一个空值
        selectedCorpId: '', // 选择的企业ID
        remarkBase: 'nickname', // 备注基础：nickname-客户昵称，current-当前备注
      },
      rules: {
        selectedCorpId: [
          {
            validator: (val: string) => {
              if (this.qyRelations.length > 1 && (!val || val.trim() === '')) {
                return { result: false, message: '请选择要设置备注的企业', type: 'error' };
              }
              return { result: true };
            }
          }
        ],
        remark: [
          { required: true, message: '请输入备注内容', type: 'error' },
          { min: 1, max: 20, message: '备注内容长度为1-20个字符', type: 'error' },
        ],
        remarkMobiles: [
          {
            validator: (val: string[]) => {
              if (this.customerIds.length === 1) {
                // 只有选择一个客户时才验证手机号
                const validMobiles = val.filter(mobile => mobile && mobile.trim());
                if (validMobiles.length === 0) {
                  return { result: true }; // 手机号可以为空
                }
                // 验证手机号格式
                const mobileRegex = /^1[3-9]\d{9}$/;
                const invalidMobile = validMobiles.find(mobile => !mobileRegex.test(mobile.trim()));
                if (invalidMobile) {
                  return { result: false, message: '请输入正确的手机号格式', type: 'error' };
                }
              }
              return { result: true };
            }
          }
        ],
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value: boolean) {
        if (!value) {
          this.$emit('cancel');
        }
      },
    },
    corpOptions() {
      return this.qyRelations.map(relation => ({
        label: `${relation.corpName} - ${relation.qyName}`,
        value: `${relation.corpId}|_|${relation.qyUserId}`,
      }));
    },
    // 显示的客户列表（最多显示3个）
    displayCustomers() {
      return this.selectedCustomers.slice(0, 3);
    },
    // 获取单个客户的预览备注效果
    getPreviewRemark() {
      return (customer) => {
        const inputRemark = this.formData.remark.trim();
        if (!inputRemark) {
          return { finalRemark: '', truncated: false };
        }

        const nickname = customer.nickname || '客户';
        let finalRemark = `${inputRemark}-${nickname}`;

        const truncated = finalRemark.length > 20;
        if (truncated) {
          finalRemark = finalRemark.substring(0, 20);
        }

        return { finalRemark, truncated };
      };
    },
  },
  watch: {
    visible(newVal: boolean) {
      if (newVal) {
        this.resetForm();
        this.initCorpSelection();
      }
    },
  },
  methods: {
    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        remark: '',
        remarkMobiles: [''], // 重置为一个空的手机号输入框
        selectedCorpId: '', // 重置企业选择
        remarkBase: 'nickname', // 重置为客户昵称
      };
      this.$nextTick(() => {
        (this.$refs.form as any)?.clearValidate();
      });
    },

    /**
     * 初始化企业选择
     */
    initCorpSelection() {
      // 如果只有一个企微绑定，自动选择
      if (this.qyRelations.length === 1) {
        this.formData.selectedCorpId = `${this.qyRelations[0].corpId  }|_|${  this.qyRelations[0].qyUserId}`;
      } else {
        this.formData.selectedCorpId = '';
      }
    },

    /**
     * 添加手机号输入框
     */
    addMobile() {
      if (this.formData.remarkMobiles.length < 3) {
        this.formData.remarkMobiles.push('');
      }
    },

    /**
     * 移除手机号输入框
     */
    removeMobile(index: number) {
      if (this.formData.remarkMobiles.length > 1) {
        this.formData.remarkMobiles.splice(index, 1);
      }
    },

    /**
     * 取消操作
     */
    onCancel() {
      this.$emit('cancel');
    },

    /**
     * 确认设置
     */
    async onConfirm() {
      if (this.submitting) {
        return; // 防止重复提交
      }

      try {
        const valid = await (this.$refs.form as any).validate();
        if (!valid) {
          return;
        }

        this.submitting = true;

        // 批量处理备注：将客户昵称和备注拼接，并确保长度不超过20字符
        const processedRemarks = this.processCustomerRemarks();

        // 提交前校验
        const validationResult = this.validateBeforeSubmit(processedRemarks);
        if (!validationResult.valid) {
          this.$message.error(validationResult.message);
          this.submitting = false;
          return;
        }

        // 构造提交数据 - 将备注和客户ID关联
        const customerRemarks = processedRemarks.map(item => ({
          customerId: item.customerId,
          remark: item.finalRemark
        }));

        const submitData = {
          customerIds: this.customerIds, // 保留原有字段以兼容
          remark: this.formData.remark.trim(), // 保留原有字段以兼容
          corpId: this.formData.selectedCorpId.split('|_|')[0], // 选择的企业ID
          qyUserId: this.formData.selectedCorpId.split('|_|')[1],
          customerRemarks // 客户ID和备注的关联数组
        };

        // 如果只选择了一个客户，添加手机号数据
        if (this.customerIds.length === 1) {
          const validMobiles = this.formData.remarkMobiles
            .filter(mobile => mobile && mobile.trim())
            .map(mobile => mobile.trim());
          if (validMobiles.length > 0) {
            submitData.remarkMobiles = validMobiles;
          }
        }

        // 调试信息
        console.log('批量设置备注 - 客户备注关联:', customerRemarks);
        console.log('批量设置备注 - 完整数据:', submitData);

        // 发送确认事件
        this.$emit('confirm', submitData);
      } catch (error) {
        console.error('批量设置备注失败:', error);
        this.$message.error('设置失败，请重试');
        this.submitting = false;
      }
    },

    /**
     * 提交前校验
     */
    validateBeforeSubmit(processedRemarks) {
      // 1. 检查是否有客户数据
      if (!this.selectedCustomers || this.selectedCustomers.length === 0) {
        return { valid: false, message: '没有选中的客户数据' };
      }

      // 2. 检查备注内容是否为空
      const inputRemark = this.formData.remark.trim();
      if (!inputRemark) {
        return { valid: false, message: '请输入备注内容' };
      }

      // 3. 检查企微账号选择
      if (this.qyRelations.length > 1 && (!this.formData.selectedCorpId || this.formData.selectedCorpId.trim() === '')) {
        return { valid: false, message: '请选择企微账号' };
      }

      // 4. 检查处理后的备注是否有效
      if (!processedRemarks || processedRemarks.length === 0) {
        return { valid: false, message: '备注处理失败，请重试' };
      }

      // 5. 检查是否所有客户都有有效的昵称
      const invalidCustomers = processedRemarks.filter(item => !item.originalNickname || item.originalNickname.trim() === '');
      if (invalidCustomers.length > 0) {
        return { valid: false, message: `有 ${invalidCustomers.length} 个客户没有昵称，无法设置备注` };
      }

      // 6. 检查最终备注长度
      const emptyRemarks = processedRemarks.filter(item => !item.finalRemark || item.finalRemark.trim() === '');
      if (emptyRemarks.length > 0) {
        return { valid: false, message: `有 ${emptyRemarks.length} 个客户的最终备注为空，请检查` };
      }

      // 7. 统计被截断的备注数量，给出提示
      const truncatedCount = processedRemarks.filter(item => item.truncated).length;
      if (truncatedCount > 0) {
        // 这里不阻止提交，只是给出警告信息
        console.warn(`有 ${truncatedCount} 个客户的备注被截断`);
      }

      return { valid: true, message: '校验通过' };
    },

    /**
     * 处理客户备注：拼接客户昵称和备注，确保长度不超过20字符
     */
    processCustomerRemarks() {
      const inputRemark = this.formData.remark.trim();
      const processedRemarks = [];

      for (const customer of this.selectedCustomers) {
        const nickname = customer.nickname || '客户';
        let finalRemark = '';

        // 基于客户昵称：客户昵称-备注
        if (inputRemark) {
          finalRemark = `${inputRemark}-${nickname}`;
        } else {
          finalRemark = nickname;
        }

        // 确保长度不超过20字符，超出则截断
        if (finalRemark.length > 20) {
          finalRemark = finalRemark.substring(0, 20);
        }

        processedRemarks.push({
          customerId: customer.customerId,
          originalNickname: nickname,
          originalRemark: customer.wechatRemark || '',
          inputRemark,
          finalRemark,
          truncated: `${inputRemark}-${nickname}`.length > 20
        });
      }

      return processedRemarks;
    },

    /**
     * 表单提交
     */
    onSubmit() {
      this.onConfirm();
    },
  },
});
</script>

<style lang="less" scoped>
.batch-remark-dialog-wrapper {
  .dialog-content {
    padding: 0 4px;
  }

  .customer-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 16px;
    flex-shrink: 0;

    .info-row {
      margin: 0 0 6px 0;
      display: flex;
      align-items: flex-start;
      gap: 6px;
      color: #495057;
      font-size: 13px;
      line-height: 1.4;

      .t-icon {
        color: #0052d9;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .highlight {
        color: #0052d9;
        font-weight: 600;
      }

      &.corp-selection {
        color: #f59e0b;

        .t-icon {
          color: #f59e0b;
        }

        .highlight {
          color: #f59e0b;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .mobile-input-container {
    .mobile-input-row {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 6px;

      .mobile-input {
        flex: 1;
      }

      &:last-of-type {
        margin-bottom: 8px;
      }
    }

    .add-mobile-btn {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

:deep(.t-form-item) {
  margin-bottom: 12px !important;
}

:deep(.t-form-item__label) {
  font-weight: 500;
  font-size: 13px;
}

:deep(.t-dialog__body) {
  padding: 16px 20px !important;
}

.form-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.customer-preview {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: #f9fafb;

  .preview-header {
    padding: 6px 10px;
    border-bottom: 1px solid #e5e7eb;
    background: #f3f4f6;
    font-size: 12px;
    font-weight: 500;
    color: #374151;

    .show-all, .show-partial {
      color: #6b7280;
      font-weight: normal;
      font-size: 11px;
    }
  }

  .customer-list {
    max-height: 120px;
    overflow-y: auto;

    .customer-item {
      padding: 8px 10px;
      border-bottom: 1px solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      .customer-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 2px;

        .customer-nickname {
          font-weight: 500;
          color: #111827;
          font-size: 13px;
        }

        .customer-id {
          font-size: 11px;
          color: #6b7280;
          background: #f3f4f6;
          padding: 1px 4px;
          border-radius: 3px;
        }
      }

      .customer-preview-remark {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        margin-top: 4px;
        padding-top: 4px;
        border-top: 1px solid #f3f4f6;

        .remark-label {
          color: #6b7280;
          min-width: 40px;
        }

        .remark-value {
          color: #374151;
          flex: 1;
          word-break: break-all;

          &.preview-final {
            color: #059669;
            font-weight: 500;

            &.truncated {
              color: #dc2626;
            }

            .truncated-tip {
              color: #dc2626;
              font-size: 10px;
              font-weight: normal;
              margin-left: 3px;
            }
          }
        }
      }
    }

    .more-customers {
      padding: 4px 10px;
      text-align: center;
      color: #6b7280;
      font-size: 11px;
      background: #f9fafb;
      border-top: 1px solid #f3f4f6;
    }
  }
}
</style>
