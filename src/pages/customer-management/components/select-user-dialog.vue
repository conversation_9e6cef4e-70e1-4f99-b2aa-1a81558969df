<template>
  <t-dialog :visible.sync="localVisible" class="allocation-dialog" @close="handleClose" @cancel="handleClose" @confirm="handleConfirm">
    <template #header>
      <t-space>
        <span v-if="data.columnName">
          栏目 <t-tag theme="primary" variant="light">{{ data.columnName }}</t-tag>
        </span>
        <span v-if="data.salesName">
          原跟进人<t-tag theme="primary" variant="light">{{ data.salesName }}</t-tag>
        </span>
        <span>
          分配跟进人
          <t-tag theme="success" variant="light" v-show="selectUserInfo.username">{{ selectUserInfo.username }}</t-tag>
        </span>
      </t-space>
    </template>
    <t-card>
      <t-row :gutter="16">
        <t-col :span="4">
          <t-tree
            :data="companyTreeData"
            height="450"
            activable
            hover
            transition
            expandAll
            @active="companyTreeChange"
          />
        </t-col>
        <t-col :span="8">
          <t-space direction="vertical">
              <t-input
                v-model="userName"
                type="search"
                placeholder="请输入用户名（回车搜索）"
                clearable
                @enter="getList"
              />
            <t-card class="allocation-list-card">
              <t-table
                :columns="columns"
                :data="userData"
                rowKey="id"
                :verticalAlign="verticalAlign"
                :hover="true"
                :pagination="pagination"
                :selected-row-keys="selectedRowKeys"
                :loading="dataLoading"
                @change="rehandleChange"
                @select-change="rehandleSelectChange"
                :headerAffixedTop="true"
                size="small"
                :scroll="{ type: 'virtual', rowHeight: 38, bufferSize: 10 }"
                height="320"
              >
                <template #systemUserQyRelationResponses="{ row }">
                  <div class="wechat-binding-status">
                    <!-- 已绑定企微的情况 -->
                    <template v-if="row.systemUserQyRelationResponses && row.systemUserQyRelationResponses.length > 0">
                      <div class="binding-select-container">
                        <!-- 单个绑定直接显示 -->
                        <template v-if="row.systemUserQyRelationResponses.length === 1">
                          <t-tag theme="success" variant="light" class="binding-tag bound single">
                            <t-icon name="check-circle" class="binding-icon" />
                            {{ row.systemUserQyRelationResponses[0].qyName }}{{ row.systemUserQyRelationResponses[0].corpName ? `(${row.systemUserQyRelationResponses[0].corpName})` : '' }}
                          </t-tag>
                        </template>

                        <!-- 多个绑定显示下拉选择 -->
                        <template v-else>
                          <div class="binding-select-wrapper">
                            <t-select
                              :value="selectedQyUsers[row.id] || ''"
                              @change="(value) => handleQyUserSelect(value, row)"
                              placeholder="选择企微账号"
                              size="small"
                              class="qy-user-select"
                              clearable
                            >
                              <t-option
                                v-for="(item, index) in row.systemUserQyRelationResponses"
                                :key="index"
                                :value="item.qyUserId"
                                :label="`${item.qyName}${item.corpName ? `(${item.corpName})` : ''}`"
                              >
                                <div class="select-option-content">
                                  <t-icon name="check-circle" class="option-icon" />
                                  <span class="option-text">{{ item.qyName }}</span>
                                  <span v-if="item.corpName" class="option-corp">({{ item.corpName }})</span>
                                </div>
                              </t-option>
                            </t-select>
                            <div class="binding-count-tip">
                              <t-icon name="info-circle" />
                              共{{ row.systemUserQyRelationResponses.length }}个企微账号
                            </div>
                          </div>
                        </template>
                      </div>
                    </template>

                    <!-- 暂未绑定企微的情况 -->
                    <template v-else>
                      <t-tag theme="warning" variant="light" class="binding-tag unbound">
                        <t-icon name="close-circle" class="binding-icon" />
                        暂未绑定企微
                      </t-tag>
                    </template>
                  </div>
                </template>
              </t-table>
            </t-card>
          </t-space>
        </t-col>
      </t-row>
    </t-card>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import CustomerDetailComponent from "@/pages/customer-management/components/customer-detail-component.vue";
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import {treeNodeConvertService} from "@/service/tree-node-convert.service";
import apiUser from "@/constants/api/back/system-user.api";
import {Code} from "@/constants/enum/general/code.enum";

export default Vue.extend({
  name: 'SelectUserDialog',
  components: {},
  props: {
    visible: {
      default: false,
    },
    data: {
      default: () => ({}),
    },
  },
  data() {
    return {
      userName: '',
      localVisible: this.visible, // 新增本地状态
      companyTreeData: [], // 公司组织树数据
      allSysCompanyInfos: [],
      selectedRowKeys: [],
      selectUserInfo: {
        username: '',
        columnId: '',
        companyId: '',
        salesGroupId: '',
        headquartersId: '',
      },

      userData: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',
      selectedQyUsers: {}, // 存储每个用户选中的企微账号 qyUserId
      active: {
        headquartersId: '',
        columnId: '',
        companyId: '',
        salesGroupId: '',
      },
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      columns: [
        {
          colKey: 'row-select',
          type: 'single',
          width: 64,
          fixed: 'left',
          disabled: (data: any) => data.row.accountId === 'admin',
        },
        {
          title: '用户名',
          align: 'left',
          width: 150,
          colKey: 'username',
        },
        {
          title: '绑定企微',
          align: 'left',
          width: 200,
          colKey: 'systemUserQyRelationResponses',
        },
        {
          title: '手机号',
          align: 'left',
          width: 160,
          colKey: 'phone',
        },
      ],
      nodeContrast: {
        id: 'id',
        parentId: '',
        children: 'columns',
        cascadeField: 'columns',
        subNodeContrast: {
          id: 'id',
          parentId: '',
          children: 'companies',  // 注意这里必须与实际结构一致
          cascadeField: 'companies',
          subNodeContrast: {
            id: 'id',
            parentId: '',
            children: 'salesGroups', // 最后一层没有 children
            cascadeField: 'salesGroups',
            subNodeContrast: {
              id: 'id',
              parentId: '',
              children: '', // 最后一层没有 children
              cascadeField: '',
            }
          }
        }
      },
      nzNodeContrast: {
        key: 'id',
        title: 'name',
        label: 'name',
        children: 'columns',
        headquartersId: 'id',
        cascadeField: 'columns',
        subNzNodeContrast: {
          key: 'id',
          title: 'name',
          label: 'name',
          children: 'companies',
          columnId: 'id',
          cascadeField: 'companies',
          subNzNodeContrast: {
            key: 'id',
            label: 'name',
            children: 'salesGroups',
            companyId: 'id',
            cascadeField: 'salesGroups',
            subNzNodeContrast: {
              key: 'id',
              title: 'name',
              label: 'name',
              salesGroupId: 'id',
            }
          }
        }
      },
    };
  },
  watch: {
    visible(newVal) {
      this.localVisible = newVal;
      if (newVal) {
        this.getCompanyAuth();
      }
    },
  },
  mounted() {
    this.getCompanyAuth();
  },
  methods: {
    handleClose() {
      this.localVisible = false;
      this.$emit('cancel');
    },
    handleConfirm() {
      if (!this.selectedRowKeys[0]) {
        this.$message.error('请选择跟进人');
        return;
      }

      // 获取选中用户的企微账号信息
      const selectedUserId = this.selectedRowKeys[0];
      let selectedQyUserId = this.selectedQyUsers[selectedUserId];

      // 如果没有通过下拉框选择，但用户有企微账号，则自动使用第一个（适用于单个绑定的情况）
      if (!selectedQyUserId && this.selectUserInfo.systemUserQyRelationResponses && this.selectUserInfo.systemUserQyRelationResponses.length > 0) {
        selectedQyUserId = this.selectUserInfo.systemUserQyRelationResponses[0].qyUserId;
        console.log('自动使用第一个企微账号ID:', selectedQyUserId);
      }

      console.log('最终选中的企微账号ID:', selectedQyUserId);
      this.localVisible = false;
      this.$emit('confirm', {
        columnId: this.selectUserInfo.columnId,
        companyId: this.selectUserInfo.companyId,
        userId: selectedUserId,
        userName: this.selectUserInfo.username,
        salesGroupId: this.selectUserInfo.salesGroupId,
        qyUserId: selectedQyUserId, // 传递选中的企微账号ID
      });
    },
    /**
     * 获取组织架构列表
     */
    async getCompanyAuth() {
      try {
        const params = {
          id: 31000, // 总公司ID
          level: 4, // 公司层级
        };
        const { code, data } = await (this as any).$request.get(sysCompanyGroupApi.queryHeadquartersCompanyList.url, { params });
        console.error(data);
        if (code === 0 && data) {
          const companyTreeData = [
            {
              id: data.id, // 31000
              name: data.name || '总部',
              columns: data.columns.map((column: any) => ({
                ...column,
                companies: column.companies.map((company: any) => ({
                  ...company,
                  salesGroups: [...company.salesGroups],
                })),
              })),              },
          ];
          this.allSysCompanyInfos = data.columns;
          const treeData = treeNodeConvertService.arrayConvertToNzTreeNode(
            companyTreeData,
            this.nodeContrast,
            this.nzNodeContrast,
          );
          console.log(treeData);
          this.companyTreeData = treeData;
        }
      } catch (e) {
        console.log(e);
        this.$message.error('请求失败');
      }
    },
    companyTreeChange(value: any, context: any) {
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      const { headquartersId, columnId, companyId, salesGroupId } = context.node.data;
      this.active.headquartersId = headquartersId;
      this.active.columnId = columnId;
      this.active.companyId = companyId;
      this.active.salesGroupId = salesGroupId;
      this.getList(); // 查询拉取列表
    },
    rehandleSelectChange(selectedRowKeys: string[], options: any) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectUserInfo = options.selectedRowData[0];
      console.log("this.selectUserInfo", this.selectUserInfo);
    },

    // 处理企微账号选择
    handleQyUserSelect(qyUserId: string, row: any) {
      console.log(`用户 ${row.id} 选择了企微账号: ${qyUserId}`);

      // 更新选中的企微账号
      this.$set(this.selectedQyUsers, row.id, qyUserId);

      // 如果当前选中的用户就是这个用户，更新选择信息
      if (this.selectedRowKeys.includes(row.id)) {
        // 找到对应的企微账号信息
        const selectedQyUser = row.systemUserQyRelationResponses.find(item => item.qyUserId === qyUserId);
        if (selectedQyUser) {
          console.log('选中的企微账号信息:', selectedQyUser);
          // 可以在这里触发其他逻辑，比如更新界面显示等
        }
      }
    },
    // 查询列表
    async getList() {
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.post(
          apiUser.queryUserByPage.url,
          {
            current: this.pagination.current,
            size: this.pagination.pageSize,
            companyId: this.active.companyId || '',
            salesGroupId: this.active.salesGroupId,
            headquartersId: this.active.headquartersId,
            columnId: this.active.columnId,
            status: 1, // 启用状态
            auditStatus: 2, // 审核通过状态
            username: this.userName ? this.userName.trim() : '',
          },
        );
        const { code, data, msg } = res;
        if (code === Code.OK.code) {
          this.userData = data?.records || [];
          console.log()
          this.pagination.total = Number(data?.total || 0);
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.dataLoading = false;
      }
    },
    rehandleChange(changeParams: any) {
      this.pagination.pageSize = changeParams.pagination.pageSize;
      this.pagination.current = changeParams.pagination.current;
      this.getList();
    },
  },
});
</script>
<style lang="less" scoped>
.allocation-dialog {
  /deep/ .t-dialog {
    min-width: 600px;
    width: 60%;
    .allocation-list-card {
      .t-card__body {
        padding: 4px;
      }
      .allocation-list {
        height: 342px;
      }
    }
  }
}

// 企微绑定状态样式
.wechat-binding-status {
  .binding-select-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .binding-tag {
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    border-radius: 4px;
    padding: 2px 6px;

    .binding-icon {
      margin-right: 4px;
      font-size: 12px;
    }

    // 已绑定样式
    &.bound {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;

      .binding-icon {
        color: #52c41a;
      }

      // 单个绑定的样式
      &.single {
        max-width: 180px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    // 未绑定样式
    &.unbound {
      background-color: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;

      .binding-icon {
        color: #fa8c16;
      }
    }
  }

  .binding-select-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .qy-user-select {
      min-width: 160px;
      max-width: 200px;
    }

    .binding-count-tip {
      display: flex;
      align-items: center;
      font-size: 11px;
      color: #666;
      gap: 2px;

      .t-icon {
        font-size: 11px;
      }
    }
  }

  // 下拉选项样式
  .select-option-content {
    display: flex;
    align-items: center;
    gap: 4px;

    .option-icon {
      color: #52c41a;
      font-size: 12px;
    }

    .option-text {
      font-weight: 500;
    }

    .option-corp {
      color: #666;
      font-size: 11px;
    }
  }
}

// 全局下拉选项样式
/deep/ .t-select__option {
  .select-option-content {
    display: flex;
    align-items: center;
    gap: 4px;

    .option-icon {
      color: #52c41a;
      font-size: 12px;
    }

    .option-text {
      font-weight: 500;
    }

    .option-corp {
      color: #666;
      font-size: 11px;
    }
  }
}
</style>
