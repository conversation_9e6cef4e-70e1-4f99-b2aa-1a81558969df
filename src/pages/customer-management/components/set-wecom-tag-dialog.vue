<template>
  <t-dialog
    :visible.sync="localVisible"
    class="wecom-tag-dialog"
    :header="operationType === 'add' ? '添加企微标签' : '删除企微标签'"
    @close="handleClose"
    @cancel="handleClose"
    width="700px"
  >
    <template #footer>
      <t-space>
        <t-button theme="default" @click="handleClose">取消</t-button>
        <t-button theme="primary" @click="handleConfirm">
          {{ operationType === 'add' ? '确认添加' : '确认删除' }}
        </t-button>
      </t-space>
    </template>
    <div class="corp-selector">
      <div>请选择企业微信：</div>
      <t-select style="flex: 1" v-model="corpId" placeholder="选择企业微信" :options="corpOptions" />
    </div>
    <div class="operation-type-selector">
      <t-radio-group v-model="operationType">
        <t-radio value="add">添加标签</t-radio>
        <t-radio value="remove">删除标签</t-radio>
      </t-radio-group>
    </div>
    <t-loading :loading="loading">
      <div class="tag-groups-container">
        <template v-if="tagGroups.length > 0">
          <div class="operation-hint">
            {{ operationType === 'add' ? '请选择要添加的标签：' : '请选择要删除的标签：' }}
          </div>

          <!-- 搜索和统计信息 -->
          <div class="search-section">
            <t-input
              v-model="searchKeyword"
              placeholder="搜索标签名称..."
              clearable
              class="search-input"
              @input="handleSearch"
            >
              <template #prefix-icon>
                <t-icon name="search" />
              </template>
            </t-input>
            <div class="tag-stats">
              <span>已选择 {{ selectedTagIds.length }} 个标签</span>
              <span>共 {{ totalTagCount }} 个标签</span>
            </div>
          </div>

          <!-- 标签组列表 -->
          <div class="tag-groups-list">
            <div v-for="group in filteredTagGroups" :key="group.groupId" class="tag-group-card">
              <div class="tag-group-header" @click="toggleGroupExpand(group.groupId)">
                <div class="group-title">
                  <t-icon :name="expandedGroups[group.groupId] ? 'chevron-down' : 'chevron-right'" />
                  <h4>{{ group.name }}</h4>
                  <span class="tag-count">({{ getGroupVisibleTagCount(group) }})</span>
                </div>
                <div class="group-actions">
                  <t-button
                    size="small"
                    variant="text"
                    @click.stop="selectAllGroupTags(group)"
                    v-if="expandedGroups[group.groupId] && getGroupVisibleTags(group).length > 0"
                  >
                    全选
                  </t-button>
                  <t-button
                    size="small"
                    variant="text"
                    @click.stop="unselectAllGroupTags(group)"
                    v-if="expandedGroups[group.groupId] && getGroupVisibleTags(group).length > 0"
                  >
                    取消全选
                  </t-button>
                </div>
              </div>

              <!-- 标签列表 - 只在展开时渲染 -->
              <div v-if="expandedGroups[group.groupId]" class="tag-list">
                <template v-if="getGroupVisibleTags(group).length > 0">
                  <!-- 分页显示标签 -->
                  <div class="tag-pagination-container">
                    <t-checkbox-group v-model="selectedTagIds" class="tag-checkbox-group">
                      <t-checkbox
                        v-for="tag in getPaginatedTags(group)"
                        :key="tag.tagId"
                        :value="tag.tagId"
                        class="tag-checkbox"
                      >
                        {{ tag.name }}
                      </t-checkbox>
                    </t-checkbox-group>

                    <!-- 分页控件 -->
                    <t-pagination
                      v-if="getGroupVisibleTagCount(group) > pageSize"
                      :total="getGroupVisibleTagCount(group)"
                      :page-size="pageSize"
                      :current="groupCurrentPage[group.groupId] || 1"
                      @change="(pageInfo) => handlePageChange(pageInfo, group.groupId)"
                      size="small"
                      :show-total="false"
                      :show-page-size-options="false"
                      class="tag-pagination"
                      :key="`pagination-${group.groupId}-${searchKeyword}-${groupCurrentPage[group.groupId] || 1}`"
                    />
                  </div>
                </template>
                <div v-else class="no-tags">
                  {{ searchKeyword ? '没有匹配的标签' : '暂无标签' }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <t-empty v-else description="暂无标签组数据" />
      </div>
    </t-loading>
  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import customerWecomTagsApi from "@/constants/api/hxsy-admin/customer-wecom-tags.api";

interface TagInfo {
  id: number;
  tagId: string;
  name: string;
  groupId: string;
  corpId: string;
  createdAt: string;
  updatedAt: string;
}

interface TagGroup {
  id: number;
  groupId: string;
  name: string;
  corpId: string;
  createdAt: string;
  updatedAt: string;
  tags: TagInfo[];
}

export default Vue.extend({
  name: 'SetWecomTagDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      localVisible: this.visible,
      loading: false,
      tagGroups: [] as TagGroup[],
      selectedTagIds: [] as string[],
      operationType: 'add', // 操作类型：add-添加标签，remove-删除标签
      corpId: '', // 企业微信ID
      searchKeyword: '', // 搜索关键词
      expandedGroups: {} as Record<string, boolean>, // 展开的标签组
      groupCurrentPage: {} as Record<string, number>, // 每个组的当前页码
      pageSize: 20, // 每页显示的标签数量
    };
  },
  computed: {
    corpOptions() {
      const user = JSON.parse(window.localStorage.getItem('core:user') || '{}');
      const systemUserQyRelationResponses = user.systemUserQyRelationResponses || [];
      return systemUserQyRelationResponses.length ? systemUserQyRelationResponses.map((item: any) => ({ label: `${ item.qyName }（${item.corpName}）`, value: `${item.corpId  }/${  item.qyUserId}`, qyId: item.qyUserId })) : [];
    },

    // 过滤后的标签组
    filteredTagGroups() {
      if (!this.searchKeyword.trim()) {
        return this.tagGroups;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return this.tagGroups.map(group => ({
        ...group,
        tags: group.tags.filter(tag =>
          tag.name.toLowerCase().includes(keyword)
        )
      })).filter(group => group.tags.length > 0);
    },

    // 总标签数量
    totalTagCount() {
      return this.tagGroups.reduce((total, group) => total + (group.tags?.length || 0), 0);
    },
  },
  watch: {
    visible(newVal) {
      this.localVisible = newVal;
      if (newVal) {
        this.fetchTagGroups();
      }
    },
    corpId(newVal, oldVal) {
      if (newVal) {
        const corpId = oldVal ? oldVal.split('/')[0] : '';
        const currentCorpId = newVal ? newVal.split('/')[0] : '';
        if (!corpId || !currentCorpId || corpId === currentCorpId) return;
        this.fetchTagGroups();
      }
    },
  },
  methods: {
    async fetchTagGroups() {
      this.loading = true;
      try {
        if (this.corpOptions.length === 0) {
          this.$message.warning('当前业务人员未关联任何企业微信');
          return;
        }
        if (!this.corpId) this.corpId = this.corpOptions[0].value;
        const res = await this.$request({
          url: customerWecomTagsApi.getLocalTags.url,
          method: customerWecomTagsApi.getLocalTags.method,
          params: {
            corpId: this.corpId.split('/')[0], // 这里应该使用实际的企业微信ID
          }
        });

        if (res.code === 0 && res.data) {
          this.tagGroups = res.data;
          // 初始化展开状态 - 默认展开第一个组，其他折叠
          this.expandedGroups = {};
          if (this.tagGroups.length > 0) {
            this.$set(this.expandedGroups, this.tagGroups[0].groupId, true);
          }
          // 重置分页状态
          this.groupCurrentPage = {};
        } else {
          this.$message.error(res.msg || '获取标签组数据失败');
        }
      } catch (error) {
        console.error('获取标签组数据出错:', error);
        this.$message.error('获取标签组数据出错');
      } finally {
        this.loading = false;
      }
    },
    handleClose() {
      this.selectedTagIds = [];
      this.operationType = 'add'; // 重置操作类型为添加
      this.searchKeyword = ''; // 重置搜索关键词
      this.expandedGroups = {}; // 重置展开状态
      this.groupCurrentPage = {}; // 重置分页状态
      this.localVisible = false;
      this.$emit('cancel');
    },
    async handleConfirm() {
      if (this.selectedTagIds.length === 0) {
        this.$message.warning('请至少选择一个标签');
        return;
      }

      console.log(this.data);
      try {
        // 企微对应企业成员userid
        const userId = this.corpOptions.find((item: any) => item.value === this.corpId)?.qyId;
        // 根据操作类型构建请求参数
        const requestData: any = {
          corpId: this.corpId.split('/')[0] || "", // 使用传入的企业成员userid
          // corpId: "wpAiSqIAAAlw5ozDW8NNg4gBU_smL1Zg", // 这里应该使用实际的企业微信ID
          userId: userId || "", // 使用传入的企业成员userid
          // userId: "woAiSqIAAAXTBsrDGKOtGcovYDbnpMgQ", // 使用传入的企业成员userid
          externalUserId: this.data.externalUserId || "", // 使用传入的外部联系人userid
          // externalUserId: "wmAiSqIAAAFmIIv38NcNT5yqaK3AEsng", // 使用传入的外部联系人userid
        };

        // 根据操作类型设置不同的参数
        if (this.operationType === 'add') {
          requestData.addTagIds = this.selectedTagIds;
        } else {
          requestData.removeTagIds = this.selectedTagIds;
        }

        const res = await this.$request({
          url: customerWecomTagsApi.markCustomerTag.url,
          method: customerWecomTagsApi.markCustomerTag.method,
          data: requestData
        });

        if (res.code === 0) {
          const successMessage = this.operationType === 'add' ? '添加企微标签成功' : '删除企微标签成功';
          this.$message.success(successMessage);
          this.localVisible = false;
          this.$emit('confirm', {
            tagIds: this.selectedTagIds,
            operationType: this.operationType
          });
          this.selectedTagIds = [];
        } else {
          const errorMessage = this.operationType === 'add' ? '添加企微标签失败' : '删除企微标签失败';
          this.$message.error(res.msg || errorMessage);
        }
      } catch (error) {
        console.error('企微标签操作出错:', error);
        const errorMessage = this.operationType === 'add' ? '添加企微标签出错' : '删除企微标签出错';
        this.$message.error(errorMessage);
      }
    },

    // 处理搜索
    handleSearch() {
      // 搜索时重置所有组的分页状态
      this.groupCurrentPage = {};
      // 如果有搜索关键词，展开所有有匹配结果的组
      if (this.searchKeyword.trim()) {
        const newExpandedGroups = {};
        this.filteredTagGroups.forEach(group => {
          newExpandedGroups[group.groupId] = true;
        });
        this.expandedGroups = newExpandedGroups;
      }
    },

    // 切换标签组展开/折叠状态
    toggleGroupExpand(groupId: string) {
      this.$set(this.expandedGroups, groupId, !this.expandedGroups[groupId]);
    },

    // 获取标签组的可见标签
    getGroupVisibleTags(group: TagGroup) {
      if (!this.searchKeyword.trim()) {
        return group.tags || [];
      }
      const keyword = this.searchKeyword.toLowerCase();
      return (group.tags || []).filter(tag =>
        tag.name.toLowerCase().includes(keyword)
      );
    },

    // 获取标签组的可见标签数量
    getGroupVisibleTagCount(group: TagGroup) {
      return this.getGroupVisibleTags(group).length;
    },

    // 获取分页后的标签
    getPaginatedTags(group: TagGroup) {
      const visibleTags = this.getGroupVisibleTags(group);
      let currentPage = this.groupCurrentPage[group.groupId] || 1;

      // 如果当前页超出了可见标签的范围，重置到第一页
      const maxPage = Math.ceil(visibleTags.length / this.pageSize) || 1;
      if (currentPage > maxPage) {
        console.log(`标签组 ${group.groupId} 页码超出范围: 当前页 ${currentPage}, 最大页 ${maxPage}, 重置到第1页`);
        currentPage = 1;
        this.$set(this.groupCurrentPage, group.groupId, 1);
      }

      const start = (currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      const result = visibleTags.slice(start, end);

      console.log(`标签组 ${group.groupId} 分页计算: 当前页 ${currentPage}, 可见标签总数 ${visibleTags.length}, 显示 ${start}-${end}, 结果数量 ${result.length}`);

      return result;
    },

    // 处理分页变化
    handlePageChange(pageInfo: any, groupId: string) {
      // TDesign 分页组件的 change 事件传递的是包含 current 和 pageSize 的对象
      let currentPage;
      if (typeof pageInfo === 'object' && pageInfo !== null) {
        currentPage = pageInfo.current || 1;
      } else {
        currentPage = pageInfo || 1;
      }

      console.log(`标签组 ${groupId} 页码变化: 切换到第 ${currentPage} 页`, pageInfo);
      this.$set(this.groupCurrentPage, groupId, currentPage);
    },

    // 全选标签组的所有可见标签
    selectAllGroupTags(group: TagGroup) {
      const visibleTags = this.getGroupVisibleTags(group);
      const tagIds = visibleTags.map(tag => tag.tagId);
      const newSelectedIds = [...new Set([...this.selectedTagIds, ...tagIds])];
      this.selectedTagIds = newSelectedIds;
    },

    // 取消选择标签组的所有可见标签
    unselectAllGroupTags(group: TagGroup) {
      const visibleTags = this.getGroupVisibleTags(group);
      const tagIds = visibleTags.map(tag => tag.tagId);
      this.selectedTagIds = this.selectedTagIds.filter(id => !tagIds.includes(id));
    },
  },
});
</script>

<style lang="less" scoped>
.wecom-tag-dialog {
  .tag-groups-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 8px;
  }

  .tag-groups-container::-webkit-scrollbar {
    width: 8px;
    background: #f1f1f1;
  }

  .tag-groups-container::-webkit-scrollbar-thumb {
    background: #999999;
    border-radius: 4px;
  }

  .operation-hint {
    margin-bottom: 16px;
  }

  .search-section {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-input {
      margin-bottom: 8px;
    }

    .tag-stats {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #666;

      span {
        padding: 2px 8px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
      }
    }
  }

  .tag-groups-list {
    .tag-group-card {
      border: 1px solid #eee;
      border-radius: 6px;
      margin-bottom: 12px;
      overflow: hidden;

      .tag-group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: #f0f0f0;
        }

        .group-title {
          display: flex;
          align-items: center;
          gap: 8px;

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
          }

          .tag-count {
            font-size: 12px;
            color: #666;
          }
        }

        .group-actions {
          display: flex;
          gap: 8px;
        }
      }

      .tag-list {
        padding: 16px;

        .tag-pagination-container {
          .tag-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;

            .tag-checkbox {
              margin: 0;
              padding: 4px 8px;
              border: 1px solid #e0e0e0;
              border-radius: 4px;
              background: #fff;
              transition: all 0.2s;

              &:hover {
                border-color: #0052d9;
                background: #f0f8ff;
              }
            }
          }

          .tag-pagination {
            display: flex;
            justify-content: center;
            margin-top: 12px;
          }
        }

        .no-tags {
          color: rgba(0, 0, 0, 0.4);
          font-style: italic;
          text-align: center;
          padding: 20px;
        }
      }
    }
  }
}

.corp-selector {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.operation-type-selector {
  margin-bottom: 16px;
}

// 优化复选框样式
/deep/ .t-checkbox {
  .t-checkbox__label {
    font-size: 13px;
    line-height: 1.4;
  }
}

// 优化分页组件样式
/deep/ .t-pagination {
  .t-pagination__btn,
  .t-pagination__number {
    min-width: 28px;
    height: 28px;
    font-size: 12px;
  }
}
</style>
