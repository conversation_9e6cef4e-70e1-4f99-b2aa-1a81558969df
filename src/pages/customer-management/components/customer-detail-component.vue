<template>
    <div class="background-container">
      <t-row :gutter="[16, 16]">
        <t-col :flex="3">
          <t-card class="user-info-list" header-bordered :bordered="false">
            <template #avatar>
              <t-avatar :image="data.avatarUrl" size="56px"></t-avatar>
            </template>
            <template #title>
              {{ data.nickname }} <t-icon name="logo-wechat-stroke" color="#07C160">微信</t-icon>
            </template>
            <template #actions>
              <t-space>
                <t-select label="栏目：" v-model="selectedColumnId" placeholder="请选择栏目" @change="columnChange">
                  <t-option v-for="item in columnInfos" :value="item.id" :label="item.columnName" :key="item.id"></t-option>
                </t-select>
                <t-button v-if="campPeriodOptions.length === 1" theme="primary" @click="showSetTagVisible">添加标签</t-button>
                <t-dropdown v-else-if="campPeriodOptions.length > 1" :options="campPeriodOptions" @click="campPeriodOptionsClick">
                  <t-button>按营期添加标签</t-button>
                </t-dropdown>
                <set-tag-dialog :visible.sync="tagVisible" @cancel="closeTagVisible" @confirm="confirmTag" />

                <t-button v-if="wecomTagData.length > 0" theme="primary" @click="showWecomTagVisible">添加企微标签</t-button>
                <set-wecom-tag-dialog :visible.sync="wecomTagVisible" :data="wecomTagDialogData" @cancel="closeWecomTagVisible" @confirm="confirmWecomTag" />

                <t-button v-if="hasAllocationAuth" theme="primary" @click="showAllocationVisible">分配</t-button>
                <select-user-dialog :visible.sync="allocationVisible" :data="{ ...selectedColumnInfo }" @cancel="closeAllocationVisible" @confirm="confirmAllocation" />
              </t-space>
            </template>
            <template #description>
              <t-popup :overlayInnerStyle="{ width: '98%', textAlign: 'left', margin: '0 auto', padding: '15px' }" :overlayStyle="{ width: '100%' }">
                <t-space direction="vertical">
                  <t-space break-line>
                    <t-tag theme="primary" size="small" v-for="(tag, index) in getSomeTags(data.tags, 5, 'tagsName')" :key="index" max-width="50" :title="tag">
                      {{ tag }}
                    </t-tag>
                  </t-space>
                  <t-space break-line>
                    <t-tag theme="success" size="small" v-for="(tag, index) in getSomeTags(data.tags, 5, 'manualTagsName')" :key="index" max-width="50" :title="tag">
                      {{ tag }}
                    </t-tag>
                  </t-space>
                </t-space>
                <div slot="content" class="operator-content">
                  <t-space direction="vertical">
                    <t-space direction="vertical">
                      <t-space direction="vertical" v-for="item in data.tags" :key="item.id">
                        <t-tooltip content="自动标签">
                          <t-space break-line>
                            <template v-for="(tag, index) in getTags(item, 'tagsName')">
                              <t-tag :theme="index === 0? 'primary' : 'default'" :key="index" :title="tag">
                                {{ tag }}
                              </t-tag>
                            </template>
                          </t-space>
                        </t-tooltip>
                      </t-space>
                      <t-tooltip content="手动标签">
                        <t-space break-line>
                          <t-tag theme="success" size="small" v-for="(tag, index) in getSomeTags(data.tags, undefined, 'manualTagsName')" :key="index" max-width="50" :title="tag">
                            {{ tag }}
                          </t-tag>
                        </t-space>
                      </t-tooltip>
                    </t-space>
                  </t-space>
                </div>
              </t-popup>
            </template>
            <template #content>
              <t-row class="content" justify="space-between">
                <t-col v-for="(item, index) in userInfo" :key="index" class="contract" :span="item.span || 2">
                  <div class="contract-title">
                    {{ item.title }}
                  </div>
                  <div class="contract-detail">
                    <!-- 已加微信特殊展示 -->
                    <template v-if="item.isWechat">
                      <t-popup
                        v-if="wecomTagData.length > 0"
                        :overlayInnerStyle="{ maxWidth: '400px', textAlign: 'left', padding: '16px' }"
                        placement="bottom"
                        trigger="hover"
                      >
                        <span class="wechat-info-trigger">{{ item.content }}</span>
                        <template #content>
                          <div class="wechat-detail-popup">
                            <div class="popup-title">已添加企微员工详情</div>
                            <div class="wechat-staff-list">
                              <div v-for="(staff, staffIndex) in wecomTagData" :key="staffIndex" class="staff-item">
                                <div class="staff-header">
                                  <t-avatar size="24px" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                    <t-icon name="user" size="12px" />
                                  </t-avatar>
                                  <div class="staff-basic">
                                    <div class="staff-name">{{ staff.qyUserInfo && staff.qyUserInfo.qyName }}</div>
                                    <div class="staff-company" v-if="staff.corpName">{{ staff.corpName }}</div>
                                  </div>
                                </div>
                                <div class="staff-details">
                                  <div class="detail-row" v-if="staff.createtime">
                                    <span class="detail-label">添加时间：</span>
                                    <span class="detail-value">{{ formatTime(staff.createtime) }}</span>
                                  </div>
                                  <div class="detail-row" v-if="staff.add_way">
                                    <span class="detail-label">添加方式：</span>
                                    <span class="detail-value">{{ getAddWayText(staff.add_way) }}</span>
                                  </div>
                                  <div class="detail-row" v-if="staff.remark">
                                    <span class="detail-label">客户备注：</span>
                                    <span class="detail-value">{{ staff.remark }}</span>
                                  </div>
                                  <!-- 如果有删除时间，显示删除信息 -->
                                  <div class="detail-row delete-info" v-if="staff.delete_time">
                                    <span class="detail-label">删除时间：</span>
                                    <span class="detail-value">{{ formatTime(staff.delete_time) }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                      <span v-else>{{ item.content }}</span>
                    </template>
                    <!-- 普通信息展示 -->
                    <template v-else>
                      {{ item.content }}
                    </template>
                  </div>
                </t-col>
              </t-row>
            </template>
          </t-card>

          <t-card class="content-container" :bordered="false">
              <t-tabs v-model="tabValue" @change="tabChange">
                <t-tab-panel value="1" label="行为轨迹" class="tab-panel-container">
                  <div class="tabs-sticky-wrapper" ref="behaviorContent">
                    <div class="tabs-select">
                      <t-space>
                        <t-select v-model="behaviorTypes"
                                  :options="behaviorTypeOptions()"
                                  placeholder="请选择行为轨迹类型"
                                  clearable
                                  multiple
                                  @change="queryCustomerBehavior"/>
                      </t-space>
                    </div>
                    <t-space direction="vertical" style="width: 100%;">
                      <template v-if="behaviorData.length !== 0">
                        <t-timeline mode="same">
                          <t-timeline-item :dotColor="getDotColor(item.behaviorType)" v-for="(item, index) in behaviorData" :key="index">
                            <template #content>
                              <t-card
                                hover-shadow
                                :class="['timerline-card', getCardClass(item.behaviorType)]"
                              >
                                <template #title>
                                  <t-space align="center">
                                    <t-icon :name="getBehaviorTypeIcon(item.behaviorType)"
                                            :style="{ color: getDotColor(item.behaviorType), fontSize: '18px' }" />
                                    <span>{{ getBehaviorTypeName(item.behaviorType) }}</span>
                                  </t-space>
                                </template>
                                <template #actions>
                                  <t-icon name="time" size="medium" />
                                  {{ item.createdAt }}
                                </template>
                                <template #content>
                                  <!-- 训练营营期报名 展示营期信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.CAMP_ENROLLMENT.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          所属训练营: {{ item.companyName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          营期名称: {{ item.campPeriodName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          跟进人: {{ item.employeeName }}
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>
                                  <!-- 训练营视频课学习 展示营期、课程信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          课程名称:&nbsp; {{ item.courseName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          所属训练营:&nbsp; {{ item.companyName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          营期名称:&nbsp; {{ item.campPeriodName }}
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>
                                  <!-- 课后答题 展示课程信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.POST_COURSE_ANSWERING.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          课程名称:&nbsp; {{ item.courseName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          所属训练营:&nbsp; {{ item.companyName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          营期名称:&nbsp; {{ item.campPeriodName }}
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>
                                  <!-- 领取红包 展示红包信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.RECEIVE_RED_PACKET.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          课程名称:&nbsp; {{ item.courseName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          所属训练营:&nbsp; {{ item.companyName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          营期名称:&nbsp; {{ item.campPeriodName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          红包金额:&nbsp; <t-tag size="small" color="#FF1F44">{{ item.rewardAmount }}</t-tag>元
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>

                                  <!-- 添加企微 展示企微信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          企业名称: {{ item.corpName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          企微昵称: {{ item.employeeWeworkName }}
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>
                                  <!-- 删除企微 展示企微信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          企业名称: {{ item.corpName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          企微昵称: {{ item.employeeWeworkName }}
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>
                                  <!-- 加入群聊 展示群聊信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.JOIN_GROUP_CHAT.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          群组名称: {{ item.groupName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          企业名称: {{ item.corpName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          入群方式: {{ getJoinSceneText(item.joinScene) }}
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>
                                  <!-- 退出群聊 展示群聊信息 -->
                                  <div v-if="item.behaviorType === behaviorTypeEnum.EXIT_GROUP_CHAT.code">
                                    <t-row :gutter="16">
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          群组名称: {{ item.groupName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          企业名称: {{ item.corpName }}
                                        </p>
                                      </t-col>
                                      <t-col :span="4">
                                        <p class="timerline-info">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          退群方式: {{ getQuitSceneText(item.quitScene) }}
                                        </p>
                                      </t-col>
                                    </t-row>
                                  </div>
                                </template>
                              </t-card>
                            </template>
                          </t-timeline-item>
                        </t-timeline>
                      </template>
                      <template v-else>
                        <t-empty style="margin: 120px;"/>
                      </template>
                    </t-space>
                  </div >
                  <t-pagination margin="16"
                                size="small"
                                theme="simple"
                                :page-size.sync="behaviorPagination.pageSize"
                                :total="behaviorPagination.total"
                                v-model="behaviorPagination.current"
                                @page-size-change="behaviorPagination.current = 1"
                                @change="queryCustomerBehavior" />
                </t-tab-panel>
                <t-tab-panel value="2" label="训练营" class="tab-panel-container">
                  <!-- 训练营 左边为卡片列表，右边为详细数据 -->
                  <div class="camp-sticky-wrapper">
                    <div class="camp-sticky-operator" ref="campContent">
                      <div class="camp-list-header">
                        <t-space align="center">
                          <t-icon name="education" size="18px" style="color: #0052D9;" />
                          <span class="header-title">训练营列表</span>
                          <t-tag size="small" variant="light" theme="primary">{{ campPeriodInfos.length }}</t-tag>
                        </t-space>
                      </div>
                      <t-space direction="vertical" class="camp-list-content">
                        <t-list :split="false" v-if="campPeriodInfos.length > 0" class="camp-list">
                          <t-list-item v-for="(item, index) in campPeriodInfos"
                                       :key="index"
                                       :class="['camp-item', {'active-item': selectedCampId === item.id}]"
                                       @click="selectCamp(item)"
                          >
                            <div class="camp-item-content">
                              <div class="camp-image-wrapper">
                                <t-image :src="item.campperiodCoverpath"
                                         class="camp-image"
                                         :error="'default'"
                                         fit="cover">
                                  <template #error>
                                    <div class="image-error">
                                      <t-icon name="image" size="24px" />
                                    </div>
                                  </template>
                                </t-image>
                                <div class="camp-status-badge" v-if="selectedCampId === item.id">
                                  <t-icon name="check" size="12px" />
                                </div>
                              </div>
                              <div class="camp-info">
                                <div class="camp-title">{{ item.campperiodName }}</div>
                                <div class="camp-description">{{ item.campperiodIntroduction || '暂无介绍' }}</div>
                                <div class="camp-meta">
                                  <t-tag size="small" variant="light" theme="success">进行中</t-tag>
                                </div>
                              </div>
                            </div>
                          </t-list-item>
                        </t-list>
                        <div v-else class="empty-state">
                          <t-empty description="暂无训练营数据">
                            <template #image>
                              <t-icon name="education" size="48px" style="color: #ddd;" />
                            </template>
                          </t-empty>
                        </div>
                      </t-space>
                    </div >
                    <div class="camp-sticky-content">
                      <div v-if="selectedCampId" class="camp-detail-wrapper">
                        <!-- 训练营概览卡片 -->
                        <t-card class="camp-overview-card" :bordered="false">
                          <template #header>
                            <t-space align="center">
                              <t-icon name="chart-bar" size="18px" style="color: #0052D9;" />
                              <span class="card-title">学习概览</span>
                            </t-space>
                          </template>
                          <div class="overview-stats-inline">
                            <div class="stat-item">
                              <div class="stat-value">{{ courseData.length }}</div>
                              <div class="stat-label">总课程数</div>
                            </div>
                            <div class="stat-item">
                              <div class="stat-value">{{ getCompletedCourses() }}</div>
                              <div class="stat-label">已完成</div>
                            </div>
                            <div class="stat-item">
                              <div class="stat-value">{{ getInProgressCourses() }}</div>
                              <div class="stat-label">进行中</div>
                            </div>
                          </div>
                        </t-card>

                        <!-- 课程数据列表 -->
                        <t-card class="course-table-card" :bordered="false">
                          <template #header>
                            <t-space align="center">
                              <t-icon name="play-circle" size="18px" style="color: #0052D9;" />
                              <span class="card-title">课程详情</span>
                            </t-space>
                          </template>
                          <t-table
                            row-key="id"
                            :columns="courseColumns"
                            :data="courseData"
                            :pagination="coursePagination"
                            :loading="courseDataLoading"
                            @change="courseRehandleChange"
                            size="small"
                            class="course-table"
                          >
                            <template #arrivalStatus="{ row }">
                              <t-tag v-if="row.arrivalStatus === 0" variant="light" theme="default">未到课</t-tag>
                              <t-tag v-else-if="row.arrivalStatus === 1" variant="light" theme="warning">已到课</t-tag>
                              <t-tag v-else-if="row.arrivalStatus === 2" variant="light" theme="success">已完课</t-tag>
                            </template>
                            <template #playProgress="{ row }">
                              <div class="progress-wrapper">
                                <span class="progress-text">{{ secondsToTime(row.playProgress) }}</span>
                                <t-progress
                                  :percentage="getProgressPercentage(row)"
                                  size="small"
                                  :theme="getProgressTheme(row)"
                                  class="progress-bar"
                                />
                              </div>
                            </template>
                          </t-table>
                        </t-card>
                      </div>
                      <div v-else class="no-selection">
                        <t-empty description="请选择一个训练营查看详情">
                          <template #image>
                            <t-icon name="education" size="64px" style="color: #ddd;" />
                          </template>
                        </t-empty>
                      </div>
                    </div>
                  </div>
                </t-tab-panel>
                <t-tab-panel value="3" label="企微标签" class="tab-panel-container">
                  <div class="wecom-tags-wrapper" ref="wecomTagContent">
                    <template v-if="wecomTagData.length !== 0">
                      <div v-for="(followUser, index) in wecomTagData" :key="index" class="follow-user-card">
                        <div class="follow-user-header">
                          <div class="user-info-left">
                            <t-avatar size="32px" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                              <t-icon name="user" />
                            </t-avatar>
                            <div class="user-basic-info">
                              <div class="user-name">
                                {{ (followUser.qyUserInfo && followUser.qyUserInfo.qyName) }}
                                <span v-if="followUser.corpName" class="corp-name">@{{ followUser.corpName }}</span>
                              </div>
                              <div class="user-meta">
                                <t-tag size="small" variant="light" theme="primary">{{ getAddWayText(followUser.add_way) }}</t-tag>
                                <span class="add-time">{{ formatTime(followUser.createtime) }}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="follow-user-content">
                          <!-- 基本信息 -->
                          <div class="user-details" v-if="hasUserDetails(followUser)">
                            <div class="detail-item" v-if="followUser.description">
                              <t-icon name="edit" size="14px" />
                              <span>{{ followUser.description }}</span>
                            </div>
                            <div class="detail-item" v-if="followUser.remark_mobiles && followUser.remark_mobiles.length > 0">
                              <t-icon name="mobile" size="14px" />
                              <span>{{ followUser.remark_mobiles.join(', ') }}</span>
                            </div>
                          </div>

                          <!-- 企微标签 -->
                          <div v-if="followUser.tags && followUser.tags.length > 0" class="wecom-tags-section">
                            <div class="tags-header">
                              <t-icon name="discount" size="16px" />
                              <span class="tags-title">企微标签</span>
                            </div>
                            <div class="tags-content">
                              <div v-for="(tagGroup, tagIndex) in getGroupedTags(followUser.tags)" :key="tagIndex" class="tag-group">
                                <div class="tag-group-name">{{ tagGroup.groupName }}</div>
                                <div class="tag-list">
                                  <t-tag
                                    v-for="tag in tagGroup.tags"
                                    :key="tag.tag_id || tag.tag_name"
                                    :theme="getTagTheme(tag.type)"
                                    size="small"
                                    variant="light"
                                    :title="tag.tag_name"
                                    class="wecom-tag"
                                  >
                                    {{ tag.tag_name }}
                                  </t-tag>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div v-else class="no-tags">
                            <t-icon name="info-circle" size="16px" />
                            <span>暂无企微标签</span>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="empty-state">
                        <t-empty description="暂无企微标签数据">
                          <template #image>
                            <t-icon name="discount" size="48px" style="color: #ddd;" />
                          </template>
                        </t-empty>
                      </div>
                    </template>
                  </div>
                </t-tab-panel>
                <t-tab-panel value="4" label="操作记录" class="tab-panel-container">
                  <div class="tabs-sticky-wrapper" ref="operationContent">
                    <t-space direction="vertical" style="width: 100%; margin-top: 16px;">
                      <template v-if="operationData.length !== 0">
                        <t-timeline mode="same">
                          <t-timeline-item :dotColor="getOperationDotColor()" v-for="(item, index) in operationData" :key="index">
                            <template #content>
                              <t-card
                                hover-shadow
                                :class="['timerline-card', 'operation-card']"
                              >
                                <template #title>
                                  <t-space align="center">
                                    <t-icon name="swap"
                                            :style="{ color: getOperationDotColor(), fontSize: '18px' }" />
                                    <span>客户分配</span>
                                  </t-space>
                                </template>
                                <template #actions>
                                  <t-icon name="time" size="medium" />
                                  {{ item.changeTime }}
                                </template>
                                <template #content>
                                  <!-- 操作人信息 -->
                                  <div class="operation-operator">
                                    <t-space align="center">
                                      <t-icon name="user" size="14px" style="color: #6b7280;" />
                                      <span class="operator-label">操作人：</span>
                                      <t-tag size="small" variant="light" theme="primary">{{ item.operatorName }}</t-tag>
                                    </t-space>
                                  </div>

                                  <!-- 变更信息 -->
                                  <div class="operation-changes">
                                    <!-- 原信息 -->
                                    <div class="change-section">
                                      <div class="section-title">
                                        <t-icon name="arrow-right" size="12px" style="color: #ef4444;" />
                                        <span>变更前</span>
                                      </div>
                                      <div class="change-content-inline">
                                        <span class="change-item">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          栏目: {{ item.originalColumnName || '无' }}
                                        </span>
                                        <span class="change-item">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          训练营: {{ item.originalCompanyName || '无' }}
                                        </span>
                                        <span class="change-item">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          员工: {{ item.originalEmployeeName || '无' }}
                                        </span>
                                      </div>
                                    </div>

                                    <!-- 新信息 -->
                                    <div class="change-section">
                                      <div class="section-title">
                                        <t-icon name="arrow-right" size="12px" style="color: #10b981;" />
                                        <span>变更后</span>
                                      </div>
                                      <div class="change-content-inline">
                                        <span class="change-item">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          栏目: {{ item.newColumnName || '无' }}
                                        </span>
                                        <span class="change-item">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          训练营: {{ item.newCompanyName || '无' }}
                                        </span>
                                        <span class="change-item">
                                          <t-divider layout="vertical" class="vertical-divider"/>
                                          员工: {{ item.newEmployeeName || '无' }}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </template>
                              </t-card>
                            </template>
                          </t-timeline-item>
                        </t-timeline>
                      </template>
                      <template v-else>
                        <t-empty description="暂无操作记录">
                          <template #image>
                            <t-icon name="swap" size="48px" style="color: #ddd;" />
                          </template>
                        </t-empty>
                      </template>
                    </t-space>
                  </div>
                  <t-pagination margin="16"
                                size="small"
                                theme="simple"
                                :page-size.sync="operationPagination.pageSize"
                                :total="operationPagination.total"
                                v-model="operationPagination.current"
                                @page-size-change="operationPagination.current = 1"
                                @change="queryOperation" />
                </t-tab-panel>
              </t-tabs>
          </t-card>
        </t-col>
      </t-row>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import customerBehaviorApi from "@/constants/api/hxsy-admin/customer-behavior.api";
import {BehaviorTypeEnum, BEHAVIOR_TYPE_OPTIONS} from "@/constants/enum/business/behavior-type.enum";
import customerCourseRelationApi from "@/constants/api/hxsy-admin/customer-course-relation.api";
import SelectUserDialog from "@/pages/customer-management/components/select-user-dialog.vue";
import customerSalesRelationApi from "@/constants/api/hxsy-admin/customer-sales-relation.api";
import customerAssignmentApi from "@/constants/api/hxsy-admin/customer-assignment.api";
import SetTagDialog from "@/pages/customer-management/components/set-tag-dialog.vue";
import SetWecomTagDialog from "@/pages/customer-management/components/set-wecom-tag-dialog.vue";
import customerTagsApi from "@/constants/api/hxsy-admin/customer-tags.api";
import customerWecomTagsApi from "@/constants/api/hxsy-admin/customer-wecom-tags.api";
import customerApi from "@/constants/api/hxsy-admin/customer.api";
import {Select} from "tdesign-vue";

const ARRIVAL_STATUS = [
  { label: '未到课', value: 0 },
  { label: '已到课', value: 1 },
  { label: '已完课', value: 2 },
];
export default Vue.extend({
  name: 'CustomerDetailComponent',
  components: {SetTagDialog, SelectUserDialog, SetWecomTagDialog},
  props: {
    data : {
      default: () => ({
        customerId: '',
        nickname: '',
        avatarUrl: '',
        mobile: '',
        wechatRemark: '',
        lastActiveTime: '',
        tags: [],
      }),
    },
    hasAllocationAuth: {
      default: false,
    }
  },
  data() {
    return {
      allocationVisible: false, // 分配用户弹窗
      tagVisible: false, // 设置标签弹窗
      wecomTagVisible: false, // 设置企微标签弹窗
      behaviorTypeEnum: BehaviorTypeEnum,
      tabValue: "1", // 主页面tabValue
      behaviorPagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },
      coursePagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      courseDataLoading: false,
      behaviorData: [], // 行为轨迹数据
      COLOR_MAP: { // 行为类型颜色
        // [BehaviorTypeEnum.CUSTOMER_REGISTRATION.code]: '#0052D9',
        [BehaviorTypeEnum.CAMP_ENROLLMENT.code]: '#FF7A00',
        [BehaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.code]: '#00C979',
        [BehaviorTypeEnum.POST_COURSE_ANSWERING.code]: '#FF4D4F',
        [BehaviorTypeEnum.RECEIVE_RED_PACKET.code]: '#FF1F44',
        [BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code]: '#07C160', // 企微绿色
        [BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code]: '#FF4757', // 醒目红色
        [BehaviorTypeEnum.JOIN_GROUP_CHAT.code]: '#3742FA', // 醒目蓝色
        [BehaviorTypeEnum.EXIT_GROUP_CHAT.code]: '#747D8C', // 灰色
      },
      behaviorTypes: [],
      columnInfos: [], // 已参加的所有栏目信息
      selectedColumnId: "", // 已选择的栏目id
      selectedColumnInfo: { columnName: '', salesName: '' }, // 已选择的栏目信息
      customerCourseRelations: [], // 已选择栏目下的客户课程关联信息
      selectedCampPeriodInfo: {
        campPeriodId: '',
        campPeriodName: '',
      },// 已选择需要更改营期的信息(用于添加标签)
      campPeriodInfos: [], // 已参加的营期信息
      selectedCampId: "", // 已选择的营期id（用于查询营期下的课程记录）
      courseData: [], // 课程数据
      courseColumns: [
        {
          title: '课程名称',
          colKey: 'courseName',
          align: 'center',
          width: 150,
        },
        // {
        //   title: '课程简介',
        //   colKey: 'courseIntroduction',
        //   align: 'center',
        //   width: 150,
        // },
        {
          title: '课程开始时间',
          colKey: 'startTime',
          align: 'center',
          width: 150,
        },
        {
          title: '课程状态',
          colKey: 'arrivalStatus',
          align: 'center',
          width: 150,
        },
        {
          title: '到课时间',
          colKey: 'arrivalTime',
          align: 'center',
          width: 150,
        },
        {
          title: '完播时间',
          colKey: 'completeTime',
          align: 'center',
          width: 150,
        },
        {
          title: '播放时长',
          colKey: 'playProgress',
          align: 'center',
          width: 150,
        },
      ],

      // 操作记录数据
      operationData: [], // 操作记录数据
      operationPagination: {
        current: 1,
        pageSize: 5,
        total: 0,
      },
      wecomTagDialogData: {
        userId: '', // 企业成员的userid
        externalUserId: '', // 外部联系人userid
      },
      // 企微标签展示数据
      wecomTagData: [], // 企微标签数据（从接口获取的follow_user数据）
    };
  },
  computed: {
    userInfo() {
      return [
        {
          title: '昵称',
          content: this.data.nickname,
          span: 3,
        },
        {
          title: '跟进人',
          content: [...new Set(this.customerCourseRelations?.map((item: any) => item.salesName))].join('、') || '--',
          span: 3,
        },
        {
          title: '已加微信',
          content: this.getWechatInfo(),
          span: 3,
          isWechat: true,
        },
        {
          title: '最后活跃时间',
          content: this.data.lastActiveTime,
          span: 3,
        },
        {
          title: '手机号',
          content: this.data.mobile || '--',
          span: 3,
        }
      ]
    },
    campPeriodOptions() {
      if (!this.campPeriodInfos) {
        return [];
      }
      return this.campPeriodInfos.map((item: any) => ({
        content: item.campperiodName,
        value: item.id,
        info: item,
      }));
    },
    courseColumns() {
      return [
        {
          title: '课程名称',
          colKey: 'courseName',
          align: 'center',
          width: 150,
        },
        {
          title: '课程开始时间',
          colKey: 'startTime',
          align: 'center',
          width: 150,
        },
        {
          title: '课程状态',
          colKey: 'arrivalStatus',
          align: 'center',
          width: 150,
          edit: {
            component: Select,
            props: {
              clearable: true,
              autofocus: true,
              options: ARRIVAL_STATUS,
            },
            // 除了点击非自身元素退出编辑态之外，还有哪些事件退出编辑态
            abortEditOnEvent: ['onEnter'],
            // 编辑完成
            onEdited: (context: any) => {
              (this as any).courseData.splice(context.rowIndex, 1, context.newRowData);
              (this as any).setToClass(context.newRowData);
            },
          }
        },
        {
          title: '到课时间',
          colKey: 'arrivalTime',
          align: 'center',
          width: 150,
        },
        {
          title: '完播时间',
          colKey: 'completeTime',
          align: 'center',
          width: 150,
        },
        {
          title: '播放时长',
          colKey: 'playProgress',
          align: 'center',
          width: 150,
        },
      ]
    }
  },
  mounted() {
    this.listByCustomerId();
    // 初始化时也加载企微标签数据
    this.queryWecomTags();
  },
  methods: {
    /**
     * @Description: 获取入群方式文本
     * <AUTHOR>
     * @date 2025/7/26
     * @time 15:00
     */
    getJoinSceneText(joinScene: number): string {
      const joinSceneMap: { [key: number]: string } = {
        0: '由成员邀请入群',
        3: '扫描群二维码'
      };
      return joinSceneMap[joinScene] || '未知方式';
    },
    /**
     * @Description: 获取退群方式文本
     * <AUTHOR>
     * @date 2025/7/26
     * @time 15:00
     */
    getQuitSceneText(quitScene: number): string {
      const quitSceneMap: { [key: number]: string } = {
        0: '自己退群',
        1: '群主/管理员踢出'
      };
      return quitSceneMap[quitScene] || '未知方式';
    },
    /**
     * @Description: 获取已完成课程数
     * <AUTHOR>
     * @date 2025/8/2
     * @time 16:00
     */
    getCompletedCourses() {
      return this.courseData.filter(course => course.arrivalStatus === 2).length;
    },
    /**
     * @Description: 获取进行中课程数
     * <AUTHOR>
     * @date 2025/8/2
     * @time 16:00
     */
    getInProgressCourses() {
      return this.courseData.filter(course => course.arrivalStatus === 1).length;
    },
    /**
     * @Description: 获取未开始课程数
     * <AUTHOR>
     * @date 2025/8/2
     * @time 16:00
     */
    getNotStartedCourses() {
      return this.courseData.filter(course => course.arrivalStatus === 0).length;
    },
    /**
     * @Description: 获取进度百分比
     * <AUTHOR>
     * @date 2025/8/2
     * @time 16:00
     */
    getProgressPercentage(row) {
      if (!row.totalDuration || row.totalDuration === 0) return 0;
      return Math.round((row.playProgress / row.totalDuration) * 100);
    },
    /**
     * @Description: 获取进度条主题
     * <AUTHOR>
     * @date 2025/8/2
     * @time 16:00
     */
    getProgressTheme(row) {
      const percentage = this.getProgressPercentage(row);
      if (percentage >= 100) return 'success';
      if (percentage >= 50) return 'warning';
      return 'default';
    },
    /**
     * @Description: 获取操作记录圆点颜色
     * <AUTHOR>
     * @date 2025/8/2
     * @time 16:30
     */
    getOperationDotColor() {
      return '#8B5CF6'; // 紫色，区别于行为轨迹的颜色
    },
    // 用于控制哪些行或哪些单元格不允许出现编辑态
    editableCellState(cellParams: any) {
      const { row } = cellParams;
      return this.hasAllocationAuth && row.arrivalStatus !== 2;
    },
    // 手动将课程设为已到课
    async setToClass(row: any) {
      console.log("row", row);

      const res = await (this as any).$request.post(
        customerApi.updCourseCustomerRelStatus.url,
        {
          campPeriodId: this.selectedCampId, // 营期id
          customerId: this.data.customerId, // 客户id
          courseId: row.id, // 课程id
          arrivalStatus: row.arrivalStatus, // 到课状态
        },
      );
      if (res.code === 200) {
        await this.$message.success('操作成功');
      }
    },
    // 将秒转为时分秒
    secondsToTime(seconds: number): string {
      if (!seconds) {
        return '';
      }
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      let result = '';
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分`;
      if (remainingSeconds > 0 || result === '') result += `${remainingSeconds}秒`;
      return result;
    },
    // 刷新滚动条
    refreshScroll(type?: string) {
      this.$nextTick(() => {
        if (type === 'behavior' || !type) {
          console.log("refreshScroll", type);
          const behaviorContent = this.$refs.behaviorContent as HTMLElement;
          if (behaviorContent) {
            behaviorContent.scrollTo({ top: 0, behavior: 'smooth' });
          }
        }
        if (type === 'camp' || !type) {
          const campContent = this.$refs.campContent as HTMLElement;
          console.log("refreshScroll", type);
          if (campContent) {
            campContent.scrollTo({ top: 0, behavior: 'smooth' });
          }
        }
        if (type === 'wecomTag' || !type) {
          console.log("refreshScroll", type);
          const wecomTagContent = this.$refs.wecomTagContent as HTMLElement;
          if (wecomTagContent) {
            wecomTagContent.scrollTo({ top: 0, behavior: 'smooth' });
          }
        }
        if (type === 'operation' || !type) {
          console.log("refreshScroll", type);
          const operationContent = this.$refs.operationContent as HTMLElement;
          if (operationContent) {
            operationContent.scrollTo({ top: 0, behavior: 'smooth' });
          }
        }
      });
    },
    closeAllocationVisible() {
      this.allocationVisible = false;
    },
    confirmAllocation(userData: any) {
      console.log("confirmAllocation", userData);
      // 这里可以添加分配用户的逻辑
      this.assignCustomer(userData.columnId, userData.companyId, userData.salesGroupId, userData.userId, userData.userName, userData.qyUserId);
    },
    // 选择行为类型
    behaviorTypeOptions() {
      return BEHAVIOR_TYPE_OPTIONS
    },
    // 获取指定数量的标签名
    getSomeTags(tags: any, max: number | undefined, name: string) {
      if (!tags || !Array.isArray(tags)) {
        return [];
      }
      const someTags: string[] = [];
      let count = 0;

      if (max && max > 0) {
        for (let i = 0; i < tags.length && count < max; i++) {
          const tagsNameArr = this.getTags(tags[i], name);
          for (let j = 0; j < tagsNameArr.length && count < max; j++) {
            someTags.push(tagsNameArr[j]);
            count += 1;
            if (count === max) {
              someTags.push(" ... ");
            }
          }
        }
      } else {
        tags.forEach((item: any) => {
          const tagsNameArr = this.getTags(item, name);
          tagsNameArr.forEach((item: any) => {
            someTags.push(item);
          })
        })
      }
      return someTags;
    },

    // 获取并处理标签名
    getTags(item: any, name: string): string[] {
      if (!item || typeof item?.[name] !== 'string' || item?.[name] === '') {
        return [];
      }
      if (item?.[name].indexOf(',') === -1) {
        return [item?.[name]];
      }
      const itemTags = item?.[name].split(',');
      return itemTags.map((tag: string) => tag.trim());
    },
    /**
     * @Description: 获取行为类型对应的颜色
     * <AUTHOR>
     * @date 2025/5/14
     * @time 18:00
     */
    getDotColor(type: number) {
      return this.COLOR_MAP[type] || 'default';
    },
    /**
     * @Description: 获取行为类型名称
     * <AUTHOR>
     * @date 2025/5/14
     * @time 18:00
     */
    getBehaviorTypeName(type: number) {
      const behaviorType = Object.values(BehaviorTypeEnum).find(item => item.code === type);
      return behaviorType?.name || '';
    },
    /**
     * @Description: 获取行为类型图标
     * <AUTHOR>
     * @date 2025/8/2
     * @time 15:00
     */
    getBehaviorTypeIcon(type: number) {
      const iconMap = {
        [BehaviorTypeEnum.CAMP_ENROLLMENT.code]: 'user-add',
        [BehaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.code]: 'play-circle',
        [BehaviorTypeEnum.POST_COURSE_ANSWERING.code]: 'edit',
        [BehaviorTypeEnum.RECEIVE_RED_PACKET.code]: 'money-filled',
        [BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code]: 'user-add',
        [BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code]: 'user-clear',
        [BehaviorTypeEnum.JOIN_GROUP_CHAT.code]: 'usergroup-add',
        [BehaviorTypeEnum.EXIT_GROUP_CHAT.code]: 'usergroup-clear',
      };
      return iconMap[type] || 'circle';
    },
    /**
     * @Description: 获取卡片样式类
     * <AUTHOR>
     * @date 2025/8/2
     * @time 15:00
     */
    getCardClass(type: number) {
      const classMap = {
        [BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.code]: 'wechat-enterprise-card add-wechat',
        [BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.code]: 'wechat-enterprise-card delete-wechat',
        [BehaviorTypeEnum.JOIN_GROUP_CHAT.code]: 'group-chat-card join-group',
        [BehaviorTypeEnum.EXIT_GROUP_CHAT.code]: 'group-chat-card exit-group',
      };
      return classMap[type] || '';
    },
    // 选择营期
    selectCamp(item: any) {
      this.selectedCampId = item.id;
      // 这里可以添加加载右侧数据的逻辑
      this.queryCourseRelation();
    },
    // 给客户分配销售
    async assignCustomer(columnId: string, companyId: string, salesGroupId: string, salesId: string, salesName: string, qyUserId: string) {
      const res = await (this as any).$request.post(
        customerSalesRelationApi.assignCustomer.url,
        {
          oldColumnId:  this.selectedColumnId,
          customerIds: [ this.data.customerId ],
          columnId,
          companyId,
          salesGroupId,
          salesId,
          salesName,
          qyUserId,
        },
      );
      if (res.code === 0) {
        await this.$message.success('分配成功');
        await this.listByCustomerId();
        await this.queryOperation();
      } else {
        await this.$message.error(res.msg || '分配失败');
      }
      this.closeAllocationVisible();
    },
    /**
     * @Description: 查询客户行为轨迹
     * <AUTHOR>
     * @date 2025/5/4
     * @time 11:25
     */
    async queryCustomerBehavior() {
      if (!this.data.customerId) {
        return;
      }
      const query = {
        pageNum: this.behaviorPagination.current,
        pageSize: this.behaviorPagination.pageSize,
      };
      const formData = {
        customerId: this.data.customerId,
        behaviorTypes: this.behaviorTypes,
        columnId: this.selectedColumnId,
      };
      const res = await (this as any).$request.post(
        customerBehaviorApi.page.url,
        formData,
        {
          params: query,
        },
      );
      const { code, data, msg } = res;
      if (code === 0) {
        this.behaviorData = data.records || [];
        this.behaviorPagination.total = +data.total || 0;
        console.log("this.behaviorData", this.behaviorData);
        this.refreshScroll('behavior');
      } else {
        await this.$message.error(msg || '请求失败');
      }
    },
    /**
     * @Description: 查询该客户的分配销售记录
     * <AUTHOR>
     * @date 2025/5/18
     * @time 12:49
     */
    async queryOperation() {
      const query = {
        pageNum: this.operationPagination.current,
        pageSize: this.operationPagination.pageSize,
        customerId: this.data.customerId,
      }
      const res = await (this as any).$request.get(
        customerAssignmentApi.getCustomerAssignmentList.url,
        {
          params: query,
        },
      );
      const { code, data, msg } = res;
      if (code === 0) {
        this.operationData = data.records || [];
        this.operationPagination.total = +data.total || 0;
        console.log("getCustomerAssignmentList  data", data);
        this.refreshScroll('operation');
      } else {
        await this.$message.error(msg || '查询操作记录请求失败');
      }
    },
    // 查询已参加的营期
    async queryEnrolledCamps() {
      const query = {
        customerId: this.data.customerId,
        columnId: this.selectedColumnId,
      }
      this.courseData = [];
      this.coursePagination.total = 0;
      const res = await (this as any).$request.get(
        customerCourseRelationApi.getCampPeriodInfoByCustomerId.url,
        {
          params: query,
        },
      );
      const { code, data, msg } = res;
      if (code === 0) {
        this.campPeriodInfos = data || [];
        if (this.campPeriodInfos.length > 0) {
          this.selectedCampId = this.campPeriodInfos[0].id;
          await this.queryCourseRelation();
        }
        console.log("getCampPeriodInfoByCustomerId  data", data);
      } else {
        await this.$message.error(msg || '查询客户营期请求失败');
      }
    },
    // 根据客户id查询客户课程关联信息
    async queryCourseRelation() {
      const query = {
        customerId: this.data.customerId,
        pageSize: this.coursePagination.pageSize,
        pageNum: this.coursePagination.current,
        campPeriodId: this.selectedCampId,
      }
      const res = await (this as any).$request.get(
        customerCourseRelationApi.getCourseRelationByCustomerId.url,
        {
          params: query,
        },
      );
      const { code, data, msg } = res;
      if (code === 0) {
        this.courseData = data.records || [];
        this.coursePagination.total = +data.total || 0;
        console.log("getCourseRelationByCustomerId  data", data);
      } else {
        await this.$message.error(msg || '查询客户营期请求失败');
      }
    },
    // 课程数据分页改变
    courseRehandleChange(changeParams: any) {
      this.coursePagination.pageSize = changeParams.pagination.pageSize;
      this.coursePagination.current = changeParams.pagination.current;
      this.queryCourseRelation();
    },
    // 获取客户已分配信息
    async listByCustomerId() {
      const query = {
        customerId: this.data.customerId,
      }
      const res = await (this as any).$request.get(
        customerSalesRelationApi.listByCustomerId.url,
        {
          params: query,
        },
      );
      const { code, data, msg } = res;
      if (code === 0) {
        this.columnInfos = data || [];
        this.selectedColumnId = data[0].id || "";
        this.columnChange();
        console.log("listByCustomerId  data", data);
      } else {
        await this.$message.error(msg || '查询客户营期请求失败');
      }
    },
    showAllocationVisible() {
      const column: any = this.columnInfos.find((item: any) => item.id === this.selectedColumnId);
      this.selectedColumnInfo.columnName = column.columnName || "";
      this.selectedColumnInfo.salesName = [...new Set(this.customerCourseRelations?.map((item: any) => item.salesName))].join('、')
      this.allocationVisible = true;
    },
    showSetTagVisible() {
      console.log("this.campPeriodInfos", this.campPeriodInfos);
      const campPeriodInfo: any = this.campPeriodInfos[0];
      this.selectedCampPeriodInfo.campPeriodId = campPeriodInfo.id;
      this.selectedCampPeriodInfo.campPeriodName = campPeriodInfo.campperiodName;
      this.tagVisible = true;
    },
    /**
     * @Description: 添加标签时选择营期
     * <AUTHOR>
     * @date 2025/5/25
     * @time 23:25
     */
    campPeriodOptionsClick(item: any, context: any) {
      this.selectedCampPeriodInfo.campPeriodId = item.info.id;
      this.selectedCampPeriodInfo.campPeriodName = item.info.campperiodName;
      this.tagVisible = true;
    },
    closeTagVisible() {
      this.tagVisible = false;
    },
    // 打开企微标签对话框
    showWecomTagVisible() {
      // 获取当前选中栏目的跟进人信息
      const currentColumnInfo = this.columnInfos.find((item: any) => item.id === this.selectedColumnId);
      const salesRelation = currentColumnInfo?.customerSalesRelationList?.[0];

      // 设置企微标签所需的数据
      this.wecomTagDialogData = {
        userId: salesRelation?.salesId || '', // 企业成员的userid，使用当前栏目的跟进人ID
        externalUserId: this.data.externalUserId || this.data.customerId || '', // 外部联系人userid
      };

      console.log('企微标签数据:', this.wecomTagDialogData);
      this.wecomTagVisible = true;
    },
    // 关闭企微标签对话框
    closeWecomTagVisible() {
      this.wecomTagVisible = false;
    },
    // 确认添加企微标签
    async confirmWecomTag(tagData: any) {
      console.log('确认添加企微标签', tagData);
      // 如果需要刷新客户信息
      await this.listByCustomerId();
      // 刷新企微标签数据
      await this.queryWecomTags();
      this.closeWecomTagVisible();
    },
    /**
     * @Description: 查询客户企微标签信息（使用新的接口数据格式）
     * <AUTHOR>
     * @date 2025/7/26
     * @time 15:00
     */
    async queryWecomTags() {
      if (!this.data.customerId) {
        return;
      }

      try {
        // 调用真实的客户详情接口
        const res = await (this as any).$request.get(
          customerApi.getCustomerDetail.url,
          {
            params: {
              customerId: this.data.customerId,
            },
          },
        );

        const { code, data, msg } = res;
        if (code === 0 && data) {
          // 处理接口返回的数据格式，合并所有企业的follow_user数据
          const allFollowUsers = [];

          // 检查data是否为数组格式
          const corpDataList = Array.isArray(data) ? data : [data];

          corpDataList.forEach(corp => {
            // 检查是否有customerDetails
            const customerDetails = corp.customerDetails || [];
            customerDetails.forEach(detail => {
              if (detail.follow_user && detail.follow_user.length > 0) {
                // 为每个follow_user添加企业信息
                detail.follow_user.forEach(user => {
                  allFollowUsers.push({
                    ...user,
                    corpName: corp.corpName, // 添加企业名称
                    corpId: corp.corpId
                  });
                });
              }
            });
          });

          this.wecomTagData = allFollowUsers;
          console.log("queryWecomTags 接口数据", this.wecomTagData);
          this.refreshScroll('wecomTag');
        } else {
          console.warn('获取企微标签数据失败:', msg);
          this.wecomTagData = [];
        }
      } catch (error) {
        console.error('查询企微标签出错:', error);
        this.wecomTagData = [];
        await this.$message.error('查询企微标签出错');
      }
    },
    /**
     * @Description: 格式化时间戳
     * <AUTHOR>
     * @date 2025/7/26
     * @time 15:00
     */
    formatTime(timestamp: number): string {
      if (!timestamp) return '';
      return new Date(timestamp * 1000).toLocaleString();
    },
    /**
     * @Description: 获取添加方式文本
     * <AUTHOR>
     * @date 2025/7/26
     * @time 15:00
     */
    getAddWayText(addWay: number): string {
      const addWayMap: { [key: number]: string } = {
        0: '未知来源',
        1: '扫描二维码',
        2: '搜索手机号',
        3: '名片分享',
        4: '群聊',
        5: '手机通讯录',
        6: '微信联系人',
        7: '来自微信的添加好友申请',
        8: '安装第三方应用时自动添加的客服人员',
        9: '搜索邮箱',
        10: '视频号',
        201: '内部成员共享',
        202: '管理员/负责人分配'
      };
      return addWayMap[addWay] || '未知来源';
    },
    /**
     * @Description: 根据标签类型获取主题
     * <AUTHOR>
     * @date 2025/7/26
     * @time 15:00
     */
    getTagTheme(type: number): string {
      const themeMap: { [key: number]: string } = {
        1: 'primary',   // 企业设置
        2: 'success',   // 用户自定义
        3: 'warning'    // 规则组标签
      };
      return themeMap[type] || 'default';
    },
    /**
     * @Description: 将标签按分组整理
     * <AUTHOR>
     * @date 2025/7/26
     * @time 15:00
     */
    getGroupedTags(tags: any[]): any[] {
      if (!tags || !Array.isArray(tags)) {
        return [];
      }

      const groupMap = new Map();

      tags.forEach(tag => {
        const groupName = tag.group_name || '未分组';
        if (!groupMap.has(groupName)) {
          groupMap.set(groupName, {
            groupName,
            tags: []
          });
        }
        groupMap.get(groupName).tags.push(tag);
      });

      return Array.from(groupMap.values());
    },
    /**
     * @Description: 检查用户是否有详细信息
     * <AUTHOR>
     * @date 2025/7/26
     * @time 16:00
     */
    hasUserDetails(followUser: any): boolean {
      return !!(followUser.corpName || (followUser.qyUserInfo && followUser.qyUserInfo.qyName) || followUser.remark);
    },

    /**
     * @Description: 获取微信信息展示文本
     * <AUTHOR>
     * @date 2025/7/26
     * @time 17:00
     */
    getWechatInfo(): string {
      if (!this.wecomTagData || this.wecomTagData.length === 0) {
        return '--';
      }

      const activeStaff = this.wecomTagData.filter(staff => !staff.delete_time);
      const deletedStaff = this.wecomTagData.filter(staff => staff.delete_time);

      if (activeStaff.length === 0 && deletedStaff.length === 0) {
        return '--';
      }

      let result = '';
      if (activeStaff.length > 0) {
        result += `已添加${activeStaff.length}个`;
      }
      if (deletedStaff.length > 0) {
        if (result) result += '，';
        result += `已删除${deletedStaff.length}个`;
      }

      return result;
    },
    async confirmTag(tagData: any) {
      const res = await (this as any).$request.post(
        customerTagsApi.saveManualTag.url,
        {
          customerIds: [this.data.customerId],
          manualTagsName: tagData.tag,
          campPeriodId: this.selectedCampPeriodInfo.campPeriodId,
          campPeriodName: this.selectedCampPeriodInfo.campPeriodName,
        },
      );
      if (res.code === 0) {
        await this.$message.success('设置标签成功');
        this.data.tags.forEach((item: any) => {
          if (item.campPeriodId === this.selectedCampPeriodInfo.campPeriodId) {
            if (item.manualTagsName === '') {
              item.manualTagsName = tagData.tag;
            } else {
              item.manualTagsName += `,${  tagData.tag}`;
            }
          }
        });
      } else {
        await this.$message.error(res.msg || '设置标签失败');
      }
      this.closeTagVisible();
    },
    //  tab切换
    tabChange(tab: any) {
      this.tabValue = tab;
      console.log("tabChange", tab);
      if (tab === "1") {
        this.queryCustomerBehavior();
      } else if (tab === "2") {
        this.queryEnrolledCamps();
      } else if (tab === "3") {
        this.queryWecomTags();
      } else if (tab === "4") {
        this.queryOperation();
      }
    },
    // 营期切换
    columnChange() {
      console.log("columnChange", this.columnInfos);
      const columnInfo: any = this.columnInfos.filter((item: any) => item.id === this.selectedColumnId);
      console.log("columnInfo", columnInfo);
      this.customerCourseRelations  = columnInfo[0].customerSalesRelationList;
      console.log("columnChange", this.customerCourseRelations);
      this.queryEnrolledCamps();
      this.queryCustomerBehavior();
    }
  },
});
</script>
<style lang="less" scoped>
/deep/ .t-card__title {
  font-size: var(--td-font-size-body-medium);
}
.user-info-list {
  .contract {
    //width: 340px;
    //height: 88px;
    border-radius: var(--td-radius-default);
    margin: 8px 0;

    &-title {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      line-height: 24px;
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-placeholder);
    }

    &-detail {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-secondary);
    }
  }

  .contract:last-child {
    margin-bottom: 0;
  }
}
.content-container {
  margin-top: 16px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-default);

  // 选项卡固定样式
  .tab-panel-container {
    height: calc(100vh - 406px);
    .tabs-sticky-wrapper {
      // 下拉选择
      .tabs-select {
        margin: 16px 0;
      }
      .timerline-card {
        background-color: #F7FAFF;
        border: 1px solid #E4EEFF;
        .timerline-info {
          white-space: normal;
          word-wrap: break-word;
          hyphens: auto;
          overflow: visible;
          max-width: none;
          display: block;
          color: var(--td-text-color-secondary);
          .vertical-divider {
            margin: 0;
            border-left: 1px solid yellowgreen;
          }
        }

        // 企微相关行为卡片样式 - 添加企微
        &.wechat-enterprise-card.add-wechat {
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border: 2px solid #07C160;
          box-shadow: 0 4px 12px rgba(7, 193, 96, 0.15);

          .timerline-info .vertical-divider {
            border-left: 1px solid #07C160;
          }
        }

        // 企微相关行为卡片样式 - 删除企微
        &.wechat-enterprise-card.delete-wechat {
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
          border: 2px solid #FF4757;
          box-shadow: 0 4px 12px rgba(255, 71, 87, 0.15);

          .timerline-info .vertical-divider {
            border-left: 1px solid #FF4757;
          }
        }

        // 群聊相关行为卡片样式 - 加入群聊
        &.group-chat-card.join-group {
          background: linear-gradient(135deg, #f8faff 0%, #f1f5ff 100%);
          border: 2px solid #3742FA;
          box-shadow: 0 4px 12px rgba(55, 66, 250, 0.15);

          .timerline-info .vertical-divider {
            border-left: 1px solid #3742FA;
          }
        }

        // 群聊相关行为卡片样式 - 退出群聊
        &.group-chat-card.exit-group {
          background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
          border: 2px solid #747D8C;
          box-shadow: 0 4px 12px rgba(116, 125, 140, 0.15);

          .timerline-info .vertical-divider {
            border-left: 1px solid #747D8C;
          }
        }

        // 操作记录卡片样式
        &.operation-card {
          background-color: #F7FAFF;
          border: 1px solid #E4EEFF;

          .timerline-info .vertical-divider {
            border-left: 1px solid #8B5CF6;
          }

          .operation-operator {
            margin-bottom: 8px;
            padding: 6px 10px;
            background: rgba(139, 92, 246, 0.05);
            border-radius: 4px;
            border-left: 3px solid #8B5CF6;

            .operator-label {
              font-size: 11px;
              color: #6b7280;
              font-weight: 500;
            }
          }

          .operation-changes {
            .change-section {
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .section-title {
                display: flex;
                align-items: center;
                gap: 4px;
                margin-bottom: 4px;
                font-size: 11px;
                font-weight: 500;
                color: #374151;
              }

              .change-content-inline {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;
                padding-left: 12px;

                .change-item {
                  font-size: 12px;
                  color: var(--td-text-color-secondary);
                  white-space: nowrap;

                  .vertical-divider {
                    margin: 0 4px 0 0;
                    border-left: 1px solid #8B5CF6;
                  }
                }
              }
            }
          }
        }
      }
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); // 添加阴影防止内容穿透
      height: calc(100vh - 452px); // 减去分页和顶部的固定高度
      overflow-y: auto;
      margin-bottom: 16px;
    }
    .camp-sticky-wrapper {
      display: flex;
      height: 100%;
      gap: 0;

      .camp-sticky-operator {
        flex: 0 0 260px;
        height: 100%;
        background: #fafbfc;
        border-right: 1px solid var(--td-component-border);
        display: flex;
        flex-direction: column;

        .camp-list-header {
          padding: 16px;
          border-bottom: 1px solid var(--td-component-border);
          background: #fff;

          .header-title {
            font-weight: 500;
            font-size: 16px;
            color: #1f2937;
          }
        }

        .camp-list-content {
          flex: 1;
          overflow-y: auto;
          padding: 8px;

          .camp-list {
            .camp-item {
              margin-bottom: 8px;
              border-radius: 8px;
              border: 1px solid #e5e7eb;
              background: #fff;
              cursor: pointer;
              transition: all 0.3s ease;
              overflow: hidden;

              &:hover {
                border-color: #0052D9;
                box-shadow: 0 2px 8px rgba(0, 82, 217, 0.1);
              }

              &.active-item {
                border-color: #0052D9;
                background: linear-gradient(135deg, #f0f7ff 0%, #e6f3ff 100%);
                box-shadow: 0 4px 12px rgba(0, 82, 217, 0.15);

                .camp-item-content {
                  .camp-info .camp-title {
                    color: #0052D9;
                    font-weight: 600;
                  }
                }
              }

              .camp-item-content {
                display: flex;
                padding: 12px;
                gap: 12px;

                .camp-image-wrapper {
                  position: relative;
                  flex-shrink: 0;

                  .camp-image {
                    width: 70px;
                    height: 50px;
                    border-radius: 6px;
                    overflow: hidden;
                  }

                  .image-error {
                    width: 70px;
                    height: 50px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f5f5f5;
                    border-radius: 6px;
                    color: #ccc;
                  }

                  .camp-status-badge {
                    position: absolute;
                    top: -6px;
                    right: -6px;
                    width: 18px;
                    height: 18px;
                    background: #0052D9;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                    border: 2px solid white;
                    z-index: 10;
                  }
                }

                .camp-info {
                  flex: 1;
                  min-width: 0;

                  .camp-title {
                    font-size: 14px;
                    font-weight: 500;
                    color: #1f2937;
                    margin-bottom: 4px;
                    line-height: 1.4;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }

                  .camp-description {
                    font-size: 12px;
                    color: #6b7280;
                    line-height: 1.4;
                    margin-bottom: 8px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                  }

                  .camp-meta {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  }
                }
              }
            }
          }

          .empty-state {
            padding: 40px 20px;
            text-align: center;
          }
        }
      }

      .camp-sticky-content {
        flex: 1;
        height: 100%;
        overflow-y: auto;
        padding: 16px;
        background: #fafbfc;

        .camp-detail-wrapper {
          .camp-overview-card {
            margin-bottom: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            .card-title {
              font-weight: 500;
              font-size: 16px;
              color: #1f2937;
            }

            .overview-stats-inline {
              display: flex;
              justify-content: space-around;
              align-items: center;
              padding: 12px 0;

              .stat-item {
                text-align: center;
                flex: 1;

                .stat-value {
                  font-size: 18px;
                  font-weight: 600;
                  color: #0052D9;
                  margin-bottom: 2px;
                }

                .stat-label {
                  font-size: 11px;
                  color: #6b7280;
                }
              }
            }
          }

          .course-table-card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            .card-title {
              font-weight: 500;
              font-size: 16px;
              color: #1f2937;
            }

            .course-table {
              .progress-wrapper {
                .progress-text {
                  font-size: 12px;
                  color: #6b7280;
                  margin-bottom: 4px;
                  display: block;
                }

                .progress-bar {
                  width: 100%;
                }
              }
            }
          }
        }

        .no-selection {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      // 响应式设计
      @media (max-width: 1200px) {
        .camp-sticky-operator {
          flex: 0 0 240px;
        }
      }

      @media (max-width: 768px) {
        flex-direction: column;

        .camp-sticky-operator {
          flex: 0 0 200px;
          border-right: none;
          border-bottom: 1px solid var(--td-component-border);

          .camp-list-content .camp-list .camp-item .camp-item-content {
            padding: 8px;
            gap: 8px;

            .camp-image-wrapper .camp-image {
              width: 50px;
              height: 38px;
            }

            .camp-info .camp-title {
              font-size: 13px;
            }

            .camp-info .camp-description {
              font-size: 11px;
            }
          }
        }

        .camp-sticky-content {
          flex: 1;
          padding: 12px;

          .camp-detail-wrapper {
            .overview-stats-inline {
              padding: 8px 0;

              .stat-item {
                .stat-value {
                  font-size: 16px;
                }

                .stat-label {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 企微标签相关样式
.wecom-tags-wrapper {
  overflow-y: auto;
  height: 100%;
  padding: 12px 0;
}

.follow-user-card {
  margin-bottom: 12px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e7e7e7;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: #0052d9;
    box-shadow: 0 2px 8px rgba(0, 82, 217, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.follow-user-header {
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-bottom: 1px solid #e7e7e7;

  .user-info-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .user-basic-info {
    flex: 1;

    .user-name {
      font-size: 15px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 4px;

      .corp-name {
        font-weight: 400;
        color: #6b7280;
        margin-left: 4px;
      }
    }

    .user-meta {
      display: flex;
      align-items: center;
      gap: 8px;

      .add-time {
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
}

.follow-user-content {
  padding: 16px;
}

.user-details {
  margin-bottom: 16px;

  .detail-item {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
    font-size: 13px;
    color: #4b5563;

    &:last-child {
      margin-bottom: 0;
    }

    .t-icon {
      color: #9ca3af;
      flex-shrink: 0;
    }
  }
}

.wecom-tags-section {
  .tags-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 12px;

    .tags-title {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      margin: 0;
    }

    .t-icon {
      color: #0052d9;
    }
  }

  .tags-content {
    .tag-group {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .tag-group-name {
        font-size: 12px;
        font-weight: 500;
        color: #6b7280;
        margin-bottom: 6px;
      }

      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .wecom-tag {
          font-size: 12px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

.no-tags {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #9ca3af;
  font-size: 13px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px dashed #d1d5db;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

// 已加微信悬浮详情样式
.wechat-info-trigger {
  cursor: pointer;
  color: #0052d9;
  border-bottom: 1px dashed #0052d9;

  &:hover {
    color: #003d99;
    border-bottom-color: #003d99;
  }
}

.wechat-detail-popup {
  .popup-title {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
  }

  .wechat-staff-list {
    max-height: 300px;
    overflow-y: auto;

    .staff-item {
      margin-bottom: 12px;
      padding: 12px;
      background: #f9fafb;
      border-radius: 6px;
      border: 1px solid #e5e7eb;

      &:last-child {
        margin-bottom: 0;
      }

      .staff-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .staff-basic {
          flex: 1;

          .staff-name {
            font-size: 13px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 2px;
          }

          .staff-company {
            font-size: 11px;
            color: #6b7280;
          }
        }
      }

      .staff-details {
        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 4px;
          font-size: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            color: #6b7280;
            min-width: 60px;
            flex-shrink: 0;
          }

          .detail-value {
            color: #374151;
            flex: 1;
          }

          &.delete-info {
            .detail-label,
            .detail-value {
              color: #dc2626;
            }
          }
        }
      }
    }
  }
}
</style>
