<template>
  <t-dialog :visible.sync="localVisible" class="tag-dialog" @close="handleClose" @cancel="handleClose" @confirm="handleConfirm">
    <t-form style="height: 60px">
      <t-form-item label="标签" name="tag">
        <t-input
          v-model="tag"
          type="search"
          placeholder="请输入标签名称"
          :style="{ minWidth: '134px' }"
          :status="status"
          :tips="tips"
          maxlength="10"
          @change="tagChange"
        />
      </t-form-item>
    </t-form>

  </t-dialog>
</template>

<script lang="ts">
import Vue from 'vue';
import CustomerDetailComponent from "@/pages/customer-management/components/customer-detail-component.vue";
import sysCompanyGroupApi from "@/constants/api/back/sys-company.api";
import {treeNodeConvertService} from "@/service/tree-node-convert.service";
import apiUser from "@/constants/api/back/system-user.api";
import {Code} from "@/constants/enum/general/code.enum";

export default Vue.extend({
  name: 'SetTagDialog',
  components: {},
  props: {
    visible: {
      default: false,
    },
    data: {
      default: () => ({}),
    },
  },
  data() {
    return {
      tag: '',
      status: 'warning',
      tips: '请输入标签名称',
      localVisible: this.visible, // 新增本地状态
    };
  },
  watch: {
    visible(newVal) {
      this.localVisible = newVal;
    },
  },
  methods: {
    tagChange(value: string) {
      if (value) {
        this.status = 'default';
        this.tips = '';
      } else {
        this.status = 'warning';
        this.tips = '请输入标签名称';
      }
    },
    handleClose() {
      this.tag = '';
      this.localVisible = false;
      this.$emit('cancel');
    },
    handleConfirm() {
      if (!this.tag || this.tag === '') {
        this.status = 'error';
        this.tips = '请输入标签名称';
        this.$message.error('请输入标签名称');
        return;
      }
      this.localVisible = false;
      this.$emit('confirm', {
        tag: this.tag,
      });
      this.tag = '';
    },
  },
});
</script>
<style lang="less" scoped>
.tag-dialog {
  ///deep/ .t-dialog {
  //  min-width: 50px;
  //  width: 60%;
  //}
}
</style>
