<template>
  <t-drawer
    :visible.sync="localVisible"
    :size="size"
    :placement="placement"
    @close="handleClose"
    :header="false"
    :footer="false"
    destroyOnClose
  >
    <customer-detail-component :hasAllocationAuth="hasAllocationAuth" :data="data"></customer-detail-component>
  </t-drawer>
</template>

<script lang="ts">
import Vue from 'vue';
import CustomerDetailComponent from "@/pages/customer-management/components/customer-detail-component.vue";

export default Vue.extend({
  name: 'CustomerDrawer',
  components: { CustomerDetailComponent },
  props: {
    visible: {
      default: false,
    },
    size: {
      default: '80%',
    },
    placement: {
      default: 'right',
    },
    data: {
      default: () => ({}),
    },
    hasAllocationAuth: {
      default: false,
    }
  },
  data() {
    return {
      tabValue: 1,
      localVisible: this.visible, // 新增本地状态
      userInfo: [
        {
          title: '手机',
          content: '+86 13923734567',
        },
        {
          title: '座机',
          content: '734567',
        },
        {
          title: '办公室邮箱',
          content: '<EMAIL>',
        },
        {
          title: '座位',
          content: 'T32F 012',
        },
        {
          title: '管理主体',
          content: '腾讯集团',
        },
        {
          title: '直属上级',
          content: 'Michael Wang',
        },
        {
          title: '职位',
          content: '高级 UI 设计师',
        },
        {
          title: '入职时间',
          content: '2021-07-01',
        },
      ],
    };
  },
  watch: {
    visible(newVal) {
      this.localVisible = newVal;
    },
  },
  methods: {
    handleClose() {
      this.localVisible = false;
      this.$emit('cancel');
    },
  },
});
</script>
<style lang="less" scoped>
/deep/ .t-drawer__body {
  background-color: var(--td-bg-color-page);
}
</style>
