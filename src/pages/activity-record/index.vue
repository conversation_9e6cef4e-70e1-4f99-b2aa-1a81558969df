<template>
  <div>
    <div class="search-input">
      <t-form :data="formData" layout="inline">
        <t-form-item label="活动名称" name="activityName">
          <t-input-adornment>
            <t-input v-model="formData.activityName" type="search" placeholder="请输入活动名称" :style="{ minWidth: '134px' }" />
          </t-input-adornment>
        </t-form-item>
      </t-form>
      <div class="search-btn">
        <t-button theme="primary" @click="onSubmit">查询</t-button>
        <t-button theme="primary" @click="onReset" ghost>重置</t-button>
      </div>
    </div>
    <t-card class="card-container">
      <t-table :columns="columns" :data="data" :rowKey="rowKey" :pagination="pagination"
        :selected-row-keys="selectedRowKeys" :loading="dataLoading" @change="rehandleChange"
        @select-change="rehandleSelectChange">
        <template #activityType="{ row }">
          <span v-if="row.activityType === '1'"> 红包 </span>
          <span v-else-if="row.type === '2'"> 优惠券 </span>
          <span v-else-if="row.type === '3'"> 礼品 </span>
          <span v-else-if="row.type === '4'"> 积分 </span>
          <span v-else-if="row.type === '5'"> 答题 </span>
          <span v-else-if="row.type === '6'"> 红包 </span>
        </template>
        <template #actionType="{ row }">
          <span v-if="row.actionType === '1'"> 领取 </span>
          <span v-else-if="row.status === '2'"> 已使用/已回答 </span>
          <span v-else-if="row.status === '3'"> 过期 </span>
        </template>
      </t-table>
    </t-card>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import { ACTIVE_TYPE_OPTIONS } from '@/constants';
import dayjs from 'dayjs';
import sysActivityRecord from '@/constants/api/back/sys-activity-record.api';

export default Vue.extend({
  name: 'ActivityRecordMain',
  components: {
  },
  data() {
    return {
      ACTIVE_TYPE_OPTIONS,
      // 侧边栏
      drawerVar: {
        visible: false,
        data: {},
      },
      // 客户创建时间
      createTime: [
        dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建开始时间
        dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'), // 客户创建结束时间
      ],
      // 搜索条件
      formData: {
        activityName: '',
        activityType: '',
      },
      // 批量操作选项
      batchOperationOption: [
        {
          content: '暂未开放',
          value: '1',
        },
      ],
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          width: 64,
          fixed: 'left',
          disabled: (data: any) => {
            const { row } = data;
            return !!row.superAdmin;
          },
        },
        {
          title: '用户ID',
          align: 'left',
          width: 140,
          colKey: 'userId',
        },
        {
          title: '活动名称',
          align: 'left',
          width: 140,
          colKey: 'activityName',
        },
        {
          title: '活动类型',
          align: 'left',
          width: 120,
          colKey: 'activityType',
        },
        {
          title: '活动领取状态',
          align: 'left',
          width: 140,
          colKey: 'actionType',
        },
        {
          title: '领取时间',
          align: 'left',
          width: 140,
          colKey: 'createdAt',
        }
      ],
      rowKey: 'customerId',
      selectedRowKeys: [],
      data: [],
      dataLoading: false,
      tableLayout: 'auto',
      verticalAlign: 'top',
      pagination: {
        total: 0,
        current: 1,
        pageSize: 10,
      },
      deleteDialogVisible: false,
      selectedRow: null
    };
  },
  computed: {
    offsetTop() {
      return this.$store.state.setting.isUseTabsRouter ? 48 : 0;
    },
  },
  async mounted() {
    this.getList();
  },

  methods: {
    rehandleChange(changeParams: any) {
      this.pagination.pageSize = changeParams.pagination.pageSize;
      this.pagination.current = changeParams.pagination.current;
      this.getList();
      // console.log('统一Change', changeParams);
    },
    onSubmit() {
      console.log("this.formData", this.formData);
      this.pagination.current = 1;
      this.getList();
    },
    onReset() {
      this.pagination.current = 1;
      this.formData = {
        activityName: '',
        activityType: '',
      };
      this.getList();
    },
    rehandleSelectChange(selectedRowKeys: number[]) {
      (this as any).selectedRowKeys = selectedRowKeys;
    },
    // 获取初始表单数据方法
    async getList() {
      const query = {
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
        ...this.formData // 合并查询条件
      };
      try {
        this.dataLoading = true;
        const res = await (this as any).$request.get(
          sysActivityRecord.queryActivityListByPage.url,
          {
            params: query,
          },
        );
        const { code, data, msg } = res;
        if (code === 0) {
          this.data = data.records || [];
          this.pagination.total = +data.total || 0;
        } else {
          this.$message.error(msg || '请求失败');
        }
        this.dataLoading = false;
      } catch (e) {
        console.log(e);
        this.dataLoading = false;
      }
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.operation-container {
  margin: 15px 0;
}

/deep/ .t-table {
  height: 100%;
  overflow: hidden;

  /deep/ .t-table__content {
    height: 450px;
  }
}

/deep/ table {
  tr th {
    background: #fff;
  }
}

/deep/ .t-table__pagination {
  background: #f3f3f3;
  padding: 10px 20px;
  border-top: 1px solid #eee;
}

/deep/ .pla {
  color: #0052d9;
  padding: 0 3px;
}

/deep/ .t-table--striped {
  background: #999999;
  color: red;
}


::v-deep .t-button--variant-text {
  padding: 0 !important;
}

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
}

.left-operation-container {
  padding: 0 0 10px 0;

  .selected-count {
    display: inline-block;
    color: @text-color-secondary;
  }
}

.search-input {
  margin: 15px 0;

  .search-btn {
    text-align: right;
    margin-right: 20px;
    margin-top: -35px;
  }
}

.operator-title-tag {
  margin-right: 8px;
  margin-top: 8px;
  margin-left: unset;
  border: unset;
}

.operator-content {
  height: 320px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
