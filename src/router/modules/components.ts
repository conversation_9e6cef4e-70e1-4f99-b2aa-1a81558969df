import Layout from '@/layouts';

// @ts-ignore
export default [
  {
    path: '/organization-management',
    name: 'OrganizationManagementMain',
    redirect: '/organization-management/organization-management-main',
    component: Layout,
    children: [
      {
        path: 'organization-management-main',
        name: 'OrganizationManagement',
        component: () => import('@/pages/organization-management/organization-management-main/index.vue'),
        meta: {title: '组织架构', keepAlive: false, refresh: true},
      },
    ],
  },
  {
    path: '/customer-management',
    name: 'CustomerManagement',
    redirect: '/customer-management/customer-management-main',
    component: Layout,
    children: [
      {
        path: 'customer-management-main',
        name: 'CustomerManagementMain',
        component: () => import('@/pages/customer-management/customer-management-main/index.vue'),
        meta: {title: '客户管理', keepAlive: true, refresh: true, refreshFlag: '/customer-management/customer-management-main'},
      },
    ],
  },
  {
    path: '/user-management',
    name: 'UserManagement',
    redirect: '/user-management/user-management-main',
    component: Layout,
    children: [
      {
        path: 'user-management-main',
        name: 'UserManagementMain',
        component: () => import('@/pages/user-management/user-management-main/index.vue'),
        meta: {title: '用户管理', keepAlive: true, refresh: true, refreshFlag: '/user-management/user-management-main'},
      },
    ],
  },
  {
    path: '/role-management',
    name: 'RoleManagement',
    redirect: '/role-management/role-management-main',
    component: Layout,
    children: [
      {
        path: 'role-management-main',
        name: 'RoleManagementMain',
        component: () => import('@/pages/role-management/role-management-main/index.vue'),
        meta: {title: '角色管理', keepAlive: false, refresh: true},
      },
    ],
  },
  {
    path: '/app-management',
    name: 'AppManagement',
    redirect: '/app-management/app-management-main',
    component: Layout,
    children: [
      {
        path: 'app-menu-config',
        name: 'AppMenuConfig',
        component: () => import('@/pages/app-management/app-menu-config/index.vue'),
        meta: {title: '菜单管理', keepAlive: false, refresh: true},
      },
    ],
  },
  {
    path: '/video-management',
    name: 'VideoManagement',
    redirect: '/video-management/video-management-main',
    component: Layout,
    children: [
      {
        path: 'video-management-main',
        name: 'video-management-main',
        component: () => import('@/pages/video-management/video-list/index.vue'),
        meta: {title: '课程管理', keepAlive: true, refresh: true, refreshFlag: 'video-management-main'},
      },
      {
        path: 'new-video',
        name: 'new-video',
        component: () => import('@/pages/video-management/video-management-main/video.vue'),
        meta: {title: '新建课程', hasBack: true, keepAlive: true, refresh: true, refreshFlag: 'new-video'},
      },
    ]
  },
  {
    path: '/video-center',
    name: 'VideoCenter',
    redirect: '/video-center/video-center-main',
    component: Layout,
    children: [
      {
        path: 'video-center-main',
        name: 'video-center-main',
        component: () => import('@/pages/video-center/video-center-main/index.vue'),
        meta: {title: '视频中心', keepAlive: true, refresh: true},
      }
    ]
  },
  {
    path: '/training-camp',
    name: 'TrainingCamp',
    redirect: '/training-camp/training-camp-main',
    component: Layout,
    children: [
      {
        path: 'training-camp-main',
        name: 'training-camp-main',
        component: () => import('@/pages/training-camp/index.vue'),
        meta: {title: '训练营', keepAlive: true, refresh: true, refreshFlag: 'training-camp-main'},
      },
      {
        path: 'new-training-camp',
        name: 'new-training-camp',
        component: () => import('@/pages/training-camp/edit.vue'),
        meta: {title: '新建营期', hasBack: true, keepAlive: true, refresh: true, refreshFlag: 'new-training-camp'},
      },
      {
        path: 'training-camp-detail',
        name: 'training-camp-detail',
        component: () => import('@/pages/training-camp/detail.vue'),
        meta: {title: '营期详情', hasBack: true, keepAlive: false, refresh: true},
      },
      {
        path: 'training-camp-data',
        name: 'training-camp-data',
        component: () => import('@/pages/training-camp/detailData.vue'),
        meta: {title: '营期数据', hasBack: true, keepAlive: true, refresh: true, refreshFlag: 'training-camp-data'},
      }
    ]
  },
  {
    path: '/miniprogram-management',
    name: 'Miniprogram',
    redirect: '/miniprogram-management/miniprogram-management-main',
    component: Layout,
    children: [
      {
        path: 'miniprogram-management-main',
        name: 'miniprogram-management-main',
        component: () => import('@/pages/miniprogram-management/index.vue'),
        meta: {title: '小程序管理', keepAlive: false, refresh: true},
      }
    ]
  },
  {
    path: '/enterprise-wechat-management',
    name: 'EnterpriseWechatManagement',
    redirect: '/enterprise-wechat-management/enterprise-wechat-management-main',
    component: Layout,
    children: [
      {
        path: 'enterprise-wechat-management-main',
        name: 'enterprise-wechat-management-main',
        component: () => import('@/pages/enterprise-wechat-management/index.vue'),
        meta: {title: '企业微信管理', keepAlive: false, refresh: true},
      },
      {
        path: 'enterprise-wechat-message',
        name: 'enterprise-wechat-message',
        component: () => import('@/pages/enterprise-wechat-management/auth-message.vue'),
        meta: {title: '授权信息', keepAlive: true, refresh: true, hasBack: true, refreshFlag:'marketing-campaign-main'},
      },
      {
        path: 'enterprise-wechat-tag',
        name: 'enterprise-wechat-tag',
        component: () => import('@/pages/enterprise-wechat-management/tag-management.vue'),
        meta: {title: '标签管理', keepAlive: false, refresh: true, hasBack: true},
      }
    ]
  },
  // {
  //   path: '/activity-record',
  //   name: 'ActivityRecord',
  //   redirect: '/activity-record/activity-record-main',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'activity-record-main',
  //       name: 'activity-record-main',
  //       component: () => import('@/pages/activity-record/index.vue'),
  //       meta: {title: '活动记录'},
  //     }
  //   ]
  // },
  {
    path: '/course-redpacket',
    name: 'CourseRedPacket',
    redirect: '/course-redpacket/course-redpacket-main',
    component: Layout,
    children: [
      {
        path: 'course-redpacket-main',
        name: 'course-redpacket-main',
        component: () => import('@/pages/course-redpacket/index.vue'),
        meta: {title: '红包领取记录', keepAlive: false, refresh: true},
      }
    ]
  },
  {
    path: '/mall-management',
    name: 'MallManagement',
    redirect: '/mall-management/mall-management-home',
    component: Layout,
    children: [
      {
        path: 'mall-management-home',
        name: 'mall-management-home',
        component: () => import('@/pages/mall-management/home-page/index.vue'),
        meta: {title: '商城管理首页', keepAlive: false, refresh: true},
      },
      {
        path: 'mall-management-store-setting',
        name: 'mall-management-store-setting',
        component: () => import('@/pages/mall-management/store-setting/index.vue'),
        meta: {title: '店铺设置', keepAlive: false, refresh: true},
      },
      {
        path: 'store-form/add',
        name: 'store-form-add',
        component: () => import('@/pages/mall-management/store-setting/form.vue'),
        meta: {title: '新增店铺', hasBack: true, keepAlive: false, refresh: true},
      },
      {
        path: 'store-form/edit/:id',
        name: 'store-form-edit',
        component: () => import('@/pages/mall-management/store-setting/form.vue'),
        meta: {title: '编辑店铺', hasBack: true, keepAlive: false, refresh: true},
      },
      {
        path: 'mall-management-category-list',
        name: 'mall-management-category-list',
        component: () => import('@/pages/mall-management/category-list/index.vue'),
        meta: {title: '分类列表', keepAlive: false, refresh: true},
      },
      {
        path: 'mall-management-product-list',
        name: 'mall-management-product-list',
        component: () => import('@/pages/mall-management/product-list/index.vue'),
        meta: {title: '商品列表', keepAlive: false, refresh: true},
      },
      {
        path: 'product-form/add',
        name: 'product-form-add',
        component: () => import('@/pages/mall-management/product-list/form.vue'),
        meta: {title: '新增商品', hasBack: true, keepAlive: false, refresh: true},
      },
      {
        path: 'product-form/edit/:id',
        name: 'product-form-edit',
        component: () => import('@/pages/mall-management/product-list/form.vue'),
        meta: {title: '编辑商品', hasBack: true, keepAlive: false, refresh: true},
      },
      {
        path: 'mall-management-order-list',
        name: 'mall-management-order-list',
        component: () => import('@/pages/mall-management/order-list/index.vue'),
        meta: {title: '订单列表', keepAlive: false, refresh: true},
      },
      {
        path: 'miniprogram-settings',
        name: 'miniprogram-settings',
        component: () => import('@/pages/mall-management/miniprogram-settings/index.vue'),
        meta: {title: '横幅管理', keepAlive: false, refresh: true},
      },
    ]
  },
  {
    path: '/marketing-campaign',
    name: 'MarketingCampaign',
    redirect: '/marketing-campaign/marketing-campaign-main',
    component: Layout,
    children: [
      {
        path: 'marketing-campaign-main',
        name: 'marketing-campaign-main',
        component: () => import('@/pages/marketing-campaign/index.vue'),
        meta: {title: '营销活动', keepAlive: true, refresh: true, refreshFlag:'marketing-campaign-main'},
      },
      {
        path: 'new-marketing-campaign',
        name: 'new-marketing-campaign',
        component: () => import('@/pages/marketing-campaign/edit.vue'),
        meta: {title: '新建营销活动', hasBack: true, keepAlive: true, refresh: true, refreshFlag: 'new-marketing-campaign'},
      }
    ]
  },
  {
    path: '/tenant-parameter-config',
    name: '/TenantParameterConfig',
    redirect: '/tenant-parameter-config/main',
    component: Layout,
    children: [
      {
        path: 'main',
        name: 'TenantParameterConfig',
        component: () => import('@/pages/tenant-parameter-config/index.vue'),
        meta: {title: '系统参数配置', keepAlive: false, refresh: true},
      },
    ],
  },
];
