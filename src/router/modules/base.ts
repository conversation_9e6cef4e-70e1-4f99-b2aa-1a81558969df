import { HomeIcon } from 'tdesign-icons-vue';
import Layout from '@/layouts';

export const baseRouterForMenu = [{
  path: '/home/<USER>',
  name: 'home',
  type:'base',
  meta: {
    title: '首页',
    icon: HomeIcon,
  },
}];
export default [
  {
    path: '',
    component: Layout,
    redirect: '/home/<USER>',
    name: 'home',
    meta: {
      title: '首页',
      icon: 'HomeIcon',
    },
    children: [
      {
        path: '/home/<USER>',
        name: 'WelCome',
        // component: () => import('@/pages/home/<USER>/index.vue'),
        component: () => import('@/pages/home/<USER>/index.vue'),
        meta: { hideTitle: true },
      },
    ],
  },
];
