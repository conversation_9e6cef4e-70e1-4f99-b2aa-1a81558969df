import Vue from 'vue';
import Vuex from 'vuex';
import user from './modules/user';
import setting from './modules/setting';
import permission from './modules/permission';
import tabRouter from './modules/tab-router'; // 多标签管理
import config from './modules/config';
import configStore  from '@/store/modules/config';
import upload from './modules/upload'; // 上传管理

Vue.use(Vuex);

window.sysConfigStore = configStore;

const store = new Vuex.Store({
  strict: import.meta.env.MODE === 'release',
  modules: {
    user,
    setting,
    config,
    permission,
    tabRouter,
    upload,
  },
});

export default store;
