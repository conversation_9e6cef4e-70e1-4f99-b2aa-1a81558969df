export default {
  state: {
    activeTasks: [],
    completedTasks: [],
    isShowUploadList: false,
    lockedTasks: new Set(),
    successTasks: [],
  },
  mutations: {
    LOCK_TASK(state: any, taskId: string) {
      state.lockedTasks.add(taskId);
    },
    UNLOCK_TASK(state: any, taskId: string) {
      state.lockedTasks.delete(taskId);
    },
    SET_SHOW_UPLOAD_LIST(state: any, isShow: any) {
      state.isShowUploadList = isShow;
    },
    ADD_UPLOAD_TASKS(state: any, tasks: any) {
      state.activeTasks.push(...tasks);
    },
    UPDATE_PROGRESS(state: any, { id, progress }) {
      const task = state.activeTasks.find((t: any) => t.id === id);
      if (task) task.progress = progress;
    },
    SET_TASK_CHUNK(state: any, { id, currentChunk }) {
      const task = state.activeTasks.find((t: any) => t.id === id);
      if (task) {
        task.status = 'pending';
        task.retries = 0;
        // task.currentChunk = currentChunk;
      }
    },
    SET_TASK_SUCCESS(state: any, { id }) {
      // 更新任务状态为成功
      const task = state.activeTasks.find((t: any) => t.id === id);
      if (task) {
        task.status = 'success';
        // task.currentChunk = task.chunks.length;
        task.progress = 100;
        state.lockedTasks.delete(id); // 确保解锁
        state.successTasks.push(task.id); // 成功任务列表
      }
    },
    SET_TASK_ERROR(state: any, { id, status, retries }) {
      // 更新任务状态为失败及重试次数，每片有三次重试机会
      const task = state.activeTasks.find((t: any) => t.id === id);
      if (task) {
        task.status = task.retries < 3 ? 'retrying' : 'error';
        task.retries = retries;
      }
    },
    deleteTask(state: any, id: any) {
      const index = state.activeTasks.findIndex((t: any) => t.id === id);
      if (index !== -1) state.activeTasks.splice(index, 1);
    },
    // 关闭全局展示上传任务时，清空所有上传任务
    deleteAllTasks(state: any) {
      state.activeTasks = [];
      state.completedTasks = [];
    },
  },
  getters: {
    nextPendingTasks: (state: any) => (max: number) =>
      state.activeTasks
        .filter((task: any) =>
          (task.status === 'pending' || task.status === 'retrying') &&
          task.retries < 3 &&
          !state.lockedTasks.has(task.id)
        )
        .slice(0, max),
    getShowUpload: (state: any) => state.isShowUploadList,
    getTaskById: (state: any) => (id: any) => state.activeTasks.find((t: any) => t.id === id),
  }
};
