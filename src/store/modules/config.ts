import sysConfigApi from '@/constants/api/back/sys-config.api';
import { API_CODE } from '@/constants/index';
import request from '@/utils/request.ts';
import store from 'store';

/* eslint-disable no-param-reassign */
const TenantParameterConfig = {
  systemName: '-',
};

const NAMESPACE = 'app:';

// 定义的state初始值
const state = {
  tenantParameterConfig: TenantParameterConfig,
};

const mutations = {
  update(state: any, payload: any) {
    state.tenantParameterConfig = payload;
  },
};

const getters = {
  tenantParameterConfig: (state: any) => state.tenantParameterConfig,
};

const actions = {
  changeTenantParameterConfig({ commit, dispatch }, payload: any) {
    commit('update', payload || TenantParameterConfig);
  },
};

class ConfigStore {
  private static readonly CONFIG = `${NAMESPACE}config`;
  private static readonly MENUS = `${NAMESPACE}menus`;

  // 菜单
  public set sysMenuInfos(menus: any) {
    if (menus) {
      store.set(ConfigStore.MENUS, menus);
    } else {
      store.remove(ConfigStore.MENUS);
    }
  }

  // 菜单
  public get sysMenuInfos(): any {
    return store.get(ConfigStore.MENUS);
  }

  public set sysConfig(config: any) {
    if (config) {
      store.set(ConfigStore.CONFIG, config);
    } else {
      store.remove(ConfigStore.CONFIG);
    }
  }

  public get sysConfig(): any {
    const config = store.get(ConfigStore.CONFIG) || {};
    if (!config.systemName) {
      config.systemName = TenantParameterConfig.systemName;
    }
    return config;
  }
  // 系统logo
  public get systemLogo(): string {
    return this.sysConfig.systemLogo;
  }

  // 系统名称
  public get systemName(): string {
    return this.sysConfig.systemName;
  }

}

const configStore: ConfigStore = new ConfigStore();
export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
  ConfigStore,
  configStore,
};
