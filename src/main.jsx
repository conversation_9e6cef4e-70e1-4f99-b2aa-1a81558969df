import './installCompositionApi.js';
import Vue from 'vue';
import VueKonva from 'vue-konva';
import VueRouter from 'vue-router';
import { sync } from 'vuex-router-sync';
import TDesign from 'tdesign-vue';
import VueClipboard from 'vue-clipboard2';
import axiosInstance from '@/utils/request';
import filterService from './filters/filter.service';
import App from './App.vue';
import router from './router';
import zhConfig from 'tdesign-vue/es/locale/zh_CN';
import promiseFinally from 'promise.prototype.finally';
import apiService from "./service/api.service";

import '@/style/index.less';

import './permission';
import store from './store';

promiseFinally.shim();
Vue.use(VueRouter);
Vue.use(TDesign);
Vue.use(VueClipboard);
Vue.use(VueKonva); // 使用Konva

/* 注册全局过滤器 */
filterService.init();

/* 注册axios */
apiService.init();

Vue.component('t-page-header');


Vue.prototype.$request = axiosInstance;
Vue.prototype.$store = store;

const originPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originPush.call(this, location).catch((err) => err);
};

const originReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originReplace.call(this, location).catch((err) => err);
};

Vue.config.productionTip = false;
sync(store, router);

// 挂载应用之前, 先获取 COS 前缀
const startApp = async () => {

  window.VUE_APP_COS_URL = import.meta.env.VITE_APP_COS_URL;

  const app = new Vue({
    router,
    store,
    // eslint-disable-next-line no-unused-vars
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    render: (h) => (
      <div>
        {/* 可以通过config-provider提供全局（多语言、全局属性）配置，如
      <t-config-provider globalConfig={enConfig}> */}
        <t-config-provider globalConfig={zhConfig}>
          <App />
        </t-config-provider>
      </div>
    ),
  });
  app.$mount('#app');
};

startApp();
