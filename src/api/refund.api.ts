/**
 * 退款相关API接口
 * 对接后端退款服务接口
 * <AUTHOR>
 * @date 2025-01-07
 */

import { get, post } from "@/utils/http.request";
import { HeaderType } from "@/constants/enum/header-type.enum";
import { Result } from "@/model/domain/Result";

const baseURL = (process.env.VUE_APP_ENABLE_GATEWAY === "on" ? process.env.VUE_APP_MS_GATEWAY_API + process.env.VUE_APP_MALL_API : process.env.VUE_APP_MALL_API) + process.env.VUE_APP_API_PREFIX;
const prefix = "mall/refund";

export interface RefundApplyParams {
  orderId: string
  customerId: string
  refundType: 'FULL' // 当前只支持全额退款
  refundReason: string
  refundDescription?: string
  refundImages?: string[] // 退款凭证图片URL列表（最多5张）
}

export interface RefundListParams {
  customerId: string
  refundStatus?: string
  refundNo?: string
  orderNumber?: string
  pageNum?: number
  pageSize?: number
}

export interface RefundDetailParams {
  refundId: string
  customerId: string
}

/**
 * @Description: 申请退款
 */
export const applyRefund = (data: RefundApplyParams): Promise<Result> => {
  return post(`${baseURL}/${prefix}/apply`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 查询我的退款列表
 */
export const getMyRefundList = (data: RefundListParams): Promise<Result> => {
  const params = {
    ...data,
    pageNum: data.pageNum || 1,
    pageSize: data.pageSize || 10
  };
  return post(`${baseURL}/${prefix}/my-list`, params, HeaderType.AUTH.code);
};

/**
 * @Description: 获取退款详情
 */
export const getRefundDetail = (refundId: string, customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/detail/${refundId}`, { customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 检查订单退款资格
 */
export const checkRefundEligibility = (orderId: string, customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/check-eligibility`, { orderId, customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 取消退款申请
 */
export const cancelRefund = (refundId: string, customerId: string): Promise<Result> => {
  return post(`${baseURL}/${prefix}/cancel`, { refundId, customerId }, HeaderType.AUTH.code);
};

/**
 * @Description: 根据订单ID获取退款信息
 */
export const getRefundByOrderId = (orderId: string, customerId: string): Promise<Result> => {
  return get(`${baseURL}/${prefix}/by-order/${orderId}`, { customerId }, HeaderType.AUTH.code);
};

// ==================== 管理端退款审核接口 ====================

export interface RefundAuditListParams {
  pageNum?: number
  pageSize?: number
  refundStatus?: string
  searchKeyword?: string
  applyDate?: string
  startDate?: string
  endDate?: string
}

export interface RefundAuditParams {
  refundId: string
  auditResult: 'APPROVED' | 'REJECTED'
  auditRemark?: string
}

export interface BatchAuditParams {
  refundIds: string[]
  auditResult: 'APPROVED' | 'REJECTED'
  auditRemark?: string
}

/**
 * @Description: 获取退款审核列表（管理端）
 */
export const getRefundAuditList = (data: RefundAuditListParams): Promise<Result> => {
  const params = {
    ...data,
    pageNum: data.pageNum || 1,
    pageSize: data.pageSize || 10
  };
  return post(`${baseURL}/admin/refund/list`, params, HeaderType.AUTH.code);
};

/**
 * @Description: 获取退款详情（管理端）
 */
export const getRefundDetail = (refundId: string): Promise<Result> => {
  return get(`${baseURL}/admin/refund/detail/${refundId}`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 审核退款申请
 */
export const auditRefund = (data: RefundAuditParams): Promise<Result> => {
  return post(`${baseURL}/admin/refund/audit`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 批量审核退款申请
 */
export const batchAuditRefunds = (data: BatchAuditParams): Promise<Result> => {
  return post(`${baseURL}/admin/refund/batch-audit`, data, HeaderType.AUTH.code);
};

/**
 * @Description: 获取退款统计数据
 */
export const getRefundStatistics = (): Promise<Result> => {
  return get(`${baseURL}/admin/refund/statistics`, {}, HeaderType.AUTH.code);
};

/**
 * @Description: 获取待处理退款数量
 */
export const getPendingRefundCount = (): Promise<Result> => {
  return get(`${baseURL}/admin/refund/pending-count`, {}, HeaderType.AUTH.code);
};