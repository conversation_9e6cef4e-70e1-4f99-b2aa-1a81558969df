import { Code } from '@/constants/enum/general/code.enum';

export class Result<T = any> {
  code?: number;

  msg?: string;

  data?: T;

  meta?: any;

  constructor(
    options: {
      code?: number;
      msg?: string;
      data?: T;
      meta?: any;
    } = {},
  ) {
    this.code = options.code;
    this.msg = options.msg || '';
    this.data = options.data;
    this.meta = options.meta;
  }

  static ok(data?: any, msg = Code.OK.name) {
    return new Result({ code: Code.OK.code, msg, data });
  }

  static failure(msg = Code.FAIL.name) {
    return new Result({ code: Code.FAIL.code, msg });
  }
}
